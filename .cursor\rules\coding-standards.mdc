---
description: 
globs: 
alwaysApply: true
---
# Standar Coding

## PHP
- Gunakan PDO untuk koneksi database
- <PERSON><PERSON><PERSON> prepared statements untuk query
- Gunakan transaksi database untuk operasi yang membutuhkan konsistensi
- Tangani error dengan try-catch
- Log error ke file untuk debugging
- Gunakan header yang sesuai untuk response

## Form Handling
- Validasi input di server side
- Sanitasi data sebelum disimpan ke database
- Gunakan CSRF token untuk keamanan
- Batasi ukuran dan tipe file upload
- Generate nama file unik untuk upload

## Database
- Gunakan foreign key untuk relasi antar tabel
- Set ON DELETE CASCADE untuk relasi yang sesuai
- Gunakan tipe data yang tepat untuk setiap kolom
- Tambahkan index untuk kolom yang sering dicari
- Gunakan charset utf8mb4 untuk dukungan Unicode

## File Structure
- Pisahkan konfigurasi ke file terpisah
- Gunakan direktori yang terorganisir
- Batasi ukuran file maksimal 300 baris
- Dokumentasikan fungsi dan class
- Gunakan nama file yang deskriptif

## Security
- <PERSON>an simpan password di kode
- Gunakan environment variables untuk data sensitif
- Validasi dan sanitasi semua input user
- Batasi akses ke direktori upload
- Gunakan HTTPS untuk transfer data


