let currentStep = 1;
const totalSteps = 4;
let selectedFiles = [];
const maxFileSize = 10 * 1024 * 1024; // 10MB
const allowedTypes = {
  "application/pdf": "pdf",
  "image/jpeg": "image",
  "image/jpg": "image",
  "image/png": "image",
  "audio/mpeg": "audio",
  "audio/mp3": "audio",
  "audio/wav": "audio",
  "video/mp4": "video",
  "application/msword": "document",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    "document",
  "application/vnd.ms-excel": "spreadsheet",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
    "spreadsheet",
  "application/vnd.ms-powerpoint": "presentation",
  "application/vnd.openxmlformats-officedocument.presentationml.presentation":
    "presentation",
};

// Fungsi untuk menampilkan step tertentu
function showStep(step) {
  // Sembunyikan semua step
  document.querySelectorAll(".step").forEach((stepElement) => {
    stepElement.classList.remove("active");
  });

  // Tampilkan step yang dipilih
  const targetStep = document.querySelector(`[data-step="${step}"]`);
  if (targetStep) {
    targetStep.classList.add("active");
  }

  // Update progress bar
  updateProgressBar(step);

  // Update progress text
  document.getElementById(
    "progress-text"
  ).textContent = `Step ${step} of ${totalSteps}`;

  // Scroll ke atas
  window.scrollTo({ top: 0, behavior: "smooth" });
}

// Fungsi untuk update progress bar
function updateProgressBar(step) {
  const progressBar = document.getElementById("progress-bar");
  const percentage = (step / totalSteps) * 100;
  progressBar.style.width = `${percentage}%`;
}

// Fungsi untuk validasi step saat ini
function validateCurrentStep() {
  const currentStepElement = document.querySelector(
    `[data-step="${currentStep}"]`
  );
  const requiredFields = currentStepElement.querySelectorAll("[required]");

  for (let field of requiredFields) {
    // Validasi untuk checkbox tools
    if (field.name === "tools_used") {
      const checkboxes = currentStepElement.querySelectorAll(
        'input[name="tools_used"]:checked'
      );
      if (checkboxes.length === 0) {
        showNotification(
          "Mohon pilih minimal satu tools yang pernah digunakan",
          "error"
        );
        field.focus();
        return false;
      }
      continue;
    }

    // Validasi untuk field biasa
    if (!field.value.trim()) {
      field.focus();
      field.classList.add("border-red-500");
      showNotification("Mohon lengkapi semua field yang wajib diisi", "error");
      return false;
    } else {
      field.classList.remove("border-red-500");
    }

    // Validasi khusus untuk radio button
    if (field.type === "radio") {
      const radioGroup = currentStepElement.querySelectorAll(
        `[name="${field.name}"]`
      );
      const isChecked = Array.from(radioGroup).some((radio) => radio.checked);
      if (!isChecked) {
        showNotification("Mohon pilih salah satu opsi", "error");
        return false;
      }
    }
  }

  return true;
}

// Fungsi untuk ke step berikutnya
function nextStep() {
  if (validateCurrentStep()) {
    if (currentStep < totalSteps) {
      currentStep++;
      showStep(currentStep);
    }
  }
}

// Fungsi untuk ke step sebelumnya
function prevStep() {
  if (currentStep > 1) {
    currentStep--;
    showStep(currentStep);
  }
}

// Fungsi untuk menampilkan notifikasi
function showNotification(message, type = "info") {
  // Hapus notifikasi yang ada
  const existingNotification = document.querySelector(".notification");
  if (existingNotification) {
    existingNotification.remove();
  }

  // Buat notifikasi baru
  const notification = document.createElement("div");
  notification.className = `notification fixed top-4 right-4 px-6 py-4 rounded-lg shadow-lg z-50 max-w-sm`;

  if (type === "error") {
    notification.className += " bg-red-500 text-white";
  } else if (type === "success") {
    notification.className += " bg-green-500 text-white";
  } else {
    notification.className += " bg-blue-500 text-white";
  }

  notification.textContent = message;
  document.body.appendChild(notification);

  // Hapus notifikasi setelah 3 detik
  setTimeout(() => {
    if (notification) {
      notification.remove();
    }
  }, 3000);
}

// Event listener untuk form submit
document.addEventListener("DOMContentLoaded", function () {
  const form = document.getElementById("multiStepForm");
  form.addEventListener("submit", handleFormSubmit);

  // Inisialisasi tampilan awal
  showStep(1);

  // Initialize file upload functionality
  initializeFileUpload();

  // Tambahkan event listener untuk input validation real-time
  const inputs = document.querySelectorAll("input, textarea, select");
  inputs.forEach((input) => {
    input.addEventListener("blur", function () {
      if (
        this.hasAttribute("required") &&
        !this.value.trim() &&
        this.type !== "checkbox"
      ) {
        this.classList.add("border-red-500");
      } else {
        this.classList.remove("border-red-500");
      }
    });

    input.addEventListener("input", function () {
      if (this.classList.contains("border-red-500") && this.value.trim()) {
        this.classList.remove("border-red-500");
      }
    });

    input.addEventListener("change", function () {
      if (this.classList.contains("border-red-500") && this.value.trim()) {
        this.classList.remove("border-red-500");
      }
    });
  });
});

// Fungsi untuk menangani submit form
async function handleFormSubmit(event) {
  event.preventDefault();

  if (!validateCurrentStep()) {
    return;
  }

  // Validasi file upload - gunakan selectedFiles array
  if (selectedFiles.length === 0) {
    showNotification("Mohon upload portfolio atau dokumentasi", "error");
    return;
  }

  try {
    const form = document.getElementById("multiStepForm");
    const formData = new FormData(form);

    // File sudah otomatis terambil dari input karena sudah di-update via updateFileInput()
    console.log('FormData files:', formData.getAll('portfolio_files[]'));

    // Tampilkan loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = 'Mengirim...';

    // Tampilkan progress upload
    const uploadProgressContainer = document.getElementById('uploadProgressContainer');
    const uploadProgressFill = document.getElementById('uploadProgressFill');
    const uploadProgressText = document.getElementById('uploadProgressText');
    uploadProgressContainer.style.display = 'block';

    showNotification("Sedang mengirim aplikasi...", "info");

    const response = await fetch('process_form2.php', {
      method: 'POST',
      body: formData
    });

    const result = await response.json();

    if (result.success) {
      // Tampilkan pesan sukses
      document.getElementById("multiStepForm").style.display = "none";
      document.getElementById("successMessage").classList.remove("hidden");
      showNotification("Aplikasi berhasil dikirim!", "success");

      // Clear saved data
      clearSavedData();

      // Scroll ke pesan sukses
      document.getElementById("successMessage").scrollIntoView({
        behavior: "smooth",
      });
    } else {
      throw new Error(result.error || 'Terjadi kesalahan saat mengirim aplikasi');
    }

  } catch (error) {
    showNotification(error.message, "error");
  } finally {
    // Reset button state
    const submitBtn = document.getElementById("multiStepForm").querySelector('button[type="submit"]');
    if (submitBtn) {
      submitBtn.disabled = false;
      submitBtn.innerHTML = 'Kirim Aplikasi';
    }

    // Hide progress
    const uploadProgressContainer = document.getElementById('uploadProgressContainer');
    if (uploadProgressContainer) {
      uploadProgressContainer.style.display = 'none';
    }
  }
}

// Fungsi untuk navigasi dengan keyboard (optional)
document.addEventListener("keydown", function (event) {
  // Enter untuk lanjut (kecuali di textarea)
  if (event.key === "Enter" && event.target.tagName !== "TEXTAREA") {
    event.preventDefault();
    if (currentStep < totalSteps) {
      nextStep();
    }
  }

  // Escape untuk kembali
  if (event.key === "Escape") {
    if (currentStep > 1) {
      prevStep();
    }
  }
});

// Fungsi untuk menyimpan data form ke localStorage (optional)
function saveFormData() {
  const formData = new FormData(document.getElementById("multiStepForm"));
  const data = {};

  // Handle checkbox values
  const checkboxes = document.querySelectorAll(
    'input[type="checkbox"]:checked'
  );
  const toolsUsed = [];
  checkboxes.forEach((checkbox) => {
    if (checkbox.name === "tools_used") {
      toolsUsed.push(checkbox.value);
    }
  });

  for (let [key, value] of formData.entries()) {
    if (key === "tools_used") {
      data[key] = toolsUsed;
    } else {
      data[key] = value;
    }
  }

  localStorage.setItem("growthConsultantFormData", JSON.stringify(data));
}

// Fungsi untuk memuat data form dari localStorage (optional)
function loadFormData() {
  const savedData = localStorage.getItem("growthConsultantFormData");
  if (savedData) {
    const data = JSON.parse(savedData);
    const form = document.getElementById("multiStepForm");

    Object.keys(data).forEach((key) => {
      if (key === "tools_used" && Array.isArray(data[key])) {
        // Handle checkbox restoration
        data[key].forEach((value) => {
          const checkbox = form.querySelector(
            `[name="${key}"][value="${value}"]`
          );
          if (checkbox) {
            checkbox.checked = true;
          }
        });
      } else {
        const field = form.querySelector(`[name="${key}"]`);
        if (field) {
          if (field.type === "radio") {
            const radioButton = form.querySelector(
              `[name="${key}"][value="${data[key]}"]`
            );
            if (radioButton) {
              radioButton.checked = true;
            }
          } else {
            field.value = data[key];
          }
        }
      }
    });
  }
}

// Auto-save form data setiap kali user mengubah input
document.addEventListener("change", saveFormData);
document.addEventListener("input", saveFormData);

// Load saved data saat halaman dimuat
window.addEventListener("load", loadFormData);

// Function untuk clear saved data saat form berhasil dikirim
function clearSavedData() {
  localStorage.removeItem("growthConsultantFormData");
}

// File Upload Functions
function initializeFileUpload() {
  const fileUploadArea = document.getElementById("fileUploadArea");
  const fileInput = document.getElementById("fileInput");
  const fileUploadButton = fileUploadArea.querySelector(".file-upload-button");

  // Click handler for upload area
  fileUploadArea.addEventListener("click", (e) => {
    // Jika klik bukan pada tombol, trigger file input
    if (!e.target.closest(".file-upload-button")) {
      fileInput.click();
    }
  });

  // Click handler untuk tombol Pilih File
  fileUploadButton.addEventListener("click", (e) => {
    e.preventDefault();
    e.stopPropagation();
    fileInput.click();
  });

  // File input change handler
  fileInput.addEventListener("change", handleFileSelect);

  // Drag and drop handlers
  fileUploadArea.addEventListener("dragover", handleDragOver);
  fileUploadArea.addEventListener("dragleave", handleDragLeave);
  fileUploadArea.addEventListener("drop", handleFileDrop);

  // Prevent default drag behaviors
  ["dragenter", "dragover", "dragleave", "drop"].forEach(eventName => {
    fileUploadArea.addEventListener(eventName, preventDefaults, false);
    document.body.addEventListener(eventName, preventDefaults, false);
  });

  function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
  }
}

function handleDragOver(e) {
  e.preventDefault();
  e.stopPropagation();
  e.currentTarget.classList.add("drag-over");
}

function handleDragLeave(e) {
  e.preventDefault();
  e.stopPropagation();
  e.currentTarget.classList.remove("drag-over");
}

function handleFileDrop(e) {
  e.preventDefault();
  e.stopPropagation();
  e.currentTarget.classList.remove("drag-over");

  const files = Array.from(e.dataTransfer.files);
  processFiles(files);
}

function handleFileSelect(e) {
  const files = Array.from(e.target.files);
  processFiles(files);
}

function processFiles(files) {
  const validFiles = [];
  const errors = [];

  files.forEach((file) => {
    const validation = validateFile(file);
    if (validation.valid) {
      validFiles.push(file);
    } else {
      errors.push(`${file.name}: ${validation.error}`);
    }
  });

  if (errors.length > 0) {
    showFileError(errors.join("\n"));
  }

  if (validFiles.length > 0) {
    addFilesToSelection(validFiles);
    updateFilePreview();
    showFileSuccess(`${validFiles.length} file(s) berhasil dipilih`);
  }
}

function validateFile(file) {
  // Check file type
  if (!allowedTypes[file.type]) {
    return { valid: false, error: "Tipe file tidak didukung" };
  }

  // Check file size
  if (file.size > maxFileSize) {
    return { valid: false, error: "Ukuran file terlalu besar (maksimal 10MB)" };
  }

  // Check if file already exists
  if (selectedFiles.some((f) => f.name === file.name && f.size === file.size)) {
    return { valid: false, error: "File sudah dipilih" };
  }

  return { valid: true };
}

function addFilesToSelection(files) {
  files.forEach((file) => {
    file.id = String(Date.now() + Math.random()); // Convert to string immediately
    selectedFiles.push(file);
  });

  // Update the actual file input with selected files
  updateFileInput();
}

function updateFileInput() {
  const fileInput = document.getElementById("fileInput");
  const dt = new DataTransfer();

  selectedFiles.forEach(file => {
    dt.items.add(file);
  });

  fileInput.files = dt.files;
}

function removeFileFromSelection(fileId) {
  // Convert fileId to string to ensure proper comparison
  const idToRemove = String(fileId);
  selectedFiles = selectedFiles.filter(
    (file) => String(file.id) !== idToRemove
  );

  // Update the actual file input
  updateFileInput();
  updateFilePreview();

  if (selectedFiles.length === 0) {
    document.getElementById("filePreviewContainer").style.display = "none";
  }
}

function updateFilePreview() {
  const container = document.getElementById("filePreviewContainer");
  const list = document.getElementById("filePreviewList");

  if (selectedFiles.length === 0) {
    container.style.display = "none";
    return;
  }

  container.style.display = "block";
  list.innerHTML = "";

  selectedFiles.forEach((file) => {
    const previewItem = createFilePreviewItem(file);
    list.appendChild(previewItem);
  });
}

function createFilePreviewItem(file) {
  const item = document.createElement("div");
  item.className = "file-preview-item";

  const fileType = allowedTypes[file.type];
  const fileSize = formatFileSize(file.size);

  item.innerHTML = `
        ${
          fileType === "image"
            ? createImagePreview(file)
            : createIconPreview(fileType)
        }
        <div class="file-preview-info">
            <div class="file-preview-name">${file.name}</div>
            <div class="file-preview-size">${fileSize}</div>
        </div>
        <button type="button" class="file-preview-remove" data-file-id="${
          file.id
        }">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    `;

  // Add event listener to the remove button
  const removeButton = item.querySelector(".file-preview-remove");
  removeButton.addEventListener("click", () => {
    removeFileFromSelection(file.id);
  });

  return item;
}

function createImagePreview(file) {
  const img = document.createElement("img");
  img.className = "file-preview-image";
  img.src = URL.createObjectURL(file);
  return img.outerHTML;
}

function createIconPreview(fileType) {
  const typeLabels = {
    pdf: "PDF",
    image: "IMG",
    audio: "AUD",
    video: "VID",
    document: "DOC",
    spreadsheet: "XLS",
    presentation: "PPT",
  };

  return `<div class="file-preview-icon ${fileType}">${typeLabels[fileType]}</div>`;
}

function formatFileSize(bytes) {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

function showFileError(message) {
  removeFileMessages();
  const container = document.getElementById("fileUploadArea").parentNode;
  const errorDiv = document.createElement("div");
  errorDiv.className = "file-error-message";
  errorDiv.textContent = message;
  container.appendChild(errorDiv);

  document.getElementById("fileUploadArea").classList.add("file-upload-error");

  setTimeout(() => {
    removeFileMessages();
    document
      .getElementById("fileUploadArea")
      .classList.remove("file-upload-error");
  }, 5000);
}

function showFileSuccess(message) {
  removeFileMessages();
  const container = document.getElementById("fileUploadArea").parentNode;
  const successDiv = document.createElement("div");
  successDiv.className = "file-success-message";
  successDiv.textContent = message;
  container.appendChild(successDiv);

  document
    .getElementById("fileUploadArea")
    .classList.add("file-upload-success");

  setTimeout(() => {
    removeFileMessages();
    document
      .getElementById("fileUploadArea")
      .classList.remove("file-upload-success");
  }, 3000);
}

function removeFileMessages() {
  const container = document.getElementById("fileUploadArea").parentNode;
  const existingMessages = container.querySelectorAll(
    ".file-error-message, .file-success-message"
  );
  existingMessages.forEach((msg) => msg.remove());
}

function simulateUploadProgress() {
  const progressContainer = document.getElementById("uploadProgressContainer");
  const progressFill = document.getElementById("uploadProgressFill");
  const progressText = document.getElementById("uploadProgressText");

  progressContainer.style.display = "block";
  let progress = 0;

  const interval = setInterval(() => {
    progress += Math.random() * 15;
    if (progress > 100) progress = 100;

    progressFill.style.width = progress + "%";
    progressText.textContent = `Uploading... ${Math.round(progress)}%`;

    if (progress >= 100) {
      clearInterval(interval);
      progressText.textContent = "Upload complete!";
      setTimeout(() => {
        progressContainer.style.display = "none";
      }, 1000);
    }
  }, 200);
}

// Toggle experience detail
function toggleExperienceDetail(show) {
  const detailDiv = document.getElementById('experience_detail');
  detailDiv.style.display = show ? 'block' : 'none';
  const textarea = detailDiv.querySelector('textarea');
  textarea.required = show;
}
