CREATE TABLE IF NOT EXISTS applications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    profile_type VARCHAR(1) NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255) NOT NULL,
    domicile VARCHAR(255) NOT NULL,
    has_experience ENUM('ya', 'tidak') NOT NULL,
    experience_description TEXT,
    presentation_frequency TEXT NOT NULL,
    ads_improvement_ideas TEXT,
    voice_comfort_level VARCHAR(50) NOT NULL,
    new_tools_approach TEXT NOT NULL,
    meta_ads_learning_plan TEXT NOT NULL,
    learning_frustration TEXT,
    quick_learning_decision TEXT,
    ai_usage_experience TEXT,
    challenge_response VARCHAR(1) NOT NULL,
    motivation_reason TEXT NOT NULL,
    criticism_response TEXT NOT NULL,
    initiative_moment TEXT,
    work_preference VARCHAR(1) NOT NULL,
    work_preference_reason TEXT,
    career_goals TEXT NOT NULL,
    main_motivation TEXT NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS portfolio_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INT NOT NULL,
    created_at DATETIME NOT NULL,
    FOREIGN KEY (application_id) REFERENCES applications(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS applications2 (
    id INT AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    email VARCHAR(255) NOT NULL,
    domicile VARCHAR(255) NOT NULL,
    has_experience ENUM('ya', 'tidak') NOT NULL,
    experience_description TEXT,
    meta_ads_frequency VARCHAR(50) NOT NULL,
    pixel_experience VARCHAR(50) NOT NULL,
    ai_tools_usage TEXT NOT NULL,
    technical_explanation_comfort VARCHAR(50) NOT NULL,
    tools_used JSON NOT NULL,
    other_tools TEXT,
    campaign_failure_experience TEXT NOT NULL,
    technical_explanation_experience TEXT NOT NULL,
    client_confusion_approach TEXT NOT NULL,
    consultant_role_opinion TEXT NOT NULL,
    case_study_response TEXT NOT NULL,
    joining_motivation TEXT NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS portfolio_files2 (
    id INT AUTO_INCREMENT PRIMARY KEY,
    application_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    file_size INT NOT NULL,
    created_at DATETIME NOT NULL,
    FOREIGN KEY (application_id) REFERENCES applications2(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 