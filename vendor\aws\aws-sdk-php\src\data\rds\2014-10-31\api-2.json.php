<?php
// This file was auto-generated from sdk-root/src/data/rds/2014-10-31/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2014-10-31', 'endpointPrefix' => 'rds', 'protocol' => 'query', 'protocols' => [ 'query', ], 'serviceAbbreviation' => 'Amazon RDS', 'serviceFullName' => 'Amazon Relational Database Service', 'serviceId' => 'RDS', 'signatureVersion' => 'v4', 'uid' => 'rds-2014-10-31', 'xmlNamespace' => 'http://rds.amazonaws.com/doc/2014-10-31/', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AddRoleToDBCluster' => [ 'name' => 'AddRoleToDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddRoleToDBClusterMessage', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBClusterRoleAlreadyExistsFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBClusterRoleQuotaExceededFault', ], ], ], 'AddRoleToDBInstance' => [ 'name' => 'AddRoleToDBInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddRoleToDBInstanceMessage', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBInstanceRoleAlreadyExistsFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'DBInstanceRoleQuotaExceededFault', ], ], ], 'AddSourceIdentifierToSubscription' => [ 'name' => 'AddSourceIdentifierToSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddSourceIdentifierToSubscriptionMessage', ], 'output' => [ 'shape' => 'AddSourceIdentifierToSubscriptionResult', 'resultWrapper' => 'AddSourceIdentifierToSubscriptionResult', ], 'errors' => [ [ 'shape' => 'SubscriptionNotFoundFault', ], [ 'shape' => 'SourceNotFoundFault', ], ], ], 'AddTagsToResource' => [ 'name' => 'AddTagsToResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTagsToResourceMessage', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBSnapshotNotFoundFault', ], [ 'shape' => 'DBProxyNotFoundFault', ], [ 'shape' => 'DBProxyTargetGroupNotFoundFault', ], [ 'shape' => 'BlueGreenDeploymentNotFoundFault', ], [ 'shape' => 'IntegrationNotFoundFault', ], [ 'shape' => 'TenantDatabaseNotFoundFault', ], [ 'shape' => 'DBSnapshotTenantDatabaseNotFoundFault', ], ], ], 'ApplyPendingMaintenanceAction' => [ 'name' => 'ApplyPendingMaintenanceAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ApplyPendingMaintenanceActionMessage', ], 'output' => [ 'shape' => 'ApplyPendingMaintenanceActionResult', 'resultWrapper' => 'ApplyPendingMaintenanceActionResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'AuthorizeDBSecurityGroupIngress' => [ 'name' => 'AuthorizeDBSecurityGroupIngress', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AuthorizeDBSecurityGroupIngressMessage', ], 'output' => [ 'shape' => 'AuthorizeDBSecurityGroupIngressResult', 'resultWrapper' => 'AuthorizeDBSecurityGroupIngressResult', ], 'errors' => [ [ 'shape' => 'DBSecurityGroupNotFoundFault', ], [ 'shape' => 'InvalidDBSecurityGroupStateFault', ], [ 'shape' => 'AuthorizationAlreadyExistsFault', ], [ 'shape' => 'AuthorizationQuotaExceededFault', ], ], ], 'BacktrackDBCluster' => [ 'name' => 'BacktrackDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BacktrackDBClusterMessage', ], 'output' => [ 'shape' => 'DBClusterBacktrack', 'resultWrapper' => 'BacktrackDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], ], ], 'CancelExportTask' => [ 'name' => 'CancelExportTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelExportTaskMessage', ], 'output' => [ 'shape' => 'ExportTask', 'resultWrapper' => 'CancelExportTaskResult', ], 'errors' => [ [ 'shape' => 'ExportTaskNotFoundFault', ], [ 'shape' => 'InvalidExportTaskStateFault', ], ], ], 'CopyDBClusterParameterGroup' => [ 'name' => 'CopyDBClusterParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopyDBClusterParameterGroupMessage', ], 'output' => [ 'shape' => 'CopyDBClusterParameterGroupResult', 'resultWrapper' => 'CopyDBClusterParameterGroupResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'DBParameterGroupQuotaExceededFault', ], [ 'shape' => 'DBParameterGroupAlreadyExistsFault', ], ], ], 'CopyDBClusterSnapshot' => [ 'name' => 'CopyDBClusterSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopyDBClusterSnapshotMessage', ], 'output' => [ 'shape' => 'CopyDBClusterSnapshotResult', 'resultWrapper' => 'CopyDBClusterSnapshotResult', ], 'errors' => [ [ 'shape' => 'DBClusterSnapshotAlreadyExistsFault', ], [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBClusterSnapshotStateFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], ], ], 'CopyDBParameterGroup' => [ 'name' => 'CopyDBParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopyDBParameterGroupMessage', ], 'output' => [ 'shape' => 'CopyDBParameterGroupResult', 'resultWrapper' => 'CopyDBParameterGroupResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'DBParameterGroupAlreadyExistsFault', ], [ 'shape' => 'DBParameterGroupQuotaExceededFault', ], ], ], 'CopyDBSnapshot' => [ 'name' => 'CopyDBSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopyDBSnapshotMessage', ], 'output' => [ 'shape' => 'CopyDBSnapshotResult', 'resultWrapper' => 'CopyDBSnapshotResult', ], 'errors' => [ [ 'shape' => 'DBSnapshotAlreadyExistsFault', ], [ 'shape' => 'DBSnapshotNotFoundFault', ], [ 'shape' => 'InvalidDBSnapshotStateFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'CustomAvailabilityZoneNotFoundFault', ], ], ], 'CopyOptionGroup' => [ 'name' => 'CopyOptionGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopyOptionGroupMessage', ], 'output' => [ 'shape' => 'CopyOptionGroupResult', 'resultWrapper' => 'CopyOptionGroupResult', ], 'errors' => [ [ 'shape' => 'OptionGroupAlreadyExistsFault', ], [ 'shape' => 'OptionGroupNotFoundFault', ], [ 'shape' => 'OptionGroupQuotaExceededFault', ], ], ], 'CreateBlueGreenDeployment' => [ 'name' => 'CreateBlueGreenDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBlueGreenDeploymentRequest', ], 'output' => [ 'shape' => 'CreateBlueGreenDeploymentResponse', 'resultWrapper' => 'CreateBlueGreenDeploymentResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'SourceDatabaseNotSupportedFault', ], [ 'shape' => 'SourceClusterNotSupportedFault', ], [ 'shape' => 'BlueGreenDeploymentAlreadyExistsFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'DBClusterParameterGroupNotFoundFault', ], [ 'shape' => 'InstanceQuotaExceededFault', ], [ 'shape' => 'DBClusterQuotaExceededFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], ], ], 'CreateCustomDBEngineVersion' => [ 'name' => 'CreateCustomDBEngineVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCustomDBEngineVersionMessage', ], 'output' => [ 'shape' => 'DBEngineVersion', 'resultWrapper' => 'CreateCustomDBEngineVersionResult', ], 'errors' => [ [ 'shape' => 'CustomDBEngineVersionAlreadyExistsFault', ], [ 'shape' => 'CustomDBEngineVersionQuotaExceededFault', ], [ 'shape' => 'Ec2ImagePropertiesNotSupportedFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'CreateCustomDBEngineVersionFault', ], ], ], 'CreateDBCluster' => [ 'name' => 'CreateDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBClusterMessage', ], 'output' => [ 'shape' => 'CreateDBClusterResult', 'resultWrapper' => 'CreateDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterAlreadyExistsFault', ], [ 'shape' => 'InsufficientDBInstanceCapacityFault', ], [ 'shape' => 'InsufficientStorageClusterCapacityFault', ], [ 'shape' => 'DBClusterQuotaExceededFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBSubnetGroupFault', ], [ 'shape' => 'InvalidDBSubnetGroupStateFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'DBClusterParameterGroupNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'GlobalClusterNotFoundFault', ], [ 'shape' => 'InvalidGlobalClusterStateFault', ], [ 'shape' => 'DomainNotFoundFault', ], [ 'shape' => 'OptionGroupNotFoundFault', ], ], ], 'CreateDBClusterEndpoint' => [ 'name' => 'CreateDBClusterEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBClusterEndpointMessage', ], 'output' => [ 'shape' => 'DBClusterEndpoint', 'resultWrapper' => 'CreateDBClusterEndpointResult', ], 'errors' => [ [ 'shape' => 'DBClusterEndpointQuotaExceededFault', ], [ 'shape' => 'DBClusterEndpointAlreadyExistsFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'CreateDBClusterParameterGroup' => [ 'name' => 'CreateDBClusterParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBClusterParameterGroupMessage', ], 'output' => [ 'shape' => 'CreateDBClusterParameterGroupResult', 'resultWrapper' => 'CreateDBClusterParameterGroupResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupQuotaExceededFault', ], [ 'shape' => 'DBParameterGroupAlreadyExistsFault', ], ], ], 'CreateDBClusterSnapshot' => [ 'name' => 'CreateDBClusterSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBClusterSnapshotMessage', ], 'output' => [ 'shape' => 'CreateDBClusterSnapshotResult', 'resultWrapper' => 'CreateDBClusterSnapshotResult', ], 'errors' => [ [ 'shape' => 'DBClusterSnapshotAlreadyExistsFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'InvalidDBClusterSnapshotStateFault', ], ], ], 'CreateDBInstance' => [ 'name' => 'CreateDBInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBInstanceMessage', ], 'output' => [ 'shape' => 'CreateDBInstanceResult', 'resultWrapper' => 'CreateDBInstanceResult', ], 'errors' => [ [ 'shape' => 'DBInstanceAlreadyExistsFault', ], [ 'shape' => 'InsufficientDBInstanceCapacityFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'DBSecurityGroupNotFoundFault', ], [ 'shape' => 'InstanceQuotaExceededFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'DBSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'ProvisionedIopsNotAvailableInAZFault', ], [ 'shape' => 'OptionGroupNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'StorageTypeNotSupportedFault', ], [ 'shape' => 'AuthorizationNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'DomainNotFoundFault', ], [ 'shape' => 'BackupPolicyNotFoundFault', ], [ 'shape' => 'NetworkTypeNotSupported', ], [ 'shape' => 'CertificateNotFoundFault', ], [ 'shape' => 'TenantDatabaseQuotaExceededFault', ], ], ], 'CreateDBInstanceReadReplica' => [ 'name' => 'CreateDBInstanceReadReplica', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBInstanceReadReplicaMessage', ], 'output' => [ 'shape' => 'CreateDBInstanceReadReplicaResult', 'resultWrapper' => 'CreateDBInstanceReadReplicaResult', ], 'errors' => [ [ 'shape' => 'DBInstanceAlreadyExistsFault', ], [ 'shape' => 'InsufficientDBInstanceCapacityFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'DBSecurityGroupNotFoundFault', ], [ 'shape' => 'InstanceQuotaExceededFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'DBSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'ProvisionedIopsNotAvailableInAZFault', ], [ 'shape' => 'OptionGroupNotFoundFault', ], [ 'shape' => 'DBSubnetGroupNotAllowedFault', ], [ 'shape' => 'InvalidDBSubnetGroupFault', ], [ 'shape' => 'StorageTypeNotSupportedFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'DomainNotFoundFault', ], [ 'shape' => 'NetworkTypeNotSupported', ], [ 'shape' => 'TenantDatabaseQuotaExceededFault', ], [ 'shape' => 'CertificateNotFoundFault', ], ], ], 'CreateDBParameterGroup' => [ 'name' => 'CreateDBParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBParameterGroupMessage', ], 'output' => [ 'shape' => 'CreateDBParameterGroupResult', 'resultWrapper' => 'CreateDBParameterGroupResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupQuotaExceededFault', ], [ 'shape' => 'DBParameterGroupAlreadyExistsFault', ], ], ], 'CreateDBProxy' => [ 'name' => 'CreateDBProxy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBProxyRequest', ], 'output' => [ 'shape' => 'CreateDBProxyResponse', 'resultWrapper' => 'CreateDBProxyResult', ], 'errors' => [ [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'DBProxyAlreadyExistsFault', ], [ 'shape' => 'DBProxyQuotaExceededFault', ], ], ], 'CreateDBProxyEndpoint' => [ 'name' => 'CreateDBProxyEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBProxyEndpointRequest', ], 'output' => [ 'shape' => 'CreateDBProxyEndpointResponse', 'resultWrapper' => 'CreateDBProxyEndpointResult', ], 'errors' => [ [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'DBProxyNotFoundFault', ], [ 'shape' => 'DBProxyEndpointAlreadyExistsFault', ], [ 'shape' => 'DBProxyEndpointQuotaExceededFault', ], [ 'shape' => 'InvalidDBProxyStateFault', ], ], ], 'CreateDBSecurityGroup' => [ 'name' => 'CreateDBSecurityGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBSecurityGroupMessage', ], 'output' => [ 'shape' => 'CreateDBSecurityGroupResult', 'resultWrapper' => 'CreateDBSecurityGroupResult', ], 'errors' => [ [ 'shape' => 'DBSecurityGroupAlreadyExistsFault', ], [ 'shape' => 'DBSecurityGroupQuotaExceededFault', ], [ 'shape' => 'DBSecurityGroupNotSupportedFault', ], ], ], 'CreateDBShardGroup' => [ 'name' => 'CreateDBShardGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBShardGroupMessage', ], 'output' => [ 'shape' => 'DBShardGroup', 'resultWrapper' => 'CreateDBShardGroupResult', ], 'errors' => [ [ 'shape' => 'DBShardGroupAlreadyExistsFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'MaxDBShardGroupLimitReached', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'UnsupportedDBEngineVersionFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'NetworkTypeNotSupported', ], ], ], 'CreateDBSnapshot' => [ 'name' => 'CreateDBSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBSnapshotMessage', ], 'output' => [ 'shape' => 'CreateDBSnapshotResult', 'resultWrapper' => 'CreateDBSnapshotResult', ], 'errors' => [ [ 'shape' => 'DBSnapshotAlreadyExistsFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], ], ], 'CreateDBSubnetGroup' => [ 'name' => 'CreateDBSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBSubnetGroupMessage', ], 'output' => [ 'shape' => 'CreateDBSubnetGroupResult', 'resultWrapper' => 'CreateDBSubnetGroupResult', ], 'errors' => [ [ 'shape' => 'DBSubnetGroupAlreadyExistsFault', ], [ 'shape' => 'DBSubnetGroupQuotaExceededFault', ], [ 'shape' => 'DBSubnetQuotaExceededFault', ], [ 'shape' => 'DBSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidSubnet', ], ], ], 'CreateEventSubscription' => [ 'name' => 'CreateEventSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEventSubscriptionMessage', ], 'output' => [ 'shape' => 'CreateEventSubscriptionResult', 'resultWrapper' => 'CreateEventSubscriptionResult', ], 'errors' => [ [ 'shape' => 'EventSubscriptionQuotaExceededFault', ], [ 'shape' => 'SubscriptionAlreadyExistFault', ], [ 'shape' => 'SNSInvalidTopicFault', ], [ 'shape' => 'SNSNoAuthorizationFault', ], [ 'shape' => 'SNSTopicArnNotFoundFault', ], [ 'shape' => 'SubscriptionCategoryNotFoundFault', ], [ 'shape' => 'SourceNotFoundFault', ], ], ], 'CreateGlobalCluster' => [ 'name' => 'CreateGlobalCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGlobalClusterMessage', ], 'output' => [ 'shape' => 'CreateGlobalClusterResult', 'resultWrapper' => 'CreateGlobalClusterResult', ], 'errors' => [ [ 'shape' => 'GlobalClusterAlreadyExistsFault', ], [ 'shape' => 'GlobalClusterQuotaExceededFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'CreateIntegration' => [ 'name' => 'CreateIntegration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateIntegrationMessage', ], 'output' => [ 'shape' => 'Integration', 'resultWrapper' => 'CreateIntegrationResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'IntegrationAlreadyExistsFault', ], [ 'shape' => 'IntegrationQuotaExceededFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'IntegrationConflictOperationFault', ], ], ], 'CreateOptionGroup' => [ 'name' => 'CreateOptionGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateOptionGroupMessage', ], 'output' => [ 'shape' => 'CreateOptionGroupResult', 'resultWrapper' => 'CreateOptionGroupResult', ], 'errors' => [ [ 'shape' => 'OptionGroupAlreadyExistsFault', ], [ 'shape' => 'OptionGroupQuotaExceededFault', ], ], ], 'CreateTenantDatabase' => [ 'name' => 'CreateTenantDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTenantDatabaseMessage', ], 'output' => [ 'shape' => 'CreateTenantDatabaseResult', 'resultWrapper' => 'CreateTenantDatabaseResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'TenantDatabaseAlreadyExistsFault', ], [ 'shape' => 'TenantDatabaseQuotaExceededFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], ], ], 'DeleteBlueGreenDeployment' => [ 'name' => 'DeleteBlueGreenDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBlueGreenDeploymentRequest', ], 'output' => [ 'shape' => 'DeleteBlueGreenDeploymentResponse', 'resultWrapper' => 'DeleteBlueGreenDeploymentResult', ], 'errors' => [ [ 'shape' => 'BlueGreenDeploymentNotFoundFault', ], [ 'shape' => 'InvalidBlueGreenDeploymentStateFault', ], ], ], 'DeleteCustomDBEngineVersion' => [ 'name' => 'DeleteCustomDBEngineVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCustomDBEngineVersionMessage', ], 'output' => [ 'shape' => 'DBEngineVersion', 'resultWrapper' => 'DeleteCustomDBEngineVersionResult', ], 'errors' => [ [ 'shape' => 'CustomDBEngineVersionNotFoundFault', ], [ 'shape' => 'InvalidCustomDBEngineVersionStateFault', ], ], ], 'DeleteDBCluster' => [ 'name' => 'DeleteDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBClusterMessage', ], 'output' => [ 'shape' => 'DeleteDBClusterResult', 'resultWrapper' => 'DeleteDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBClusterSnapshotAlreadyExistsFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'InvalidDBClusterSnapshotStateFault', ], [ 'shape' => 'DBClusterAutomatedBackupQuotaExceededFault', ], ], ], 'DeleteDBClusterAutomatedBackup' => [ 'name' => 'DeleteDBClusterAutomatedBackup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBClusterAutomatedBackupMessage', ], 'output' => [ 'shape' => 'DeleteDBClusterAutomatedBackupResult', 'resultWrapper' => 'DeleteDBClusterAutomatedBackupResult', ], 'errors' => [ [ 'shape' => 'InvalidDBClusterAutomatedBackupStateFault', ], [ 'shape' => 'DBClusterAutomatedBackupNotFoundFault', ], ], ], 'DeleteDBClusterEndpoint' => [ 'name' => 'DeleteDBClusterEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBClusterEndpointMessage', ], 'output' => [ 'shape' => 'DBClusterEndpoint', 'resultWrapper' => 'DeleteDBClusterEndpointResult', ], 'errors' => [ [ 'shape' => 'InvalidDBClusterEndpointStateFault', ], [ 'shape' => 'DBClusterEndpointNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], ], ], 'DeleteDBClusterParameterGroup' => [ 'name' => 'DeleteDBClusterParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBClusterParameterGroupMessage', ], 'errors' => [ [ 'shape' => 'InvalidDBParameterGroupStateFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'DeleteDBClusterSnapshot' => [ 'name' => 'DeleteDBClusterSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBClusterSnapshotMessage', ], 'output' => [ 'shape' => 'DeleteDBClusterSnapshotResult', 'resultWrapper' => 'DeleteDBClusterSnapshotResult', ], 'errors' => [ [ 'shape' => 'InvalidDBClusterSnapshotStateFault', ], [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], ], ], 'DeleteDBInstance' => [ 'name' => 'DeleteDBInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBInstanceMessage', ], 'output' => [ 'shape' => 'DeleteDBInstanceResult', 'resultWrapper' => 'DeleteDBInstanceResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'DBSnapshotAlreadyExistsFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBInstanceAutomatedBackupQuotaExceededFault', ], ], ], 'DeleteDBInstanceAutomatedBackup' => [ 'name' => 'DeleteDBInstanceAutomatedBackup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBInstanceAutomatedBackupMessage', ], 'output' => [ 'shape' => 'DeleteDBInstanceAutomatedBackupResult', 'resultWrapper' => 'DeleteDBInstanceAutomatedBackupResult', ], 'errors' => [ [ 'shape' => 'InvalidDBInstanceAutomatedBackupStateFault', ], [ 'shape' => 'DBInstanceAutomatedBackupNotFoundFault', ], ], ], 'DeleteDBParameterGroup' => [ 'name' => 'DeleteDBParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBParameterGroupMessage', ], 'errors' => [ [ 'shape' => 'InvalidDBParameterGroupStateFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'DeleteDBProxy' => [ 'name' => 'DeleteDBProxy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBProxyRequest', ], 'output' => [ 'shape' => 'DeleteDBProxyResponse', 'resultWrapper' => 'DeleteDBProxyResult', ], 'errors' => [ [ 'shape' => 'DBProxyNotFoundFault', ], [ 'shape' => 'InvalidDBProxyStateFault', ], ], ], 'DeleteDBProxyEndpoint' => [ 'name' => 'DeleteDBProxyEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBProxyEndpointRequest', ], 'output' => [ 'shape' => 'DeleteDBProxyEndpointResponse', 'resultWrapper' => 'DeleteDBProxyEndpointResult', ], 'errors' => [ [ 'shape' => 'DBProxyEndpointNotFoundFault', ], [ 'shape' => 'InvalidDBProxyEndpointStateFault', ], ], ], 'DeleteDBSecurityGroup' => [ 'name' => 'DeleteDBSecurityGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBSecurityGroupMessage', ], 'errors' => [ [ 'shape' => 'InvalidDBSecurityGroupStateFault', ], [ 'shape' => 'DBSecurityGroupNotFoundFault', ], ], ], 'DeleteDBShardGroup' => [ 'name' => 'DeleteDBShardGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBShardGroupMessage', ], 'output' => [ 'shape' => 'DBShardGroup', 'resultWrapper' => 'DeleteDBShardGroupResult', ], 'errors' => [ [ 'shape' => 'DBShardGroupNotFoundFault', ], [ 'shape' => 'InvalidDBShardGroupStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], ], ], 'DeleteDBSnapshot' => [ 'name' => 'DeleteDBSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBSnapshotMessage', ], 'output' => [ 'shape' => 'DeleteDBSnapshotResult', 'resultWrapper' => 'DeleteDBSnapshotResult', ], 'errors' => [ [ 'shape' => 'InvalidDBSnapshotStateFault', ], [ 'shape' => 'DBSnapshotNotFoundFault', ], ], ], 'DeleteDBSubnetGroup' => [ 'name' => 'DeleteDBSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBSubnetGroupMessage', ], 'errors' => [ [ 'shape' => 'InvalidDBSubnetGroupStateFault', ], [ 'shape' => 'InvalidDBSubnetStateFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], ], ], 'DeleteEventSubscription' => [ 'name' => 'DeleteEventSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEventSubscriptionMessage', ], 'output' => [ 'shape' => 'DeleteEventSubscriptionResult', 'resultWrapper' => 'DeleteEventSubscriptionResult', ], 'errors' => [ [ 'shape' => 'SubscriptionNotFoundFault', ], [ 'shape' => 'InvalidEventSubscriptionStateFault', ], ], ], 'DeleteGlobalCluster' => [ 'name' => 'DeleteGlobalCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteGlobalClusterMessage', ], 'output' => [ 'shape' => 'DeleteGlobalClusterResult', 'resultWrapper' => 'DeleteGlobalClusterResult', ], 'errors' => [ [ 'shape' => 'GlobalClusterNotFoundFault', ], [ 'shape' => 'InvalidGlobalClusterStateFault', ], ], ], 'DeleteIntegration' => [ 'name' => 'DeleteIntegration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteIntegrationMessage', ], 'output' => [ 'shape' => 'Integration', 'resultWrapper' => 'DeleteIntegrationResult', ], 'errors' => [ [ 'shape' => 'IntegrationNotFoundFault', ], [ 'shape' => 'IntegrationConflictOperationFault', ], [ 'shape' => 'InvalidIntegrationStateFault', ], ], ], 'DeleteOptionGroup' => [ 'name' => 'DeleteOptionGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteOptionGroupMessage', ], 'errors' => [ [ 'shape' => 'OptionGroupNotFoundFault', ], [ 'shape' => 'InvalidOptionGroupStateFault', ], ], ], 'DeleteTenantDatabase' => [ 'name' => 'DeleteTenantDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTenantDatabaseMessage', ], 'output' => [ 'shape' => 'DeleteTenantDatabaseResult', 'resultWrapper' => 'DeleteTenantDatabaseResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'TenantDatabaseNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'DeregisterDBProxyTargets' => [ 'name' => 'DeregisterDBProxyTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeregisterDBProxyTargetsRequest', ], 'output' => [ 'shape' => 'DeregisterDBProxyTargetsResponse', 'resultWrapper' => 'DeregisterDBProxyTargetsResult', ], 'errors' => [ [ 'shape' => 'DBProxyTargetNotFoundFault', ], [ 'shape' => 'DBProxyTargetGroupNotFoundFault', ], [ 'shape' => 'DBProxyNotFoundFault', ], [ 'shape' => 'InvalidDBProxyStateFault', ], ], ], 'DescribeAccountAttributes' => [ 'name' => 'DescribeAccountAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeAccountAttributesMessage', ], 'output' => [ 'shape' => 'AccountAttributesMessage', 'resultWrapper' => 'DescribeAccountAttributesResult', ], ], 'DescribeBlueGreenDeployments' => [ 'name' => 'DescribeBlueGreenDeployments', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeBlueGreenDeploymentsRequest', ], 'output' => [ 'shape' => 'DescribeBlueGreenDeploymentsResponse', 'resultWrapper' => 'DescribeBlueGreenDeploymentsResult', ], 'errors' => [ [ 'shape' => 'BlueGreenDeploymentNotFoundFault', ], ], ], 'DescribeCertificates' => [ 'name' => 'DescribeCertificates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCertificatesMessage', ], 'output' => [ 'shape' => 'CertificateMessage', 'resultWrapper' => 'DescribeCertificatesResult', ], 'errors' => [ [ 'shape' => 'CertificateNotFoundFault', ], ], ], 'DescribeDBClusterAutomatedBackups' => [ 'name' => 'DescribeDBClusterAutomatedBackups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBClusterAutomatedBackupsMessage', ], 'output' => [ 'shape' => 'DBClusterAutomatedBackupMessage', 'resultWrapper' => 'DescribeDBClusterAutomatedBackupsResult', ], 'errors' => [ [ 'shape' => 'DBClusterAutomatedBackupNotFoundFault', ], ], ], 'DescribeDBClusterBacktracks' => [ 'name' => 'DescribeDBClusterBacktracks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBClusterBacktracksMessage', ], 'output' => [ 'shape' => 'DBClusterBacktrackMessage', 'resultWrapper' => 'DescribeDBClusterBacktracksResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBClusterBacktrackNotFoundFault', ], ], ], 'DescribeDBClusterEndpoints' => [ 'name' => 'DescribeDBClusterEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBClusterEndpointsMessage', ], 'output' => [ 'shape' => 'DBClusterEndpointMessage', 'resultWrapper' => 'DescribeDBClusterEndpointsResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'DescribeDBClusterParameterGroups' => [ 'name' => 'DescribeDBClusterParameterGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBClusterParameterGroupsMessage', ], 'output' => [ 'shape' => 'DBClusterParameterGroupsMessage', 'resultWrapper' => 'DescribeDBClusterParameterGroupsResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'DescribeDBClusterParameters' => [ 'name' => 'DescribeDBClusterParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBClusterParametersMessage', ], 'output' => [ 'shape' => 'DBClusterParameterGroupDetails', 'resultWrapper' => 'DescribeDBClusterParametersResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'DescribeDBClusterSnapshotAttributes' => [ 'name' => 'DescribeDBClusterSnapshotAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBClusterSnapshotAttributesMessage', ], 'output' => [ 'shape' => 'DescribeDBClusterSnapshotAttributesResult', 'resultWrapper' => 'DescribeDBClusterSnapshotAttributesResult', ], 'errors' => [ [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], ], ], 'DescribeDBClusterSnapshots' => [ 'name' => 'DescribeDBClusterSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBClusterSnapshotsMessage', ], 'output' => [ 'shape' => 'DBClusterSnapshotMessage', 'resultWrapper' => 'DescribeDBClusterSnapshotsResult', ], 'errors' => [ [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], ], ], 'DescribeDBClusters' => [ 'name' => 'DescribeDBClusters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBClustersMessage', ], 'output' => [ 'shape' => 'DBClusterMessage', 'resultWrapper' => 'DescribeDBClustersResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'DescribeDBEngineVersions' => [ 'name' => 'DescribeDBEngineVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBEngineVersionsMessage', ], 'output' => [ 'shape' => 'DBEngineVersionMessage', 'resultWrapper' => 'DescribeDBEngineVersionsResult', ], ], 'DescribeDBInstanceAutomatedBackups' => [ 'name' => 'DescribeDBInstanceAutomatedBackups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBInstanceAutomatedBackupsMessage', ], 'output' => [ 'shape' => 'DBInstanceAutomatedBackupMessage', 'resultWrapper' => 'DescribeDBInstanceAutomatedBackupsResult', ], 'errors' => [ [ 'shape' => 'DBInstanceAutomatedBackupNotFoundFault', ], ], ], 'DescribeDBInstances' => [ 'name' => 'DescribeDBInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBInstancesMessage', ], 'output' => [ 'shape' => 'DBInstanceMessage', 'resultWrapper' => 'DescribeDBInstancesResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], ], ], 'DescribeDBLogFiles' => [ 'name' => 'DescribeDBLogFiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBLogFilesMessage', ], 'output' => [ 'shape' => 'DescribeDBLogFilesResponse', 'resultWrapper' => 'DescribeDBLogFilesResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBInstanceNotReadyFault', ], ], ], 'DescribeDBMajorEngineVersions' => [ 'name' => 'DescribeDBMajorEngineVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBMajorEngineVersionsRequest', ], 'output' => [ 'shape' => 'DescribeDBMajorEngineVersionsResponse', 'resultWrapper' => 'DescribeDBMajorEngineVersionsResult', ], ], 'DescribeDBParameterGroups' => [ 'name' => 'DescribeDBParameterGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBParameterGroupsMessage', ], 'output' => [ 'shape' => 'DBParameterGroupsMessage', 'resultWrapper' => 'DescribeDBParameterGroupsResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'DescribeDBParameters' => [ 'name' => 'DescribeDBParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBParametersMessage', ], 'output' => [ 'shape' => 'DBParameterGroupDetails', 'resultWrapper' => 'DescribeDBParametersResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'DescribeDBProxies' => [ 'name' => 'DescribeDBProxies', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBProxiesRequest', ], 'output' => [ 'shape' => 'DescribeDBProxiesResponse', 'resultWrapper' => 'DescribeDBProxiesResult', ], 'errors' => [ [ 'shape' => 'DBProxyNotFoundFault', ], ], ], 'DescribeDBProxyEndpoints' => [ 'name' => 'DescribeDBProxyEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBProxyEndpointsRequest', ], 'output' => [ 'shape' => 'DescribeDBProxyEndpointsResponse', 'resultWrapper' => 'DescribeDBProxyEndpointsResult', ], 'errors' => [ [ 'shape' => 'DBProxyNotFoundFault', ], [ 'shape' => 'DBProxyEndpointNotFoundFault', ], ], ], 'DescribeDBProxyTargetGroups' => [ 'name' => 'DescribeDBProxyTargetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBProxyTargetGroupsRequest', ], 'output' => [ 'shape' => 'DescribeDBProxyTargetGroupsResponse', 'resultWrapper' => 'DescribeDBProxyTargetGroupsResult', ], 'errors' => [ [ 'shape' => 'DBProxyNotFoundFault', ], [ 'shape' => 'DBProxyTargetGroupNotFoundFault', ], [ 'shape' => 'InvalidDBProxyStateFault', ], ], ], 'DescribeDBProxyTargets' => [ 'name' => 'DescribeDBProxyTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBProxyTargetsRequest', ], 'output' => [ 'shape' => 'DescribeDBProxyTargetsResponse', 'resultWrapper' => 'DescribeDBProxyTargetsResult', ], 'errors' => [ [ 'shape' => 'DBProxyNotFoundFault', ], [ 'shape' => 'DBProxyTargetNotFoundFault', ], [ 'shape' => 'DBProxyTargetGroupNotFoundFault', ], [ 'shape' => 'InvalidDBProxyStateFault', ], ], ], 'DescribeDBRecommendations' => [ 'name' => 'DescribeDBRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBRecommendationsMessage', ], 'output' => [ 'shape' => 'DBRecommendationsMessage', 'resultWrapper' => 'DescribeDBRecommendationsResult', ], ], 'DescribeDBSecurityGroups' => [ 'name' => 'DescribeDBSecurityGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBSecurityGroupsMessage', ], 'output' => [ 'shape' => 'DBSecurityGroupMessage', 'resultWrapper' => 'DescribeDBSecurityGroupsResult', ], 'errors' => [ [ 'shape' => 'DBSecurityGroupNotFoundFault', ], ], ], 'DescribeDBShardGroups' => [ 'name' => 'DescribeDBShardGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBShardGroupsMessage', ], 'output' => [ 'shape' => 'DescribeDBShardGroupsResponse', 'resultWrapper' => 'DescribeDBShardGroupsResult', ], 'errors' => [ [ 'shape' => 'DBShardGroupNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'DescribeDBSnapshotAttributes' => [ 'name' => 'DescribeDBSnapshotAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBSnapshotAttributesMessage', ], 'output' => [ 'shape' => 'DescribeDBSnapshotAttributesResult', 'resultWrapper' => 'DescribeDBSnapshotAttributesResult', ], 'errors' => [ [ 'shape' => 'DBSnapshotNotFoundFault', ], ], ], 'DescribeDBSnapshotTenantDatabases' => [ 'name' => 'DescribeDBSnapshotTenantDatabases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBSnapshotTenantDatabasesMessage', ], 'output' => [ 'shape' => 'DBSnapshotTenantDatabasesMessage', 'resultWrapper' => 'DescribeDBSnapshotTenantDatabasesResult', ], 'errors' => [ [ 'shape' => 'DBSnapshotNotFoundFault', ], ], ], 'DescribeDBSnapshots' => [ 'name' => 'DescribeDBSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBSnapshotsMessage', ], 'output' => [ 'shape' => 'DBSnapshotMessage', 'resultWrapper' => 'DescribeDBSnapshotsResult', ], 'errors' => [ [ 'shape' => 'DBSnapshotNotFoundFault', ], ], ], 'DescribeDBSubnetGroups' => [ 'name' => 'DescribeDBSubnetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBSubnetGroupsMessage', ], 'output' => [ 'shape' => 'DBSubnetGroupMessage', 'resultWrapper' => 'DescribeDBSubnetGroupsResult', ], 'errors' => [ [ 'shape' => 'DBSubnetGroupNotFoundFault', ], ], ], 'DescribeEngineDefaultClusterParameters' => [ 'name' => 'DescribeEngineDefaultClusterParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEngineDefaultClusterParametersMessage', ], 'output' => [ 'shape' => 'DescribeEngineDefaultClusterParametersResult', 'resultWrapper' => 'DescribeEngineDefaultClusterParametersResult', ], ], 'DescribeEngineDefaultParameters' => [ 'name' => 'DescribeEngineDefaultParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEngineDefaultParametersMessage', ], 'output' => [ 'shape' => 'DescribeEngineDefaultParametersResult', 'resultWrapper' => 'DescribeEngineDefaultParametersResult', ], ], 'DescribeEventCategories' => [ 'name' => 'DescribeEventCategories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventCategoriesMessage', ], 'output' => [ 'shape' => 'EventCategoriesMessage', 'resultWrapper' => 'DescribeEventCategoriesResult', ], ], 'DescribeEventSubscriptions' => [ 'name' => 'DescribeEventSubscriptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventSubscriptionsMessage', ], 'output' => [ 'shape' => 'EventSubscriptionsMessage', 'resultWrapper' => 'DescribeEventSubscriptionsResult', ], 'errors' => [ [ 'shape' => 'SubscriptionNotFoundFault', ], ], ], 'DescribeEvents' => [ 'name' => 'DescribeEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventsMessage', ], 'output' => [ 'shape' => 'EventsMessage', 'resultWrapper' => 'DescribeEventsResult', ], ], 'DescribeExportTasks' => [ 'name' => 'DescribeExportTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeExportTasksMessage', ], 'output' => [ 'shape' => 'ExportTasksMessage', 'resultWrapper' => 'DescribeExportTasksResult', ], 'errors' => [ [ 'shape' => 'ExportTaskNotFoundFault', ], ], ], 'DescribeGlobalClusters' => [ 'name' => 'DescribeGlobalClusters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGlobalClustersMessage', ], 'output' => [ 'shape' => 'GlobalClustersMessage', 'resultWrapper' => 'DescribeGlobalClustersResult', ], 'errors' => [ [ 'shape' => 'GlobalClusterNotFoundFault', ], ], ], 'DescribeIntegrations' => [ 'name' => 'DescribeIntegrations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeIntegrationsMessage', ], 'output' => [ 'shape' => 'DescribeIntegrationsResponse', 'resultWrapper' => 'DescribeIntegrationsResult', ], 'errors' => [ [ 'shape' => 'IntegrationNotFoundFault', ], ], ], 'DescribeOptionGroupOptions' => [ 'name' => 'DescribeOptionGroupOptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOptionGroupOptionsMessage', ], 'output' => [ 'shape' => 'OptionGroupOptionsMessage', 'resultWrapper' => 'DescribeOptionGroupOptionsResult', ], ], 'DescribeOptionGroups' => [ 'name' => 'DescribeOptionGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOptionGroupsMessage', ], 'output' => [ 'shape' => 'OptionGroups', 'resultWrapper' => 'DescribeOptionGroupsResult', ], 'errors' => [ [ 'shape' => 'OptionGroupNotFoundFault', ], ], ], 'DescribeOrderableDBInstanceOptions' => [ 'name' => 'DescribeOrderableDBInstanceOptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOrderableDBInstanceOptionsMessage', ], 'output' => [ 'shape' => 'OrderableDBInstanceOptionsMessage', 'resultWrapper' => 'DescribeOrderableDBInstanceOptionsResult', ], ], 'DescribePendingMaintenanceActions' => [ 'name' => 'DescribePendingMaintenanceActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePendingMaintenanceActionsMessage', ], 'output' => [ 'shape' => 'PendingMaintenanceActionsMessage', 'resultWrapper' => 'DescribePendingMaintenanceActionsResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeReservedDBInstances' => [ 'name' => 'DescribeReservedDBInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReservedDBInstancesMessage', ], 'output' => [ 'shape' => 'ReservedDBInstanceMessage', 'resultWrapper' => 'DescribeReservedDBInstancesResult', ], 'errors' => [ [ 'shape' => 'ReservedDBInstanceNotFoundFault', ], ], ], 'DescribeReservedDBInstancesOfferings' => [ 'name' => 'DescribeReservedDBInstancesOfferings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeReservedDBInstancesOfferingsMessage', ], 'output' => [ 'shape' => 'ReservedDBInstancesOfferingMessage', 'resultWrapper' => 'DescribeReservedDBInstancesOfferingsResult', ], 'errors' => [ [ 'shape' => 'ReservedDBInstancesOfferingNotFoundFault', ], ], ], 'DescribeSourceRegions' => [ 'name' => 'DescribeSourceRegions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeSourceRegionsMessage', ], 'output' => [ 'shape' => 'SourceRegionMessage', 'resultWrapper' => 'DescribeSourceRegionsResult', ], ], 'DescribeTenantDatabases' => [ 'name' => 'DescribeTenantDatabases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTenantDatabasesMessage', ], 'output' => [ 'shape' => 'TenantDatabasesMessage', 'resultWrapper' => 'DescribeTenantDatabasesResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], ], ], 'DescribeValidDBInstanceModifications' => [ 'name' => 'DescribeValidDBInstanceModifications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeValidDBInstanceModificationsMessage', ], 'output' => [ 'shape' => 'DescribeValidDBInstanceModificationsResult', 'resultWrapper' => 'DescribeValidDBInstanceModificationsResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'DisableHttpEndpoint' => [ 'name' => 'DisableHttpEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisableHttpEndpointRequest', ], 'output' => [ 'shape' => 'DisableHttpEndpointResponse', 'resultWrapper' => 'DisableHttpEndpointResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'DownloadDBLogFilePortion' => [ 'name' => 'DownloadDBLogFilePortion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DownloadDBLogFilePortionMessage', ], 'output' => [ 'shape' => 'DownloadDBLogFilePortionDetails', 'resultWrapper' => 'DownloadDBLogFilePortionResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBInstanceNotReadyFault', ], [ 'shape' => 'DBLogFileNotFoundFault', ], ], ], 'EnableHttpEndpoint' => [ 'name' => 'EnableHttpEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'EnableHttpEndpointRequest', ], 'output' => [ 'shape' => 'EnableHttpEndpointResponse', 'resultWrapper' => 'EnableHttpEndpointResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'InvalidResourceStateFault', ], ], ], 'FailoverDBCluster' => [ 'name' => 'FailoverDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'FailoverDBClusterMessage', ], 'output' => [ 'shape' => 'FailoverDBClusterResult', 'resultWrapper' => 'FailoverDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'FailoverGlobalCluster' => [ 'name' => 'FailoverGlobalCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'FailoverGlobalClusterMessage', ], 'output' => [ 'shape' => 'FailoverGlobalClusterResult', 'resultWrapper' => 'FailoverGlobalClusterResult', ], 'errors' => [ [ 'shape' => 'GlobalClusterNotFoundFault', ], [ 'shape' => 'InvalidGlobalClusterStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceMessage', ], 'output' => [ 'shape' => 'TagListMessage', 'resultWrapper' => 'ListTagsForResourceResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBSnapshotNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBProxyNotFoundFault', ], [ 'shape' => 'DBProxyTargetGroupNotFoundFault', ], [ 'shape' => 'BlueGreenDeploymentNotFoundFault', ], [ 'shape' => 'IntegrationNotFoundFault', ], [ 'shape' => 'TenantDatabaseNotFoundFault', ], [ 'shape' => 'DBSnapshotTenantDatabaseNotFoundFault', ], ], ], 'ModifyActivityStream' => [ 'name' => 'ModifyActivityStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyActivityStreamRequest', ], 'output' => [ 'shape' => 'ModifyActivityStreamResponse', 'resultWrapper' => 'ModifyActivityStreamResult', ], 'errors' => [ [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], ], ], 'ModifyCertificates' => [ 'name' => 'ModifyCertificates', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyCertificatesMessage', ], 'output' => [ 'shape' => 'ModifyCertificatesResult', 'resultWrapper' => 'ModifyCertificatesResult', ], 'errors' => [ [ 'shape' => 'CertificateNotFoundFault', ], ], ], 'ModifyCurrentDBClusterCapacity' => [ 'name' => 'ModifyCurrentDBClusterCapacity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyCurrentDBClusterCapacityMessage', ], 'output' => [ 'shape' => 'DBClusterCapacityInfo', 'resultWrapper' => 'ModifyCurrentDBClusterCapacityResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBClusterCapacityFault', ], ], ], 'ModifyCustomDBEngineVersion' => [ 'name' => 'ModifyCustomDBEngineVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyCustomDBEngineVersionMessage', ], 'output' => [ 'shape' => 'DBEngineVersion', 'resultWrapper' => 'ModifyCustomDBEngineVersionResult', ], 'errors' => [ [ 'shape' => 'CustomDBEngineVersionNotFoundFault', ], [ 'shape' => 'InvalidCustomDBEngineVersionStateFault', ], ], ], 'ModifyDBCluster' => [ 'name' => 'ModifyDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBClusterMessage', ], 'output' => [ 'shape' => 'ModifyDBClusterResult', 'resultWrapper' => 'ModifyDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'InvalidDBSubnetGroupStateFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'DBClusterParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidDBSecurityGroupStateFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'DBClusterAlreadyExistsFault', ], [ 'shape' => 'DBInstanceAlreadyExistsFault', ], [ 'shape' => 'DomainNotFoundFault', ], [ 'shape' => 'StorageTypeNotAvailableFault', ], [ 'shape' => 'OptionGroupNotFoundFault', ], ], ], 'ModifyDBClusterEndpoint' => [ 'name' => 'ModifyDBClusterEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBClusterEndpointMessage', ], 'output' => [ 'shape' => 'DBClusterEndpoint', 'resultWrapper' => 'ModifyDBClusterEndpointResult', ], 'errors' => [ [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBClusterEndpointStateFault', ], [ 'shape' => 'DBClusterEndpointNotFoundFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'ModifyDBClusterParameterGroup' => [ 'name' => 'ModifyDBClusterParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBClusterParameterGroupMessage', ], 'output' => [ 'shape' => 'DBClusterParameterGroupNameMessage', 'resultWrapper' => 'ModifyDBClusterParameterGroupResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidDBParameterGroupStateFault', ], ], ], 'ModifyDBClusterSnapshotAttribute' => [ 'name' => 'ModifyDBClusterSnapshotAttribute', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBClusterSnapshotAttributeMessage', ], 'output' => [ 'shape' => 'ModifyDBClusterSnapshotAttributeResult', 'resultWrapper' => 'ModifyDBClusterSnapshotAttributeResult', ], 'errors' => [ [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], [ 'shape' => 'InvalidDBClusterSnapshotStateFault', ], [ 'shape' => 'SharedSnapshotQuotaExceededFault', ], ], ], 'ModifyDBInstance' => [ 'name' => 'ModifyDBInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBInstanceMessage', ], 'output' => [ 'shape' => 'ModifyDBInstanceResult', 'resultWrapper' => 'ModifyDBInstanceResult', ], 'errors' => [ [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'InvalidDBSecurityGroupStateFault', ], [ 'shape' => 'DBInstanceAlreadyExistsFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBSecurityGroupNotFoundFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'InsufficientDBInstanceCapacityFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'ProvisionedIopsNotAvailableInAZFault', ], [ 'shape' => 'OptionGroupNotFoundFault', ], [ 'shape' => 'DBUpgradeDependencyFailureFault', ], [ 'shape' => 'StorageTypeNotSupportedFault', ], [ 'shape' => 'AuthorizationNotFoundFault', ], [ 'shape' => 'CertificateNotFoundFault', ], [ 'shape' => 'DomainNotFoundFault', ], [ 'shape' => 'BackupPolicyNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'NetworkTypeNotSupported', ], [ 'shape' => 'TenantDatabaseQuotaExceededFault', ], ], ], 'ModifyDBParameterGroup' => [ 'name' => 'ModifyDBParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBParameterGroupMessage', ], 'output' => [ 'shape' => 'DBParameterGroupNameMessage', 'resultWrapper' => 'ModifyDBParameterGroupResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidDBParameterGroupStateFault', ], ], ], 'ModifyDBProxy' => [ 'name' => 'ModifyDBProxy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBProxyRequest', ], 'output' => [ 'shape' => 'ModifyDBProxyResponse', 'resultWrapper' => 'ModifyDBProxyResult', ], 'errors' => [ [ 'shape' => 'DBProxyNotFoundFault', ], [ 'shape' => 'DBProxyAlreadyExistsFault', ], [ 'shape' => 'InvalidDBProxyStateFault', ], ], ], 'ModifyDBProxyEndpoint' => [ 'name' => 'ModifyDBProxyEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBProxyEndpointRequest', ], 'output' => [ 'shape' => 'ModifyDBProxyEndpointResponse', 'resultWrapper' => 'ModifyDBProxyEndpointResult', ], 'errors' => [ [ 'shape' => 'DBProxyEndpointNotFoundFault', ], [ 'shape' => 'DBProxyEndpointAlreadyExistsFault', ], [ 'shape' => 'InvalidDBProxyEndpointStateFault', ], [ 'shape' => 'InvalidDBProxyStateFault', ], ], ], 'ModifyDBProxyTargetGroup' => [ 'name' => 'ModifyDBProxyTargetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBProxyTargetGroupRequest', ], 'output' => [ 'shape' => 'ModifyDBProxyTargetGroupResponse', 'resultWrapper' => 'ModifyDBProxyTargetGroupResult', ], 'errors' => [ [ 'shape' => 'DBProxyNotFoundFault', ], [ 'shape' => 'DBProxyTargetGroupNotFoundFault', ], [ 'shape' => 'InvalidDBProxyStateFault', ], ], ], 'ModifyDBRecommendation' => [ 'name' => 'ModifyDBRecommendation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBRecommendationMessage', ], 'output' => [ 'shape' => 'DBRecommendationMessage', 'resultWrapper' => 'ModifyDBRecommendationResult', ], 'errors' => [], ], 'ModifyDBShardGroup' => [ 'name' => 'ModifyDBShardGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBShardGroupMessage', ], 'output' => [ 'shape' => 'DBShardGroup', 'resultWrapper' => 'ModifyDBShardGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBShardGroupAlreadyExistsFault', ], [ 'shape' => 'DBShardGroupNotFoundFault', ], ], ], 'ModifyDBSnapshot' => [ 'name' => 'ModifyDBSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBSnapshotMessage', ], 'output' => [ 'shape' => 'ModifyDBSnapshotResult', 'resultWrapper' => 'ModifyDBSnapshotResult', ], 'errors' => [ [ 'shape' => 'DBSnapshotNotFoundFault', ], ], ], 'ModifyDBSnapshotAttribute' => [ 'name' => 'ModifyDBSnapshotAttribute', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBSnapshotAttributeMessage', ], 'output' => [ 'shape' => 'ModifyDBSnapshotAttributeResult', 'resultWrapper' => 'ModifyDBSnapshotAttributeResult', ], 'errors' => [ [ 'shape' => 'DBSnapshotNotFoundFault', ], [ 'shape' => 'InvalidDBSnapshotStateFault', ], [ 'shape' => 'SharedSnapshotQuotaExceededFault', ], ], ], 'ModifyDBSubnetGroup' => [ 'name' => 'ModifyDBSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBSubnetGroupMessage', ], 'output' => [ 'shape' => 'ModifyDBSubnetGroupResult', 'resultWrapper' => 'ModifyDBSubnetGroupResult', ], 'errors' => [ [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'DBSubnetQuotaExceededFault', ], [ 'shape' => 'SubnetAlreadyInUse', ], [ 'shape' => 'DBSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidSubnet', ], ], ], 'ModifyEventSubscription' => [ 'name' => 'ModifyEventSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyEventSubscriptionMessage', ], 'output' => [ 'shape' => 'ModifyEventSubscriptionResult', 'resultWrapper' => 'ModifyEventSubscriptionResult', ], 'errors' => [ [ 'shape' => 'EventSubscriptionQuotaExceededFault', ], [ 'shape' => 'SubscriptionNotFoundFault', ], [ 'shape' => 'SNSInvalidTopicFault', ], [ 'shape' => 'SNSNoAuthorizationFault', ], [ 'shape' => 'SNSTopicArnNotFoundFault', ], [ 'shape' => 'SubscriptionCategoryNotFoundFault', ], ], ], 'ModifyGlobalCluster' => [ 'name' => 'ModifyGlobalCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyGlobalClusterMessage', ], 'output' => [ 'shape' => 'ModifyGlobalClusterResult', 'resultWrapper' => 'ModifyGlobalClusterResult', ], 'errors' => [ [ 'shape' => 'GlobalClusterNotFoundFault', ], [ 'shape' => 'GlobalClusterAlreadyExistsFault', ], [ 'shape' => 'InvalidGlobalClusterStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'ModifyIntegration' => [ 'name' => 'ModifyIntegration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyIntegrationMessage', ], 'output' => [ 'shape' => 'Integration', 'resultWrapper' => 'ModifyIntegrationResult', ], 'errors' => [ [ 'shape' => 'IntegrationNotFoundFault', ], [ 'shape' => 'InvalidIntegrationStateFault', ], [ 'shape' => 'IntegrationConflictOperationFault', ], ], ], 'ModifyOptionGroup' => [ 'name' => 'ModifyOptionGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyOptionGroupMessage', ], 'output' => [ 'shape' => 'ModifyOptionGroupResult', 'resultWrapper' => 'ModifyOptionGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidOptionGroupStateFault', ], [ 'shape' => 'OptionGroupNotFoundFault', ], ], ], 'ModifyTenantDatabase' => [ 'name' => 'ModifyTenantDatabase', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyTenantDatabaseMessage', ], 'output' => [ 'shape' => 'ModifyTenantDatabaseResult', 'resultWrapper' => 'ModifyTenantDatabaseResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'TenantDatabaseNotFoundFault', ], [ 'shape' => 'TenantDatabaseAlreadyExistsFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], ], ], 'PromoteReadReplica' => [ 'name' => 'PromoteReadReplica', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PromoteReadReplicaMessage', ], 'output' => [ 'shape' => 'PromoteReadReplicaResult', 'resultWrapper' => 'PromoteReadReplicaResult', ], 'errors' => [ [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], ], ], 'PromoteReadReplicaDBCluster' => [ 'name' => 'PromoteReadReplicaDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PromoteReadReplicaDBClusterMessage', ], 'output' => [ 'shape' => 'PromoteReadReplicaDBClusterResult', 'resultWrapper' => 'PromoteReadReplicaDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], ], ], 'PurchaseReservedDBInstancesOffering' => [ 'name' => 'PurchaseReservedDBInstancesOffering', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PurchaseReservedDBInstancesOfferingMessage', ], 'output' => [ 'shape' => 'PurchaseReservedDBInstancesOfferingResult', 'resultWrapper' => 'PurchaseReservedDBInstancesOfferingResult', ], 'errors' => [ [ 'shape' => 'ReservedDBInstancesOfferingNotFoundFault', ], [ 'shape' => 'ReservedDBInstanceAlreadyExistsFault', ], [ 'shape' => 'ReservedDBInstanceQuotaExceededFault', ], ], ], 'RebootDBCluster' => [ 'name' => 'RebootDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RebootDBClusterMessage', ], 'output' => [ 'shape' => 'RebootDBClusterResult', 'resultWrapper' => 'RebootDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'RebootDBInstance' => [ 'name' => 'RebootDBInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RebootDBInstanceMessage', ], 'output' => [ 'shape' => 'RebootDBInstanceResult', 'resultWrapper' => 'RebootDBInstanceResult', ], 'errors' => [ [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], ], ], 'RebootDBShardGroup' => [ 'name' => 'RebootDBShardGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RebootDBShardGroupMessage', ], 'output' => [ 'shape' => 'DBShardGroup', 'resultWrapper' => 'RebootDBShardGroupResult', ], 'errors' => [ [ 'shape' => 'DBShardGroupNotFoundFault', ], [ 'shape' => 'InvalidDBShardGroupStateFault', ], ], ], 'RegisterDBProxyTargets' => [ 'name' => 'RegisterDBProxyTargets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RegisterDBProxyTargetsRequest', ], 'output' => [ 'shape' => 'RegisterDBProxyTargetsResponse', 'resultWrapper' => 'RegisterDBProxyTargetsResult', ], 'errors' => [ [ 'shape' => 'DBProxyNotFoundFault', ], [ 'shape' => 'DBProxyTargetGroupNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBProxyTargetAlreadyRegisteredFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBProxyStateFault', ], [ 'shape' => 'InsufficientAvailableIPsInSubnetFault', ], ], ], 'RemoveFromGlobalCluster' => [ 'name' => 'RemoveFromGlobalCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveFromGlobalClusterMessage', ], 'output' => [ 'shape' => 'RemoveFromGlobalClusterResult', 'resultWrapper' => 'RemoveFromGlobalClusterResult', ], 'errors' => [ [ 'shape' => 'GlobalClusterNotFoundFault', ], [ 'shape' => 'InvalidGlobalClusterStateFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'RemoveRoleFromDBCluster' => [ 'name' => 'RemoveRoleFromDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveRoleFromDBClusterMessage', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBClusterRoleNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], ], ], 'RemoveRoleFromDBInstance' => [ 'name' => 'RemoveRoleFromDBInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveRoleFromDBInstanceMessage', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBInstanceRoleNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'RemoveSourceIdentifierFromSubscription' => [ 'name' => 'RemoveSourceIdentifierFromSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveSourceIdentifierFromSubscriptionMessage', ], 'output' => [ 'shape' => 'RemoveSourceIdentifierFromSubscriptionResult', 'resultWrapper' => 'RemoveSourceIdentifierFromSubscriptionResult', ], 'errors' => [ [ 'shape' => 'SubscriptionNotFoundFault', ], [ 'shape' => 'SourceNotFoundFault', ], ], ], 'RemoveTagsFromResource' => [ 'name' => 'RemoveTagsFromResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTagsFromResourceMessage', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBSnapshotNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBProxyNotFoundFault', ], [ 'shape' => 'DBProxyTargetGroupNotFoundFault', ], [ 'shape' => 'BlueGreenDeploymentNotFoundFault', ], [ 'shape' => 'IntegrationNotFoundFault', ], [ 'shape' => 'TenantDatabaseNotFoundFault', ], [ 'shape' => 'DBSnapshotTenantDatabaseNotFoundFault', ], ], ], 'ResetDBClusterParameterGroup' => [ 'name' => 'ResetDBClusterParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetDBClusterParameterGroupMessage', ], 'output' => [ 'shape' => 'DBClusterParameterGroupNameMessage', 'resultWrapper' => 'ResetDBClusterParameterGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidDBParameterGroupStateFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'ResetDBParameterGroup' => [ 'name' => 'ResetDBParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetDBParameterGroupMessage', ], 'output' => [ 'shape' => 'DBParameterGroupNameMessage', 'resultWrapper' => 'ResetDBParameterGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidDBParameterGroupStateFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'RestoreDBClusterFromS3' => [ 'name' => 'RestoreDBClusterFromS3', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreDBClusterFromS3Message', ], 'output' => [ 'shape' => 'RestoreDBClusterFromS3Result', 'resultWrapper' => 'RestoreDBClusterFromS3Result', ], 'errors' => [ [ 'shape' => 'DBClusterAlreadyExistsFault', ], [ 'shape' => 'DBClusterQuotaExceededFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBSubnetGroupStateFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'InvalidS3BucketFault', ], [ 'shape' => 'DBClusterParameterGroupNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DomainNotFoundFault', ], [ 'shape' => 'InsufficientStorageClusterCapacityFault', ], [ 'shape' => 'StorageTypeNotSupportedFault', ], ], ], 'RestoreDBClusterFromSnapshot' => [ 'name' => 'RestoreDBClusterFromSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreDBClusterFromSnapshotMessage', ], 'output' => [ 'shape' => 'RestoreDBClusterFromSnapshotResult', 'resultWrapper' => 'RestoreDBClusterFromSnapshotResult', ], 'errors' => [ [ 'shape' => 'DBClusterAlreadyExistsFault', ], [ 'shape' => 'DBClusterQuotaExceededFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'DBSnapshotNotFoundFault', ], [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], [ 'shape' => 'InsufficientDBClusterCapacityFault', ], [ 'shape' => 'InsufficientStorageClusterCapacityFault', ], [ 'shape' => 'InvalidDBSnapshotStateFault', ], [ 'shape' => 'InvalidDBClusterSnapshotStateFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'DBSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidRestoreFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'OptionGroupNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'DomainNotFoundFault', ], [ 'shape' => 'DBClusterParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'InsufficientDBInstanceCapacityFault', ], ], ], 'RestoreDBClusterToPointInTime' => [ 'name' => 'RestoreDBClusterToPointInTime', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreDBClusterToPointInTimeMessage', ], 'output' => [ 'shape' => 'RestoreDBClusterToPointInTimeResult', 'resultWrapper' => 'RestoreDBClusterToPointInTimeResult', ], 'errors' => [ [ 'shape' => 'DBClusterAlreadyExistsFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBClusterQuotaExceededFault', ], [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'InsufficientDBClusterCapacityFault', ], [ 'shape' => 'InsufficientStorageClusterCapacityFault', ], [ 'shape' => 'InvalidDBClusterSnapshotStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBSnapshotStateFault', ], [ 'shape' => 'InvalidRestoreFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'OptionGroupNotFoundFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'DomainNotFoundFault', ], [ 'shape' => 'DBClusterParameterGroupNotFoundFault', ], [ 'shape' => 'DBClusterAutomatedBackupNotFoundFault', ], [ 'shape' => 'InsufficientDBInstanceCapacityFault', ], ], ], 'RestoreDBInstanceFromDBSnapshot' => [ 'name' => 'RestoreDBInstanceFromDBSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreDBInstanceFromDBSnapshotMessage', ], 'output' => [ 'shape' => 'RestoreDBInstanceFromDBSnapshotResult', 'resultWrapper' => 'RestoreDBInstanceFromDBSnapshotResult', ], 'errors' => [ [ 'shape' => 'DBInstanceAlreadyExistsFault', ], [ 'shape' => 'DBSnapshotNotFoundFault', ], [ 'shape' => 'InstanceQuotaExceededFault', ], [ 'shape' => 'InsufficientDBInstanceCapacityFault', ], [ 'shape' => 'InvalidDBSnapshotStateFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'InvalidRestoreFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'DBSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'ProvisionedIopsNotAvailableInAZFault', ], [ 'shape' => 'OptionGroupNotFoundFault', ], [ 'shape' => 'StorageTypeNotSupportedFault', ], [ 'shape' => 'AuthorizationNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'DBSecurityGroupNotFoundFault', ], [ 'shape' => 'DomainNotFoundFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'BackupPolicyNotFoundFault', ], [ 'shape' => 'NetworkTypeNotSupported', ], [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], [ 'shape' => 'CertificateNotFoundFault', ], [ 'shape' => 'TenantDatabaseQuotaExceededFault', ], ], ], 'RestoreDBInstanceFromS3' => [ 'name' => 'RestoreDBInstanceFromS3', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreDBInstanceFromS3Message', ], 'output' => [ 'shape' => 'RestoreDBInstanceFromS3Result', 'resultWrapper' => 'RestoreDBInstanceFromS3Result', ], 'errors' => [ [ 'shape' => 'DBInstanceAlreadyExistsFault', ], [ 'shape' => 'InsufficientDBInstanceCapacityFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'DBSecurityGroupNotFoundFault', ], [ 'shape' => 'InstanceQuotaExceededFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'DBSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'InvalidS3BucketFault', ], [ 'shape' => 'ProvisionedIopsNotAvailableInAZFault', ], [ 'shape' => 'OptionGroupNotFoundFault', ], [ 'shape' => 'StorageTypeNotSupportedFault', ], [ 'shape' => 'AuthorizationNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'BackupPolicyNotFoundFault', ], [ 'shape' => 'NetworkTypeNotSupported', ], [ 'shape' => 'CertificateNotFoundFault', ], ], ], 'RestoreDBInstanceToPointInTime' => [ 'name' => 'RestoreDBInstanceToPointInTime', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreDBInstanceToPointInTimeMessage', ], 'output' => [ 'shape' => 'RestoreDBInstanceToPointInTimeResult', 'resultWrapper' => 'RestoreDBInstanceToPointInTimeResult', ], 'errors' => [ [ 'shape' => 'DBInstanceAlreadyExistsFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InstanceQuotaExceededFault', ], [ 'shape' => 'InsufficientDBInstanceCapacityFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'PointInTimeRestoreNotEnabledFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'InvalidRestoreFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'DBSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'ProvisionedIopsNotAvailableInAZFault', ], [ 'shape' => 'OptionGroupNotFoundFault', ], [ 'shape' => 'StorageTypeNotSupportedFault', ], [ 'shape' => 'AuthorizationNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'DBSecurityGroupNotFoundFault', ], [ 'shape' => 'DomainNotFoundFault', ], [ 'shape' => 'BackupPolicyNotFoundFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'DBInstanceAutomatedBackupNotFoundFault', ], [ 'shape' => 'NetworkTypeNotSupported', ], [ 'shape' => 'TenantDatabaseQuotaExceededFault', ], [ 'shape' => 'CertificateNotFoundFault', ], ], ], 'RevokeDBSecurityGroupIngress' => [ 'name' => 'RevokeDBSecurityGroupIngress', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RevokeDBSecurityGroupIngressMessage', ], 'output' => [ 'shape' => 'RevokeDBSecurityGroupIngressResult', 'resultWrapper' => 'RevokeDBSecurityGroupIngressResult', ], 'errors' => [ [ 'shape' => 'DBSecurityGroupNotFoundFault', ], [ 'shape' => 'AuthorizationNotFoundFault', ], [ 'shape' => 'InvalidDBSecurityGroupStateFault', ], ], ], 'StartActivityStream' => [ 'name' => 'StartActivityStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartActivityStreamRequest', ], 'output' => [ 'shape' => 'StartActivityStreamResponse', 'resultWrapper' => 'StartActivityStreamResult', ], 'errors' => [ [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], ], ], 'StartDBCluster' => [ 'name' => 'StartDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDBClusterMessage', ], 'output' => [ 'shape' => 'StartDBClusterResult', 'resultWrapper' => 'StartDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'StartDBInstance' => [ 'name' => 'StartDBInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDBInstanceMessage', ], 'output' => [ 'shape' => 'StartDBInstanceResult', 'resultWrapper' => 'StartDBInstanceResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'InsufficientDBInstanceCapacityFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'DBSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'AuthorizationNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], ], ], 'StartDBInstanceAutomatedBackupsReplication' => [ 'name' => 'StartDBInstanceAutomatedBackupsReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDBInstanceAutomatedBackupsReplicationMessage', ], 'output' => [ 'shape' => 'StartDBInstanceAutomatedBackupsReplicationResult', 'resultWrapper' => 'StartDBInstanceAutomatedBackupsReplicationResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'DBInstanceAutomatedBackupQuotaExceededFault', ], [ 'shape' => 'StorageTypeNotSupportedFault', ], ], ], 'StartExportTask' => [ 'name' => 'StartExportTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartExportTaskMessage', ], 'output' => [ 'shape' => 'ExportTask', 'resultWrapper' => 'StartExportTaskResult', ], 'errors' => [ [ 'shape' => 'DBSnapshotNotFoundFault', ], [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'ExportTaskAlreadyExistsFault', ], [ 'shape' => 'InvalidS3BucketFault', ], [ 'shape' => 'IamRoleNotFoundFault', ], [ 'shape' => 'IamRoleMissingPermissionsFault', ], [ 'shape' => 'InvalidExportOnlyFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'InvalidExportSourceStateFault', ], ], ], 'StopActivityStream' => [ 'name' => 'StopActivityStream', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopActivityStreamRequest', ], 'output' => [ 'shape' => 'StopActivityStreamResponse', 'resultWrapper' => 'StopActivityStreamResult', ], 'errors' => [ [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'ResourceNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], ], ], 'StopDBCluster' => [ 'name' => 'StopDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopDBClusterMessage', ], 'output' => [ 'shape' => 'StopDBClusterResult', 'resultWrapper' => 'StopDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'StopDBInstance' => [ 'name' => 'StopDBInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopDBInstanceMessage', ], 'output' => [ 'shape' => 'StopDBInstanceResult', 'resultWrapper' => 'StopDBInstanceResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'DBSnapshotAlreadyExistsFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], ], ], 'StopDBInstanceAutomatedBackupsReplication' => [ 'name' => 'StopDBInstanceAutomatedBackupsReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopDBInstanceAutomatedBackupsReplicationMessage', ], 'output' => [ 'shape' => 'StopDBInstanceAutomatedBackupsReplicationResult', 'resultWrapper' => 'StopDBInstanceAutomatedBackupsReplicationResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'SwitchoverBlueGreenDeployment' => [ 'name' => 'SwitchoverBlueGreenDeployment', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SwitchoverBlueGreenDeploymentRequest', ], 'output' => [ 'shape' => 'SwitchoverBlueGreenDeploymentResponse', 'resultWrapper' => 'SwitchoverBlueGreenDeploymentResult', ], 'errors' => [ [ 'shape' => 'BlueGreenDeploymentNotFoundFault', ], [ 'shape' => 'InvalidBlueGreenDeploymentStateFault', ], ], ], 'SwitchoverGlobalCluster' => [ 'name' => 'SwitchoverGlobalCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SwitchoverGlobalClusterMessage', ], 'output' => [ 'shape' => 'SwitchoverGlobalClusterResult', 'resultWrapper' => 'SwitchoverGlobalClusterResult', ], 'errors' => [ [ 'shape' => 'GlobalClusterNotFoundFault', ], [ 'shape' => 'InvalidGlobalClusterStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'SwitchoverReadReplica' => [ 'name' => 'SwitchoverReadReplica', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SwitchoverReadReplicaMessage', ], 'output' => [ 'shape' => 'SwitchoverReadReplicaResult', 'resultWrapper' => 'SwitchoverReadReplicaResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], ], 'shapes' => [ 'AccountAttributesMessage' => [ 'type' => 'structure', 'members' => [ 'AccountQuotas' => [ 'shape' => 'AccountQuotaList', ], ], ], 'AccountQuota' => [ 'type' => 'structure', 'members' => [ 'AccountQuotaName' => [ 'shape' => 'String', ], 'Used' => [ 'shape' => 'Long', ], 'Max' => [ 'shape' => 'Long', ], ], 'wrapper' => true, ], 'AccountQuotaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountQuota', 'locationName' => 'AccountQuota', ], ], 'ActivityStreamMode' => [ 'type' => 'string', 'enum' => [ 'sync', 'async', ], ], 'ActivityStreamModeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ActivityStreamPolicyStatus' => [ 'type' => 'string', 'enum' => [ 'locked', 'unlocked', 'locking-policy', 'unlocking-policy', ], ], 'ActivityStreamStatus' => [ 'type' => 'string', 'enum' => [ 'stopped', 'starting', 'started', 'stopping', ], ], 'AddRoleToDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', 'RoleArn', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'RoleArn' => [ 'shape' => 'String', ], 'FeatureName' => [ 'shape' => 'String', ], ], ], 'AddRoleToDBInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', 'RoleArn', 'FeatureName', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'RoleArn' => [ 'shape' => 'String', ], 'FeatureName' => [ 'shape' => 'String', ], ], ], 'AddSourceIdentifierToSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', 'SourceIdentifier', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'SourceIdentifier' => [ 'shape' => 'String', ], ], ], 'AddSourceIdentifierToSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'AddTagsToResourceMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceName', 'Tags', ], 'members' => [ 'ResourceName' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ApplyMethod' => [ 'type' => 'string', 'enum' => [ 'immediate', 'pending-reboot', ], ], 'ApplyPendingMaintenanceActionMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceIdentifier', 'ApplyAction', 'OptInType', ], 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'String', ], 'ApplyAction' => [ 'shape' => 'String', ], 'OptInType' => [ 'shape' => 'String', ], ], ], 'ApplyPendingMaintenanceActionResult' => [ 'type' => 'structure', 'members' => [ 'ResourcePendingMaintenanceActions' => [ 'shape' => 'ResourcePendingMaintenanceActions', ], ], ], 'Arn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, ], 'AttributeValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'AttributeValue', ], ], 'AuditPolicyState' => [ 'type' => 'string', 'enum' => [ 'locked', 'unlocked', ], ], 'AuthScheme' => [ 'type' => 'string', 'enum' => [ 'SECRETS', ], ], 'AuthorizationAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'AuthorizationAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'AuthorizationNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'AuthorizationNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'AuthorizationQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'AuthorizationQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'AuthorizeDBSecurityGroupIngressMessage' => [ 'type' => 'structure', 'required' => [ 'DBSecurityGroupName', ], 'members' => [ 'DBSecurityGroupName' => [ 'shape' => 'String', ], 'CIDRIP' => [ 'shape' => 'String', ], 'EC2SecurityGroupName' => [ 'shape' => 'String', ], 'EC2SecurityGroupId' => [ 'shape' => 'String', ], 'EC2SecurityGroupOwnerId' => [ 'shape' => 'String', ], ], ], 'AuthorizeDBSecurityGroupIngressResult' => [ 'type' => 'structure', 'members' => [ 'DBSecurityGroup' => [ 'shape' => 'DBSecurityGroup', ], ], ], 'AutomationMode' => [ 'type' => 'string', 'enum' => [ 'full', 'all-paused', ], ], 'AvailabilityZone' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'AvailabilityZoneList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZone', 'locationName' => 'AvailabilityZone', ], ], 'AvailabilityZones' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'AvailabilityZone', ], ], 'AvailableProcessorFeature' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'DefaultValue' => [ 'shape' => 'String', ], 'AllowedValues' => [ 'shape' => 'String', ], ], ], 'AvailableProcessorFeatureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailableProcessorFeature', 'locationName' => 'AvailableProcessorFeature', ], ], 'AwsBackupRecoveryPointArn' => [ 'type' => 'string', 'max' => 350, 'min' => 43, 'pattern' => '^arn:aws[a-z-]*:backup:[-a-z0-9]+:[0-9]{12}:[-a-z]+:([a-z0-9\\-]+:)?[a-z][a-z0-9\\-]{0,255}$', ], 'BacktrackDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', 'BacktrackTo', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'BacktrackTo' => [ 'shape' => 'TStamp', ], 'Force' => [ 'shape' => 'BooleanOptional', ], 'UseEarliestTimeOnPointInTimeUnavailable' => [ 'shape' => 'BooleanOptional', ], ], ], 'BackupPolicyNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'deprecated' => true, 'deprecatedMessage' => 'Please avoid using this fault', 'error' => [ 'code' => 'BackupPolicyNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'BlueGreenDeployment' => [ 'type' => 'structure', 'members' => [ 'BlueGreenDeploymentIdentifier' => [ 'shape' => 'BlueGreenDeploymentIdentifier', ], 'BlueGreenDeploymentName' => [ 'shape' => 'BlueGreenDeploymentName', ], 'Source' => [ 'shape' => 'DatabaseArn', ], 'Target' => [ 'shape' => 'DatabaseArn', ], 'SwitchoverDetails' => [ 'shape' => 'SwitchoverDetailList', ], 'Tasks' => [ 'shape' => 'BlueGreenDeploymentTaskList', ], 'Status' => [ 'shape' => 'BlueGreenDeploymentStatus', ], 'StatusDetails' => [ 'shape' => 'BlueGreenDeploymentStatusDetails', ], 'CreateTime' => [ 'shape' => 'TStamp', ], 'DeleteTime' => [ 'shape' => 'TStamp', ], 'TagList' => [ 'shape' => 'TagList', ], ], ], 'BlueGreenDeploymentAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'BlueGreenDeploymentAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'BlueGreenDeploymentIdentifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[A-Za-z][0-9A-Za-z-:._]*', ], 'BlueGreenDeploymentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlueGreenDeployment', ], ], 'BlueGreenDeploymentName' => [ 'type' => 'string', 'max' => 60, 'min' => 1, 'pattern' => '[a-zA-Z][a-zA-Z0-9]*(-[a-zA-Z0-9]+)*', ], 'BlueGreenDeploymentNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'BlueGreenDeploymentNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'BlueGreenDeploymentStatus' => [ 'type' => 'string', ], 'BlueGreenDeploymentStatusDetails' => [ 'type' => 'string', ], 'BlueGreenDeploymentTask' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'BlueGreenDeploymentTaskName', ], 'Status' => [ 'shape' => 'BlueGreenDeploymentTaskStatus', ], ], ], 'BlueGreenDeploymentTaskList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlueGreenDeploymentTask', ], ], 'BlueGreenDeploymentTaskName' => [ 'type' => 'string', ], 'BlueGreenDeploymentTaskStatus' => [ 'type' => 'string', ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanOptional' => [ 'type' => 'boolean', ], 'BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '.*', ], 'CACertificateIdentifiersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'CancelExportTaskMessage' => [ 'type' => 'structure', 'required' => [ 'ExportTaskIdentifier', ], 'members' => [ 'ExportTaskIdentifier' => [ 'shape' => 'String', ], ], ], 'Certificate' => [ 'type' => 'structure', 'members' => [ 'CertificateIdentifier' => [ 'shape' => 'String', ], 'CertificateType' => [ 'shape' => 'String', ], 'Thumbprint' => [ 'shape' => 'String', ], 'ValidFrom' => [ 'shape' => 'TStamp', ], 'ValidTill' => [ 'shape' => 'TStamp', ], 'CertificateArn' => [ 'shape' => 'String', ], 'CustomerOverride' => [ 'shape' => 'BooleanOptional', ], 'CustomerOverrideValidTill' => [ 'shape' => 'TStamp', ], ], 'wrapper' => true, ], 'CertificateDetails' => [ 'type' => 'structure', 'members' => [ 'CAIdentifier' => [ 'shape' => 'String', ], 'ValidTill' => [ 'shape' => 'TStamp', ], ], ], 'CertificateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Certificate', 'locationName' => 'Certificate', ], ], 'CertificateMessage' => [ 'type' => 'structure', 'members' => [ 'DefaultCertificateForNewLaunches' => [ 'shape' => 'String', ], 'Certificates' => [ 'shape' => 'CertificateList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'CertificateNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CertificateNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'CharacterSet' => [ 'type' => 'structure', 'members' => [ 'CharacterSetName' => [ 'shape' => 'String', ], 'CharacterSetDescription' => [ 'shape' => 'String', ], ], ], 'ClientPasswordAuthType' => [ 'type' => 'string', 'enum' => [ 'MYSQL_NATIVE_PASSWORD', 'MYSQL_CACHING_SHA2_PASSWORD', 'POSTGRES_SCRAM_SHA_256', 'POSTGRES_MD5', 'SQL_SERVER_AUTHENTICATION', ], ], 'CloudwatchLogsExportConfiguration' => [ 'type' => 'structure', 'members' => [ 'EnableLogTypes' => [ 'shape' => 'LogTypeList', ], 'DisableLogTypes' => [ 'shape' => 'LogTypeList', ], ], ], 'ClusterPendingModifiedValues' => [ 'type' => 'structure', 'members' => [ 'PendingCloudwatchLogsExports' => [ 'shape' => 'PendingCloudwatchLogsExports', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'MasterUserPassword' => [ 'shape' => 'String', ], 'IAMDatabaseAuthenticationEnabled' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'RdsCustomClusterConfiguration' => [ 'shape' => 'RdsCustomClusterConfiguration', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'StorageType' => [ 'shape' => 'String', ], 'CertificateDetails' => [ 'shape' => 'CertificateDetails', ], ], ], 'ClusterScalabilityType' => [ 'type' => 'string', 'enum' => [ 'standard', 'limitless', ], ], 'ConnectionPoolConfiguration' => [ 'type' => 'structure', 'members' => [ 'MaxConnectionsPercent' => [ 'shape' => 'IntegerOptional', ], 'MaxIdleConnectionsPercent' => [ 'shape' => 'IntegerOptional', ], 'ConnectionBorrowTimeout' => [ 'shape' => 'IntegerOptional', ], 'SessionPinningFilters' => [ 'shape' => 'StringList', ], 'InitQuery' => [ 'shape' => 'String', ], ], ], 'ConnectionPoolConfigurationInfo' => [ 'type' => 'structure', 'members' => [ 'MaxConnectionsPercent' => [ 'shape' => 'Integer', ], 'MaxIdleConnectionsPercent' => [ 'shape' => 'Integer', ], 'ConnectionBorrowTimeout' => [ 'shape' => 'Integer', ], 'SessionPinningFilters' => [ 'shape' => 'StringList', ], 'InitQuery' => [ 'shape' => 'String', ], ], ], 'ContextAttribute' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'ContextAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContextAttribute', ], ], 'CopyDBClusterParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'SourceDBClusterParameterGroupIdentifier', 'TargetDBClusterParameterGroupIdentifier', 'TargetDBClusterParameterGroupDescription', ], 'members' => [ 'SourceDBClusterParameterGroupIdentifier' => [ 'shape' => 'String', ], 'TargetDBClusterParameterGroupIdentifier' => [ 'shape' => 'String', ], 'TargetDBClusterParameterGroupDescription' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CopyDBClusterParameterGroupResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterParameterGroup' => [ 'shape' => 'DBClusterParameterGroup', ], ], ], 'CopyDBClusterSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'SourceDBClusterSnapshotIdentifier', 'TargetDBClusterSnapshotIdentifier', ], 'members' => [ 'SourceDBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'TargetDBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'PreSignedUrl' => [ 'shape' => 'String', ], 'DestinationRegion' => [ 'shape' => 'String', ], 'CopyTags' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CopyDBClusterSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterSnapshot' => [ 'shape' => 'DBClusterSnapshot', ], ], ], 'CopyDBParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'SourceDBParameterGroupIdentifier', 'TargetDBParameterGroupIdentifier', 'TargetDBParameterGroupDescription', ], 'members' => [ 'SourceDBParameterGroupIdentifier' => [ 'shape' => 'String', ], 'TargetDBParameterGroupIdentifier' => [ 'shape' => 'String', ], 'TargetDBParameterGroupDescription' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CopyDBParameterGroupResult' => [ 'type' => 'structure', 'members' => [ 'DBParameterGroup' => [ 'shape' => 'DBParameterGroup', ], ], ], 'CopyDBSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'SourceDBSnapshotIdentifier', 'TargetDBSnapshotIdentifier', ], 'members' => [ 'SourceDBSnapshotIdentifier' => [ 'shape' => 'String', ], 'TargetDBSnapshotIdentifier' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], 'CopyTags' => [ 'shape' => 'BooleanOptional', ], 'PreSignedUrl' => [ 'shape' => 'String', ], 'DestinationRegion' => [ 'shape' => 'String', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'TargetCustomAvailabilityZone' => [ 'shape' => 'String', ], 'CopyOptionGroup' => [ 'shape' => 'BooleanOptional', ], ], ], 'CopyDBSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'DBSnapshot' => [ 'shape' => 'DBSnapshot', ], ], ], 'CopyOptionGroupMessage' => [ 'type' => 'structure', 'required' => [ 'SourceOptionGroupIdentifier', 'TargetOptionGroupIdentifier', 'TargetOptionGroupDescription', ], 'members' => [ 'SourceOptionGroupIdentifier' => [ 'shape' => 'String', ], 'TargetOptionGroupIdentifier' => [ 'shape' => 'String', ], 'TargetOptionGroupDescription' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CopyOptionGroupResult' => [ 'type' => 'structure', 'members' => [ 'OptionGroup' => [ 'shape' => 'OptionGroup', ], ], ], 'CreateBlueGreenDeploymentRequest' => [ 'type' => 'structure', 'required' => [ 'BlueGreenDeploymentName', 'Source', ], 'members' => [ 'BlueGreenDeploymentName' => [ 'shape' => 'BlueGreenDeploymentName', ], 'Source' => [ 'shape' => 'DatabaseArn', ], 'TargetEngineVersion' => [ 'shape' => 'TargetEngineVersion', ], 'TargetDBParameterGroupName' => [ 'shape' => 'TargetDBParameterGroupName', ], 'TargetDBClusterParameterGroupName' => [ 'shape' => 'TargetDBClusterParameterGroupName', ], 'Tags' => [ 'shape' => 'TagList', ], 'TargetDBInstanceClass' => [ 'shape' => 'TargetDBInstanceClass', ], 'UpgradeTargetStorageConfig' => [ 'shape' => 'BooleanOptional', ], 'TargetIops' => [ 'shape' => 'IntegerOptional', ], 'TargetStorageType' => [ 'shape' => 'TargetStorageType', ], 'TargetAllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'TargetStorageThroughput' => [ 'shape' => 'IntegerOptional', ], ], ], 'CreateBlueGreenDeploymentResponse' => [ 'type' => 'structure', 'members' => [ 'BlueGreenDeployment' => [ 'shape' => 'BlueGreenDeployment', ], ], ], 'CreateCustomDBEngineVersionFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CreateCustomDBEngineVersionFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CreateCustomDBEngineVersionMessage' => [ 'type' => 'structure', 'required' => [ 'Engine', 'EngineVersion', ], 'members' => [ 'Engine' => [ 'shape' => 'CustomEngineName', ], 'EngineVersion' => [ 'shape' => 'CustomEngineVersion', ], 'DatabaseInstallationFilesS3BucketName' => [ 'shape' => 'BucketName', ], 'DatabaseInstallationFilesS3Prefix' => [ 'shape' => 'String255', ], 'ImageId' => [ 'shape' => 'String255', ], 'KMSKeyId' => [ 'shape' => 'KmsKeyIdOrArn', ], 'Description' => [ 'shape' => 'Description', ], 'Manifest' => [ 'shape' => 'CustomDBEngineVersionManifest', ], 'Tags' => [ 'shape' => 'TagList', ], 'SourceCustomDbEngineVersionIdentifier' => [ 'shape' => 'String255', ], 'UseAwsProvidedLatestImage' => [ 'shape' => 'BooleanOptional', ], ], ], 'CreateDBClusterEndpointMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', 'DBClusterEndpointIdentifier', 'EndpointType', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterEndpointIdentifier' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'String', ], 'StaticMembers' => [ 'shape' => 'StringList', ], 'ExcludedMembers' => [ 'shape' => 'StringList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', 'Engine', ], 'members' => [ 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'CharacterSetName' => [ 'shape' => 'String', ], 'DatabaseName' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'MasterUsername' => [ 'shape' => 'String', ], 'MasterUserPassword' => [ 'shape' => 'String', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'ReplicationSourceIdentifier' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], 'StorageEncrypted' => [ 'shape' => 'BooleanOptional', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'PreSignedUrl' => [ 'shape' => 'String', ], 'DestinationRegion' => [ 'shape' => 'String', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'BacktrackWindow' => [ 'shape' => 'LongOptional', ], 'EnableCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'EngineMode' => [ 'shape' => 'String', ], 'ScalingConfiguration' => [ 'shape' => 'ScalingConfiguration', ], 'RdsCustomClusterConfiguration' => [ 'shape' => 'RdsCustomClusterConfiguration', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'GlobalClusterIdentifier' => [ 'shape' => 'String', ], 'EnableHttpEndpoint' => [ 'shape' => 'BooleanOptional', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'Domain' => [ 'shape' => 'String', ], 'DomainIAMRoleName' => [ 'shape' => 'String', ], 'EnableGlobalWriteForwarding' => [ 'shape' => 'BooleanOptional', ], 'DBClusterInstanceClass' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'StorageType' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'MonitoringInterval' => [ 'shape' => 'IntegerOptional', ], 'MonitoringRoleArn' => [ 'shape' => 'String', ], 'DatabaseInsightsMode' => [ 'shape' => 'DatabaseInsightsMode', ], 'EnablePerformanceInsights' => [ 'shape' => 'BooleanOptional', ], 'PerformanceInsightsKMSKeyId' => [ 'shape' => 'String', ], 'PerformanceInsightsRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'EnableLimitlessDatabase' => [ 'shape' => 'BooleanOptional', ], 'ServerlessV2ScalingConfiguration' => [ 'shape' => 'ServerlessV2ScalingConfiguration', ], 'NetworkType' => [ 'shape' => 'String', ], 'ClusterScalabilityType' => [ 'shape' => 'ClusterScalabilityType', ], 'DBSystemId' => [ 'shape' => 'String', ], 'ManageMasterUserPassword' => [ 'shape' => 'BooleanOptional', ], 'MasterUserSecretKmsKeyId' => [ 'shape' => 'String', ], 'EnableLocalWriteForwarding' => [ 'shape' => 'BooleanOptional', ], 'CACertificateIdentifier' => [ 'shape' => 'String', ], 'EngineLifecycleSupport' => [ 'shape' => 'String', ], ], ], 'CreateDBClusterParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterParameterGroupName', 'DBParameterGroupFamily', 'Description', ], 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBClusterParameterGroupResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterParameterGroup' => [ 'shape' => 'DBClusterParameterGroup', ], ], ], 'CreateDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'CreateDBClusterSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterSnapshotIdentifier', 'DBClusterIdentifier', ], 'members' => [ 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBClusterSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterSnapshot' => [ 'shape' => 'DBClusterSnapshot', ], ], ], 'CreateDBInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', 'DBInstanceClass', 'Engine', ], 'members' => [ 'DBName' => [ 'shape' => 'String', ], 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'MasterUsername' => [ 'shape' => 'String', ], 'MasterUserPassword' => [ 'shape' => 'String', ], 'DBSecurityGroups' => [ 'shape' => 'DBSecurityGroupNameList', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'DBParameterGroupName' => [ 'shape' => 'String', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'LicenseModel' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'CharacterSetName' => [ 'shape' => 'String', ], 'NcharCharacterSetName' => [ 'shape' => 'String', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'TdeCredentialArn' => [ 'shape' => 'String', ], 'TdeCredentialPassword' => [ 'shape' => 'String', ], 'StorageEncrypted' => [ 'shape' => 'BooleanOptional', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'Domain' => [ 'shape' => 'String', ], 'DomainFqdn' => [ 'shape' => 'String', ], 'DomainOu' => [ 'shape' => 'String', ], 'DomainAuthSecretArn' => [ 'shape' => 'String', ], 'DomainDnsIps' => [ 'shape' => 'StringList', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'MonitoringInterval' => [ 'shape' => 'IntegerOptional', ], 'MonitoringRoleArn' => [ 'shape' => 'String', ], 'DomainIAMRoleName' => [ 'shape' => 'String', ], 'PromotionTier' => [ 'shape' => 'IntegerOptional', ], 'Timezone' => [ 'shape' => 'String', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'DatabaseInsightsMode' => [ 'shape' => 'DatabaseInsightsMode', ], 'EnablePerformanceInsights' => [ 'shape' => 'BooleanOptional', ], 'PerformanceInsightsKMSKeyId' => [ 'shape' => 'String', ], 'PerformanceInsightsRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'EnableCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'ProcessorFeatures' => [ 'shape' => 'ProcessorFeatureList', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'MaxAllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'EnableCustomerOwnedIp' => [ 'shape' => 'BooleanOptional', ], 'CustomIamInstanceProfile' => [ 'shape' => 'String', ], 'BackupTarget' => [ 'shape' => 'String', ], 'NetworkType' => [ 'shape' => 'String', ], 'StorageThroughput' => [ 'shape' => 'IntegerOptional', ], 'ManageMasterUserPassword' => [ 'shape' => 'BooleanOptional', ], 'MasterUserSecretKmsKeyId' => [ 'shape' => 'String', ], 'CACertificateIdentifier' => [ 'shape' => 'String', ], 'DBSystemId' => [ 'shape' => 'String', ], 'DedicatedLogVolume' => [ 'shape' => 'BooleanOptional', ], 'MultiTenant' => [ 'shape' => 'BooleanOptional', ], 'EngineLifecycleSupport' => [ 'shape' => 'String', ], ], ], 'CreateDBInstanceReadReplicaMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'SourceDBInstanceIdentifier' => [ 'shape' => 'String', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'DBParameterGroupName' => [ 'shape' => 'String', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'StorageType' => [ 'shape' => 'String', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'MonitoringInterval' => [ 'shape' => 'IntegerOptional', ], 'MonitoringRoleArn' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'PreSignedUrl' => [ 'shape' => 'String', ], 'DestinationRegion' => [ 'shape' => 'String', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'DatabaseInsightsMode' => [ 'shape' => 'DatabaseInsightsMode', ], 'EnablePerformanceInsights' => [ 'shape' => 'BooleanOptional', ], 'PerformanceInsightsKMSKeyId' => [ 'shape' => 'String', ], 'PerformanceInsightsRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'EnableCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'ProcessorFeatures' => [ 'shape' => 'ProcessorFeatureList', ], 'UseDefaultProcessorFeatures' => [ 'shape' => 'BooleanOptional', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'Domain' => [ 'shape' => 'String', ], 'DomainIAMRoleName' => [ 'shape' => 'String', ], 'DomainFqdn' => [ 'shape' => 'String', ], 'DomainOu' => [ 'shape' => 'String', ], 'DomainAuthSecretArn' => [ 'shape' => 'String', ], 'DomainDnsIps' => [ 'shape' => 'StringList', ], 'ReplicaMode' => [ 'shape' => 'ReplicaMode', ], 'MaxAllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'CustomIamInstanceProfile' => [ 'shape' => 'String', ], 'NetworkType' => [ 'shape' => 'String', ], 'StorageThroughput' => [ 'shape' => 'IntegerOptional', ], 'EnableCustomerOwnedIp' => [ 'shape' => 'BooleanOptional', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'SourceDBClusterIdentifier' => [ 'shape' => 'String', ], 'DedicatedLogVolume' => [ 'shape' => 'BooleanOptional', ], 'UpgradeStorageConfig' => [ 'shape' => 'BooleanOptional', ], 'CACertificateIdentifier' => [ 'shape' => 'String', ], ], ], 'CreateDBInstanceReadReplicaResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'CreateDBInstanceResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'CreateDBParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBParameterGroupName', 'DBParameterGroupFamily', 'Description', ], 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBParameterGroupResult' => [ 'type' => 'structure', 'members' => [ 'DBParameterGroup' => [ 'shape' => 'DBParameterGroup', ], ], ], 'CreateDBProxyEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'DBProxyName', 'DBProxyEndpointName', 'VpcSubnetIds', ], 'members' => [ 'DBProxyName' => [ 'shape' => 'DBProxyName', ], 'DBProxyEndpointName' => [ 'shape' => 'DBProxyEndpointName', ], 'VpcSubnetIds' => [ 'shape' => 'StringList', ], 'VpcSecurityGroupIds' => [ 'shape' => 'StringList', ], 'TargetRole' => [ 'shape' => 'DBProxyEndpointTargetRole', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBProxyEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'DBProxyEndpoint' => [ 'shape' => 'DBProxyEndpoint', ], ], ], 'CreateDBProxyRequest' => [ 'type' => 'structure', 'required' => [ 'DBProxyName', 'EngineFamily', 'Auth', 'RoleArn', 'VpcSubnetIds', ], 'members' => [ 'DBProxyName' => [ 'shape' => 'String', ], 'EngineFamily' => [ 'shape' => 'EngineFamily', ], 'Auth' => [ 'shape' => 'UserAuthConfigList', ], 'RoleArn' => [ 'shape' => 'String', ], 'VpcSubnetIds' => [ 'shape' => 'StringList', ], 'VpcSecurityGroupIds' => [ 'shape' => 'StringList', ], 'RequireTLS' => [ 'shape' => 'Boolean', ], 'IdleClientTimeout' => [ 'shape' => 'IntegerOptional', ], 'DebugLogging' => [ 'shape' => 'Boolean', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBProxyResponse' => [ 'type' => 'structure', 'members' => [ 'DBProxy' => [ 'shape' => 'DBProxy', ], ], ], 'CreateDBSecurityGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBSecurityGroupName', 'DBSecurityGroupDescription', ], 'members' => [ 'DBSecurityGroupName' => [ 'shape' => 'String', ], 'DBSecurityGroupDescription' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBSecurityGroupResult' => [ 'type' => 'structure', 'members' => [ 'DBSecurityGroup' => [ 'shape' => 'DBSecurityGroup', ], ], ], 'CreateDBShardGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBShardGroupIdentifier', 'DBClusterIdentifier', 'MaxACU', ], 'members' => [ 'DBShardGroupIdentifier' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'ComputeRedundancy' => [ 'shape' => 'IntegerOptional', ], 'MaxACU' => [ 'shape' => 'DoubleOptional', ], 'MinACU' => [ 'shape' => 'DoubleOptional', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'DBSnapshotIdentifier', 'DBInstanceIdentifier', ], 'members' => [ 'DBSnapshotIdentifier' => [ 'shape' => 'String', ], 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'DBSnapshot' => [ 'shape' => 'DBSnapshot', ], ], ], 'CreateDBSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBSubnetGroupName', 'DBSubnetGroupDescription', 'SubnetIds', ], 'members' => [ 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'DBSubnetGroupDescription' => [ 'shape' => 'String', ], 'SubnetIds' => [ 'shape' => 'SubnetIdentifierList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBSubnetGroupResult' => [ 'type' => 'structure', 'members' => [ 'DBSubnetGroup' => [ 'shape' => 'DBSubnetGroup', ], ], ], 'CreateEventSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', 'SnsTopicArn', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'SourceIds' => [ 'shape' => 'SourceIdsList', ], 'Enabled' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateEventSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'CreateGlobalClusterMessage' => [ 'type' => 'structure', 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'String', ], 'SourceDBClusterIdentifier' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'EngineLifecycleSupport' => [ 'shape' => 'String', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'StorageEncrypted' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateGlobalClusterResult' => [ 'type' => 'structure', 'members' => [ 'GlobalCluster' => [ 'shape' => 'GlobalCluster', ], ], ], 'CreateIntegrationMessage' => [ 'type' => 'structure', 'required' => [ 'SourceArn', 'TargetArn', 'IntegrationName', ], 'members' => [ 'SourceArn' => [ 'shape' => 'SourceArn', ], 'TargetArn' => [ 'shape' => 'Arn', ], 'IntegrationName' => [ 'shape' => 'IntegrationName', ], 'KMSKeyId' => [ 'shape' => 'String', ], 'AdditionalEncryptionContext' => [ 'shape' => 'EncryptionContextMap', ], 'Tags' => [ 'shape' => 'TagList', ], 'DataFilter' => [ 'shape' => 'DataFilter', ], 'Description' => [ 'shape' => 'IntegrationDescription', ], ], ], 'CreateOptionGroupMessage' => [ 'type' => 'structure', 'required' => [ 'OptionGroupName', 'EngineName', 'MajorEngineVersion', 'OptionGroupDescription', ], 'members' => [ 'OptionGroupName' => [ 'shape' => 'String', ], 'EngineName' => [ 'shape' => 'String', ], 'MajorEngineVersion' => [ 'shape' => 'String', ], 'OptionGroupDescription' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateOptionGroupResult' => [ 'type' => 'structure', 'members' => [ 'OptionGroup' => [ 'shape' => 'OptionGroup', ], ], ], 'CreateTenantDatabaseMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', 'TenantDBName', 'MasterUsername', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'TenantDBName' => [ 'shape' => 'String', ], 'MasterUsername' => [ 'shape' => 'String', ], 'MasterUserPassword' => [ 'shape' => 'SensitiveString', ], 'CharacterSetName' => [ 'shape' => 'String', ], 'NcharCharacterSetName' => [ 'shape' => 'String', ], 'ManageMasterUserPassword' => [ 'shape' => 'BooleanOptional', ], 'MasterUserSecretKmsKeyId' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateTenantDatabaseResult' => [ 'type' => 'structure', 'members' => [ 'TenantDatabase' => [ 'shape' => 'TenantDatabase', ], ], ], 'CustomAvailabilityZoneNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CustomAvailabilityZoneNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'CustomDBEngineVersionAMI' => [ 'type' => 'structure', 'members' => [ 'ImageId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'CustomDBEngineVersionAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CustomDBEngineVersionAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CustomDBEngineVersionManifest' => [ 'type' => 'string', 'max' => 51000, 'min' => 1, 'pattern' => '[\\s\\S]*', ], 'CustomDBEngineVersionNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CustomDBEngineVersionNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'CustomDBEngineVersionQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CustomDBEngineVersionQuotaExceededFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CustomEngineName' => [ 'type' => 'string', 'max' => 35, 'min' => 1, 'pattern' => '^[A-Za-z0-9-]{1,35}$', ], 'CustomEngineVersion' => [ 'type' => 'string', 'max' => 60, 'min' => 1, 'pattern' => '^[a-z0-9_.-]{1,60}$', ], 'CustomEngineVersionStatus' => [ 'type' => 'string', 'enum' => [ 'available', 'inactive', 'inactive-except-restore', ], ], 'DBCluster' => [ 'type' => 'structure', 'members' => [ 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'CharacterSetName' => [ 'shape' => 'String', ], 'DatabaseName' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterParameterGroup' => [ 'shape' => 'String', ], 'DBSubnetGroup' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'AutomaticRestartTime' => [ 'shape' => 'TStamp', ], 'PercentProgress' => [ 'shape' => 'String', ], 'EarliestRestorableTime' => [ 'shape' => 'TStamp', ], 'Endpoint' => [ 'shape' => 'String', ], 'ReaderEndpoint' => [ 'shape' => 'String', ], 'CustomEndpoints' => [ 'shape' => 'StringList', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'LatestRestorableTime' => [ 'shape' => 'TStamp', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'MasterUsername' => [ 'shape' => 'String', ], 'DBClusterOptionGroupMemberships' => [ 'shape' => 'DBClusterOptionGroupMemberships', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'ReplicationSourceIdentifier' => [ 'shape' => 'String', ], 'ReadReplicaIdentifiers' => [ 'shape' => 'ReadReplicaIdentifierList', ], 'StatusInfos' => [ 'shape' => 'DBClusterStatusInfoList', ], 'DBClusterMembers' => [ 'shape' => 'DBClusterMemberList', ], 'VpcSecurityGroups' => [ 'shape' => 'VpcSecurityGroupMembershipList', ], 'HostedZoneId' => [ 'shape' => 'String', ], 'StorageEncrypted' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'DbClusterResourceId' => [ 'shape' => 'String', ], 'DBClusterArn' => [ 'shape' => 'String', ], 'AssociatedRoles' => [ 'shape' => 'DBClusterRoles', ], 'IAMDatabaseAuthenticationEnabled' => [ 'shape' => 'BooleanOptional', ], 'CloneGroupId' => [ 'shape' => 'String', ], 'ClusterCreateTime' => [ 'shape' => 'TStamp', ], 'EarliestBacktrackTime' => [ 'shape' => 'TStamp', ], 'BacktrackWindow' => [ 'shape' => 'LongOptional', ], 'BacktrackConsumedChangeRecords' => [ 'shape' => 'LongOptional', ], 'EnabledCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'Capacity' => [ 'shape' => 'IntegerOptional', ], 'EngineMode' => [ 'shape' => 'String', ], 'ScalingConfigurationInfo' => [ 'shape' => 'ScalingConfigurationInfo', ], 'RdsCustomClusterConfiguration' => [ 'shape' => 'RdsCustomClusterConfiguration', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'HttpEndpointEnabled' => [ 'shape' => 'BooleanOptional', ], 'ActivityStreamMode' => [ 'shape' => 'ActivityStreamMode', ], 'ActivityStreamStatus' => [ 'shape' => 'ActivityStreamStatus', ], 'ActivityStreamKmsKeyId' => [ 'shape' => 'String', ], 'ActivityStreamKinesisStreamName' => [ 'shape' => 'String', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'CrossAccountClone' => [ 'shape' => 'BooleanOptional', ], 'DomainMemberships' => [ 'shape' => 'DomainMembershipList', ], 'TagList' => [ 'shape' => 'TagList', ], 'GlobalWriteForwardingStatus' => [ 'shape' => 'WriteForwardingStatus', ], 'GlobalWriteForwardingRequested' => [ 'shape' => 'BooleanOptional', ], 'PendingModifiedValues' => [ 'shape' => 'ClusterPendingModifiedValues', ], 'DBClusterInstanceClass' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'MonitoringInterval' => [ 'shape' => 'IntegerOptional', ], 'MonitoringRoleArn' => [ 'shape' => 'String', ], 'DatabaseInsightsMode' => [ 'shape' => 'DatabaseInsightsMode', ], 'PerformanceInsightsEnabled' => [ 'shape' => 'BooleanOptional', ], 'PerformanceInsightsKMSKeyId' => [ 'shape' => 'String', ], 'PerformanceInsightsRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'ServerlessV2ScalingConfiguration' => [ 'shape' => 'ServerlessV2ScalingConfigurationInfo', ], 'NetworkType' => [ 'shape' => 'String', ], 'DBSystemId' => [ 'shape' => 'String', ], 'MasterUserSecret' => [ 'shape' => 'MasterUserSecret', ], 'IOOptimizedNextAllowedModificationTime' => [ 'shape' => 'TStamp', ], 'LocalWriteForwardingStatus' => [ 'shape' => 'LocalWriteForwardingStatus', ], 'AwsBackupRecoveryPointArn' => [ 'shape' => 'String', ], 'LimitlessDatabase' => [ 'shape' => 'LimitlessDatabase', ], 'StorageThroughput' => [ 'shape' => 'IntegerOptional', ], 'ClusterScalabilityType' => [ 'shape' => 'ClusterScalabilityType', ], 'CertificateDetails' => [ 'shape' => 'CertificateDetails', ], 'EngineLifecycleSupport' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'DBClusterAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterAutomatedBackup' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'DBClusterAutomatedBackupsArn' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'RestoreWindow' => [ 'shape' => 'RestoreWindow', ], 'MasterUsername' => [ 'shape' => 'String', ], 'DbClusterResourceId' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], 'LicenseModel' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'IAMDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], 'ClusterCreateTime' => [ 'shape' => 'TStamp', ], 'StorageEncrypted' => [ 'shape' => 'Boolean', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'EngineVersion' => [ 'shape' => 'String', ], 'DBClusterArn' => [ 'shape' => 'String', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'EngineMode' => [ 'shape' => 'String', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'Port' => [ 'shape' => 'Integer', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'AwsBackupRecoveryPointArn' => [ 'shape' => 'String', ], 'StorageThroughput' => [ 'shape' => 'IntegerOptional', ], ], 'wrapper' => true, ], 'DBClusterAutomatedBackupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterAutomatedBackup', 'locationName' => 'DBClusterAutomatedBackup', ], ], 'DBClusterAutomatedBackupMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBClusterAutomatedBackups' => [ 'shape' => 'DBClusterAutomatedBackupList', ], ], ], 'DBClusterAutomatedBackupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterAutomatedBackupNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterAutomatedBackupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterAutomatedBackupQuotaExceededFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterBacktrack' => [ 'type' => 'structure', 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'BacktrackIdentifier' => [ 'shape' => 'String', ], 'BacktrackTo' => [ 'shape' => 'TStamp', ], 'BacktrackedFrom' => [ 'shape' => 'TStamp', ], 'BacktrackRequestCreationTime' => [ 'shape' => 'TStamp', ], 'Status' => [ 'shape' => 'String', ], ], ], 'DBClusterBacktrackList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterBacktrack', 'locationName' => 'DBClusterBacktrack', ], ], 'DBClusterBacktrackMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBClusterBacktracks' => [ 'shape' => 'DBClusterBacktrackList', ], ], ], 'DBClusterBacktrackNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterBacktrackNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterCapacityInfo' => [ 'type' => 'structure', 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'PendingCapacity' => [ 'shape' => 'IntegerOptional', ], 'CurrentCapacity' => [ 'shape' => 'IntegerOptional', ], 'SecondsBeforeTimeout' => [ 'shape' => 'IntegerOptional', ], 'TimeoutAction' => [ 'shape' => 'String', ], ], ], 'DBClusterEndpoint' => [ 'type' => 'structure', 'members' => [ 'DBClusterEndpointIdentifier' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterEndpointResourceIdentifier' => [ 'shape' => 'String', ], 'Endpoint' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'String', ], 'CustomEndpointType' => [ 'shape' => 'String', ], 'StaticMembers' => [ 'shape' => 'StringList', ], 'ExcludedMembers' => [ 'shape' => 'StringList', ], 'DBClusterEndpointArn' => [ 'shape' => 'String', ], ], ], 'DBClusterEndpointAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterEndpointAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterEndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterEndpoint', 'locationName' => 'DBClusterEndpointList', ], ], 'DBClusterEndpointMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBClusterEndpoints' => [ 'shape' => 'DBClusterEndpointList', ], ], ], 'DBClusterEndpointNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterEndpointNotFoundFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterEndpointQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterEndpointQuotaExceededFault', 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterIdentifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[A-Za-z][0-9A-Za-z-:._]*', ], 'DBClusterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBCluster', 'locationName' => 'DBCluster', ], ], 'DBClusterMember' => [ 'type' => 'structure', 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'IsClusterWriter' => [ 'shape' => 'Boolean', ], 'DBClusterParameterGroupStatus' => [ 'shape' => 'String', ], 'PromotionTier' => [ 'shape' => 'IntegerOptional', ], ], 'wrapper' => true, ], 'DBClusterMemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterMember', 'locationName' => 'DBClusterMember', ], ], 'DBClusterMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBClusters' => [ 'shape' => 'DBClusterList', ], ], ], 'DBClusterNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterOptionGroupMemberships' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterOptionGroupStatus', 'locationName' => 'DBClusterOptionGroup', ], ], 'DBClusterOptionGroupStatus' => [ 'type' => 'structure', 'members' => [ 'DBClusterOptionGroupName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'DBClusterParameterGroup' => [ 'type' => 'structure', 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'DBClusterParameterGroupArn' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'DBClusterParameterGroupDetails' => [ 'type' => 'structure', 'members' => [ 'Parameters' => [ 'shape' => 'ParametersList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DBClusterParameterGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterParameterGroup', 'locationName' => 'DBClusterParameterGroup', ], ], 'DBClusterParameterGroupNameMessage' => [ 'type' => 'structure', 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], ], ], 'DBClusterParameterGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterParameterGroupNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterParameterGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBClusterParameterGroups' => [ 'shape' => 'DBClusterParameterGroupList', ], ], ], 'DBClusterQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterQuotaExceededFault', 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterRole' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'FeatureName' => [ 'shape' => 'String', ], ], ], 'DBClusterRoleAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterRoleAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterRoleNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterRoleNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterRoleQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterRoleQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterRoles' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterRole', 'locationName' => 'DBClusterRole', ], ], 'DBClusterSnapshot' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'SnapshotCreateTime' => [ 'shape' => 'TStamp', ], 'Engine' => [ 'shape' => 'String', ], 'EngineMode' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'Status' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'Integer', ], 'VpcId' => [ 'shape' => 'String', ], 'ClusterCreateTime' => [ 'shape' => 'TStamp', ], 'MasterUsername' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'LicenseModel' => [ 'shape' => 'String', ], 'SnapshotType' => [ 'shape' => 'String', ], 'PercentProgress' => [ 'shape' => 'Integer', ], 'StorageEncrypted' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'DBClusterSnapshotArn' => [ 'shape' => 'String', ], 'SourceDBClusterSnapshotArn' => [ 'shape' => 'String', ], 'IAMDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], 'TagList' => [ 'shape' => 'TagList', ], 'DBSystemId' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'DbClusterResourceId' => [ 'shape' => 'String', ], 'StorageThroughput' => [ 'shape' => 'IntegerOptional', ], ], 'wrapper' => true, ], 'DBClusterSnapshotAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterSnapshotAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterSnapshotAttribute' => [ 'type' => 'structure', 'members' => [ 'AttributeName' => [ 'shape' => 'String', ], 'AttributeValues' => [ 'shape' => 'AttributeValueList', ], ], ], 'DBClusterSnapshotAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterSnapshotAttribute', 'locationName' => 'DBClusterSnapshotAttribute', ], ], 'DBClusterSnapshotAttributesResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'DBClusterSnapshotAttributes' => [ 'shape' => 'DBClusterSnapshotAttributeList', ], ], 'wrapper' => true, ], 'DBClusterSnapshotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterSnapshot', 'locationName' => 'DBClusterSnapshot', ], ], 'DBClusterSnapshotMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBClusterSnapshots' => [ 'shape' => 'DBClusterSnapshotList', ], ], ], 'DBClusterSnapshotNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterSnapshotNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterStatusInfo' => [ 'type' => 'structure', 'members' => [ 'StatusType' => [ 'shape' => 'String', ], 'Normal' => [ 'shape' => 'Boolean', ], 'Status' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], ], 'DBClusterStatusInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterStatusInfo', 'locationName' => 'DBClusterStatusInfo', ], ], 'DBEngineVersion' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'DBEngineDescription' => [ 'shape' => 'String', ], 'DBEngineVersionDescription' => [ 'shape' => 'String', ], 'DefaultCharacterSet' => [ 'shape' => 'CharacterSet', ], 'Image' => [ 'shape' => 'CustomDBEngineVersionAMI', ], 'DBEngineMediaType' => [ 'shape' => 'String', ], 'SupportedCharacterSets' => [ 'shape' => 'SupportedCharacterSetsList', ], 'SupportedNcharCharacterSets' => [ 'shape' => 'SupportedCharacterSetsList', ], 'ValidUpgradeTarget' => [ 'shape' => 'ValidUpgradeTargetList', ], 'SupportedTimezones' => [ 'shape' => 'SupportedTimezonesList', ], 'ExportableLogTypes' => [ 'shape' => 'LogTypeList', ], 'SupportsLogExportsToCloudwatchLogs' => [ 'shape' => 'Boolean', ], 'SupportsReadReplica' => [ 'shape' => 'Boolean', ], 'SupportedEngineModes' => [ 'shape' => 'EngineModeList', ], 'SupportedFeatureNames' => [ 'shape' => 'FeatureNameList', ], 'Status' => [ 'shape' => 'String', ], 'SupportsParallelQuery' => [ 'shape' => 'Boolean', ], 'SupportsGlobalDatabases' => [ 'shape' => 'Boolean', ], 'MajorEngineVersion' => [ 'shape' => 'String', ], 'DatabaseInstallationFilesS3BucketName' => [ 'shape' => 'String', ], 'DatabaseInstallationFilesS3Prefix' => [ 'shape' => 'String', ], 'DBEngineVersionArn' => [ 'shape' => 'String', ], 'KMSKeyId' => [ 'shape' => 'String', ], 'CreateTime' => [ 'shape' => 'TStamp', ], 'TagList' => [ 'shape' => 'TagList', ], 'SupportsBabelfish' => [ 'shape' => 'Boolean', ], 'CustomDBEngineVersionManifest' => [ 'shape' => 'CustomDBEngineVersionManifest', ], 'SupportsLimitlessDatabase' => [ 'shape' => 'Boolean', ], 'SupportsCertificateRotationWithoutRestart' => [ 'shape' => 'BooleanOptional', ], 'SupportedCACertificateIdentifiers' => [ 'shape' => 'CACertificateIdentifiersList', ], 'SupportsLocalWriteForwarding' => [ 'shape' => 'BooleanOptional', ], 'SupportsIntegrations' => [ 'shape' => 'Boolean', ], 'ServerlessV2FeaturesSupport' => [ 'shape' => 'ServerlessV2FeaturesSupport', ], ], ], 'DBEngineVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBEngineVersion', 'locationName' => 'DBEngineVersion', ], ], 'DBEngineVersionMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBEngineVersions' => [ 'shape' => 'DBEngineVersionList', ], ], ], 'DBInstance' => [ 'type' => 'structure', 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'DBInstanceStatus' => [ 'shape' => 'String', ], 'AutomaticRestartTime' => [ 'shape' => 'TStamp', ], 'MasterUsername' => [ 'shape' => 'String', ], 'DBName' => [ 'shape' => 'String', ], 'Endpoint' => [ 'shape' => 'Endpoint', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'InstanceCreateTime' => [ 'shape' => 'TStamp', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], 'BackupRetentionPeriod' => [ 'shape' => 'Integer', ], 'DBSecurityGroups' => [ 'shape' => 'DBSecurityGroupMembershipList', ], 'VpcSecurityGroups' => [ 'shape' => 'VpcSecurityGroupMembershipList', ], 'DBParameterGroups' => [ 'shape' => 'DBParameterGroupStatusList', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'DBSubnetGroup' => [ 'shape' => 'DBSubnetGroup', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'PendingModifiedValues' => [ 'shape' => 'PendingModifiedValues', ], 'LatestRestorableTime' => [ 'shape' => 'TStamp', ], 'MultiAZ' => [ 'shape' => 'Boolean', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'ReadReplicaSourceDBInstanceIdentifier' => [ 'shape' => 'String', ], 'ReadReplicaDBInstanceIdentifiers' => [ 'shape' => 'ReadReplicaDBInstanceIdentifierList', ], 'ReadReplicaDBClusterIdentifiers' => [ 'shape' => 'ReadReplicaDBClusterIdentifierList', ], 'ReplicaMode' => [ 'shape' => 'ReplicaMode', ], 'LicenseModel' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'OptionGroupMemberships' => [ 'shape' => 'OptionGroupMembershipList', ], 'CharacterSetName' => [ 'shape' => 'String', ], 'NcharCharacterSetName' => [ 'shape' => 'String', ], 'SecondaryAvailabilityZone' => [ 'shape' => 'String', ], 'PubliclyAccessible' => [ 'shape' => 'Boolean', ], 'StatusInfos' => [ 'shape' => 'DBInstanceStatusInfoList', ], 'StorageType' => [ 'shape' => 'String', ], 'TdeCredentialArn' => [ 'shape' => 'String', ], 'DbInstancePort' => [ 'shape' => 'Integer', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'StorageEncrypted' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'DbiResourceId' => [ 'shape' => 'String', ], 'CACertificateIdentifier' => [ 'shape' => 'String', ], 'DomainMemberships' => [ 'shape' => 'DomainMembershipList', ], 'CopyTagsToSnapshot' => [ 'shape' => 'Boolean', ], 'MonitoringInterval' => [ 'shape' => 'IntegerOptional', ], 'EnhancedMonitoringResourceArn' => [ 'shape' => 'String', ], 'MonitoringRoleArn' => [ 'shape' => 'String', ], 'PromotionTier' => [ 'shape' => 'IntegerOptional', ], 'DBInstanceArn' => [ 'shape' => 'String', ], 'Timezone' => [ 'shape' => 'String', ], 'IAMDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], 'DatabaseInsightsMode' => [ 'shape' => 'DatabaseInsightsMode', ], 'PerformanceInsightsEnabled' => [ 'shape' => 'BooleanOptional', ], 'PerformanceInsightsKMSKeyId' => [ 'shape' => 'String', ], 'PerformanceInsightsRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'EnabledCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'ProcessorFeatures' => [ 'shape' => 'ProcessorFeatureList', ], 'DeletionProtection' => [ 'shape' => 'Boolean', ], 'AssociatedRoles' => [ 'shape' => 'DBInstanceRoles', ], 'ListenerEndpoint' => [ 'shape' => 'Endpoint', ], 'MaxAllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'TagList' => [ 'shape' => 'TagList', ], 'DBInstanceAutomatedBackupsReplications' => [ 'shape' => 'DBInstanceAutomatedBackupsReplicationList', ], 'CustomerOwnedIpEnabled' => [ 'shape' => 'BooleanOptional', ], 'AwsBackupRecoveryPointArn' => [ 'shape' => 'String', ], 'ActivityStreamStatus' => [ 'shape' => 'ActivityStreamStatus', ], 'ActivityStreamKmsKeyId' => [ 'shape' => 'String', ], 'ActivityStreamKinesisStreamName' => [ 'shape' => 'String', ], 'ActivityStreamMode' => [ 'shape' => 'ActivityStreamMode', ], 'ActivityStreamEngineNativeAuditFieldsIncluded' => [ 'shape' => 'BooleanOptional', ], 'AutomationMode' => [ 'shape' => 'AutomationMode', ], 'ResumeFullAutomationModeTime' => [ 'shape' => 'TStamp', ], 'CustomIamInstanceProfile' => [ 'shape' => 'String', ], 'BackupTarget' => [ 'shape' => 'String', ], 'NetworkType' => [ 'shape' => 'String', ], 'ActivityStreamPolicyStatus' => [ 'shape' => 'ActivityStreamPolicyStatus', ], 'StorageThroughput' => [ 'shape' => 'IntegerOptional', ], 'DBSystemId' => [ 'shape' => 'String', ], 'MasterUserSecret' => [ 'shape' => 'MasterUserSecret', ], 'CertificateDetails' => [ 'shape' => 'CertificateDetails', ], 'ReadReplicaSourceDBClusterIdentifier' => [ 'shape' => 'String', ], 'PercentProgress' => [ 'shape' => 'String', ], 'DedicatedLogVolume' => [ 'shape' => 'Boolean', ], 'IsStorageConfigUpgradeAvailable' => [ 'shape' => 'BooleanOptional', ], 'MultiTenant' => [ 'shape' => 'BooleanOptional', ], 'EngineLifecycleSupport' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'DBInstanceAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBInstanceAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBInstanceAutomatedBackup' => [ 'type' => 'structure', 'members' => [ 'DBInstanceArn' => [ 'shape' => 'String', ], 'DbiResourceId' => [ 'shape' => 'String', ], 'Region' => [ 'shape' => 'String', ], 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'RestoreWindow' => [ 'shape' => 'RestoreWindow', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'Status' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'Integer', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'InstanceCreateTime' => [ 'shape' => 'TStamp', ], 'MasterUsername' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'LicenseModel' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'TdeCredentialArn' => [ 'shape' => 'String', ], 'Encrypted' => [ 'shape' => 'Boolean', ], 'StorageType' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'Timezone' => [ 'shape' => 'String', ], 'IAMDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'DBInstanceAutomatedBackupsArn' => [ 'shape' => 'String', ], 'DBInstanceAutomatedBackupsReplications' => [ 'shape' => 'DBInstanceAutomatedBackupsReplicationList', ], 'BackupTarget' => [ 'shape' => 'String', ], 'StorageThroughput' => [ 'shape' => 'IntegerOptional', ], 'AwsBackupRecoveryPointArn' => [ 'shape' => 'String', ], 'DedicatedLogVolume' => [ 'shape' => 'BooleanOptional', ], 'MultiTenant' => [ 'shape' => 'BooleanOptional', ], ], 'wrapper' => true, ], 'DBInstanceAutomatedBackupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBInstanceAutomatedBackup', 'locationName' => 'DBInstanceAutomatedBackup', ], ], 'DBInstanceAutomatedBackupMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBInstanceAutomatedBackups' => [ 'shape' => 'DBInstanceAutomatedBackupList', ], ], ], 'DBInstanceAutomatedBackupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBInstanceAutomatedBackupNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBInstanceAutomatedBackupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBInstanceAutomatedBackupQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBInstanceAutomatedBackupsReplication' => [ 'type' => 'structure', 'members' => [ 'DBInstanceAutomatedBackupsArn' => [ 'shape' => 'String', ], ], ], 'DBInstanceAutomatedBackupsReplicationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBInstanceAutomatedBackupsReplication', 'locationName' => 'DBInstanceAutomatedBackupsReplication', ], ], 'DBInstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBInstance', 'locationName' => 'DBInstance', ], ], 'DBInstanceMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBInstances' => [ 'shape' => 'DBInstanceList', ], ], ], 'DBInstanceNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBInstanceNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBInstanceNotReadyFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBInstanceNotReady', 'httpStatusCode' => 503, ], 'exception' => true, ], 'DBInstanceRole' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'String', ], 'FeatureName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'DBInstanceRoleAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBInstanceRoleAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBInstanceRoleNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBInstanceRoleNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBInstanceRoleQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBInstanceRoleQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBInstanceRoles' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBInstanceRole', 'locationName' => 'DBInstanceRole', ], ], 'DBInstanceStatusInfo' => [ 'type' => 'structure', 'members' => [ 'StatusType' => [ 'shape' => 'String', ], 'Normal' => [ 'shape' => 'Boolean', ], 'Status' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], ], 'DBInstanceStatusInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBInstanceStatusInfo', 'locationName' => 'DBInstanceStatusInfo', ], ], 'DBLogFileNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBLogFileNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBMajorEngineVersion' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'MajorEngineVersion' => [ 'shape' => 'String', ], 'SupportedEngineLifecycles' => [ 'shape' => 'SupportedEngineLifecycleList', ], ], ], 'DBMajorEngineVersionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBMajorEngineVersion', 'locationName' => 'DBMajorEngineVersion', ], ], 'DBParameterGroup' => [ 'type' => 'structure', 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'DBParameterGroupArn' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'DBParameterGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBParameterGroupAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBParameterGroupDetails' => [ 'type' => 'structure', 'members' => [ 'Parameters' => [ 'shape' => 'ParametersList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DBParameterGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBParameterGroup', 'locationName' => 'DBParameterGroup', ], ], 'DBParameterGroupNameMessage' => [ 'type' => 'structure', 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], ], ], 'DBParameterGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBParameterGroupNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBParameterGroupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBParameterGroupQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBParameterGroupStatus' => [ 'type' => 'structure', 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], 'ParameterApplyStatus' => [ 'shape' => 'String', ], ], ], 'DBParameterGroupStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBParameterGroupStatus', 'locationName' => 'DBParameterGroup', ], ], 'DBParameterGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBParameterGroups' => [ 'shape' => 'DBParameterGroupList', ], ], ], 'DBProxy' => [ 'type' => 'structure', 'members' => [ 'DBProxyName' => [ 'shape' => 'String', ], 'DBProxyArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'DBProxyStatus', ], 'EngineFamily' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'StringList', ], 'VpcSubnetIds' => [ 'shape' => 'StringList', ], 'Auth' => [ 'shape' => 'UserAuthConfigInfoList', ], 'RoleArn' => [ 'shape' => 'String', ], 'Endpoint' => [ 'shape' => 'String', ], 'RequireTLS' => [ 'shape' => 'Boolean', ], 'IdleClientTimeout' => [ 'shape' => 'Integer', ], 'DebugLogging' => [ 'shape' => 'Boolean', ], 'CreatedDate' => [ 'shape' => 'TStamp', ], 'UpdatedDate' => [ 'shape' => 'TStamp', ], ], ], 'DBProxyAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBProxyAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBProxyEndpoint' => [ 'type' => 'structure', 'members' => [ 'DBProxyEndpointName' => [ 'shape' => 'String', ], 'DBProxyEndpointArn' => [ 'shape' => 'String', ], 'DBProxyName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'DBProxyEndpointStatus', ], 'VpcId' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'StringList', ], 'VpcSubnetIds' => [ 'shape' => 'StringList', ], 'Endpoint' => [ 'shape' => 'String', ], 'CreatedDate' => [ 'shape' => 'TStamp', ], 'TargetRole' => [ 'shape' => 'DBProxyEndpointTargetRole', ], 'IsDefault' => [ 'shape' => 'Boolean', ], ], ], 'DBProxyEndpointAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBProxyEndpointAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBProxyEndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBProxyEndpoint', ], ], 'DBProxyEndpointName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[a-zA-Z][a-zA-Z0-9]*(-[a-zA-Z0-9]+)*', ], 'DBProxyEndpointNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBProxyEndpointNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBProxyEndpointQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBProxyEndpointQuotaExceededFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBProxyEndpointStatus' => [ 'type' => 'string', 'enum' => [ 'available', 'modifying', 'incompatible-network', 'insufficient-resource-limits', 'creating', 'deleting', ], ], 'DBProxyEndpointTargetRole' => [ 'type' => 'string', 'enum' => [ 'READ_WRITE', 'READ_ONLY', ], ], 'DBProxyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBProxy', ], ], 'DBProxyName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[a-zA-Z][a-zA-Z0-9]*(-[a-zA-Z0-9]+)*', ], 'DBProxyNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBProxyNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBProxyQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBProxyQuotaExceededFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBProxyStatus' => [ 'type' => 'string', 'enum' => [ 'available', 'modifying', 'incompatible-network', 'insufficient-resource-limits', 'creating', 'deleting', 'suspended', 'suspending', 'reactivating', ], ], 'DBProxyTarget' => [ 'type' => 'structure', 'members' => [ 'TargetArn' => [ 'shape' => 'String', ], 'Endpoint' => [ 'shape' => 'String', ], 'TrackedClusterId' => [ 'shape' => 'String', ], 'RdsResourceId' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'Integer', ], 'Type' => [ 'shape' => 'TargetType', ], 'Role' => [ 'shape' => 'TargetRole', ], 'TargetHealth' => [ 'shape' => 'TargetHealth', ], ], ], 'DBProxyTargetAlreadyRegisteredFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBProxyTargetAlreadyRegisteredFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBProxyTargetGroup' => [ 'type' => 'structure', 'members' => [ 'DBProxyName' => [ 'shape' => 'String', ], 'TargetGroupName' => [ 'shape' => 'String', ], 'TargetGroupArn' => [ 'shape' => 'String', ], 'IsDefault' => [ 'shape' => 'Boolean', ], 'Status' => [ 'shape' => 'String', ], 'ConnectionPoolConfig' => [ 'shape' => 'ConnectionPoolConfigurationInfo', ], 'CreatedDate' => [ 'shape' => 'TStamp', ], 'UpdatedDate' => [ 'shape' => 'TStamp', ], ], ], 'DBProxyTargetGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBProxyTargetGroupNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBProxyTargetNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBProxyTargetNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBRecommendation' => [ 'type' => 'structure', 'members' => [ 'RecommendationId' => [ 'shape' => 'String', ], 'TypeId' => [ 'shape' => 'String', ], 'Severity' => [ 'shape' => 'String', ], 'ResourceArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'CreatedTime' => [ 'shape' => 'TStamp', ], 'UpdatedTime' => [ 'shape' => 'TStamp', ], 'Detection' => [ 'shape' => 'String', ], 'Recommendation' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'String', ], 'RecommendedActions' => [ 'shape' => 'RecommendedActionList', ], 'Category' => [ 'shape' => 'String', ], 'Source' => [ 'shape' => 'String', ], 'TypeDetection' => [ 'shape' => 'String', ], 'TypeRecommendation' => [ 'shape' => 'String', ], 'Impact' => [ 'shape' => 'String', ], 'AdditionalInfo' => [ 'shape' => 'String', ], 'Links' => [ 'shape' => 'DocLinkList', ], 'IssueDetails' => [ 'shape' => 'IssueDetails', ], ], ], 'DBRecommendationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBRecommendation', ], ], 'DBRecommendationMessage' => [ 'type' => 'structure', 'members' => [ 'DBRecommendation' => [ 'shape' => 'DBRecommendation', ], ], ], 'DBRecommendationsMessage' => [ 'type' => 'structure', 'members' => [ 'DBRecommendations' => [ 'shape' => 'DBRecommendationList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DBSecurityGroup' => [ 'type' => 'structure', 'members' => [ 'OwnerId' => [ 'shape' => 'String', ], 'DBSecurityGroupName' => [ 'shape' => 'String', ], 'DBSecurityGroupDescription' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'EC2SecurityGroups' => [ 'shape' => 'EC2SecurityGroupList', ], 'IPRanges' => [ 'shape' => 'IPRangeList', ], 'DBSecurityGroupArn' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'DBSecurityGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSecurityGroupAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBSecurityGroupMembership' => [ 'type' => 'structure', 'members' => [ 'DBSecurityGroupName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'DBSecurityGroupMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBSecurityGroupMembership', 'locationName' => 'DBSecurityGroup', ], ], 'DBSecurityGroupMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBSecurityGroups' => [ 'shape' => 'DBSecurityGroups', ], ], ], 'DBSecurityGroupNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'DBSecurityGroupName', ], ], 'DBSecurityGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSecurityGroupNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBSecurityGroupNotSupportedFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSecurityGroupNotSupported', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBSecurityGroupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'QuotaExceeded.DBSecurityGroup', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBSecurityGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBSecurityGroup', 'locationName' => 'DBSecurityGroup', ], ], 'DBShardGroup' => [ 'type' => 'structure', 'members' => [ 'DBShardGroupResourceId' => [ 'shape' => 'String', ], 'DBShardGroupIdentifier' => [ 'shape' => 'DBShardGroupIdentifier', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'MaxACU' => [ 'shape' => 'DoubleOptional', ], 'MinACU' => [ 'shape' => 'DoubleOptional', ], 'ComputeRedundancy' => [ 'shape' => 'IntegerOptional', ], 'Status' => [ 'shape' => 'String', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'Endpoint' => [ 'shape' => 'String', ], 'DBShardGroupArn' => [ 'shape' => 'String', ], 'TagList' => [ 'shape' => 'TagList', ], ], ], 'DBShardGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBShardGroupAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBShardGroupIdentifier' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[a-zA-Z][a-zA-Z0-9]*(-[a-zA-Z0-9]+)*', ], 'DBShardGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBShardGroupNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBShardGroupsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBShardGroup', 'locationName' => 'DBShardGroup', ], ], 'DBSnapshot' => [ 'type' => 'structure', 'members' => [ 'DBSnapshotIdentifier' => [ 'shape' => 'String', ], 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'SnapshotCreateTime' => [ 'shape' => 'TStamp', ], 'Engine' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'Status' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'Integer', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'InstanceCreateTime' => [ 'shape' => 'TStamp', ], 'MasterUsername' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'LicenseModel' => [ 'shape' => 'String', ], 'SnapshotType' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'PercentProgress' => [ 'shape' => 'Integer', ], 'SourceRegion' => [ 'shape' => 'String', ], 'SourceDBSnapshotIdentifier' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'TdeCredentialArn' => [ 'shape' => 'String', ], 'Encrypted' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'DBSnapshotArn' => [ 'shape' => 'String', ], 'Timezone' => [ 'shape' => 'String', ], 'IAMDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], 'ProcessorFeatures' => [ 'shape' => 'ProcessorFeatureList', ], 'DbiResourceId' => [ 'shape' => 'String', ], 'TagList' => [ 'shape' => 'TagList', ], 'OriginalSnapshotCreateTime' => [ 'shape' => 'TStamp', ], 'SnapshotDatabaseTime' => [ 'shape' => 'TStamp', ], 'SnapshotTarget' => [ 'shape' => 'String', ], 'StorageThroughput' => [ 'shape' => 'IntegerOptional', ], 'DBSystemId' => [ 'shape' => 'String', ], 'DedicatedLogVolume' => [ 'shape' => 'Boolean', ], 'MultiTenant' => [ 'shape' => 'BooleanOptional', ], ], 'wrapper' => true, ], 'DBSnapshotAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSnapshotAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBSnapshotAttribute' => [ 'type' => 'structure', 'members' => [ 'AttributeName' => [ 'shape' => 'String', ], 'AttributeValues' => [ 'shape' => 'AttributeValueList', ], ], 'wrapper' => true, ], 'DBSnapshotAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBSnapshotAttribute', 'locationName' => 'DBSnapshotAttribute', ], ], 'DBSnapshotAttributesResult' => [ 'type' => 'structure', 'members' => [ 'DBSnapshotIdentifier' => [ 'shape' => 'String', ], 'DBSnapshotAttributes' => [ 'shape' => 'DBSnapshotAttributeList', ], ], 'wrapper' => true, ], 'DBSnapshotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBSnapshot', 'locationName' => 'DBSnapshot', ], ], 'DBSnapshotMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBSnapshots' => [ 'shape' => 'DBSnapshotList', ], ], ], 'DBSnapshotNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSnapshotNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBSnapshotTenantDatabase' => [ 'type' => 'structure', 'members' => [ 'DBSnapshotIdentifier' => [ 'shape' => 'String', ], 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'DbiResourceId' => [ 'shape' => 'String', ], 'EngineName' => [ 'shape' => 'String', ], 'SnapshotType' => [ 'shape' => 'String', ], 'TenantDatabaseCreateTime' => [ 'shape' => 'TStamp', ], 'TenantDBName' => [ 'shape' => 'String', ], 'MasterUsername' => [ 'shape' => 'String', ], 'TenantDatabaseResourceId' => [ 'shape' => 'String', ], 'CharacterSetName' => [ 'shape' => 'String', ], 'DBSnapshotTenantDatabaseARN' => [ 'shape' => 'String', ], 'NcharCharacterSetName' => [ 'shape' => 'String', ], 'TagList' => [ 'shape' => 'TagList', ], ], 'wrapper' => true, ], 'DBSnapshotTenantDatabaseNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSnapshotTenantDatabaseNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBSnapshotTenantDatabasesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBSnapshotTenantDatabase', 'locationName' => 'DBSnapshotTenantDatabase', ], ], 'DBSnapshotTenantDatabasesMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBSnapshotTenantDatabases' => [ 'shape' => 'DBSnapshotTenantDatabasesList', ], ], ], 'DBSubnetGroup' => [ 'type' => 'structure', 'members' => [ 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'DBSubnetGroupDescription' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'SubnetGroupStatus' => [ 'shape' => 'String', ], 'Subnets' => [ 'shape' => 'SubnetList', ], 'DBSubnetGroupArn' => [ 'shape' => 'String', ], 'SupportedNetworkTypes' => [ 'shape' => 'StringList', ], ], 'wrapper' => true, ], 'DBSubnetGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSubnetGroupAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBSubnetGroupDoesNotCoverEnoughAZs' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSubnetGroupDoesNotCoverEnoughAZs', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBSubnetGroupMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBSubnetGroups' => [ 'shape' => 'DBSubnetGroups', ], ], ], 'DBSubnetGroupNotAllowedFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSubnetGroupNotAllowedFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBSubnetGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSubnetGroupNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBSubnetGroupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSubnetGroupQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBSubnetGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBSubnetGroup', 'locationName' => 'DBSubnetGroup', ], ], 'DBSubnetQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSubnetQuotaExceededFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBUpgradeDependencyFailureFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBUpgradeDependencyFailure', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DataFilter' => [ 'type' => 'string', 'max' => 25600, 'min' => 1, 'pattern' => '[a-zA-Z0-9_ "\\\\\\-$,*.:?+\\/]*', ], 'DatabaseArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:[A-Za-z][0-9A-Za-z-:._]*', ], 'DatabaseInsightsMode' => [ 'type' => 'string', 'enum' => [ 'standard', 'advanced', ], ], 'DeleteBlueGreenDeploymentRequest' => [ 'type' => 'structure', 'required' => [ 'BlueGreenDeploymentIdentifier', ], 'members' => [ 'BlueGreenDeploymentIdentifier' => [ 'shape' => 'BlueGreenDeploymentIdentifier', ], 'DeleteTarget' => [ 'shape' => 'BooleanOptional', ], ], ], 'DeleteBlueGreenDeploymentResponse' => [ 'type' => 'structure', 'members' => [ 'BlueGreenDeployment' => [ 'shape' => 'BlueGreenDeployment', ], ], ], 'DeleteCustomDBEngineVersionMessage' => [ 'type' => 'structure', 'required' => [ 'Engine', 'EngineVersion', ], 'members' => [ 'Engine' => [ 'shape' => 'CustomEngineName', ], 'EngineVersion' => [ 'shape' => 'CustomEngineVersion', ], ], ], 'DeleteDBClusterAutomatedBackupMessage' => [ 'type' => 'structure', 'required' => [ 'DbClusterResourceId', ], 'members' => [ 'DbClusterResourceId' => [ 'shape' => 'String', ], ], ], 'DeleteDBClusterAutomatedBackupResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterAutomatedBackup' => [ 'shape' => 'DBClusterAutomatedBackup', ], ], ], 'DeleteDBClusterEndpointMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterEndpointIdentifier', ], 'members' => [ 'DBClusterEndpointIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'SkipFinalSnapshot' => [ 'shape' => 'Boolean', ], 'FinalDBSnapshotIdentifier' => [ 'shape' => 'String', ], 'DeleteAutomatedBackups' => [ 'shape' => 'BooleanOptional', ], ], ], 'DeleteDBClusterParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterParameterGroupName', ], 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], ], ], 'DeleteDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'DeleteDBClusterSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterSnapshotIdentifier', ], 'members' => [ 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteDBClusterSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterSnapshot' => [ 'shape' => 'DBClusterSnapshot', ], ], ], 'DeleteDBInstanceAutomatedBackupMessage' => [ 'type' => 'structure', 'members' => [ 'DbiResourceId' => [ 'shape' => 'String', ], 'DBInstanceAutomatedBackupsArn' => [ 'shape' => 'String', ], ], ], 'DeleteDBInstanceAutomatedBackupResult' => [ 'type' => 'structure', 'members' => [ 'DBInstanceAutomatedBackup' => [ 'shape' => 'DBInstanceAutomatedBackup', ], ], ], 'DeleteDBInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'SkipFinalSnapshot' => [ 'shape' => 'Boolean', ], 'FinalDBSnapshotIdentifier' => [ 'shape' => 'String', ], 'DeleteAutomatedBackups' => [ 'shape' => 'BooleanOptional', ], ], ], 'DeleteDBInstanceResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'DeleteDBParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBParameterGroupName', ], 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], ], ], 'DeleteDBProxyEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'DBProxyEndpointName', ], 'members' => [ 'DBProxyEndpointName' => [ 'shape' => 'DBProxyEndpointName', ], ], ], 'DeleteDBProxyEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'DBProxyEndpoint' => [ 'shape' => 'DBProxyEndpoint', ], ], ], 'DeleteDBProxyRequest' => [ 'type' => 'structure', 'required' => [ 'DBProxyName', ], 'members' => [ 'DBProxyName' => [ 'shape' => 'String', ], ], ], 'DeleteDBProxyResponse' => [ 'type' => 'structure', 'members' => [ 'DBProxy' => [ 'shape' => 'DBProxy', ], ], ], 'DeleteDBSecurityGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBSecurityGroupName', ], 'members' => [ 'DBSecurityGroupName' => [ 'shape' => 'String', ], ], ], 'DeleteDBShardGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBShardGroupIdentifier', ], 'members' => [ 'DBShardGroupIdentifier' => [ 'shape' => 'DBShardGroupIdentifier', ], ], ], 'DeleteDBSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'DBSnapshotIdentifier', ], 'members' => [ 'DBSnapshotIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteDBSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'DBSnapshot' => [ 'shape' => 'DBSnapshot', ], ], ], 'DeleteDBSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBSubnetGroupName', ], 'members' => [ 'DBSubnetGroupName' => [ 'shape' => 'String', ], ], ], 'DeleteEventSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], ], ], 'DeleteEventSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'DeleteGlobalClusterMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalClusterIdentifier', ], 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteGlobalClusterResult' => [ 'type' => 'structure', 'members' => [ 'GlobalCluster' => [ 'shape' => 'GlobalCluster', ], ], ], 'DeleteIntegrationMessage' => [ 'type' => 'structure', 'required' => [ 'IntegrationIdentifier', ], 'members' => [ 'IntegrationIdentifier' => [ 'shape' => 'IntegrationIdentifier', ], ], ], 'DeleteOptionGroupMessage' => [ 'type' => 'structure', 'required' => [ 'OptionGroupName', ], 'members' => [ 'OptionGroupName' => [ 'shape' => 'String', ], ], ], 'DeleteTenantDatabaseMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', 'TenantDBName', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'TenantDBName' => [ 'shape' => 'String', ], 'SkipFinalSnapshot' => [ 'shape' => 'Boolean', ], 'FinalDBSnapshotIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteTenantDatabaseResult' => [ 'type' => 'structure', 'members' => [ 'TenantDatabase' => [ 'shape' => 'TenantDatabase', ], ], ], 'DeregisterDBProxyTargetsRequest' => [ 'type' => 'structure', 'required' => [ 'DBProxyName', ], 'members' => [ 'DBProxyName' => [ 'shape' => 'String', ], 'TargetGroupName' => [ 'shape' => 'String', ], 'DBInstanceIdentifiers' => [ 'shape' => 'StringList', ], 'DBClusterIdentifiers' => [ 'shape' => 'StringList', ], ], ], 'DeregisterDBProxyTargetsResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeAccountAttributesMessage' => [ 'type' => 'structure', 'members' => [], ], 'DescribeBlueGreenDeploymentsRequest' => [ 'type' => 'structure', 'members' => [ 'BlueGreenDeploymentIdentifier' => [ 'shape' => 'BlueGreenDeploymentIdentifier', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribeBlueGreenDeploymentsResponse' => [ 'type' => 'structure', 'members' => [ 'BlueGreenDeployments' => [ 'shape' => 'BlueGreenDeploymentList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeCertificatesMessage' => [ 'type' => 'structure', 'members' => [ 'CertificateIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBClusterAutomatedBackupsMessage' => [ 'type' => 'structure', 'members' => [ 'DbClusterResourceId' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBClusterBacktracksMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'BacktrackIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBClusterEndpointsMessage' => [ 'type' => 'structure', 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterEndpointIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBClusterParameterGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBClusterParametersMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterParameterGroupName', ], 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'Source' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBClusterSnapshotAttributesMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterSnapshotIdentifier', ], 'members' => [ 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], ], ], 'DescribeDBClusterSnapshotAttributesResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterSnapshotAttributesResult' => [ 'shape' => 'DBClusterSnapshotAttributesResult', ], ], ], 'DescribeDBClusterSnapshotsMessage' => [ 'type' => 'structure', 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'SnapshotType' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'IncludeShared' => [ 'shape' => 'Boolean', ], 'IncludePublic' => [ 'shape' => 'Boolean', ], 'DbClusterResourceId' => [ 'shape' => 'String', ], ], ], 'DescribeDBClustersMessage' => [ 'type' => 'structure', 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'IncludeShared' => [ 'shape' => 'Boolean', ], ], ], 'DescribeDBEngineVersionsMessage' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'DefaultOnly' => [ 'shape' => 'Boolean', ], 'ListSupportedCharacterSets' => [ 'shape' => 'BooleanOptional', ], 'ListSupportedTimezones' => [ 'shape' => 'BooleanOptional', ], 'IncludeAll' => [ 'shape' => 'BooleanOptional', ], ], ], 'DescribeDBInstanceAutomatedBackupsMessage' => [ 'type' => 'structure', 'members' => [ 'DbiResourceId' => [ 'shape' => 'String', ], 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'DBInstanceAutomatedBackupsArn' => [ 'shape' => 'String', ], ], ], 'DescribeDBInstancesMessage' => [ 'type' => 'structure', 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBLogFilesDetails' => [ 'type' => 'structure', 'members' => [ 'LogFileName' => [ 'shape' => 'String', ], 'LastWritten' => [ 'shape' => 'Long', ], 'Size' => [ 'shape' => 'Long', ], ], ], 'DescribeDBLogFilesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DescribeDBLogFilesDetails', 'locationName' => 'DescribeDBLogFilesDetails', ], ], 'DescribeDBLogFilesMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'FilenameContains' => [ 'shape' => 'String', ], 'FileLastWritten' => [ 'shape' => 'Long', ], 'FileSize' => [ 'shape' => 'Long', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBLogFilesResponse' => [ 'type' => 'structure', 'members' => [ 'DescribeDBLogFiles' => [ 'shape' => 'DescribeDBLogFilesList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBMajorEngineVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'Engine', ], 'MajorEngineVersion' => [ 'shape' => 'MajorEngineVersion', ], 'Marker' => [ 'shape' => 'Marker', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribeDBMajorEngineVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'DBMajorEngineVersions' => [ 'shape' => 'DBMajorEngineVersionsList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBParameterGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBParametersMessage' => [ 'type' => 'structure', 'required' => [ 'DBParameterGroupName', ], 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], 'Source' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBProxiesRequest' => [ 'type' => 'structure', 'members' => [ 'DBProxyName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribeDBProxiesResponse' => [ 'type' => 'structure', 'members' => [ 'DBProxies' => [ 'shape' => 'DBProxyList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBProxyEndpointsRequest' => [ 'type' => 'structure', 'members' => [ 'DBProxyName' => [ 'shape' => 'DBProxyName', ], 'DBProxyEndpointName' => [ 'shape' => 'DBProxyEndpointName', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribeDBProxyEndpointsResponse' => [ 'type' => 'structure', 'members' => [ 'DBProxyEndpoints' => [ 'shape' => 'DBProxyEndpointList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBProxyTargetGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'DBProxyName', ], 'members' => [ 'DBProxyName' => [ 'shape' => 'String', ], 'TargetGroupName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribeDBProxyTargetGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'TargetGroups' => [ 'shape' => 'TargetGroupList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBProxyTargetsRequest' => [ 'type' => 'structure', 'required' => [ 'DBProxyName', ], 'members' => [ 'DBProxyName' => [ 'shape' => 'String', ], 'TargetGroupName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribeDBProxyTargetsResponse' => [ 'type' => 'structure', 'members' => [ 'Targets' => [ 'shape' => 'TargetList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBRecommendationsMessage' => [ 'type' => 'structure', 'members' => [ 'LastUpdatedAfter' => [ 'shape' => 'TStamp', ], 'LastUpdatedBefore' => [ 'shape' => 'TStamp', ], 'Locale' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBSecurityGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'DBSecurityGroupName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBShardGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'DBShardGroupIdentifier' => [ 'shape' => 'DBShardGroupIdentifier', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], ], ], 'DescribeDBShardGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'DBShardGroups' => [ 'shape' => 'DBShardGroupsList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBSnapshotAttributesMessage' => [ 'type' => 'structure', 'required' => [ 'DBSnapshotIdentifier', ], 'members' => [ 'DBSnapshotIdentifier' => [ 'shape' => 'String', ], ], ], 'DescribeDBSnapshotAttributesResult' => [ 'type' => 'structure', 'members' => [ 'DBSnapshotAttributesResult' => [ 'shape' => 'DBSnapshotAttributesResult', ], ], ], 'DescribeDBSnapshotTenantDatabasesMessage' => [ 'type' => 'structure', 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'DBSnapshotIdentifier' => [ 'shape' => 'String', ], 'SnapshotType' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'DbiResourceId' => [ 'shape' => 'String', ], ], ], 'DescribeDBSnapshotsMessage' => [ 'type' => 'structure', 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'DBSnapshotIdentifier' => [ 'shape' => 'String', ], 'SnapshotType' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'IncludeShared' => [ 'shape' => 'Boolean', ], 'IncludePublic' => [ 'shape' => 'Boolean', ], 'DbiResourceId' => [ 'shape' => 'String', ], ], ], 'DescribeDBSubnetGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEngineDefaultClusterParametersMessage' => [ 'type' => 'structure', 'required' => [ 'DBParameterGroupFamily', ], 'members' => [ 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEngineDefaultClusterParametersResult' => [ 'type' => 'structure', 'members' => [ 'EngineDefaults' => [ 'shape' => 'EngineDefaults', ], ], ], 'DescribeEngineDefaultParametersMessage' => [ 'type' => 'structure', 'required' => [ 'DBParameterGroupFamily', ], 'members' => [ 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEngineDefaultParametersResult' => [ 'type' => 'structure', 'members' => [ 'EngineDefaults' => [ 'shape' => 'EngineDefaults', ], ], ], 'DescribeEventCategoriesMessage' => [ 'type' => 'structure', 'members' => [ 'SourceType' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], ], ], 'DescribeEventSubscriptionsMessage' => [ 'type' => 'structure', 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEventsMessage' => [ 'type' => 'structure', 'members' => [ 'SourceIdentifier' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'StartTime' => [ 'shape' => 'TStamp', ], 'EndTime' => [ 'shape' => 'TStamp', ], 'Duration' => [ 'shape' => 'IntegerOptional', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeExportTasksMessage' => [ 'type' => 'structure', 'members' => [ 'ExportTaskIdentifier' => [ 'shape' => 'String', ], 'SourceArn' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'MaxRecords', ], 'SourceType' => [ 'shape' => 'ExportSourceType', ], ], ], 'DescribeGlobalClustersMessage' => [ 'type' => 'structure', 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeIntegrationsMessage' => [ 'type' => 'structure', 'members' => [ 'IntegrationIdentifier' => [ 'shape' => 'IntegrationIdentifier', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'Marker', ], ], ], 'DescribeIntegrationsResponse' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'Marker', ], 'Integrations' => [ 'shape' => 'IntegrationList', ], ], ], 'DescribeOptionGroupOptionsMessage' => [ 'type' => 'structure', 'required' => [ 'EngineName', ], 'members' => [ 'EngineName' => [ 'shape' => 'String', ], 'MajorEngineVersion' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeOptionGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'OptionGroupName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'EngineName' => [ 'shape' => 'String', ], 'MajorEngineVersion' => [ 'shape' => 'String', ], ], ], 'DescribeOrderableDBInstanceOptionsMessage' => [ 'type' => 'structure', 'required' => [ 'Engine', ], 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'LicenseModel' => [ 'shape' => 'String', ], 'AvailabilityZoneGroup' => [ 'shape' => 'String', ], 'Vpc' => [ 'shape' => 'BooleanOptional', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribePendingMaintenanceActionsMessage' => [ 'type' => 'structure', 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], ], ], 'DescribeReservedDBInstancesMessage' => [ 'type' => 'structure', 'members' => [ 'ReservedDBInstanceId' => [ 'shape' => 'String', ], 'ReservedDBInstancesOfferingId' => [ 'shape' => 'String', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'Duration' => [ 'shape' => 'String', ], 'ProductDescription' => [ 'shape' => 'String', ], 'OfferingType' => [ 'shape' => 'String', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'LeaseId' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeReservedDBInstancesOfferingsMessage' => [ 'type' => 'structure', 'members' => [ 'ReservedDBInstancesOfferingId' => [ 'shape' => 'String', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'Duration' => [ 'shape' => 'String', ], 'ProductDescription' => [ 'shape' => 'String', ], 'OfferingType' => [ 'shape' => 'String', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeSourceRegionsMessage' => [ 'type' => 'structure', 'members' => [ 'RegionName' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], ], ], 'DescribeTenantDatabasesMessage' => [ 'type' => 'structure', 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'TenantDBName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], ], ], 'DescribeValidDBInstanceModificationsMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], ], ], 'DescribeValidDBInstanceModificationsResult' => [ 'type' => 'structure', 'members' => [ 'ValidDBInstanceModificationsMessage' => [ 'shape' => 'ValidDBInstanceModificationsMessage', ], ], ], 'Description' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '.*', ], 'DisableHttpEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], ], ], 'DisableHttpEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'HttpEndpointEnabled' => [ 'shape' => 'Boolean', ], ], ], 'DocLink' => [ 'type' => 'structure', 'members' => [ 'Text' => [ 'shape' => 'String', ], 'Url' => [ 'shape' => 'String', ], ], ], 'DocLinkList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocLink', ], ], 'DomainMembership' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'FQDN' => [ 'shape' => 'String', ], 'IAMRoleName' => [ 'shape' => 'String', ], 'OU' => [ 'shape' => 'String', ], 'AuthSecretArn' => [ 'shape' => 'String', ], 'DnsIps' => [ 'shape' => 'StringList', ], ], ], 'DomainMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainMembership', 'locationName' => 'DomainMembership', ], ], 'DomainNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DomainNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'Double' => [ 'type' => 'double', ], 'DoubleOptional' => [ 'type' => 'double', ], 'DoubleRange' => [ 'type' => 'structure', 'members' => [ 'From' => [ 'shape' => 'Double', ], 'To' => [ 'shape' => 'Double', ], ], ], 'DoubleRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DoubleRange', 'locationName' => 'DoubleRange', ], ], 'DownloadDBLogFilePortionDetails' => [ 'type' => 'structure', 'members' => [ 'LogFileData' => [ 'shape' => 'String', ], 'Marker' => [ 'shape' => 'String', ], 'AdditionalDataPending' => [ 'shape' => 'Boolean', ], ], ], 'DownloadDBLogFilePortionMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', 'LogFileName', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'LogFileName' => [ 'shape' => 'String', ], 'Marker' => [ 'shape' => 'String', ], 'NumberOfLines' => [ 'shape' => 'Integer', ], ], ], 'EC2SecurityGroup' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'String', ], 'EC2SecurityGroupName' => [ 'shape' => 'String', ], 'EC2SecurityGroupId' => [ 'shape' => 'String', ], 'EC2SecurityGroupOwnerId' => [ 'shape' => 'String', ], ], ], 'EC2SecurityGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EC2SecurityGroup', 'locationName' => 'EC2SecurityGroup', ], ], 'Ec2ImagePropertiesNotSupportedFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'Ec2ImagePropertiesNotSupportedFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'EnableHttpEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], ], ], 'EnableHttpEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'HttpEndpointEnabled' => [ 'shape' => 'Boolean', ], ], ], 'EncryptionContextMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'Endpoint' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'Integer', ], 'HostedZoneId' => [ 'shape' => 'String', ], ], ], 'Engine' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'EngineDefaults' => [ 'type' => 'structure', 'members' => [ 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Marker' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'ParametersList', ], ], 'wrapper' => true, ], 'EngineFamily' => [ 'type' => 'string', 'enum' => [ 'MYSQL', 'POSTGRESQL', 'SQLSERVER', ], ], 'EngineModeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Event' => [ 'type' => 'structure', 'members' => [ 'SourceIdentifier' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'Message' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'Date' => [ 'shape' => 'TStamp', ], 'SourceArn' => [ 'shape' => 'String', ], ], ], 'EventCategoriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'EventCategory', ], ], 'EventCategoriesMap' => [ 'type' => 'structure', 'members' => [ 'SourceType' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], ], 'wrapper' => true, ], 'EventCategoriesMapList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventCategoriesMap', 'locationName' => 'EventCategoriesMap', ], ], 'EventCategoriesMessage' => [ 'type' => 'structure', 'members' => [ 'EventCategoriesMapList' => [ 'shape' => 'EventCategoriesMapList', ], ], ], 'EventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Event', 'locationName' => 'Event', ], ], 'EventSubscription' => [ 'type' => 'structure', 'members' => [ 'CustomerAwsId' => [ 'shape' => 'String', ], 'CustSubscriptionId' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'SubscriptionCreationTime' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'String', ], 'SourceIdsList' => [ 'shape' => 'SourceIdsList', ], 'EventCategoriesList' => [ 'shape' => 'EventCategoriesList', ], 'Enabled' => [ 'shape' => 'Boolean', ], 'EventSubscriptionArn' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'EventSubscriptionQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'EventSubscriptionQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'EventSubscriptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventSubscription', 'locationName' => 'EventSubscription', ], ], 'EventSubscriptionsMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'EventSubscriptionsList' => [ 'shape' => 'EventSubscriptionsList', ], ], ], 'EventsMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Events' => [ 'shape' => 'EventList', ], ], ], 'ExportSourceType' => [ 'type' => 'string', 'enum' => [ 'SNAPSHOT', 'CLUSTER', ], ], 'ExportTask' => [ 'type' => 'structure', 'members' => [ 'ExportTaskIdentifier' => [ 'shape' => 'String', ], 'SourceArn' => [ 'shape' => 'String', ], 'ExportOnly' => [ 'shape' => 'StringList', ], 'SnapshotTime' => [ 'shape' => 'TStamp', ], 'TaskStartTime' => [ 'shape' => 'TStamp', ], 'TaskEndTime' => [ 'shape' => 'TStamp', ], 'S3Bucket' => [ 'shape' => 'String', ], 'S3Prefix' => [ 'shape' => 'String', ], 'IamRoleArn' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'PercentProgress' => [ 'shape' => 'Integer', ], 'TotalExtractedDataInGB' => [ 'shape' => 'Integer', ], 'FailureCause' => [ 'shape' => 'String', ], 'WarningMessage' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'ExportSourceType', ], ], ], 'ExportTaskAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ExportTaskAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ExportTaskNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ExportTaskNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ExportTasksList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportTask', 'locationName' => 'ExportTask', ], ], 'ExportTasksMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ExportTasks' => [ 'shape' => 'ExportTasksList', ], ], ], 'FailoverDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'TargetDBInstanceIdentifier' => [ 'shape' => 'String', ], ], ], 'FailoverDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'FailoverGlobalClusterMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalClusterIdentifier', 'TargetDbClusterIdentifier', ], 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'GlobalClusterIdentifier', ], 'TargetDbClusterIdentifier' => [ 'shape' => 'DBClusterIdentifier', ], 'AllowDataLoss' => [ 'shape' => 'BooleanOptional', ], 'Switchover' => [ 'shape' => 'BooleanOptional', ], ], ], 'FailoverGlobalClusterResult' => [ 'type' => 'structure', 'members' => [ 'GlobalCluster' => [ 'shape' => 'GlobalCluster', ], ], ], 'FailoverState' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'FailoverStatus', ], 'FromDbClusterArn' => [ 'shape' => 'String', ], 'ToDbClusterArn' => [ 'shape' => 'String', ], 'IsDataLossAllowed' => [ 'shape' => 'Boolean', ], ], 'wrapper' => true, ], 'FailoverStatus' => [ 'type' => 'string', 'enum' => [ 'pending', 'failing-over', 'cancelling', ], ], 'FeatureNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', 'locationName' => 'Filter', ], ], 'FilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'Value', ], ], 'GlobalCluster' => [ 'type' => 'structure', 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'String', ], 'GlobalClusterResourceId' => [ 'shape' => 'String', ], 'GlobalClusterArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'EngineLifecycleSupport' => [ 'shape' => 'String', ], 'DatabaseName' => [ 'shape' => 'String', ], 'StorageEncrypted' => [ 'shape' => 'BooleanOptional', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'GlobalClusterMembers' => [ 'shape' => 'GlobalClusterMemberList', ], 'Endpoint' => [ 'shape' => 'String', ], 'FailoverState' => [ 'shape' => 'FailoverState', ], 'TagList' => [ 'shape' => 'TagList', ], ], 'wrapper' => true, ], 'GlobalClusterAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'GlobalClusterAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'GlobalClusterIdentifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[A-Za-z][0-9A-Za-z-:._]*', ], 'GlobalClusterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalCluster', 'locationName' => 'GlobalClusterMember', ], ], 'GlobalClusterMember' => [ 'type' => 'structure', 'members' => [ 'DBClusterArn' => [ 'shape' => 'String', ], 'Readers' => [ 'shape' => 'ReadersArnList', ], 'IsWriter' => [ 'shape' => 'Boolean', ], 'GlobalWriteForwardingStatus' => [ 'shape' => 'WriteForwardingStatus', ], 'SynchronizationStatus' => [ 'shape' => 'GlobalClusterMemberSynchronizationStatus', ], ], 'wrapper' => true, ], 'GlobalClusterMemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalClusterMember', 'locationName' => 'GlobalClusterMember', ], ], 'GlobalClusterMemberSynchronizationStatus' => [ 'type' => 'string', 'enum' => [ 'connected', 'pending-resync', ], ], 'GlobalClusterNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'GlobalClusterNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'GlobalClusterQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'GlobalClusterQuotaExceededFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'GlobalClustersMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'GlobalClusters' => [ 'shape' => 'GlobalClusterList', ], ], ], 'IAMAuthMode' => [ 'type' => 'string', 'enum' => [ 'DISABLED', 'REQUIRED', 'ENABLED', ], ], 'IPRange' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'String', ], 'CIDRIP' => [ 'shape' => 'String', ], ], ], 'IPRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IPRange', 'locationName' => 'IPRange', ], ], 'IamRoleMissingPermissionsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'IamRoleMissingPermissions', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IamRoleNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'IamRoleNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'InstanceQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InstanceQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InsufficientAvailableIPsInSubnetFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InsufficientAvailableIPsInSubnetFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InsufficientDBClusterCapacityFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InsufficientDBClusterCapacityFault', 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'InsufficientDBInstanceCapacityFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InsufficientDBInstanceCapacity', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InsufficientStorageClusterCapacityFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InsufficientStorageClusterCapacity', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Integer' => [ 'type' => 'integer', ], 'IntegerOptional' => [ 'type' => 'integer', ], 'Integration' => [ 'type' => 'structure', 'members' => [ 'SourceArn' => [ 'shape' => 'SourceArn', ], 'TargetArn' => [ 'shape' => 'Arn', ], 'IntegrationName' => [ 'shape' => 'IntegrationName', ], 'IntegrationArn' => [ 'shape' => 'IntegrationArn', ], 'KMSKeyId' => [ 'shape' => 'String', ], 'AdditionalEncryptionContext' => [ 'shape' => 'EncryptionContextMap', ], 'Status' => [ 'shape' => 'IntegrationStatus', ], 'Tags' => [ 'shape' => 'TagList', ], 'CreateTime' => [ 'shape' => 'TStamp', ], 'Errors' => [ 'shape' => 'IntegrationErrorList', ], 'DataFilter' => [ 'shape' => 'DataFilter', ], 'Description' => [ 'shape' => 'IntegrationDescription', ], ], ], 'IntegrationAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'IntegrationAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IntegrationArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => 'arn:aws[a-z\\-]*:rds(-[a-z]*)?:[a-z0-9\\-]*:[0-9]*:integration:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'IntegrationConflictOperationFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'IntegrationConflictOperationFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IntegrationDescription' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '.*', ], 'IntegrationError' => [ 'type' => 'structure', 'required' => [ 'ErrorCode', ], 'members' => [ 'ErrorCode' => [ 'shape' => 'String', ], 'ErrorMessage' => [ 'shape' => 'String', ], ], ], 'IntegrationErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegrationError', 'locationName' => 'IntegrationError', ], ], 'IntegrationIdentifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9_:\\-\\/]+', ], 'IntegrationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integration', 'locationName' => 'Integration', ], ], 'IntegrationName' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '[a-zA-Z][a-zA-Z0-9]*(-[a-zA-Z0-9]+)*', ], 'IntegrationNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'IntegrationNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'IntegrationQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'IntegrationQuotaExceededFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IntegrationStatus' => [ 'type' => 'string', 'enum' => [ 'creating', 'active', 'modifying', 'failed', 'deleting', 'syncing', 'needs_attention', ], ], 'InvalidBlueGreenDeploymentStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidBlueGreenDeploymentStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidCustomDBEngineVersionStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidCustomDBEngineVersionStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBClusterAutomatedBackupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBClusterAutomatedBackupStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBClusterCapacityFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBClusterCapacityFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBClusterEndpointStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBClusterEndpointStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBClusterSnapshotStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBClusterSnapshotStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBClusterStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBClusterStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBInstanceAutomatedBackupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBInstanceAutomatedBackupState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBInstanceStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBInstanceState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBParameterGroupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBParameterGroupState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBProxyEndpointStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBProxyEndpointStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBProxyStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBProxyStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBSecurityGroupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBSecurityGroupState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBShardGroupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBShardGroupState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBSnapshotStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBSnapshotState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBSubnetGroupFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBSubnetGroupFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBSubnetGroupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBSubnetGroupStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBSubnetStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBSubnetStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidEventSubscriptionStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidEventSubscriptionState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidExportOnlyFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidExportOnly', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidExportSourceStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidExportSourceState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidExportTaskStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidExportTaskStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidGlobalClusterStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidGlobalClusterStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidIntegrationStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidIntegrationStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidOptionGroupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidOptionGroupStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidResourceStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidResourceStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidRestoreFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidRestoreFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidS3BucketFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidS3BucketFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidSubnet' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidSubnet', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidVPCNetworkStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidVPCNetworkStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IssueDetails' => [ 'type' => 'structure', 'members' => [ 'PerformanceIssueDetails' => [ 'shape' => 'PerformanceIssueDetails', ], ], ], 'KMSKeyNotAccessibleFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'KMSKeyNotAccessibleFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'KeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'KmsKeyIdOrArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[a-zA-Z0-9_:\\-\\/]+', ], 'LifecycleSupportName' => [ 'type' => 'string', 'enum' => [ 'open-source-rds-standard-support', 'open-source-rds-extended-support', ], ], 'LimitlessDatabase' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'LimitlessDatabaseStatus', ], 'MinRequiredACU' => [ 'shape' => 'DoubleOptional', ], ], ], 'LimitlessDatabaseStatus' => [ 'type' => 'string', 'enum' => [ 'active', 'not-in-use', 'enabled', 'disabled', 'enabling', 'disabling', 'modifying-max-capacity', 'error', ], ], 'ListTagsForResourceMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceName', ], 'members' => [ 'ResourceName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], ], ], 'LocalWriteForwardingStatus' => [ 'type' => 'string', 'enum' => [ 'enabled', 'disabled', 'enabling', 'disabling', 'requested', ], ], 'LogTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Long' => [ 'type' => 'long', ], 'LongOptional' => [ 'type' => 'long', ], 'MajorEngineVersion' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'Marker' => [ 'type' => 'string', 'max' => 340, 'min' => 1, ], 'MasterUserSecret' => [ 'type' => 'structure', 'members' => [ 'SecretArn' => [ 'shape' => 'String', ], 'SecretStatus' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], ], ], 'MaxDBShardGroupLimitReached' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'MaxDBShardGroupLimitReached', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'MaxRecords' => [ 'type' => 'integer', 'max' => 100, 'min' => 20, ], 'Metric' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'References' => [ 'shape' => 'MetricReferenceList', ], 'StatisticsDetails' => [ 'shape' => 'String', ], 'MetricQuery' => [ 'shape' => 'MetricQuery', ], ], ], 'MetricList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Metric', ], ], 'MetricQuery' => [ 'type' => 'structure', 'members' => [ 'PerformanceInsightsMetricQuery' => [ 'shape' => 'PerformanceInsightsMetricQuery', ], ], ], 'MetricReference' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'ReferenceDetails' => [ 'shape' => 'ReferenceDetails', ], ], ], 'MetricReferenceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricReference', ], ], 'MinimumEngineVersionPerAllowedValue' => [ 'type' => 'structure', 'members' => [ 'AllowedValue' => [ 'shape' => 'String', ], 'MinimumEngineVersion' => [ 'shape' => 'String', ], ], ], 'MinimumEngineVersionPerAllowedValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MinimumEngineVersionPerAllowedValue', 'locationName' => 'MinimumEngineVersionPerAllowedValue', ], ], 'ModifyActivityStreamRequest' => [ 'type' => 'structure', 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'AuditPolicyState' => [ 'shape' => 'AuditPolicyState', ], ], ], 'ModifyActivityStreamResponse' => [ 'type' => 'structure', 'members' => [ 'KmsKeyId' => [ 'shape' => 'String', ], 'KinesisStreamName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'ActivityStreamStatus', ], 'Mode' => [ 'shape' => 'ActivityStreamMode', ], 'EngineNativeAuditFieldsIncluded' => [ 'shape' => 'BooleanOptional', ], 'PolicyStatus' => [ 'shape' => 'ActivityStreamPolicyStatus', ], ], ], 'ModifyCertificatesMessage' => [ 'type' => 'structure', 'members' => [ 'CertificateIdentifier' => [ 'shape' => 'String', ], 'RemoveCustomerOverride' => [ 'shape' => 'BooleanOptional', ], ], ], 'ModifyCertificatesResult' => [ 'type' => 'structure', 'members' => [ 'Certificate' => [ 'shape' => 'Certificate', ], ], ], 'ModifyCurrentDBClusterCapacityMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'Capacity' => [ 'shape' => 'IntegerOptional', ], 'SecondsBeforeTimeout' => [ 'shape' => 'IntegerOptional', ], 'TimeoutAction' => [ 'shape' => 'String', ], ], ], 'ModifyCustomDBEngineVersionMessage' => [ 'type' => 'structure', 'required' => [ 'Engine', 'EngineVersion', ], 'members' => [ 'Engine' => [ 'shape' => 'CustomEngineName', ], 'EngineVersion' => [ 'shape' => 'CustomEngineVersion', ], 'Description' => [ 'shape' => 'Description', ], 'Status' => [ 'shape' => 'CustomEngineVersionStatus', ], ], ], 'ModifyDBClusterEndpointMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterEndpointIdentifier', ], 'members' => [ 'DBClusterEndpointIdentifier' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'String', ], 'StaticMembers' => [ 'shape' => 'StringList', ], 'ExcludedMembers' => [ 'shape' => 'StringList', ], ], ], 'ModifyDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'NewDBClusterIdentifier' => [ 'shape' => 'String', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'MasterUserPassword' => [ 'shape' => 'String', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'BacktrackWindow' => [ 'shape' => 'LongOptional', ], 'CloudwatchLogsExportConfiguration' => [ 'shape' => 'CloudwatchLogsExportConfiguration', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AllowMajorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'DBInstanceParameterGroupName' => [ 'shape' => 'String', ], 'Domain' => [ 'shape' => 'String', ], 'DomainIAMRoleName' => [ 'shape' => 'String', ], 'ScalingConfiguration' => [ 'shape' => 'ScalingConfiguration', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'EnableHttpEndpoint' => [ 'shape' => 'BooleanOptional', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'EnableGlobalWriteForwarding' => [ 'shape' => 'BooleanOptional', ], 'DBClusterInstanceClass' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'StorageType' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'MonitoringInterval' => [ 'shape' => 'IntegerOptional', ], 'MonitoringRoleArn' => [ 'shape' => 'String', ], 'DatabaseInsightsMode' => [ 'shape' => 'DatabaseInsightsMode', ], 'EnablePerformanceInsights' => [ 'shape' => 'BooleanOptional', ], 'PerformanceInsightsKMSKeyId' => [ 'shape' => 'String', ], 'PerformanceInsightsRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'ServerlessV2ScalingConfiguration' => [ 'shape' => 'ServerlessV2ScalingConfiguration', ], 'NetworkType' => [ 'shape' => 'String', ], 'ManageMasterUserPassword' => [ 'shape' => 'BooleanOptional', ], 'RotateMasterUserPassword' => [ 'shape' => 'BooleanOptional', ], 'MasterUserSecretKmsKeyId' => [ 'shape' => 'String', ], 'EngineMode' => [ 'shape' => 'String', ], 'AllowEngineModeChange' => [ 'shape' => 'Boolean', ], 'EnableLocalWriteForwarding' => [ 'shape' => 'BooleanOptional', ], 'AwsBackupRecoveryPointArn' => [ 'shape' => 'AwsBackupRecoveryPointArn', ], 'EnableLimitlessDatabase' => [ 'shape' => 'BooleanOptional', ], 'CACertificateIdentifier' => [ 'shape' => 'String', ], ], ], 'ModifyDBClusterParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterParameterGroupName', 'Parameters', ], 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'ParametersList', ], ], ], 'ModifyDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'ModifyDBClusterSnapshotAttributeMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterSnapshotIdentifier', 'AttributeName', ], 'members' => [ 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'AttributeName' => [ 'shape' => 'String', ], 'ValuesToAdd' => [ 'shape' => 'AttributeValueList', ], 'ValuesToRemove' => [ 'shape' => 'AttributeValueList', ], ], ], 'ModifyDBClusterSnapshotAttributeResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterSnapshotAttributesResult' => [ 'shape' => 'DBClusterSnapshotAttributesResult', ], ], ], 'ModifyDBInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'DBSecurityGroups' => [ 'shape' => 'DBSecurityGroupNameList', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], 'MasterUserPassword' => [ 'shape' => 'String', ], 'DBParameterGroupName' => [ 'shape' => 'String', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AllowMajorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'LicenseModel' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'NewDBInstanceIdentifier' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'TdeCredentialArn' => [ 'shape' => 'String', ], 'TdeCredentialPassword' => [ 'shape' => 'String', ], 'CACertificateIdentifier' => [ 'shape' => 'String', ], 'Domain' => [ 'shape' => 'String', ], 'DomainFqdn' => [ 'shape' => 'String', ], 'DomainOu' => [ 'shape' => 'String', ], 'DomainAuthSecretArn' => [ 'shape' => 'String', ], 'DomainDnsIps' => [ 'shape' => 'StringList', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'MonitoringInterval' => [ 'shape' => 'IntegerOptional', ], 'DBPortNumber' => [ 'shape' => 'IntegerOptional', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'MonitoringRoleArn' => [ 'shape' => 'String', ], 'DomainIAMRoleName' => [ 'shape' => 'String', ], 'DisableDomain' => [ 'shape' => 'BooleanOptional', ], 'PromotionTier' => [ 'shape' => 'IntegerOptional', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'DatabaseInsightsMode' => [ 'shape' => 'DatabaseInsightsMode', ], 'EnablePerformanceInsights' => [ 'shape' => 'BooleanOptional', ], 'PerformanceInsightsKMSKeyId' => [ 'shape' => 'String', ], 'PerformanceInsightsRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'CloudwatchLogsExportConfiguration' => [ 'shape' => 'CloudwatchLogsExportConfiguration', ], 'ProcessorFeatures' => [ 'shape' => 'ProcessorFeatureList', ], 'UseDefaultProcessorFeatures' => [ 'shape' => 'BooleanOptional', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'MaxAllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'CertificateRotationRestart' => [ 'shape' => 'BooleanOptional', ], 'ReplicaMode' => [ 'shape' => 'ReplicaMode', ], 'EnableCustomerOwnedIp' => [ 'shape' => 'BooleanOptional', ], 'AwsBackupRecoveryPointArn' => [ 'shape' => 'AwsBackupRecoveryPointArn', ], 'AutomationMode' => [ 'shape' => 'AutomationMode', ], 'ResumeFullAutomationModeMinutes' => [ 'shape' => 'IntegerOptional', ], 'NetworkType' => [ 'shape' => 'String', ], 'StorageThroughput' => [ 'shape' => 'IntegerOptional', ], 'ManageMasterUserPassword' => [ 'shape' => 'BooleanOptional', ], 'RotateMasterUserPassword' => [ 'shape' => 'BooleanOptional', ], 'MasterUserSecretKmsKeyId' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'DedicatedLogVolume' => [ 'shape' => 'BooleanOptional', ], 'MultiTenant' => [ 'shape' => 'BooleanOptional', ], ], ], 'ModifyDBInstanceResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'ModifyDBParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBParameterGroupName', 'Parameters', ], 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'ParametersList', ], ], ], 'ModifyDBProxyEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'DBProxyEndpointName', ], 'members' => [ 'DBProxyEndpointName' => [ 'shape' => 'DBProxyEndpointName', ], 'NewDBProxyEndpointName' => [ 'shape' => 'DBProxyEndpointName', ], 'VpcSecurityGroupIds' => [ 'shape' => 'StringList', ], ], ], 'ModifyDBProxyEndpointResponse' => [ 'type' => 'structure', 'members' => [ 'DBProxyEndpoint' => [ 'shape' => 'DBProxyEndpoint', ], ], ], 'ModifyDBProxyRequest' => [ 'type' => 'structure', 'required' => [ 'DBProxyName', ], 'members' => [ 'DBProxyName' => [ 'shape' => 'String', ], 'NewDBProxyName' => [ 'shape' => 'String', ], 'Auth' => [ 'shape' => 'UserAuthConfigList', ], 'RequireTLS' => [ 'shape' => 'BooleanOptional', ], 'IdleClientTimeout' => [ 'shape' => 'IntegerOptional', ], 'DebugLogging' => [ 'shape' => 'BooleanOptional', ], 'RoleArn' => [ 'shape' => 'String', ], 'SecurityGroups' => [ 'shape' => 'StringList', ], ], ], 'ModifyDBProxyResponse' => [ 'type' => 'structure', 'members' => [ 'DBProxy' => [ 'shape' => 'DBProxy', ], ], ], 'ModifyDBProxyTargetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'TargetGroupName', 'DBProxyName', ], 'members' => [ 'TargetGroupName' => [ 'shape' => 'String', ], 'DBProxyName' => [ 'shape' => 'String', ], 'ConnectionPoolConfig' => [ 'shape' => 'ConnectionPoolConfiguration', ], 'NewName' => [ 'shape' => 'String', ], ], ], 'ModifyDBProxyTargetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'DBProxyTargetGroup' => [ 'shape' => 'DBProxyTargetGroup', ], ], ], 'ModifyDBRecommendationMessage' => [ 'type' => 'structure', 'required' => [ 'RecommendationId', ], 'members' => [ 'RecommendationId' => [ 'shape' => 'String', ], 'Locale' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'RecommendedActionUpdates' => [ 'shape' => 'RecommendedActionUpdateList', ], ], ], 'ModifyDBShardGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBShardGroupIdentifier', ], 'members' => [ 'DBShardGroupIdentifier' => [ 'shape' => 'DBShardGroupIdentifier', ], 'MaxACU' => [ 'shape' => 'DoubleOptional', ], 'MinACU' => [ 'shape' => 'DoubleOptional', ], 'ComputeRedundancy' => [ 'shape' => 'IntegerOptional', ], ], ], 'ModifyDBSnapshotAttributeMessage' => [ 'type' => 'structure', 'required' => [ 'DBSnapshotIdentifier', 'AttributeName', ], 'members' => [ 'DBSnapshotIdentifier' => [ 'shape' => 'String', ], 'AttributeName' => [ 'shape' => 'String', ], 'ValuesToAdd' => [ 'shape' => 'AttributeValueList', ], 'ValuesToRemove' => [ 'shape' => 'AttributeValueList', ], ], ], 'ModifyDBSnapshotAttributeResult' => [ 'type' => 'structure', 'members' => [ 'DBSnapshotAttributesResult' => [ 'shape' => 'DBSnapshotAttributesResult', ], ], ], 'ModifyDBSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'DBSnapshotIdentifier', ], 'members' => [ 'DBSnapshotIdentifier' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'OptionGroupName' => [ 'shape' => 'String', ], ], ], 'ModifyDBSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'DBSnapshot' => [ 'shape' => 'DBSnapshot', ], ], ], 'ModifyDBSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBSubnetGroupName', 'SubnetIds', ], 'members' => [ 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'DBSubnetGroupDescription' => [ 'shape' => 'String', ], 'SubnetIds' => [ 'shape' => 'SubnetIdentifierList', ], ], ], 'ModifyDBSubnetGroupResult' => [ 'type' => 'structure', 'members' => [ 'DBSubnetGroup' => [ 'shape' => 'DBSubnetGroup', ], ], ], 'ModifyEventSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'Enabled' => [ 'shape' => 'BooleanOptional', ], ], ], 'ModifyEventSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'ModifyGlobalClusterMessage' => [ 'type' => 'structure', 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'String', ], 'NewGlobalClusterIdentifier' => [ 'shape' => 'String', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AllowMajorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], ], ], 'ModifyGlobalClusterResult' => [ 'type' => 'structure', 'members' => [ 'GlobalCluster' => [ 'shape' => 'GlobalCluster', ], ], ], 'ModifyIntegrationMessage' => [ 'type' => 'structure', 'required' => [ 'IntegrationIdentifier', ], 'members' => [ 'IntegrationIdentifier' => [ 'shape' => 'IntegrationIdentifier', ], 'IntegrationName' => [ 'shape' => 'IntegrationName', ], 'DataFilter' => [ 'shape' => 'DataFilter', ], 'Description' => [ 'shape' => 'IntegrationDescription', ], ], ], 'ModifyOptionGroupMessage' => [ 'type' => 'structure', 'required' => [ 'OptionGroupName', ], 'members' => [ 'OptionGroupName' => [ 'shape' => 'String', ], 'OptionsToInclude' => [ 'shape' => 'OptionConfigurationList', ], 'OptionsToRemove' => [ 'shape' => 'OptionNamesList', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], ], ], 'ModifyOptionGroupResult' => [ 'type' => 'structure', 'members' => [ 'OptionGroup' => [ 'shape' => 'OptionGroup', ], ], ], 'ModifyTenantDatabaseMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', 'TenantDBName', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'TenantDBName' => [ 'shape' => 'String', ], 'MasterUserPassword' => [ 'shape' => 'SensitiveString', ], 'NewTenantDBName' => [ 'shape' => 'String', ], 'ManageMasterUserPassword' => [ 'shape' => 'BooleanOptional', ], 'RotateMasterUserPassword' => [ 'shape' => 'BooleanOptional', ], 'MasterUserSecretKmsKeyId' => [ 'shape' => 'String', ], ], ], 'ModifyTenantDatabaseResult' => [ 'type' => 'structure', 'members' => [ 'TenantDatabase' => [ 'shape' => 'TenantDatabase', ], ], ], 'NetworkTypeNotSupported' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'NetworkTypeNotSupported', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Option' => [ 'type' => 'structure', 'members' => [ 'OptionName' => [ 'shape' => 'String', ], 'OptionDescription' => [ 'shape' => 'String', ], 'Persistent' => [ 'shape' => 'Boolean', ], 'Permanent' => [ 'shape' => 'Boolean', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'OptionVersion' => [ 'shape' => 'String', ], 'OptionSettings' => [ 'shape' => 'OptionSettingConfigurationList', ], 'DBSecurityGroupMemberships' => [ 'shape' => 'DBSecurityGroupMembershipList', ], 'VpcSecurityGroupMemberships' => [ 'shape' => 'VpcSecurityGroupMembershipList', ], ], ], 'OptionConfiguration' => [ 'type' => 'structure', 'required' => [ 'OptionName', ], 'members' => [ 'OptionName' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'OptionVersion' => [ 'shape' => 'String', ], 'DBSecurityGroupMemberships' => [ 'shape' => 'DBSecurityGroupNameList', ], 'VpcSecurityGroupMemberships' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'OptionSettings' => [ 'shape' => 'OptionSettingsList', ], ], ], 'OptionConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OptionConfiguration', 'locationName' => 'OptionConfiguration', ], ], 'OptionGroup' => [ 'type' => 'structure', 'members' => [ 'OptionGroupName' => [ 'shape' => 'String', ], 'OptionGroupDescription' => [ 'shape' => 'String', ], 'EngineName' => [ 'shape' => 'String', ], 'MajorEngineVersion' => [ 'shape' => 'String', ], 'Options' => [ 'shape' => 'OptionsList', ], 'AllowsVpcAndNonVpcInstanceMemberships' => [ 'shape' => 'Boolean', ], 'VpcId' => [ 'shape' => 'String', ], 'OptionGroupArn' => [ 'shape' => 'String', ], 'SourceOptionGroup' => [ 'shape' => 'String', ], 'SourceAccountId' => [ 'shape' => 'String', ], 'CopyTimestamp' => [ 'shape' => 'TStamp', ], ], 'wrapper' => true, ], 'OptionGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'OptionGroupAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'OptionGroupMembership' => [ 'type' => 'structure', 'members' => [ 'OptionGroupName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'OptionGroupMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OptionGroupMembership', 'locationName' => 'OptionGroupMembership', ], ], 'OptionGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'OptionGroupNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'OptionGroupOption' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'EngineName' => [ 'shape' => 'String', ], 'MajorEngineVersion' => [ 'shape' => 'String', ], 'MinimumRequiredMinorEngineVersion' => [ 'shape' => 'String', ], 'PortRequired' => [ 'shape' => 'Boolean', ], 'DefaultPort' => [ 'shape' => 'IntegerOptional', ], 'OptionsDependedOn' => [ 'shape' => 'OptionsDependedOn', ], 'OptionsConflictsWith' => [ 'shape' => 'OptionsConflictsWith', ], 'Persistent' => [ 'shape' => 'Boolean', ], 'Permanent' => [ 'shape' => 'Boolean', ], 'RequiresAutoMinorEngineVersionUpgrade' => [ 'shape' => 'Boolean', ], 'VpcOnly' => [ 'shape' => 'Boolean', ], 'SupportsOptionVersionDowngrade' => [ 'shape' => 'BooleanOptional', ], 'OptionGroupOptionSettings' => [ 'shape' => 'OptionGroupOptionSettingsList', ], 'OptionGroupOptionVersions' => [ 'shape' => 'OptionGroupOptionVersionsList', ], 'CopyableCrossAccount' => [ 'shape' => 'BooleanOptional', ], ], ], 'OptionGroupOptionSetting' => [ 'type' => 'structure', 'members' => [ 'SettingName' => [ 'shape' => 'String', ], 'SettingDescription' => [ 'shape' => 'String', ], 'DefaultValue' => [ 'shape' => 'String', ], 'ApplyType' => [ 'shape' => 'String', ], 'AllowedValues' => [ 'shape' => 'String', ], 'IsModifiable' => [ 'shape' => 'Boolean', ], 'IsRequired' => [ 'shape' => 'Boolean', ], 'MinimumEngineVersionPerAllowedValue' => [ 'shape' => 'MinimumEngineVersionPerAllowedValueList', ], ], ], 'OptionGroupOptionSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OptionGroupOptionSetting', 'locationName' => 'OptionGroupOptionSetting', ], ], 'OptionGroupOptionVersionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OptionVersion', 'locationName' => 'OptionVersion', ], ], 'OptionGroupOptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OptionGroupOption', 'locationName' => 'OptionGroupOption', ], ], 'OptionGroupOptionsMessage' => [ 'type' => 'structure', 'members' => [ 'OptionGroupOptions' => [ 'shape' => 'OptionGroupOptionsList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'OptionGroupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'OptionGroupQuotaExceededFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'OptionGroups' => [ 'type' => 'structure', 'members' => [ 'OptionGroupsList' => [ 'shape' => 'OptionGroupsList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'OptionGroupsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OptionGroup', 'locationName' => 'OptionGroup', ], ], 'OptionNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'OptionSetting' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], 'DefaultValue' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'ApplyType' => [ 'shape' => 'String', ], 'DataType' => [ 'shape' => 'String', ], 'AllowedValues' => [ 'shape' => 'String', ], 'IsModifiable' => [ 'shape' => 'Boolean', ], 'IsCollection' => [ 'shape' => 'Boolean', ], ], ], 'OptionSettingConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OptionSetting', 'locationName' => 'OptionSetting', ], ], 'OptionSettingsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OptionSetting', 'locationName' => 'OptionSetting', ], ], 'OptionVersion' => [ 'type' => 'structure', 'members' => [ 'Version' => [ 'shape' => 'String', ], 'IsDefault' => [ 'shape' => 'Boolean', ], ], ], 'OptionsConflictsWith' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'OptionConflictName', ], ], 'OptionsDependedOn' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'OptionName', ], ], 'OptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Option', 'locationName' => 'Option', ], ], 'OrderableDBInstanceOption' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'LicenseModel' => [ 'shape' => 'String', ], 'AvailabilityZoneGroup' => [ 'shape' => 'String', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZoneList', ], 'MultiAZCapable' => [ 'shape' => 'Boolean', ], 'ReadReplicaCapable' => [ 'shape' => 'Boolean', ], 'Vpc' => [ 'shape' => 'Boolean', ], 'SupportsStorageEncryption' => [ 'shape' => 'Boolean', ], 'StorageType' => [ 'shape' => 'String', ], 'SupportsIops' => [ 'shape' => 'Boolean', ], 'SupportsEnhancedMonitoring' => [ 'shape' => 'Boolean', ], 'SupportsIAMDatabaseAuthentication' => [ 'shape' => 'Boolean', ], 'SupportsPerformanceInsights' => [ 'shape' => 'Boolean', ], 'MinStorageSize' => [ 'shape' => 'IntegerOptional', ], 'MaxStorageSize' => [ 'shape' => 'IntegerOptional', ], 'MinIopsPerDbInstance' => [ 'shape' => 'IntegerOptional', ], 'MaxIopsPerDbInstance' => [ 'shape' => 'IntegerOptional', ], 'MinIopsPerGib' => [ 'shape' => 'DoubleOptional', ], 'MaxIopsPerGib' => [ 'shape' => 'DoubleOptional', ], 'AvailableProcessorFeatures' => [ 'shape' => 'AvailableProcessorFeatureList', ], 'SupportedEngineModes' => [ 'shape' => 'EngineModeList', ], 'SupportsStorageAutoscaling' => [ 'shape' => 'BooleanOptional', ], 'SupportsKerberosAuthentication' => [ 'shape' => 'BooleanOptional', ], 'OutpostCapable' => [ 'shape' => 'Boolean', ], 'SupportedActivityStreamModes' => [ 'shape' => 'ActivityStreamModeList', ], 'SupportsGlobalDatabases' => [ 'shape' => 'Boolean', ], 'SupportsClusters' => [ 'shape' => 'Boolean', ], 'SupportedNetworkTypes' => [ 'shape' => 'StringList', ], 'SupportsStorageThroughput' => [ 'shape' => 'Boolean', ], 'MinStorageThroughputPerDbInstance' => [ 'shape' => 'IntegerOptional', ], 'MaxStorageThroughputPerDbInstance' => [ 'shape' => 'IntegerOptional', ], 'MinStorageThroughputPerIops' => [ 'shape' => 'DoubleOptional', ], 'MaxStorageThroughputPerIops' => [ 'shape' => 'DoubleOptional', ], 'SupportsDedicatedLogVolume' => [ 'shape' => 'Boolean', ], ], 'wrapper' => true, ], 'OrderableDBInstanceOptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrderableDBInstanceOption', 'locationName' => 'OrderableDBInstanceOption', ], ], 'OrderableDBInstanceOptionsMessage' => [ 'type' => 'structure', 'members' => [ 'OrderableDBInstanceOptions' => [ 'shape' => 'OrderableDBInstanceOptionsList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'Outpost' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'String', ], ], ], 'Parameter' => [ 'type' => 'structure', 'members' => [ 'ParameterName' => [ 'shape' => 'String', ], 'ParameterValue' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Source' => [ 'shape' => 'String', ], 'ApplyType' => [ 'shape' => 'String', ], 'DataType' => [ 'shape' => 'String', ], 'AllowedValues' => [ 'shape' => 'String', ], 'IsModifiable' => [ 'shape' => 'Boolean', ], 'MinimumEngineVersion' => [ 'shape' => 'String', ], 'ApplyMethod' => [ 'shape' => 'ApplyMethod', ], 'SupportedEngineModes' => [ 'shape' => 'EngineModeList', ], ], ], 'ParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parameter', 'locationName' => 'Parameter', ], ], 'PendingCloudwatchLogsExports' => [ 'type' => 'structure', 'members' => [ 'LogTypesToEnable' => [ 'shape' => 'LogTypeList', ], 'LogTypesToDisable' => [ 'shape' => 'LogTypeList', ], ], ], 'PendingMaintenanceAction' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'String', ], 'AutoAppliedAfterDate' => [ 'shape' => 'TStamp', ], 'ForcedApplyDate' => [ 'shape' => 'TStamp', ], 'OptInStatus' => [ 'shape' => 'String', ], 'CurrentApplyDate' => [ 'shape' => 'TStamp', ], 'Description' => [ 'shape' => 'String', ], ], ], 'PendingMaintenanceActionDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'PendingMaintenanceAction', 'locationName' => 'PendingMaintenanceAction', ], ], 'PendingMaintenanceActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcePendingMaintenanceActions', 'locationName' => 'ResourcePendingMaintenanceActions', ], ], 'PendingMaintenanceActionsMessage' => [ 'type' => 'structure', 'members' => [ 'PendingMaintenanceActions' => [ 'shape' => 'PendingMaintenanceActions', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'PendingModifiedValues' => [ 'type' => 'structure', 'members' => [ 'DBInstanceClass' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'MasterUserPassword' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'LicenseModel' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'CACertificateIdentifier' => [ 'shape' => 'String', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'PendingCloudwatchLogsExports' => [ 'shape' => 'PendingCloudwatchLogsExports', ], 'ProcessorFeatures' => [ 'shape' => 'ProcessorFeatureList', ], 'IAMDatabaseAuthenticationEnabled' => [ 'shape' => 'BooleanOptional', ], 'AutomationMode' => [ 'shape' => 'AutomationMode', ], 'ResumeFullAutomationModeTime' => [ 'shape' => 'TStamp', ], 'StorageThroughput' => [ 'shape' => 'IntegerOptional', ], 'Engine' => [ 'shape' => 'String', ], 'DedicatedLogVolume' => [ 'shape' => 'BooleanOptional', ], 'MultiTenant' => [ 'shape' => 'BooleanOptional', ], ], ], 'PerformanceInsightsMetricDimensionGroup' => [ 'type' => 'structure', 'members' => [ 'Dimensions' => [ 'shape' => 'StringList', ], 'Group' => [ 'shape' => 'String', ], 'Limit' => [ 'shape' => 'Integer', ], ], ], 'PerformanceInsightsMetricQuery' => [ 'type' => 'structure', 'members' => [ 'GroupBy' => [ 'shape' => 'PerformanceInsightsMetricDimensionGroup', ], 'Metric' => [ 'shape' => 'String', ], ], ], 'PerformanceIssueDetails' => [ 'type' => 'structure', 'members' => [ 'StartTime' => [ 'shape' => 'TStamp', ], 'EndTime' => [ 'shape' => 'TStamp', ], 'Metrics' => [ 'shape' => 'MetricList', ], 'Analysis' => [ 'shape' => 'String', ], ], ], 'PointInTimeRestoreNotEnabledFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'PointInTimeRestoreNotEnabled', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ProcessorFeature' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'ProcessorFeatureList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProcessorFeature', 'locationName' => 'ProcessorFeature', ], ], 'PromoteReadReplicaDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], ], ], 'PromoteReadReplicaDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'PromoteReadReplicaMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], ], ], 'PromoteReadReplicaResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'ProvisionedIopsNotAvailableInAZFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ProvisionedIopsNotAvailableInAZFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'PurchaseReservedDBInstancesOfferingMessage' => [ 'type' => 'structure', 'required' => [ 'ReservedDBInstancesOfferingId', ], 'members' => [ 'ReservedDBInstancesOfferingId' => [ 'shape' => 'String', ], 'ReservedDBInstanceId' => [ 'shape' => 'String', ], 'DBInstanceCount' => [ 'shape' => 'IntegerOptional', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'PurchaseReservedDBInstancesOfferingResult' => [ 'type' => 'structure', 'members' => [ 'ReservedDBInstance' => [ 'shape' => 'ReservedDBInstance', ], ], ], 'Range' => [ 'type' => 'structure', 'members' => [ 'From' => [ 'shape' => 'Integer', ], 'To' => [ 'shape' => 'Integer', ], 'Step' => [ 'shape' => 'IntegerOptional', ], ], ], 'RangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Range', 'locationName' => 'Range', ], ], 'RdsCustomClusterConfiguration' => [ 'type' => 'structure', 'members' => [ 'InterconnectSubnetId' => [ 'shape' => 'String', ], 'TransitGatewayMulticastDomainId' => [ 'shape' => 'String', ], 'ReplicaMode' => [ 'shape' => 'ReplicaMode', ], ], ], 'ReadReplicaDBClusterIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'ReadReplicaDBClusterIdentifier', ], ], 'ReadReplicaDBInstanceIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'ReadReplicaDBInstanceIdentifier', ], ], 'ReadReplicaIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'ReadReplicaIdentifier', ], ], 'ReadersArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'RebootDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], ], ], 'RebootDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'RebootDBInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'ForceFailover' => [ 'shape' => 'BooleanOptional', ], ], ], 'RebootDBInstanceResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'RebootDBShardGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBShardGroupIdentifier', ], 'members' => [ 'DBShardGroupIdentifier' => [ 'shape' => 'DBShardGroupIdentifier', ], ], ], 'RecommendedAction' => [ 'type' => 'structure', 'members' => [ 'ActionId' => [ 'shape' => 'String', ], 'Title' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Operation' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'RecommendedActionParameterList', ], 'ApplyModes' => [ 'shape' => 'StringList', ], 'Status' => [ 'shape' => 'String', ], 'IssueDetails' => [ 'shape' => 'IssueDetails', ], 'ContextAttributes' => [ 'shape' => 'ContextAttributeList', ], ], ], 'RecommendedActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendedAction', ], ], 'RecommendedActionParameter' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'RecommendedActionParameterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendedActionParameter', ], ], 'RecommendedActionUpdate' => [ 'type' => 'structure', 'required' => [ 'ActionId', 'Status', ], 'members' => [ 'ActionId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'RecommendedActionUpdateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendedActionUpdate', ], ], 'RecurringCharge' => [ 'type' => 'structure', 'members' => [ 'RecurringChargeAmount' => [ 'shape' => 'Double', ], 'RecurringChargeFrequency' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'RecurringChargeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecurringCharge', 'locationName' => 'RecurringCharge', ], ], 'ReferenceDetails' => [ 'type' => 'structure', 'members' => [ 'ScalarReferenceDetails' => [ 'shape' => 'ScalarReferenceDetails', ], ], ], 'RegisterDBProxyTargetsRequest' => [ 'type' => 'structure', 'required' => [ 'DBProxyName', ], 'members' => [ 'DBProxyName' => [ 'shape' => 'String', ], 'TargetGroupName' => [ 'shape' => 'String', ], 'DBInstanceIdentifiers' => [ 'shape' => 'StringList', ], 'DBClusterIdentifiers' => [ 'shape' => 'StringList', ], ], ], 'RegisterDBProxyTargetsResponse' => [ 'type' => 'structure', 'members' => [ 'DBProxyTargets' => [ 'shape' => 'TargetList', ], ], ], 'RemoveFromGlobalClusterMessage' => [ 'type' => 'structure', 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'String', ], 'DbClusterIdentifier' => [ 'shape' => 'String', ], ], ], 'RemoveFromGlobalClusterResult' => [ 'type' => 'structure', 'members' => [ 'GlobalCluster' => [ 'shape' => 'GlobalCluster', ], ], ], 'RemoveRoleFromDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', 'RoleArn', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'RoleArn' => [ 'shape' => 'String', ], 'FeatureName' => [ 'shape' => 'String', ], ], ], 'RemoveRoleFromDBInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', 'RoleArn', 'FeatureName', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'RoleArn' => [ 'shape' => 'String', ], 'FeatureName' => [ 'shape' => 'String', ], ], ], 'RemoveSourceIdentifierFromSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', 'SourceIdentifier', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'SourceIdentifier' => [ 'shape' => 'String', ], ], ], 'RemoveSourceIdentifierFromSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'RemoveTagsFromResourceMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceName', 'TagKeys', ], 'members' => [ 'ResourceName' => [ 'shape' => 'String', ], 'TagKeys' => [ 'shape' => 'KeyList', ], ], ], 'ReplicaMode' => [ 'type' => 'string', 'enum' => [ 'open-read-only', 'mounted', ], ], 'ReservedDBInstance' => [ 'type' => 'structure', 'members' => [ 'ReservedDBInstanceId' => [ 'shape' => 'String', ], 'ReservedDBInstancesOfferingId' => [ 'shape' => 'String', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'StartTime' => [ 'shape' => 'TStamp', ], 'Duration' => [ 'shape' => 'Integer', ], 'FixedPrice' => [ 'shape' => 'Double', ], 'UsagePrice' => [ 'shape' => 'Double', ], 'CurrencyCode' => [ 'shape' => 'String', ], 'DBInstanceCount' => [ 'shape' => 'Integer', ], 'ProductDescription' => [ 'shape' => 'String', ], 'OfferingType' => [ 'shape' => 'String', ], 'MultiAZ' => [ 'shape' => 'Boolean', ], 'State' => [ 'shape' => 'String', ], 'RecurringCharges' => [ 'shape' => 'RecurringChargeList', ], 'ReservedDBInstanceArn' => [ 'shape' => 'String', ], 'LeaseId' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'ReservedDBInstanceAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ReservedDBInstanceAlreadyExists', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ReservedDBInstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReservedDBInstance', 'locationName' => 'ReservedDBInstance', ], ], 'ReservedDBInstanceMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ReservedDBInstances' => [ 'shape' => 'ReservedDBInstanceList', ], ], ], 'ReservedDBInstanceNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ReservedDBInstanceNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ReservedDBInstanceQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ReservedDBInstanceQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ReservedDBInstancesOffering' => [ 'type' => 'structure', 'members' => [ 'ReservedDBInstancesOfferingId' => [ 'shape' => 'String', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'Duration' => [ 'shape' => 'Integer', ], 'FixedPrice' => [ 'shape' => 'Double', ], 'UsagePrice' => [ 'shape' => 'Double', ], 'CurrencyCode' => [ 'shape' => 'String', ], 'ProductDescription' => [ 'shape' => 'String', ], 'OfferingType' => [ 'shape' => 'String', ], 'MultiAZ' => [ 'shape' => 'Boolean', ], 'RecurringCharges' => [ 'shape' => 'RecurringChargeList', ], ], 'wrapper' => true, ], 'ReservedDBInstancesOfferingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReservedDBInstancesOffering', 'locationName' => 'ReservedDBInstancesOffering', ], ], 'ReservedDBInstancesOfferingMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'ReservedDBInstancesOfferings' => [ 'shape' => 'ReservedDBInstancesOfferingList', ], ], ], 'ReservedDBInstancesOfferingNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ReservedDBInstancesOfferingNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResetDBClusterParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterParameterGroupName', ], 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'ResetAllParameters' => [ 'shape' => 'Boolean', ], 'Parameters' => [ 'shape' => 'ParametersList', ], ], ], 'ResetDBParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBParameterGroupName', ], 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], 'ResetAllParameters' => [ 'shape' => 'Boolean', ], 'Parameters' => [ 'shape' => 'ParametersList', ], ], ], 'ResourceNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ResourceNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourcePendingMaintenanceActions' => [ 'type' => 'structure', 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'String', ], 'PendingMaintenanceActionDetails' => [ 'shape' => 'PendingMaintenanceActionDetails', ], ], 'wrapper' => true, ], 'RestoreDBClusterFromS3Message' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', 'Engine', 'MasterUsername', 'SourceEngine', 'SourceEngineVersion', 'S3BucketName', 'S3IngestionRoleArn', ], 'members' => [ 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'CharacterSetName' => [ 'shape' => 'String', ], 'DatabaseName' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'MasterUsername' => [ 'shape' => 'String', ], 'MasterUserPassword' => [ 'shape' => 'String', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], 'StorageEncrypted' => [ 'shape' => 'BooleanOptional', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'SourceEngine' => [ 'shape' => 'String', ], 'SourceEngineVersion' => [ 'shape' => 'String', ], 'S3BucketName' => [ 'shape' => 'String', ], 'S3Prefix' => [ 'shape' => 'String', ], 'S3IngestionRoleArn' => [ 'shape' => 'String', ], 'BacktrackWindow' => [ 'shape' => 'LongOptional', ], 'EnableCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'Domain' => [ 'shape' => 'String', ], 'DomainIAMRoleName' => [ 'shape' => 'String', ], 'ServerlessV2ScalingConfiguration' => [ 'shape' => 'ServerlessV2ScalingConfiguration', ], 'NetworkType' => [ 'shape' => 'String', ], 'ManageMasterUserPassword' => [ 'shape' => 'BooleanOptional', ], 'MasterUserSecretKmsKeyId' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'EngineLifecycleSupport' => [ 'shape' => 'String', ], ], ], 'RestoreDBClusterFromS3Result' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'RestoreDBClusterFromSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', 'SnapshotIdentifier', 'Engine', ], 'members' => [ 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'SnapshotIdentifier' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'DatabaseName' => [ 'shape' => 'String', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'Tags' => [ 'shape' => 'TagList', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'BacktrackWindow' => [ 'shape' => 'LongOptional', ], 'EnableCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'EngineMode' => [ 'shape' => 'String', ], 'ScalingConfiguration' => [ 'shape' => 'ScalingConfiguration', ], 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'Domain' => [ 'shape' => 'String', ], 'DomainIAMRoleName' => [ 'shape' => 'String', ], 'DBClusterInstanceClass' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'ServerlessV2ScalingConfiguration' => [ 'shape' => 'ServerlessV2ScalingConfiguration', ], 'NetworkType' => [ 'shape' => 'String', ], 'RdsCustomClusterConfiguration' => [ 'shape' => 'RdsCustomClusterConfiguration', ], 'MonitoringInterval' => [ 'shape' => 'IntegerOptional', ], 'MonitoringRoleArn' => [ 'shape' => 'String', ], 'EnablePerformanceInsights' => [ 'shape' => 'BooleanOptional', ], 'PerformanceInsightsKMSKeyId' => [ 'shape' => 'String', ], 'PerformanceInsightsRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'EngineLifecycleSupport' => [ 'shape' => 'String', ], ], ], 'RestoreDBClusterFromSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'RestoreDBClusterToPointInTimeMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'RestoreType' => [ 'shape' => 'String', ], 'SourceDBClusterIdentifier' => [ 'shape' => 'String', ], 'RestoreToTime' => [ 'shape' => 'TStamp', ], 'UseLatestRestorableTime' => [ 'shape' => 'Boolean', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'Tags' => [ 'shape' => 'TagList', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'BacktrackWindow' => [ 'shape' => 'LongOptional', ], 'EnableCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'Domain' => [ 'shape' => 'String', ], 'DomainIAMRoleName' => [ 'shape' => 'String', ], 'ScalingConfiguration' => [ 'shape' => 'ScalingConfiguration', ], 'EngineMode' => [ 'shape' => 'String', ], 'DBClusterInstanceClass' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'ServerlessV2ScalingConfiguration' => [ 'shape' => 'ServerlessV2ScalingConfiguration', ], 'NetworkType' => [ 'shape' => 'String', ], 'SourceDbClusterResourceId' => [ 'shape' => 'String', ], 'RdsCustomClusterConfiguration' => [ 'shape' => 'RdsCustomClusterConfiguration', ], 'MonitoringInterval' => [ 'shape' => 'IntegerOptional', ], 'MonitoringRoleArn' => [ 'shape' => 'String', ], 'EnablePerformanceInsights' => [ 'shape' => 'BooleanOptional', ], 'PerformanceInsightsKMSKeyId' => [ 'shape' => 'String', ], 'PerformanceInsightsRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'EngineLifecycleSupport' => [ 'shape' => 'String', ], ], ], 'RestoreDBClusterToPointInTimeResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'RestoreDBInstanceFromDBSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'DBSnapshotIdentifier' => [ 'shape' => 'String', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'LicenseModel' => [ 'shape' => 'String', ], 'DBName' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], 'StorageType' => [ 'shape' => 'String', ], 'TdeCredentialArn' => [ 'shape' => 'String', ], 'TdeCredentialPassword' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'Domain' => [ 'shape' => 'String', ], 'DomainFqdn' => [ 'shape' => 'String', ], 'DomainOu' => [ 'shape' => 'String', ], 'DomainAuthSecretArn' => [ 'shape' => 'String', ], 'DomainDnsIps' => [ 'shape' => 'StringList', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'DomainIAMRoleName' => [ 'shape' => 'String', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'EnableCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'ProcessorFeatures' => [ 'shape' => 'ProcessorFeatureList', ], 'UseDefaultProcessorFeatures' => [ 'shape' => 'BooleanOptional', ], 'DBParameterGroupName' => [ 'shape' => 'String', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'EnableCustomerOwnedIp' => [ 'shape' => 'BooleanOptional', ], 'CustomIamInstanceProfile' => [ 'shape' => 'String', ], 'BackupTarget' => [ 'shape' => 'String', ], 'NetworkType' => [ 'shape' => 'String', ], 'StorageThroughput' => [ 'shape' => 'IntegerOptional', ], 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'DedicatedLogVolume' => [ 'shape' => 'BooleanOptional', ], 'CACertificateIdentifier' => [ 'shape' => 'String', ], 'EngineLifecycleSupport' => [ 'shape' => 'String', ], 'ManageMasterUserPassword' => [ 'shape' => 'BooleanOptional', ], 'MasterUserSecretKmsKeyId' => [ 'shape' => 'String', ], ], ], 'RestoreDBInstanceFromDBSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'RestoreDBInstanceFromS3Message' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', 'DBInstanceClass', 'Engine', 'SourceEngine', 'SourceEngineVersion', 'S3BucketName', 'S3IngestionRoleArn', ], 'members' => [ 'DBName' => [ 'shape' => 'String', ], 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'MasterUsername' => [ 'shape' => 'String', ], 'MasterUserPassword' => [ 'shape' => 'String', ], 'DBSecurityGroups' => [ 'shape' => 'DBSecurityGroupNameList', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'DBParameterGroupName' => [ 'shape' => 'String', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'LicenseModel' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], 'StorageType' => [ 'shape' => 'String', ], 'StorageEncrypted' => [ 'shape' => 'BooleanOptional', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'MonitoringInterval' => [ 'shape' => 'IntegerOptional', ], 'MonitoringRoleArn' => [ 'shape' => 'String', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'SourceEngine' => [ 'shape' => 'String', ], 'SourceEngineVersion' => [ 'shape' => 'String', ], 'S3BucketName' => [ 'shape' => 'String', ], 'S3Prefix' => [ 'shape' => 'String', ], 'S3IngestionRoleArn' => [ 'shape' => 'String', ], 'DatabaseInsightsMode' => [ 'shape' => 'DatabaseInsightsMode', ], 'EnablePerformanceInsights' => [ 'shape' => 'BooleanOptional', ], 'PerformanceInsightsKMSKeyId' => [ 'shape' => 'String', ], 'PerformanceInsightsRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'EnableCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'ProcessorFeatures' => [ 'shape' => 'ProcessorFeatureList', ], 'UseDefaultProcessorFeatures' => [ 'shape' => 'BooleanOptional', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'MaxAllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'NetworkType' => [ 'shape' => 'String', ], 'StorageThroughput' => [ 'shape' => 'IntegerOptional', ], 'ManageMasterUserPassword' => [ 'shape' => 'BooleanOptional', ], 'MasterUserSecretKmsKeyId' => [ 'shape' => 'String', ], 'DedicatedLogVolume' => [ 'shape' => 'BooleanOptional', ], 'CACertificateIdentifier' => [ 'shape' => 'String', ], 'EngineLifecycleSupport' => [ 'shape' => 'String', ], ], ], 'RestoreDBInstanceFromS3Result' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'RestoreDBInstanceToPointInTimeMessage' => [ 'type' => 'structure', 'required' => [ 'TargetDBInstanceIdentifier', ], 'members' => [ 'SourceDBInstanceIdentifier' => [ 'shape' => 'String', ], 'TargetDBInstanceIdentifier' => [ 'shape' => 'String', ], 'RestoreTime' => [ 'shape' => 'TStamp', ], 'UseLatestRestorableTime' => [ 'shape' => 'Boolean', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'LicenseModel' => [ 'shape' => 'String', ], 'DBName' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], 'StorageType' => [ 'shape' => 'String', ], 'TdeCredentialArn' => [ 'shape' => 'String', ], 'TdeCredentialPassword' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'Domain' => [ 'shape' => 'String', ], 'DomainIAMRoleName' => [ 'shape' => 'String', ], 'DomainFqdn' => [ 'shape' => 'String', ], 'DomainOu' => [ 'shape' => 'String', ], 'DomainAuthSecretArn' => [ 'shape' => 'String', ], 'DomainDnsIps' => [ 'shape' => 'StringList', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'EnableCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'ProcessorFeatures' => [ 'shape' => 'ProcessorFeatureList', ], 'UseDefaultProcessorFeatures' => [ 'shape' => 'BooleanOptional', ], 'DBParameterGroupName' => [ 'shape' => 'String', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'SourceDbiResourceId' => [ 'shape' => 'String', ], 'MaxAllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'SourceDBInstanceAutomatedBackupsArn' => [ 'shape' => 'String', ], 'EnableCustomerOwnedIp' => [ 'shape' => 'BooleanOptional', ], 'CustomIamInstanceProfile' => [ 'shape' => 'String', ], 'BackupTarget' => [ 'shape' => 'String', ], 'NetworkType' => [ 'shape' => 'String', ], 'StorageThroughput' => [ 'shape' => 'IntegerOptional', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'DedicatedLogVolume' => [ 'shape' => 'BooleanOptional', ], 'CACertificateIdentifier' => [ 'shape' => 'String', ], 'EngineLifecycleSupport' => [ 'shape' => 'String', ], 'ManageMasterUserPassword' => [ 'shape' => 'BooleanOptional', ], 'MasterUserSecretKmsKeyId' => [ 'shape' => 'String', ], ], ], 'RestoreDBInstanceToPointInTimeResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'RestoreWindow' => [ 'type' => 'structure', 'members' => [ 'EarliestTime' => [ 'shape' => 'TStamp', ], 'LatestTime' => [ 'shape' => 'TStamp', ], ], ], 'RevokeDBSecurityGroupIngressMessage' => [ 'type' => 'structure', 'required' => [ 'DBSecurityGroupName', ], 'members' => [ 'DBSecurityGroupName' => [ 'shape' => 'String', ], 'CIDRIP' => [ 'shape' => 'String', ], 'EC2SecurityGroupName' => [ 'shape' => 'String', ], 'EC2SecurityGroupId' => [ 'shape' => 'String', ], 'EC2SecurityGroupOwnerId' => [ 'shape' => 'String', ], ], ], 'RevokeDBSecurityGroupIngressResult' => [ 'type' => 'structure', 'members' => [ 'DBSecurityGroup' => [ 'shape' => 'DBSecurityGroup', ], ], ], 'SNSInvalidTopicFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SNSInvalidTopic', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SNSNoAuthorizationFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SNSNoAuthorization', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SNSTopicArnNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SNSTopicArnNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ScalarReferenceDetails' => [ 'type' => 'structure', 'members' => [ 'Value' => [ 'shape' => 'Double', ], ], ], 'ScalingConfiguration' => [ 'type' => 'structure', 'members' => [ 'MinCapacity' => [ 'shape' => 'IntegerOptional', ], 'MaxCapacity' => [ 'shape' => 'IntegerOptional', ], 'AutoPause' => [ 'shape' => 'BooleanOptional', ], 'SecondsUntilAutoPause' => [ 'shape' => 'IntegerOptional', ], 'TimeoutAction' => [ 'shape' => 'String', ], 'SecondsBeforeTimeout' => [ 'shape' => 'IntegerOptional', ], ], ], 'ScalingConfigurationInfo' => [ 'type' => 'structure', 'members' => [ 'MinCapacity' => [ 'shape' => 'IntegerOptional', ], 'MaxCapacity' => [ 'shape' => 'IntegerOptional', ], 'AutoPause' => [ 'shape' => 'BooleanOptional', ], 'SecondsUntilAutoPause' => [ 'shape' => 'IntegerOptional', ], 'TimeoutAction' => [ 'shape' => 'String', ], 'SecondsBeforeTimeout' => [ 'shape' => 'IntegerOptional', ], ], ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'ServerlessV2FeaturesSupport' => [ 'type' => 'structure', 'members' => [ 'MinCapacity' => [ 'shape' => 'DoubleOptional', ], 'MaxCapacity' => [ 'shape' => 'DoubleOptional', ], ], ], 'ServerlessV2ScalingConfiguration' => [ 'type' => 'structure', 'members' => [ 'MinCapacity' => [ 'shape' => 'DoubleOptional', ], 'MaxCapacity' => [ 'shape' => 'DoubleOptional', ], 'SecondsUntilAutoPause' => [ 'shape' => 'IntegerOptional', ], ], ], 'ServerlessV2ScalingConfigurationInfo' => [ 'type' => 'structure', 'members' => [ 'MinCapacity' => [ 'shape' => 'DoubleOptional', ], 'MaxCapacity' => [ 'shape' => 'DoubleOptional', ], 'SecondsUntilAutoPause' => [ 'shape' => 'IntegerOptional', ], ], ], 'SharedSnapshotQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SharedSnapshotQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SnapshotQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SnapshotQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SourceArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => 'arn:aws[a-z\\-]*:rds(-[a-z]*)?:[a-z0-9\\-]*:[0-9]*:(cluster|db):[a-z][a-z0-9]*(-[a-z0-9]+)*', ], 'SourceClusterNotSupportedFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SourceClusterNotSupportedFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SourceDatabaseNotSupportedFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SourceDatabaseNotSupportedFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SourceIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'SourceId', ], ], 'SourceNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SourceNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SourceRegion' => [ 'type' => 'structure', 'members' => [ 'RegionName' => [ 'shape' => 'String', ], 'Endpoint' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'SupportsDBInstanceAutomatedBackupsReplication' => [ 'shape' => 'Boolean', ], ], ], 'SourceRegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceRegion', 'locationName' => 'SourceRegion', ], ], 'SourceRegionMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'SourceRegions' => [ 'shape' => 'SourceRegionList', ], ], ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 'db-instance', 'db-parameter-group', 'db-security-group', 'db-snapshot', 'db-cluster', 'db-cluster-snapshot', 'custom-engine-version', 'db-proxy', 'blue-green-deployment', ], ], 'StartActivityStreamRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Mode', 'KmsKeyId', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'Mode' => [ 'shape' => 'ActivityStreamMode', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'ApplyImmediately' => [ 'shape' => 'BooleanOptional', ], 'EngineNativeAuditFieldsIncluded' => [ 'shape' => 'BooleanOptional', ], ], ], 'StartActivityStreamResponse' => [ 'type' => 'structure', 'members' => [ 'KmsKeyId' => [ 'shape' => 'String', ], 'KinesisStreamName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'ActivityStreamStatus', ], 'Mode' => [ 'shape' => 'ActivityStreamMode', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], 'EngineNativeAuditFieldsIncluded' => [ 'shape' => 'BooleanOptional', ], ], ], 'StartDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], ], ], 'StartDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'StartDBInstanceAutomatedBackupsReplicationMessage' => [ 'type' => 'structure', 'required' => [ 'SourceDBInstanceArn', ], 'members' => [ 'SourceDBInstanceArn' => [ 'shape' => 'String', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'PreSignedUrl' => [ 'shape' => 'String', ], ], ], 'StartDBInstanceAutomatedBackupsReplicationResult' => [ 'type' => 'structure', 'members' => [ 'DBInstanceAutomatedBackup' => [ 'shape' => 'DBInstanceAutomatedBackup', ], ], ], 'StartDBInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], ], ], 'StartDBInstanceResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'StartExportTaskMessage' => [ 'type' => 'structure', 'required' => [ 'ExportTaskIdentifier', 'SourceArn', 'S3BucketName', 'IamRoleArn', 'KmsKeyId', ], 'members' => [ 'ExportTaskIdentifier' => [ 'shape' => 'String', ], 'SourceArn' => [ 'shape' => 'String', ], 'S3BucketName' => [ 'shape' => 'String', ], 'IamRoleArn' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'S3Prefix' => [ 'shape' => 'String', ], 'ExportOnly' => [ 'shape' => 'StringList', ], ], ], 'StopActivityStreamRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'String', ], 'ApplyImmediately' => [ 'shape' => 'BooleanOptional', ], ], ], 'StopActivityStreamResponse' => [ 'type' => 'structure', 'members' => [ 'KmsKeyId' => [ 'shape' => 'String', ], 'KinesisStreamName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'ActivityStreamStatus', ], ], ], 'StopDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], ], ], 'StopDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'StopDBInstanceAutomatedBackupsReplicationMessage' => [ 'type' => 'structure', 'required' => [ 'SourceDBInstanceArn', ], 'members' => [ 'SourceDBInstanceArn' => [ 'shape' => 'String', ], ], ], 'StopDBInstanceAutomatedBackupsReplicationResult' => [ 'type' => 'structure', 'members' => [ 'DBInstanceAutomatedBackup' => [ 'shape' => 'DBInstanceAutomatedBackup', ], ], ], 'StopDBInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'DBSnapshotIdentifier' => [ 'shape' => 'String', ], ], ], 'StopDBInstanceResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'StorageQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StorageQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'StorageTypeNotAvailableFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StorageTypeNotAvailableFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'StorageTypeNotSupportedFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StorageTypeNotSupported', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'String255' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.*', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Subnet' => [ 'type' => 'structure', 'members' => [ 'SubnetIdentifier' => [ 'shape' => 'String', ], 'SubnetAvailabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'SubnetOutpost' => [ 'shape' => 'Outpost', ], 'SubnetStatus' => [ 'shape' => 'String', ], ], ], 'SubnetAlreadyInUse' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SubnetAlreadyInUse', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SubnetIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'SubnetIdentifier', ], ], 'SubnetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subnet', 'locationName' => 'Subnet', ], ], 'SubscriptionAlreadyExistFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SubscriptionAlreadyExist', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SubscriptionCategoryNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SubscriptionCategoryNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SubscriptionNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SubscriptionNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SupportedCharacterSetsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CharacterSet', 'locationName' => 'CharacterSet', ], ], 'SupportedEngineLifecycle' => [ 'type' => 'structure', 'required' => [ 'LifecycleSupportName', 'LifecycleSupportStartDate', 'LifecycleSupportEndDate', ], 'members' => [ 'LifecycleSupportName' => [ 'shape' => 'LifecycleSupportName', ], 'LifecycleSupportStartDate' => [ 'shape' => 'TStamp', ], 'LifecycleSupportEndDate' => [ 'shape' => 'TStamp', ], ], ], 'SupportedEngineLifecycleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SupportedEngineLifecycle', 'locationName' => 'SupportedEngineLifecycle', ], ], 'SupportedTimezonesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Timezone', 'locationName' => 'Timezone', ], ], 'SwitchoverBlueGreenDeploymentRequest' => [ 'type' => 'structure', 'required' => [ 'BlueGreenDeploymentIdentifier', ], 'members' => [ 'BlueGreenDeploymentIdentifier' => [ 'shape' => 'BlueGreenDeploymentIdentifier', ], 'SwitchoverTimeout' => [ 'shape' => 'SwitchoverTimeout', ], ], ], 'SwitchoverBlueGreenDeploymentResponse' => [ 'type' => 'structure', 'members' => [ 'BlueGreenDeployment' => [ 'shape' => 'BlueGreenDeployment', ], ], ], 'SwitchoverDetail' => [ 'type' => 'structure', 'members' => [ 'SourceMember' => [ 'shape' => 'DatabaseArn', ], 'TargetMember' => [ 'shape' => 'DatabaseArn', ], 'Status' => [ 'shape' => 'SwitchoverDetailStatus', ], ], ], 'SwitchoverDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SwitchoverDetail', ], ], 'SwitchoverDetailStatus' => [ 'type' => 'string', ], 'SwitchoverGlobalClusterMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalClusterIdentifier', 'TargetDbClusterIdentifier', ], 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'GlobalClusterIdentifier', ], 'TargetDbClusterIdentifier' => [ 'shape' => 'DBClusterIdentifier', ], ], ], 'SwitchoverGlobalClusterResult' => [ 'type' => 'structure', 'members' => [ 'GlobalCluster' => [ 'shape' => 'GlobalCluster', ], ], ], 'SwitchoverReadReplicaMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], ], ], 'SwitchoverReadReplicaResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'SwitchoverTimeout' => [ 'type' => 'integer', 'min' => 30, ], 'TStamp' => [ 'type' => 'timestamp', ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', 'locationName' => 'Tag', ], ], 'TagListMessage' => [ 'type' => 'structure', 'members' => [ 'TagList' => [ 'shape' => 'TagList', ], ], ], 'TargetDBClusterParameterGroupName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[A-Za-z](?!.*--)[0-9A-Za-z-]*[^-]|^default(?!.*--)(?!.*\\.\\.)[0-9A-Za-z-.]*[^-]', ], 'TargetDBInstanceClass' => [ 'type' => 'string', 'max' => 20, 'min' => 5, 'pattern' => 'db\\.[0-9a-z]{2,6}\\.[0-9a-z]{4,9}', ], 'TargetDBParameterGroupName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[A-Za-z](?!.*--)[0-9A-Za-z-]*[^-]|^default(?!.*--)(?!.*\\.\\.)[0-9A-Za-z-.]*[^-]', ], 'TargetEngineVersion' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[0-9A-Za-z-_.]+', ], 'TargetGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBProxyTargetGroup', ], ], 'TargetHealth' => [ 'type' => 'structure', 'members' => [ 'State' => [ 'shape' => 'TargetState', ], 'Reason' => [ 'shape' => 'TargetHealthReason', ], 'Description' => [ 'shape' => 'String', ], ], ], 'TargetHealthReason' => [ 'type' => 'string', 'enum' => [ 'UNREACHABLE', 'CONNECTION_FAILED', 'AUTH_FAILURE', 'PENDING_PROXY_CAPACITY', 'INVALID_REPLICATION_STATE', ], ], 'TargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBProxyTarget', ], ], 'TargetRole' => [ 'type' => 'string', 'enum' => [ 'READ_WRITE', 'READ_ONLY', 'UNKNOWN', ], ], 'TargetState' => [ 'type' => 'string', 'enum' => [ 'REGISTERING', 'AVAILABLE', 'UNAVAILABLE', ], ], 'TargetStorageType' => [ 'type' => 'string', ], 'TargetType' => [ 'type' => 'string', 'enum' => [ 'RDS_INSTANCE', 'RDS_SERVERLESS_ENDPOINT', 'TRACKED_CLUSTER', ], ], 'TenantDatabase' => [ 'type' => 'structure', 'members' => [ 'TenantDatabaseCreateTime' => [ 'shape' => 'TStamp', ], 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'TenantDBName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'MasterUsername' => [ 'shape' => 'String', ], 'DbiResourceId' => [ 'shape' => 'String', ], 'TenantDatabaseResourceId' => [ 'shape' => 'String', ], 'TenantDatabaseARN' => [ 'shape' => 'String', ], 'CharacterSetName' => [ 'shape' => 'String', ], 'NcharCharacterSetName' => [ 'shape' => 'String', ], 'DeletionProtection' => [ 'shape' => 'Boolean', ], 'PendingModifiedValues' => [ 'shape' => 'TenantDatabasePendingModifiedValues', ], 'MasterUserSecret' => [ 'shape' => 'MasterUserSecret', ], 'TagList' => [ 'shape' => 'TagList', ], ], 'wrapper' => true, ], 'TenantDatabaseAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TenantDatabaseAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TenantDatabaseNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TenantDatabaseNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'TenantDatabasePendingModifiedValues' => [ 'type' => 'structure', 'members' => [ 'MasterUserPassword' => [ 'shape' => 'SensitiveString', ], 'TenantDBName' => [ 'shape' => 'String', ], ], ], 'TenantDatabaseQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'TenantDatabaseQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TenantDatabasesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TenantDatabase', 'locationName' => 'TenantDatabase', ], ], 'TenantDatabasesMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'TenantDatabases' => [ 'shape' => 'TenantDatabasesList', ], ], ], 'Timezone' => [ 'type' => 'structure', 'members' => [ 'TimezoneName' => [ 'shape' => 'String', ], ], ], 'UnsupportedDBEngineVersionFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'UnsupportedDBEngineVersion', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'UpgradeTarget' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'AutoUpgrade' => [ 'shape' => 'Boolean', ], 'IsMajorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'SupportedEngineModes' => [ 'shape' => 'EngineModeList', ], 'SupportsParallelQuery' => [ 'shape' => 'BooleanOptional', ], 'SupportsGlobalDatabases' => [ 'shape' => 'BooleanOptional', ], 'SupportsBabelfish' => [ 'shape' => 'BooleanOptional', ], 'SupportsLimitlessDatabase' => [ 'shape' => 'BooleanOptional', ], 'SupportsLocalWriteForwarding' => [ 'shape' => 'BooleanOptional', ], 'SupportsIntegrations' => [ 'shape' => 'BooleanOptional', ], ], ], 'UserAuthConfig' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'String', ], 'UserName' => [ 'shape' => 'String', ], 'AuthScheme' => [ 'shape' => 'AuthScheme', ], 'SecretArn' => [ 'shape' => 'String', ], 'IAMAuth' => [ 'shape' => 'IAMAuthMode', ], 'ClientPasswordAuthType' => [ 'shape' => 'ClientPasswordAuthType', ], ], ], 'UserAuthConfigInfo' => [ 'type' => 'structure', 'members' => [ 'Description' => [ 'shape' => 'String', ], 'UserName' => [ 'shape' => 'String', ], 'AuthScheme' => [ 'shape' => 'AuthScheme', ], 'SecretArn' => [ 'shape' => 'String', ], 'IAMAuth' => [ 'shape' => 'IAMAuthMode', ], 'ClientPasswordAuthType' => [ 'shape' => 'ClientPasswordAuthType', ], ], ], 'UserAuthConfigInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserAuthConfigInfo', ], ], 'UserAuthConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserAuthConfig', ], ], 'ValidDBInstanceModificationsMessage' => [ 'type' => 'structure', 'members' => [ 'Storage' => [ 'shape' => 'ValidStorageOptionsList', ], 'ValidProcessorFeatures' => [ 'shape' => 'AvailableProcessorFeatureList', ], 'SupportsDedicatedLogVolume' => [ 'shape' => 'Boolean', ], ], 'wrapper' => true, ], 'ValidStorageOptions' => [ 'type' => 'structure', 'members' => [ 'StorageType' => [ 'shape' => 'String', ], 'StorageSize' => [ 'shape' => 'RangeList', ], 'ProvisionedIops' => [ 'shape' => 'RangeList', ], 'IopsToStorageRatio' => [ 'shape' => 'DoubleRangeList', ], 'SupportsStorageAutoscaling' => [ 'shape' => 'Boolean', ], 'ProvisionedStorageThroughput' => [ 'shape' => 'RangeList', ], 'StorageThroughputToIopsRatio' => [ 'shape' => 'DoubleRangeList', ], ], ], 'ValidStorageOptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidStorageOptions', 'locationName' => 'ValidStorageOptions', ], ], 'ValidUpgradeTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpgradeTarget', 'locationName' => 'UpgradeTarget', ], ], 'VpcSecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'VpcSecurityGroupId', ], ], 'VpcSecurityGroupMembership' => [ 'type' => 'structure', 'members' => [ 'VpcSecurityGroupId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'VpcSecurityGroupMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcSecurityGroupMembership', 'locationName' => 'VpcSecurityGroupMembership', ], ], 'WriteForwardingStatus' => [ 'type' => 'string', 'enum' => [ 'enabled', 'disabled', 'enabling', 'disabling', 'unknown', ], ], ],];
