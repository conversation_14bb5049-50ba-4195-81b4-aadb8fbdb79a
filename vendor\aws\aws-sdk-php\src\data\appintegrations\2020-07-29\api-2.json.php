<?php
// This file was auto-generated from sdk-root/src/data/appintegrations/2020-07-29/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-07-29', 'endpointPrefix' => 'app-integrations', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon AppIntegrations Service', 'serviceId' => 'AppIntegrations', 'signatureVersion' => 'v4', 'signingName' => 'app-integrations', 'uid' => 'appintegrations-2020-07-29', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'CreateApplication' => [ 'name' => 'CreateApplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/applications', ], 'input' => [ 'shape' => 'CreateApplicationRequest', ], 'output' => [ 'shape' => 'CreateApplicationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ResourceQuotaExceededException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'CreateDataIntegration' => [ 'name' => 'CreateDataIntegration', 'http' => [ 'method' => 'POST', 'requestUri' => '/dataIntegrations', ], 'input' => [ 'shape' => 'CreateDataIntegrationRequest', ], 'output' => [ 'shape' => 'CreateDataIntegrationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ResourceQuotaExceededException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateDataIntegrationAssociation' => [ 'name' => 'CreateDataIntegrationAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/dataIntegrations/{Identifier}/associations', ], 'input' => [ 'shape' => 'CreateDataIntegrationAssociationRequest', ], 'output' => [ 'shape' => 'CreateDataIntegrationAssociationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ResourceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'CreateEventIntegration' => [ 'name' => 'CreateEventIntegration', 'http' => [ 'method' => 'POST', 'requestUri' => '/eventIntegrations', ], 'input' => [ 'shape' => 'CreateEventIntegrationRequest', ], 'output' => [ 'shape' => 'CreateEventIntegrationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ResourceQuotaExceededException', ], [ 'shape' => 'DuplicateResourceException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteApplication' => [ 'name' => 'DeleteApplication', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/applications/{ApplicationIdentifier}', ], 'input' => [ 'shape' => 'DeleteApplicationRequest', ], 'output' => [ 'shape' => 'DeleteApplicationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteDataIntegration' => [ 'name' => 'DeleteDataIntegration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/dataIntegrations/{Identifier}', ], 'input' => [ 'shape' => 'DeleteDataIntegrationRequest', ], 'output' => [ 'shape' => 'DeleteDataIntegrationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DeleteEventIntegration' => [ 'name' => 'DeleteEventIntegration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/eventIntegrations/{Name}', ], 'input' => [ 'shape' => 'DeleteEventIntegrationRequest', ], 'output' => [ 'shape' => 'DeleteEventIntegrationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetApplication' => [ 'name' => 'GetApplication', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{ApplicationIdentifier}', ], 'input' => [ 'shape' => 'GetApplicationRequest', ], 'output' => [ 'shape' => 'GetApplicationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetDataIntegration' => [ 'name' => 'GetDataIntegration', 'http' => [ 'method' => 'GET', 'requestUri' => '/dataIntegrations/{Identifier}', ], 'input' => [ 'shape' => 'GetDataIntegrationRequest', ], 'output' => [ 'shape' => 'GetDataIntegrationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetEventIntegration' => [ 'name' => 'GetEventIntegration', 'http' => [ 'method' => 'GET', 'requestUri' => '/eventIntegrations/{Name}', ], 'input' => [ 'shape' => 'GetEventIntegrationRequest', ], 'output' => [ 'shape' => 'GetEventIntegrationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListApplicationAssociations' => [ 'name' => 'ListApplicationAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications/{ApplicationIdentifier}/associations', ], 'input' => [ 'shape' => 'ListApplicationAssociationsRequest', ], 'output' => [ 'shape' => 'ListApplicationAssociationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListApplications' => [ 'name' => 'ListApplications', 'http' => [ 'method' => 'GET', 'requestUri' => '/applications', ], 'input' => [ 'shape' => 'ListApplicationsRequest', ], 'output' => [ 'shape' => 'ListApplicationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDataIntegrationAssociations' => [ 'name' => 'ListDataIntegrationAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/dataIntegrations/{Identifier}/associations', ], 'input' => [ 'shape' => 'ListDataIntegrationAssociationsRequest', ], 'output' => [ 'shape' => 'ListDataIntegrationAssociationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListDataIntegrations' => [ 'name' => 'ListDataIntegrations', 'http' => [ 'method' => 'GET', 'requestUri' => '/dataIntegrations', ], 'input' => [ 'shape' => 'ListDataIntegrationsRequest', ], 'output' => [ 'shape' => 'ListDataIntegrationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListEventIntegrationAssociations' => [ 'name' => 'ListEventIntegrationAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/eventIntegrations/{Name}/associations', ], 'input' => [ 'shape' => 'ListEventIntegrationAssociationsRequest', ], 'output' => [ 'shape' => 'ListEventIntegrationAssociationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListEventIntegrations' => [ 'name' => 'ListEventIntegrations', 'http' => [ 'method' => 'GET', 'requestUri' => '/eventIntegrations', ], 'input' => [ 'shape' => 'ListEventIntegrationsRequest', ], 'output' => [ 'shape' => 'ListEventIntegrationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateApplication' => [ 'name' => 'UpdateApplication', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/applications/{ApplicationIdentifier}', ], 'input' => [ 'shape' => 'UpdateApplicationRequest', ], 'output' => [ 'shape' => 'UpdateApplicationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UnsupportedOperationException', ], ], ], 'UpdateDataIntegration' => [ 'name' => 'UpdateDataIntegration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/dataIntegrations/{Identifier}', ], 'input' => [ 'shape' => 'UpdateDataIntegrationRequest', ], 'output' => [ 'shape' => 'UpdateDataIntegrationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateDataIntegrationAssociation' => [ 'name' => 'UpdateDataIntegrationAssociation', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/dataIntegrations/{Identifier}/associations/{DataIntegrationAssociationIdentifier}', ], 'input' => [ 'shape' => 'UpdateDataIntegrationAssociationRequest', ], 'output' => [ 'shape' => 'UpdateDataIntegrationAssociationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'UpdateEventIntegration' => [ 'name' => 'UpdateEventIntegration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/eventIntegrations/{Name}', ], 'input' => [ 'shape' => 'UpdateEventIntegrationRequest', ], 'output' => [ 'shape' => 'UpdateEventIntegrationResponse', ], 'errors' => [ [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'AccessDeniedException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'ApplicationApprovedOrigins' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationTrustedSource', ], 'max' => 50, 'min' => 1, ], 'ApplicationAssociationSummary' => [ 'type' => 'structure', 'members' => [ 'ApplicationAssociationArn' => [ 'shape' => 'Arn', ], 'ApplicationArn' => [ 'shape' => 'Arn', ], 'ClientId' => [ 'shape' => 'ClientId', ], ], ], 'ApplicationAssociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationAssociationSummary', ], 'max' => 50, 'min' => 1, ], 'ApplicationName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\/\\._ \\-]+$', ], 'ApplicationNamespace' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\/\\._\\-]+$', ], 'ApplicationSourceConfig' => [ 'type' => 'structure', 'members' => [ 'ExternalUrlConfig' => [ 'shape' => 'ExternalUrlConfig', ], ], ], 'ApplicationSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Id' => [ 'shape' => 'UUID', ], 'Name' => [ 'shape' => 'ApplicationName', ], 'Namespace' => [ 'shape' => 'ApplicationNamespace', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], ], ], 'ApplicationTrustedSource' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^\\w+\\:\\/\\/.*$', ], 'ApplicationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ApplicationSummary', ], 'max' => 50, 'min' => 1, ], 'Arn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^arn:aws:[A-Za-z0-9][A-Za-z0-9_/.-]{0,62}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}$', ], 'ArnOrUUID' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^(arn:aws:[A-Za-z0-9][A-Za-z0-9_/.-]{0,62}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}|[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})(:[\\w\\$]+)?$', ], 'ClientAssociationMetadata' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonBlankString', ], 'value' => [ 'shape' => 'NonBlankString', ], ], 'ClientId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.*', ], 'CreateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Namespace', 'ApplicationSourceConfig', ], 'members' => [ 'Name' => [ 'shape' => 'ApplicationName', ], 'Namespace' => [ 'shape' => 'ApplicationNamespace', ], 'Description' => [ 'shape' => 'Description', ], 'ApplicationSourceConfig' => [ 'shape' => 'ApplicationSourceConfig', ], 'Subscriptions' => [ 'shape' => 'SubscriptionList', 'deprecated' => true, 'deprecatedMessage' => 'Subscriptions has been replaced with Permissions', ], 'Publications' => [ 'shape' => 'PublicationList', 'deprecated' => true, 'deprecatedMessage' => 'Publications has been replaced with Permissions', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagMap', ], 'Permissions' => [ 'shape' => 'PermissionList', ], ], ], 'CreateApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Id' => [ 'shape' => 'UUID', ], ], ], 'CreateDataIntegrationAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'DataIntegrationIdentifier', ], 'members' => [ 'DataIntegrationIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'ClientId' => [ 'shape' => 'ClientId', ], 'ObjectConfiguration' => [ 'shape' => 'ObjectConfiguration', ], 'DestinationURI' => [ 'shape' => 'DestinationURI', ], 'ClientAssociationMetadata' => [ 'shape' => 'ClientAssociationMetadata', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'ExecutionConfiguration' => [ 'shape' => 'ExecutionConfiguration', ], ], ], 'CreateDataIntegrationAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'DataIntegrationAssociationId' => [ 'shape' => 'UUID', ], 'DataIntegrationArn' => [ 'shape' => 'Arn', ], ], ], 'CreateDataIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'KmsKey', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'KmsKey' => [ 'shape' => 'NonBlankString', ], 'SourceURI' => [ 'shape' => 'SourceURI', ], 'ScheduleConfig' => [ 'shape' => 'ScheduleConfiguration', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'FileConfiguration' => [ 'shape' => 'FileConfiguration', ], 'ObjectConfiguration' => [ 'shape' => 'ObjectConfiguration', ], ], ], 'CreateDataIntegrationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Id' => [ 'shape' => 'UUID', ], 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'KmsKey' => [ 'shape' => 'NonBlankString', ], 'SourceURI' => [ 'shape' => 'SourceURI', ], 'ScheduleConfiguration' => [ 'shape' => 'ScheduleConfiguration', ], 'Tags' => [ 'shape' => 'TagMap', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', ], 'FileConfiguration' => [ 'shape' => 'FileConfiguration', ], 'ObjectConfiguration' => [ 'shape' => 'ObjectConfiguration', ], ], ], 'CreateEventIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'EventFilter', 'EventBridgeBus', ], 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'EventFilter' => [ 'shape' => 'EventFilter', ], 'EventBridgeBus' => [ 'shape' => 'EventBridgeBus', ], 'ClientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateEventIntegrationResponse' => [ 'type' => 'structure', 'members' => [ 'EventIntegrationArn' => [ 'shape' => 'Arn', ], ], ], 'DataIntegrationAssociationSummary' => [ 'type' => 'structure', 'members' => [ 'DataIntegrationAssociationArn' => [ 'shape' => 'Arn', ], 'DataIntegrationArn' => [ 'shape' => 'Arn', ], 'ClientId' => [ 'shape' => 'ClientId', ], 'DestinationURI' => [ 'shape' => 'DestinationURI', ], 'LastExecutionStatus' => [ 'shape' => 'LastExecutionStatus', ], 'ExecutionConfiguration' => [ 'shape' => 'ExecutionConfiguration', ], ], ], 'DataIntegrationAssociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataIntegrationAssociationSummary', ], 'max' => 50, 'min' => 1, ], 'DataIntegrationSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'Name', ], 'SourceURI' => [ 'shape' => 'SourceURI', ], ], ], 'DataIntegrationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataIntegrationSummary', ], 'max' => 50, 'min' => 1, ], 'DeleteApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'ArnOrUUID', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], ], ], 'DeleteApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDataIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'DataIntegrationIdentifier', ], 'members' => [ 'DataIntegrationIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'DeleteDataIntegrationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteEventIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'DeleteEventIntegrationResponse' => [ 'type' => 'structure', 'members' => [], ], 'Description' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, 'pattern' => '.*', ], 'DestinationURI' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^(\\w+\\:\\/\\/[\\w.-]+[\\w/!@#+=.-]+$)|(\\w+\\:\\/\\/[\\w.-]+[\\w/!@#+=.-]+[\\w/!@#+=.-]+[\\w/!@#+=.,-]+$)', ], 'DuplicateResourceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'EventBridgeBus' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\/\\._\\-]+$', ], 'EventBridgeRuleName' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\/\\._\\-]+$', ], 'EventDefinitionSchema' => [ 'type' => 'string', 'max' => 10240, 'min' => 1, 'pattern' => '^.*$', ], 'EventFilter' => [ 'type' => 'structure', 'required' => [ 'Source', ], 'members' => [ 'Source' => [ 'shape' => 'Source', ], ], ], 'EventIntegration' => [ 'type' => 'structure', 'members' => [ 'EventIntegrationArn' => [ 'shape' => 'Arn', ], 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'EventFilter' => [ 'shape' => 'EventFilter', ], 'EventBridgeBus' => [ 'shape' => 'EventBridgeBus', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'EventIntegrationAssociation' => [ 'type' => 'structure', 'members' => [ 'EventIntegrationAssociationArn' => [ 'shape' => 'Arn', ], 'EventIntegrationAssociationId' => [ 'shape' => 'UUID', ], 'EventIntegrationName' => [ 'shape' => 'Name', ], 'ClientId' => [ 'shape' => 'ClientId', ], 'EventBridgeRuleName' => [ 'shape' => 'EventBridgeRuleName', ], 'ClientAssociationMetadata' => [ 'shape' => 'ClientAssociationMetadata', ], ], ], 'EventIntegrationAssociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventIntegrationAssociation', ], 'max' => 50, 'min' => 1, ], 'EventIntegrationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventIntegration', ], 'max' => 50, 'min' => 1, ], 'EventName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\/\\._\\-]+::[a-zA-Z0-9\\/\\._\\-]+(?:\\*)?$', ], 'ExecutionConfiguration' => [ 'type' => 'structure', 'required' => [ 'ExecutionMode', ], 'members' => [ 'ExecutionMode' => [ 'shape' => 'ExecutionMode', ], 'OnDemandConfiguration' => [ 'shape' => 'OnDemandConfiguration', ], 'ScheduleConfiguration' => [ 'shape' => 'ScheduleConfiguration', ], ], ], 'ExecutionMode' => [ 'type' => 'string', 'enum' => [ 'ON_DEMAND', 'SCHEDULED', ], ], 'ExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETED', 'IN_PROGRESS', 'FAILED', ], ], 'ExternalUrlConfig' => [ 'type' => 'structure', 'required' => [ 'AccessUrl', ], 'members' => [ 'AccessUrl' => [ 'shape' => 'URL', ], 'ApprovedOrigins' => [ 'shape' => 'ApplicationApprovedOrigins', ], ], ], 'Fields' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\/\\._\\-]+$', ], 'FieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Fields', ], 'max' => 2048, 'min' => 1, ], 'FieldsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonBlankString', ], 'value' => [ 'shape' => 'FieldsList', ], ], 'FileConfiguration' => [ 'type' => 'structure', 'required' => [ 'Folders', ], 'members' => [ 'Folders' => [ 'shape' => 'FolderList', ], 'Filters' => [ 'shape' => 'FieldsMap', ], ], ], 'FolderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonBlankLongString', ], 'max' => 10, 'min' => 1, ], 'GetApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'ArnOrUUID', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], ], ], 'GetApplicationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Id' => [ 'shape' => 'UUID', ], 'Name' => [ 'shape' => 'ApplicationName', ], 'Namespace' => [ 'shape' => 'ApplicationNamespace', ], 'Description' => [ 'shape' => 'Description', ], 'ApplicationSourceConfig' => [ 'shape' => 'ApplicationSourceConfig', ], 'Subscriptions' => [ 'shape' => 'SubscriptionList', 'deprecated' => true, 'deprecatedMessage' => 'Subscriptions has been replaced with Permissions', ], 'Publications' => [ 'shape' => 'PublicationList', 'deprecated' => true, 'deprecatedMessage' => 'Publications has been replaced with Permissions', ], 'CreatedTime' => [ 'shape' => 'Timestamp', ], 'LastModifiedTime' => [ 'shape' => 'Timestamp', ], 'Tags' => [ 'shape' => 'TagMap', ], 'Permissions' => [ 'shape' => 'PermissionList', ], ], ], 'GetDataIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetDataIntegrationResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'Id' => [ 'shape' => 'UUID', ], 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'KmsKey' => [ 'shape' => 'NonBlankString', ], 'SourceURI' => [ 'shape' => 'SourceURI', ], 'ScheduleConfiguration' => [ 'shape' => 'ScheduleConfiguration', ], 'Tags' => [ 'shape' => 'TagMap', ], 'FileConfiguration' => [ 'shape' => 'FileConfiguration', ], 'ObjectConfiguration' => [ 'shape' => 'ObjectConfiguration', ], ], ], 'GetEventIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'GetEventIntegrationResponse' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], 'EventIntegrationArn' => [ 'shape' => 'Arn', ], 'EventBridgeBus' => [ 'shape' => 'EventBridgeBus', ], 'EventFilter' => [ 'shape' => 'EventFilter', ], 'Tags' => [ 'shape' => 'TagMap', ], ], ], 'IdempotencyToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '.*', ], 'Identifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.*\\S.*', ], 'InternalServiceError' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'LastExecutionStatus' => [ 'type' => 'structure', 'members' => [ 'ExecutionStatus' => [ 'shape' => 'ExecutionStatus', ], 'StatusMessage' => [ 'shape' => 'NonBlankString', ], ], ], 'ListApplicationAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'ApplicationId', ], 'members' => [ 'ApplicationId' => [ 'shape' => 'ArnOrUUID', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListApplicationAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'ApplicationAssociations' => [ 'shape' => 'ApplicationAssociationsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListApplicationsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListApplicationsResponse' => [ 'type' => 'structure', 'members' => [ 'Applications' => [ 'shape' => 'ApplicationsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataIntegrationAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'DataIntegrationIdentifier', ], 'members' => [ 'DataIntegrationIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDataIntegrationAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'DataIntegrationAssociations' => [ 'shape' => 'DataIntegrationAssociationsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataIntegrationsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListDataIntegrationsResponse' => [ 'type' => 'structure', 'members' => [ 'DataIntegrations' => [ 'shape' => 'DataIntegrationsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEventIntegrationAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'EventIntegrationName', ], 'members' => [ 'EventIntegrationName' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'Name', ], 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListEventIntegrationAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'EventIntegrationAssociations' => [ 'shape' => 'EventIntegrationAssociationsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListEventIntegrationsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListEventIntegrationsResponse' => [ 'type' => 'structure', 'members' => [ 'EventIntegrations' => [ 'shape' => 'EventIntegrationsList', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'Message' => [ 'type' => 'string', ], 'Name' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\/\\._\\-]+$', ], 'NextToken' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '.*', ], 'NonBlankLongString' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '.*\\S.*', ], 'NonBlankString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.*\\S.*', ], 'Object' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\/\\._\\-]+$', ], 'ObjectConfiguration' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonBlankString', ], 'value' => [ 'shape' => 'FieldsMap', ], ], 'OnDemandConfiguration' => [ 'type' => 'structure', 'required' => [ 'StartTime', ], 'members' => [ 'StartTime' => [ 'shape' => 'NonBlankString', ], 'EndTime' => [ 'shape' => 'NonBlankString', ], ], ], 'Permission' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^[a-zA-Z0-9\\/\\._\\-\\*]+$', ], 'PermissionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Permission', ], 'max' => 150, 'min' => 0, ], 'Publication' => [ 'type' => 'structure', 'required' => [ 'Event', 'Schema', ], 'members' => [ 'Event' => [ 'shape' => 'EventName', ], 'Schema' => [ 'shape' => 'EventDefinitionSchema', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'PublicationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Publication', ], 'max' => 50, 'min' => 0, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'ScheduleConfiguration' => [ 'type' => 'structure', 'required' => [ 'ScheduleExpression', ], 'members' => [ 'FirstExecutionFrom' => [ 'shape' => 'NonBlankString', ], 'Object' => [ 'shape' => 'Object', ], 'ScheduleExpression' => [ 'shape' => 'NonBlankString', ], ], ], 'Source' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^aws\\.partner\\/.*$', ], 'SourceURI' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^(\\w+\\:\\/\\/[\\w.-]+[\\w/!@#+=.-]+$)|(\\w+\\:\\/\\/[\\w.-]+[\\w/!@#+=.-]+[\\w/!@#+=.-]+[\\w/!@#+=.,-]+$)', ], 'Subscription' => [ 'type' => 'structure', 'required' => [ 'Event', ], 'members' => [ 'Event' => [ 'shape' => 'EventName', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'SubscriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subscription', ], 'max' => 50, 'min' => 0, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^(?!aws:)[a-zA-Z+-=._:/]+$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'URL' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^\\w+\\:\\/\\/.*$', ], 'UUID' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}', ], 'UnsupportedOperationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'Message', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateApplicationRequest' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'ArnOrUUID', 'location' => 'uri', 'locationName' => 'ApplicationIdentifier', ], 'Name' => [ 'shape' => 'ApplicationName', ], 'Description' => [ 'shape' => 'Description', ], 'ApplicationSourceConfig' => [ 'shape' => 'ApplicationSourceConfig', ], 'Subscriptions' => [ 'shape' => 'SubscriptionList', 'deprecated' => true, 'deprecatedMessage' => 'Subscriptions has been replaced with Permissions', ], 'Publications' => [ 'shape' => 'PublicationList', 'deprecated' => true, 'deprecatedMessage' => 'Publications has been replaced with Permissions', ], 'Permissions' => [ 'shape' => 'PermissionList', ], ], ], 'UpdateApplicationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDataIntegrationAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'DataIntegrationIdentifier', 'DataIntegrationAssociationIdentifier', 'ExecutionConfiguration', ], 'members' => [ 'DataIntegrationIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'DataIntegrationAssociationIdentifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'DataIntegrationAssociationIdentifier', ], 'ExecutionConfiguration' => [ 'shape' => 'ExecutionConfiguration', ], ], ], 'UpdateDataIntegrationAssociationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDataIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'Identifier', ], 'Name' => [ 'shape' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'UpdateDataIntegrationResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateEventIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'Name', 'location' => 'uri', 'locationName' => 'Name', ], 'Description' => [ 'shape' => 'Description', ], ], ], 'UpdateEventIntegrationResponse' => [ 'type' => 'structure', 'members' => [], ], ],];
