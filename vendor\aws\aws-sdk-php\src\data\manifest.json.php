<?php
// This file was auto-generated from sdk-root/src/data/manifest.json
return [ 'accessanalyzer' => [ 'namespace' => 'AccessAnalyzer', 'versions' => [ 'latest' => '2019-11-01', '2019-11-01' => '2019-11-01', ], 'serviceIdentifier' => 'accessanalyzer', ], 'account' => [ 'namespace' => 'Account', 'versions' => [ 'latest' => '2021-02-01', '2021-02-01' => '2021-02-01', ], 'serviceIdentifier' => 'account', ], 'acm-pca' => [ 'namespace' => 'ACMPCA', 'versions' => [ 'latest' => '2017-08-22', '2017-08-22' => '2017-08-22', ], 'serviceIdentifier' => 'acm_pca', ], 'acm' => [ 'namespace' => 'Acm', 'versions' => [ 'latest' => '2015-12-08', '2015-12-08' => '2015-12-08', ], 'serviceIdentifier' => 'acm', ], 'amp' => [ 'namespace' => 'PrometheusService', 'versions' => [ 'latest' => '2020-08-01', '2020-08-01' => '2020-08-01', ], 'serviceIdentifier' => 'amp', ], 'amplify' => [ 'namespace' => 'Amplify', 'versions' => [ 'latest' => '2017-07-25', '2017-07-25' => '2017-07-25', ], 'serviceIdentifier' => 'amplify', ], 'amplifybackend' => [ 'namespace' => 'AmplifyBackend', 'versions' => [ 'latest' => '2020-08-11', '2020-08-11' => '2020-08-11', ], 'serviceIdentifier' => 'amplifybackend', ], 'amplifyuibuilder' => [ 'namespace' => 'AmplifyUIBuilder', 'versions' => [ 'latest' => '2021-08-11', '2021-08-11' => '2021-08-11', ], 'serviceIdentifier' => 'amplifyuibuilder', ], 'apigateway' => [ 'namespace' => 'ApiGateway', 'versions' => [ 'latest' => '2015-07-09', '2015-07-09' => '2015-07-09', '2015-06-01' => '2015-07-09', ], 'serviceIdentifier' => 'api_gateway', ], 'apigatewaymanagementapi' => [ 'namespace' => 'ApiGatewayManagementApi', 'versions' => [ 'latest' => '2018-11-29', '2018-11-29' => '2018-11-29', ], 'serviceIdentifier' => 'apigatewaymanagementapi', ], 'apigatewayv2' => [ 'namespace' => 'ApiGatewayV2', 'versions' => [ 'latest' => '2018-11-29', '2018-11-29' => '2018-11-29', ], 'serviceIdentifier' => 'apigatewayv2', ], 'appconfig' => [ 'namespace' => 'AppConfig', 'versions' => [ 'latest' => '2019-10-09', '2019-10-09' => '2019-10-09', ], 'serviceIdentifier' => 'appconfig', ], 'appconfigdata' => [ 'namespace' => 'AppConfigData', 'versions' => [ 'latest' => '2021-11-11', '2021-11-11' => '2021-11-11', ], 'serviceIdentifier' => 'appconfigdata', ], 'appfabric' => [ 'namespace' => 'AppFabric', 'versions' => [ 'latest' => '2023-05-19', '2023-05-19' => '2023-05-19', ], 'serviceIdentifier' => 'appfabric', ], 'appflow' => [ 'namespace' => 'Appflow', 'versions' => [ 'latest' => '2020-08-23', '2020-08-23' => '2020-08-23', ], 'serviceIdentifier' => 'appflow', ], 'appintegrations' => [ 'namespace' => 'AppIntegrationsService', 'versions' => [ 'latest' => '2020-07-29', '2020-07-29' => '2020-07-29', ], 'serviceIdentifier' => 'appintegrations', ], 'application-autoscaling' => [ 'namespace' => 'ApplicationAutoScaling', 'versions' => [ 'latest' => '2016-02-06', '2016-02-06' => '2016-02-06', ], 'serviceIdentifier' => 'application_auto_scaling', ], 'application-insights' => [ 'namespace' => 'ApplicationInsights', 'versions' => [ 'latest' => '2018-11-25', '2018-11-25' => '2018-11-25', ], 'serviceIdentifier' => 'application_insights', ], 'application-signals' => [ 'namespace' => 'ApplicationSignals', 'versions' => [ 'latest' => '2024-04-15', '2024-04-15' => '2024-04-15', ], 'serviceIdentifier' => 'application_signals', ], 'applicationcostprofiler' => [ 'namespace' => 'ApplicationCostProfiler', 'versions' => [ 'latest' => '2020-09-10', '2020-09-10' => '2020-09-10', ], 'serviceIdentifier' => 'applicationcostprofiler', ], 'appmesh' => [ 'namespace' => 'AppMesh', 'versions' => [ 'latest' => '2019-01-25', '2019-01-25' => '2019-01-25', '2018-10-01' => '2018-10-01', ], 'serviceIdentifier' => 'app_mesh', ], 'apprunner' => [ 'namespace' => 'AppRunner', 'versions' => [ 'latest' => '2020-05-15', '2020-05-15' => '2020-05-15', ], 'serviceIdentifier' => 'apprunner', ], 'appstream' => [ 'namespace' => 'Appstream', 'versions' => [ 'latest' => '2016-12-01', '2016-12-01' => '2016-12-01', ], 'serviceIdentifier' => 'appstream', ], 'appsync' => [ 'namespace' => 'AppSync', 'versions' => [ 'latest' => '2017-07-25', '2017-07-25' => '2017-07-25', ], 'serviceIdentifier' => 'appsync', ], 'apptest' => [ 'namespace' => 'AppTest', 'versions' => [ 'latest' => '2022-12-06', '2022-12-06' => '2022-12-06', ], 'serviceIdentifier' => 'apptest', ], 'arc-zonal-shift' => [ 'namespace' => 'ARCZonalShift', 'versions' => [ 'latest' => '2022-10-30', '2022-10-30' => '2022-10-30', ], 'serviceIdentifier' => 'arc_zonal_shift', ], 'artifact' => [ 'namespace' => 'Artifact', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'artifact', ], 'athena' => [ 'namespace' => 'Athena', 'versions' => [ 'latest' => '2017-05-18', '2017-05-18' => '2017-05-18', ], 'serviceIdentifier' => 'athena', ], 'auditmanager' => [ 'namespace' => 'AuditManager', 'versions' => [ 'latest' => '2017-07-25', '2017-07-25' => '2017-07-25', ], 'serviceIdentifier' => 'auditmanager', ], 'autoscaling-plans' => [ 'namespace' => 'AutoScalingPlans', 'versions' => [ 'latest' => '2018-01-06', '2018-01-06' => '2018-01-06', ], 'serviceIdentifier' => 'auto_scaling_plans', ], 'autoscaling' => [ 'namespace' => 'AutoScaling', 'versions' => [ 'latest' => '2011-01-01', '2011-01-01' => '2011-01-01', ], 'serviceIdentifier' => 'auto_scaling', ], 'b2bi' => [ 'namespace' => 'B2bi', 'versions' => [ 'latest' => '2022-06-23', '2022-06-23' => '2022-06-23', ], 'serviceIdentifier' => 'b2bi', ], 'backup-gateway' => [ 'namespace' => 'BackupGateway', 'versions' => [ 'latest' => '2021-01-01', '2021-01-01' => '2021-01-01', ], 'serviceIdentifier' => 'backup_gateway', ], 'backup' => [ 'namespace' => 'Backup', 'versions' => [ 'latest' => '2018-11-15', '2018-11-15' => '2018-11-15', ], 'serviceIdentifier' => 'backup', ], 'backupsearch' => [ 'namespace' => 'BackupSearch', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'backupsearch', ], 'batch' => [ 'namespace' => 'Batch', 'versions' => [ 'latest' => '2016-08-10', '2016-08-10' => '2016-08-10', ], 'serviceIdentifier' => 'batch', ], 'bcm-data-exports' => [ 'namespace' => 'BCMDataExports', 'versions' => [ 'latest' => '2023-11-26', '2023-11-26' => '2023-11-26', ], 'serviceIdentifier' => 'bcm_data_exports', ], 'bcm-pricing-calculator' => [ 'namespace' => 'BCMPricingCalculator', 'versions' => [ 'latest' => '2024-06-19', '2024-06-19' => '2024-06-19', ], 'serviceIdentifier' => 'bcm_pricing_calculator', ], 'bedrock-agent-runtime' => [ 'namespace' => 'BedrockAgentRuntime', 'versions' => [ 'latest' => '2023-07-26', '2023-07-26' => '2023-07-26', ], 'serviceIdentifier' => 'bedrock_agent_runtime', ], 'bedrock-agent' => [ 'namespace' => 'BedrockAgent', 'versions' => [ 'latest' => '2023-06-05', '2023-06-05' => '2023-06-05', ], 'serviceIdentifier' => 'bedrock_agent', ], 'bedrock-data-automation-runtime' => [ 'namespace' => 'BedrockDataAutomationRuntime', 'versions' => [ 'latest' => '2024-06-13', '2024-06-13' => '2024-06-13', ], 'serviceIdentifier' => 'bedrock_data_automation_runtime', ], 'bedrock-data-automation' => [ 'namespace' => 'BedrockDataAutomation', 'versions' => [ 'latest' => '2023-07-26', '2023-07-26' => '2023-07-26', ], 'serviceIdentifier' => 'bedrock_data_automation', ], 'bedrock-runtime' => [ 'namespace' => 'BedrockRuntime', 'versions' => [ 'latest' => '2023-09-30', '2023-09-30' => '2023-09-30', ], 'serviceIdentifier' => 'bedrock_runtime', ], 'bedrock' => [ 'namespace' => 'Bedrock', 'versions' => [ 'latest' => '2023-04-20', '2023-04-20' => '2023-04-20', ], 'serviceIdentifier' => 'bedrock', ], 'billing' => [ 'namespace' => 'Billing', 'versions' => [ 'latest' => '2023-09-07', '2023-09-07' => '2023-09-07', ], 'serviceIdentifier' => 'billing', ], 'billingconductor' => [ 'namespace' => 'BillingConductor', 'versions' => [ 'latest' => '2021-07-30', '2021-07-30' => '2021-07-30', ], 'serviceIdentifier' => 'billingconductor', ], 'braket' => [ 'namespace' => 'Braket', 'versions' => [ 'latest' => '2019-09-01', '2019-09-01' => '2019-09-01', ], 'serviceIdentifier' => 'braket', ], 'budgets' => [ 'namespace' => 'Budgets', 'versions' => [ 'latest' => '2016-10-20', '2016-10-20' => '2016-10-20', ], 'serviceIdentifier' => 'budgets', ], 'ce' => [ 'namespace' => 'CostExplorer', 'versions' => [ 'latest' => '2017-10-25', '2017-10-25' => '2017-10-25', ], 'serviceIdentifier' => 'cost_explorer', ], 'chatbot' => [ 'namespace' => 'Chatbot', 'versions' => [ 'latest' => '2017-10-11', '2017-10-11' => '2017-10-11', ], 'serviceIdentifier' => 'chatbot', ], 'chime-sdk-identity' => [ 'namespace' => 'ChimeSDKIdentity', 'versions' => [ 'latest' => '2021-04-20', '2021-04-20' => '2021-04-20', ], 'serviceIdentifier' => 'chime_sdk_identity', ], 'chime-sdk-media-pipelines' => [ 'namespace' => 'ChimeSDKMediaPipelines', 'versions' => [ 'latest' => '2021-07-15', '2021-07-15' => '2021-07-15', ], 'serviceIdentifier' => 'chime_sdk_media_pipelines', ], 'chime-sdk-meetings' => [ 'namespace' => 'ChimeSDKMeetings', 'versions' => [ 'latest' => '2021-07-15', '2021-07-15' => '2021-07-15', ], 'serviceIdentifier' => 'chime_sdk_meetings', ], 'chime-sdk-messaging' => [ 'namespace' => 'ChimeSDKMessaging', 'versions' => [ 'latest' => '2021-05-15', '2021-05-15' => '2021-05-15', ], 'serviceIdentifier' => 'chime_sdk_messaging', ], 'chime-sdk-voice' => [ 'namespace' => 'ChimeSDKVoice', 'versions' => [ 'latest' => '2022-08-03', '2022-08-03' => '2022-08-03', ], 'serviceIdentifier' => 'chime_sdk_voice', ], 'chime' => [ 'namespace' => 'Chime', 'versions' => [ 'latest' => '2018-05-01', '2018-05-01' => '2018-05-01', ], 'serviceIdentifier' => 'chime', ], 'cleanrooms' => [ 'namespace' => 'CleanRooms', 'versions' => [ 'latest' => '2022-02-17', '2022-02-17' => '2022-02-17', ], 'serviceIdentifier' => 'cleanrooms', ], 'cleanroomsml' => [ 'namespace' => 'CleanRoomsML', 'versions' => [ 'latest' => '2023-09-06', '2023-09-06' => '2023-09-06', ], 'serviceIdentifier' => 'cleanroomsml', ], 'cloud9' => [ 'namespace' => 'Cloud9', 'versions' => [ 'latest' => '2017-09-23', '2017-09-23' => '2017-09-23', ], 'serviceIdentifier' => 'cloud9', ], 'cloudcontrol' => [ 'namespace' => 'CloudControlApi', 'versions' => [ 'latest' => '2021-09-30', '2021-09-30' => '2021-09-30', ], 'serviceIdentifier' => 'cloudcontrol', ], 'clouddirectory' => [ 'namespace' => 'CloudDirectory', 'versions' => [ 'latest' => '2017-01-11', '2017-01-11' => '2017-01-11', '2016-05-10' => '2016-05-10', ], 'serviceIdentifier' => 'clouddirectory', ], 'cloudformation' => [ 'namespace' => 'CloudFormation', 'versions' => [ 'latest' => '2010-05-15', '2010-05-15' => '2010-05-15', ], 'serviceIdentifier' => 'cloudformation', ], 'cloudfront-keyvaluestore' => [ 'namespace' => 'CloudFrontKeyValueStore', 'versions' => [ 'latest' => '2022-07-26', '2022-07-26' => '2022-07-26', ], 'serviceIdentifier' => 'cloudfront_keyvaluestore', ], 'cloudfront' => [ 'namespace' => 'CloudFront', 'versions' => [ 'latest' => '2020-05-31', '2020-05-31' => '2020-05-31', '2019-03-26' => '2019-03-26', '2018-11-05' => '2018-11-05', '2018-06-18' => '2018-06-18', '2017-10-30' => '2017-10-30', '2017-03-25' => '2017-03-25', '2016-11-25' => '2016-11-25', '2016-09-29' => '2016-09-29', '2016-09-07' => '2016-09-07', '2016-08-20' => '2016-08-20', '2016-08-01' => '2016-08-01', '2016-01-28' => '2016-01-28', '2016-01-13' => '2020-05-31', '2015-09-17' => '2020-05-31', '2015-07-27' => '2015-07-27', '2015-04-17' => '2015-07-27', '2014-11-06' => '2015-07-27', ], 'serviceIdentifier' => 'cloudfront', ], 'cloudhsm' => [ 'namespace' => 'CloudHsm', 'versions' => [ 'latest' => '2014-05-30', '2014-05-30' => '2014-05-30', ], 'serviceIdentifier' => 'cloudhsm', ], 'cloudhsmv2' => [ 'namespace' => 'CloudHSMV2', 'versions' => [ 'latest' => '2017-04-28', '2017-04-28' => '2017-04-28', ], 'serviceIdentifier' => 'cloudhsm_v2', ], 'cloudsearch' => [ 'namespace' => 'CloudSearch', 'versions' => [ 'latest' => '2013-01-01', '2013-01-01' => '2013-01-01', ], 'serviceIdentifier' => 'cloudsearch', ], 'cloudsearchdomain' => [ 'namespace' => 'CloudSearchDomain', 'versions' => [ 'latest' => '2013-01-01', '2013-01-01' => '2013-01-01', ], 'serviceIdentifier' => 'cloudsearch_domain', ], 'cloudtrail-data' => [ 'namespace' => 'CloudTrailData', 'versions' => [ 'latest' => '2021-08-11', '2021-08-11' => '2021-08-11', ], 'serviceIdentifier' => 'cloudtrail_data', ], 'cloudtrail' => [ 'namespace' => 'CloudTrail', 'versions' => [ 'latest' => '2013-11-01', '2013-11-01' => '2013-11-01', ], 'serviceIdentifier' => 'cloudtrail', ], 'codeartifact' => [ 'namespace' => 'CodeArtifact', 'versions' => [ 'latest' => '2018-09-22', '2018-09-22' => '2018-09-22', ], 'serviceIdentifier' => 'codeartifact', ], 'codebuild' => [ 'namespace' => 'CodeBuild', 'versions' => [ 'latest' => '2016-10-06', '2016-10-06' => '2016-10-06', ], 'serviceIdentifier' => 'codebuild', ], 'codecatalyst' => [ 'namespace' => 'CodeCatalyst', 'versions' => [ 'latest' => '2022-09-28', '2022-09-28' => '2022-09-28', ], 'serviceIdentifier' => 'codecatalyst', ], 'codecommit' => [ 'namespace' => 'CodeCommit', 'versions' => [ 'latest' => '2015-04-13', '2015-04-13' => '2015-04-13', ], 'serviceIdentifier' => 'codecommit', ], 'codeconnections' => [ 'namespace' => 'CodeConnections', 'versions' => [ 'latest' => '2023-12-01', '2023-12-01' => '2023-12-01', ], 'serviceIdentifier' => 'codeconnections', ], 'codedeploy' => [ 'namespace' => 'CodeDeploy', 'versions' => [ 'latest' => '2014-10-06', '2014-10-06' => '2014-10-06', ], 'serviceIdentifier' => 'codedeploy', ], 'codeguru-reviewer' => [ 'namespace' => 'CodeGuruReviewer', 'versions' => [ 'latest' => '2019-09-19', '2019-09-19' => '2019-09-19', ], 'serviceIdentifier' => 'codeguru_reviewer', ], 'codeguru-security' => [ 'namespace' => 'CodeGuruSecurity', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'codeguru_security', ], 'codeguruprofiler' => [ 'namespace' => 'CodeGuruProfiler', 'versions' => [ 'latest' => '2019-07-18', '2019-07-18' => '2019-07-18', ], 'serviceIdentifier' => 'codeguruprofiler', ], 'codepipeline' => [ 'namespace' => 'CodePipeline', 'versions' => [ 'latest' => '2015-07-09', '2015-07-09' => '2015-07-09', ], 'serviceIdentifier' => 'codepipeline', ], 'codestar-connections' => [ 'namespace' => 'CodeStarconnections', 'versions' => [ 'latest' => '2019-12-01', '2019-12-01' => '2019-12-01', ], 'serviceIdentifier' => 'codestar_connections', ], 'codestar-notifications' => [ 'namespace' => 'CodeStarNotifications', 'versions' => [ 'latest' => '2019-10-15', '2019-10-15' => '2019-10-15', ], 'serviceIdentifier' => 'codestar_notifications', ], 'cognito-identity' => [ 'namespace' => 'CognitoIdentity', 'versions' => [ 'latest' => '2014-06-30', '2014-06-30' => '2014-06-30', ], 'serviceIdentifier' => 'cognito_identity', ], 'cognito-idp' => [ 'namespace' => 'CognitoIdentityProvider', 'versions' => [ 'latest' => '2016-04-18', '2016-04-18' => '2016-04-18', ], 'serviceIdentifier' => 'cognito_identity_provider', ], 'cognito-sync' => [ 'namespace' => 'CognitoSync', 'versions' => [ 'latest' => '2014-06-30', '2014-06-30' => '2014-06-30', ], 'serviceIdentifier' => 'cognito_sync', ], 'comprehend' => [ 'namespace' => 'Comprehend', 'versions' => [ 'latest' => '2017-11-27', '2017-11-27' => '2017-11-27', ], 'serviceIdentifier' => 'comprehend', ], 'comprehendmedical' => [ 'namespace' => 'ComprehendMedical', 'versions' => [ 'latest' => '2018-10-30', '2018-10-30' => '2018-10-30', ], 'serviceIdentifier' => 'comprehendmedical', ], 'compute-optimizer' => [ 'namespace' => 'ComputeOptimizer', 'versions' => [ 'latest' => '2019-11-01', '2019-11-01' => '2019-11-01', ], 'serviceIdentifier' => 'compute_optimizer', ], 'config' => [ 'namespace' => 'ConfigService', 'versions' => [ 'latest' => '2014-11-12', '2014-11-12' => '2014-11-12', ], 'serviceIdentifier' => 'config_service', ], 'connect-contact-lens' => [ 'namespace' => 'ConnectContactLens', 'versions' => [ 'latest' => '2020-08-21', '2020-08-21' => '2020-08-21', ], 'serviceIdentifier' => 'connect_contact_lens', ], 'connect' => [ 'namespace' => 'Connect', 'versions' => [ 'latest' => '2017-08-08', '2017-08-08' => '2017-08-08', ], 'serviceIdentifier' => 'connect', ], 'connectcampaigns' => [ 'namespace' => 'ConnectCampaignService', 'versions' => [ 'latest' => '2021-01-30', '2021-01-30' => '2021-01-30', ], 'serviceIdentifier' => 'connectcampaigns', ], 'connectcampaignsv2' => [ 'namespace' => 'ConnectCampaignsV2', 'versions' => [ 'latest' => '2024-04-23', '2024-04-23' => '2024-04-23', ], 'serviceIdentifier' => 'connectcampaignsv2', ], 'connectcases' => [ 'namespace' => 'ConnectCases', 'versions' => [ 'latest' => '2022-10-03', '2022-10-03' => '2022-10-03', ], 'serviceIdentifier' => 'connectcases', ], 'connectparticipant' => [ 'namespace' => 'ConnectParticipant', 'versions' => [ 'latest' => '2018-09-07', '2018-09-07' => '2018-09-07', ], 'serviceIdentifier' => 'connectparticipant', ], 'controlcatalog' => [ 'namespace' => 'ControlCatalog', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'controlcatalog', ], 'controltower' => [ 'namespace' => 'ControlTower', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'controltower', ], 'cost-optimization-hub' => [ 'namespace' => 'CostOptimizationHub', 'versions' => [ 'latest' => '2022-07-26', '2022-07-26' => '2022-07-26', ], 'serviceIdentifier' => 'cost_optimization_hub', ], 'cur' => [ 'namespace' => 'CostandUsageReportService', 'versions' => [ 'latest' => '2017-01-06', '2017-01-06' => '2017-01-06', ], 'serviceIdentifier' => 'cost_and_usage_report_service', ], 'customer-profiles' => [ 'namespace' => 'CustomerProfiles', 'versions' => [ 'latest' => '2020-08-15', '2020-08-15' => '2020-08-15', ], 'serviceIdentifier' => 'customer_profiles', ], 'data.iot' => [ 'namespace' => 'IotDataPlane', 'versions' => [ 'latest' => '2015-05-28', '2015-05-28' => '2015-05-28', ], 'serviceIdentifier' => 'iot_data_plane', ], 'databrew' => [ 'namespace' => 'GlueDataBrew', 'versions' => [ 'latest' => '2017-07-25', '2017-07-25' => '2017-07-25', ], 'serviceIdentifier' => 'databrew', ], 'dataexchange' => [ 'namespace' => 'DataExchange', 'versions' => [ 'latest' => '2017-07-25', '2017-07-25' => '2017-07-25', ], 'serviceIdentifier' => 'dataexchange', ], 'datapipeline' => [ 'namespace' => 'DataPipeline', 'versions' => [ 'latest' => '2012-10-29', '2012-10-29' => '2012-10-29', ], 'serviceIdentifier' => 'data_pipeline', ], 'datasync' => [ 'namespace' => 'DataSync', 'versions' => [ 'latest' => '2018-11-09', '2018-11-09' => '2018-11-09', ], 'serviceIdentifier' => 'datasync', ], 'datazone' => [ 'namespace' => 'DataZone', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'datazone', ], 'dax' => [ 'namespace' => 'DAX', 'versions' => [ 'latest' => '2017-04-19', '2017-04-19' => '2017-04-19', ], 'serviceIdentifier' => 'dax', ], 'deadline' => [ 'namespace' => 'Deadline', 'versions' => [ 'latest' => '2023-10-12', '2023-10-12' => '2023-10-12', ], 'serviceIdentifier' => 'deadline', ], 'detective' => [ 'namespace' => 'Detective', 'versions' => [ 'latest' => '2018-10-26', '2018-10-26' => '2018-10-26', ], 'serviceIdentifier' => 'detective', ], 'devicefarm' => [ 'namespace' => 'DeviceFarm', 'versions' => [ 'latest' => '2015-06-23', '2015-06-23' => '2015-06-23', ], 'serviceIdentifier' => 'device_farm', ], 'devops-guru' => [ 'namespace' => 'DevOpsGuru', 'versions' => [ 'latest' => '2020-12-01', '2020-12-01' => '2020-12-01', ], 'serviceIdentifier' => 'devops_guru', ], 'directconnect' => [ 'namespace' => 'DirectConnect', 'versions' => [ 'latest' => '2012-10-25', '2012-10-25' => '2012-10-25', ], 'serviceIdentifier' => 'direct_connect', ], 'directory-service-data' => [ 'namespace' => 'DirectoryServiceData', 'versions' => [ 'latest' => '2023-05-31', '2023-05-31' => '2023-05-31', ], 'serviceIdentifier' => 'directory_service_data', ], 'discovery' => [ 'namespace' => 'ApplicationDiscoveryService', 'versions' => [ 'latest' => '2015-11-01', '2015-11-01' => '2015-11-01', ], 'serviceIdentifier' => 'application_discovery_service', ], 'dlm' => [ 'namespace' => 'DLM', 'versions' => [ 'latest' => '2018-01-12', '2018-01-12' => '2018-01-12', ], 'serviceIdentifier' => 'dlm', ], 'dms' => [ 'namespace' => 'DatabaseMigrationService', 'versions' => [ 'latest' => '2016-01-01', '2016-01-01' => '2016-01-01', ], 'serviceIdentifier' => 'database_migration_service', ], 'docdb-elastic' => [ 'namespace' => 'DocDBElastic', 'versions' => [ 'latest' => '2022-11-28', '2022-11-28' => '2022-11-28', ], 'serviceIdentifier' => 'docdb_elastic', ], 'docdb' => [ 'namespace' => 'DocDB', 'versions' => [ 'latest' => '2014-10-31', '2014-10-31' => '2014-10-31', ], 'serviceIdentifier' => 'docdb', ], 'drs' => [ 'namespace' => 'drs', 'versions' => [ 'latest' => '2020-02-26', '2020-02-26' => '2020-02-26', ], 'serviceIdentifier' => 'drs', ], 'ds' => [ 'namespace' => 'DirectoryService', 'versions' => [ 'latest' => '2015-04-16', '2015-04-16' => '2015-04-16', ], 'serviceIdentifier' => 'directory_service', ], 'dsql' => [ 'namespace' => 'DSQL', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'dsql', ], 'dynamodb' => [ 'namespace' => 'DynamoDb', 'versions' => [ 'latest' => '2012-08-10', '2012-08-10' => '2012-08-10', '2011-12-05' => '2011-12-05', ], 'serviceIdentifier' => 'dynamodb', ], 'ebs' => [ 'namespace' => 'EBS', 'versions' => [ 'latest' => '2019-11-02', '2019-11-02' => '2019-11-02', ], 'serviceIdentifier' => 'ebs', ], 'ec2-instance-connect' => [ 'namespace' => 'EC2InstanceConnect', 'versions' => [ 'latest' => '2018-04-02', '2018-04-02' => '2018-04-02', ], 'serviceIdentifier' => 'ec2_instance_connect', ], 'ec2' => [ 'namespace' => 'Ec2', 'versions' => [ 'latest' => '2016-11-15', '2016-11-15' => '2016-11-15', '2016-09-15' => '2016-09-15', '2016-04-01' => '2016-04-01', '2015-10-01' => '2015-10-01', '2015-04-15' => '2016-11-15', ], 'serviceIdentifier' => 'ec2', ], 'ecr-public' => [ 'namespace' => 'ECRPublic', 'versions' => [ 'latest' => '2020-10-30', '2020-10-30' => '2020-10-30', ], 'serviceIdentifier' => 'ecr_public', ], 'ecr' => [ 'namespace' => 'Ecr', 'versions' => [ 'latest' => '2015-09-21', '2015-09-21' => '2015-09-21', ], 'serviceIdentifier' => 'ecr', ], 'ecs' => [ 'namespace' => 'Ecs', 'versions' => [ 'latest' => '2014-11-13', '2014-11-13' => '2014-11-13', ], 'serviceIdentifier' => 'ecs', ], 'eks-auth' => [ 'namespace' => 'EKSAuth', 'versions' => [ 'latest' => '2023-11-26', '2023-11-26' => '2023-11-26', ], 'serviceIdentifier' => 'eks_auth', ], 'eks' => [ 'namespace' => 'EKS', 'versions' => [ 'latest' => '2017-11-01', '2017-11-01' => '2017-11-01', ], 'serviceIdentifier' => 'eks', ], 'elasticache' => [ 'namespace' => 'ElastiCache', 'versions' => [ 'latest' => '2015-02-02', '2015-02-02' => '2015-02-02', ], 'serviceIdentifier' => 'elasticache', ], 'elasticbeanstalk' => [ 'namespace' => 'ElasticBeanstalk', 'versions' => [ 'latest' => '2010-12-01', '2010-12-01' => '2010-12-01', ], 'serviceIdentifier' => 'elastic_beanstalk', ], 'elasticfilesystem' => [ 'namespace' => 'Efs', 'versions' => [ 'latest' => '2015-02-01', '2015-02-01' => '2015-02-01', ], 'serviceIdentifier' => 'efs', ], 'elasticloadbalancing' => [ 'namespace' => 'ElasticLoadBalancing', 'versions' => [ 'latest' => '2012-06-01', '2012-06-01' => '2012-06-01', ], 'serviceIdentifier' => 'elastic_load_balancing', ], 'elasticloadbalancingv2' => [ 'namespace' => 'ElasticLoadBalancingV2', 'versions' => [ 'latest' => '2015-12-01', '2015-12-01' => '2015-12-01', ], 'serviceIdentifier' => 'elastic_load_balancing_v2', ], 'elasticmapreduce' => [ 'namespace' => 'Emr', 'versions' => [ 'latest' => '2009-03-31', '2009-03-31' => '2009-03-31', ], 'serviceIdentifier' => 'emr', ], 'elastictranscoder' => [ 'namespace' => 'ElasticTranscoder', 'versions' => [ 'latest' => '2012-09-25', '2012-09-25' => '2012-09-25', ], 'serviceIdentifier' => 'elastic_transcoder', ], 'email' => [ 'namespace' => 'Ses', 'versions' => [ 'latest' => '2010-12-01', '2010-12-01' => '2010-12-01', ], 'serviceIdentifier' => 'ses', ], 'emr-containers' => [ 'namespace' => 'EMRContainers', 'versions' => [ 'latest' => '2020-10-01', '2020-10-01' => '2020-10-01', ], 'serviceIdentifier' => 'emr_containers', ], 'emr-serverless' => [ 'namespace' => 'EMRServerless', 'versions' => [ 'latest' => '2021-07-13', '2021-07-13' => '2021-07-13', ], 'serviceIdentifier' => 'emr_serverless', ], 'entitlement.marketplace' => [ 'namespace' => 'MarketplaceEntitlementService', 'versions' => [ 'latest' => '2017-01-11', '2017-01-11' => '2017-01-11', ], 'serviceIdentifier' => 'marketplace_entitlement_service', ], 'entityresolution' => [ 'namespace' => 'EntityResolution', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'entityresolution', ], 'es' => [ 'namespace' => 'ElasticsearchService', 'versions' => [ 'latest' => '2015-01-01', '2015-01-01' => '2015-01-01', ], 'serviceIdentifier' => 'elasticsearch_service', ], 'eventbridge' => [ 'namespace' => 'EventBridge', 'versions' => [ 'latest' => '2015-10-07', '2015-10-07' => '2015-10-07', ], 'serviceIdentifier' => 'eventbridge', ], 'events' => [ 'namespace' => 'CloudWatchEvents', 'versions' => [ 'latest' => '2015-10-07', '2015-10-07' => '2015-10-07', '2014-02-03' => '2015-10-07', ], 'serviceIdentifier' => 'cloudwatch_events', ], 'evidently' => [ 'namespace' => 'CloudWatchEvidently', 'versions' => [ 'latest' => '2021-02-01', '2021-02-01' => '2021-02-01', ], 'serviceIdentifier' => 'evidently', ], 'finspace-data' => [ 'namespace' => 'FinSpaceData', 'versions' => [ 'latest' => '2020-07-13', '2020-07-13' => '2020-07-13', ], 'serviceIdentifier' => 'finspace_data', ], 'finspace' => [ 'namespace' => 'finspace', 'versions' => [ 'latest' => '2021-03-12', '2021-03-12' => '2021-03-12', ], 'serviceIdentifier' => 'finspace', ], 'firehose' => [ 'namespace' => 'Firehose', 'versions' => [ 'latest' => '2015-08-04', '2015-08-04' => '2015-08-04', ], 'serviceIdentifier' => 'firehose', ], 'fis' => [ 'namespace' => 'FIS', 'versions' => [ 'latest' => '2020-12-01', '2020-12-01' => '2020-12-01', ], 'serviceIdentifier' => 'fis', ], 'fms' => [ 'namespace' => 'FMS', 'versions' => [ 'latest' => '2018-01-01', '2018-01-01' => '2018-01-01', ], 'serviceIdentifier' => 'fms', ], 'forecast' => [ 'namespace' => 'ForecastService', 'versions' => [ 'latest' => '2018-06-26', '2018-06-26' => '2018-06-26', ], 'serviceIdentifier' => 'forecast', ], 'forecastquery' => [ 'namespace' => 'ForecastQueryService', 'versions' => [ 'latest' => '2018-06-26', '2018-06-26' => '2018-06-26', ], 'serviceIdentifier' => 'forecastquery', ], 'frauddetector' => [ 'namespace' => 'FraudDetector', 'versions' => [ 'latest' => '2019-11-15', '2019-11-15' => '2019-11-15', ], 'serviceIdentifier' => 'frauddetector', ], 'freetier' => [ 'namespace' => 'FreeTier', 'versions' => [ 'latest' => '2023-09-07', '2023-09-07' => '2023-09-07', ], 'serviceIdentifier' => 'freetier', ], 'fsx' => [ 'namespace' => 'FSx', 'versions' => [ 'latest' => '2018-03-01', '2018-03-01' => '2018-03-01', ], 'serviceIdentifier' => 'fsx', ], 'gamelift' => [ 'namespace' => 'GameLift', 'versions' => [ 'latest' => '2015-10-01', '2015-10-01' => '2015-10-01', ], 'serviceIdentifier' => 'gamelift', ], 'gameliftstreams' => [ 'namespace' => 'GameLiftStreams', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'gameliftstreams', ], 'geo-maps' => [ 'namespace' => 'GeoMaps', 'versions' => [ 'latest' => '2020-11-19', '2020-11-19' => '2020-11-19', ], 'serviceIdentifier' => 'geo_maps', ], 'geo-places' => [ 'namespace' => 'GeoPlaces', 'versions' => [ 'latest' => '2020-11-19', '2020-11-19' => '2020-11-19', ], 'serviceIdentifier' => 'geo_places', ], 'geo-routes' => [ 'namespace' => 'GeoRoutes', 'versions' => [ 'latest' => '2020-11-19', '2020-11-19' => '2020-11-19', ], 'serviceIdentifier' => 'geo_routes', ], 'glacier' => [ 'namespace' => 'Glacier', 'versions' => [ 'latest' => '2012-06-01', '2012-06-01' => '2012-06-01', ], 'serviceIdentifier' => 'glacier', ], 'globalaccelerator' => [ 'namespace' => 'GlobalAccelerator', 'versions' => [ 'latest' => '2018-08-08', '2018-08-08' => '2018-08-08', ], 'serviceIdentifier' => 'global_accelerator', ], 'glue' => [ 'namespace' => 'Glue', 'versions' => [ 'latest' => '2017-03-31', '2017-03-31' => '2017-03-31', ], 'serviceIdentifier' => 'glue', ], 'grafana' => [ 'namespace' => 'ManagedGrafana', 'versions' => [ 'latest' => '2020-08-18', '2020-08-18' => '2020-08-18', ], 'serviceIdentifier' => 'grafana', ], 'greengrass' => [ 'namespace' => 'Greengrass', 'versions' => [ 'latest' => '2017-06-07', '2017-06-07' => '2017-06-07', ], 'serviceIdentifier' => 'greengrass', ], 'greengrassv2' => [ 'namespace' => 'GreengrassV2', 'versions' => [ 'latest' => '2020-11-30', '2020-11-30' => '2020-11-30', ], 'serviceIdentifier' => 'greengrassv2', ], 'groundstation' => [ 'namespace' => 'GroundStation', 'versions' => [ 'latest' => '2019-05-23', '2019-05-23' => '2019-05-23', ], 'serviceIdentifier' => 'groundstation', ], 'guardduty' => [ 'namespace' => 'GuardDuty', 'versions' => [ 'latest' => '2017-11-28', '2017-11-28' => '2017-11-28', ], 'serviceIdentifier' => 'guardduty', ], 'health' => [ 'namespace' => 'Health', 'versions' => [ 'latest' => '2016-08-04', '2016-08-04' => '2016-08-04', ], 'serviceIdentifier' => 'health', ], 'healthlake' => [ 'namespace' => 'HealthLake', 'versions' => [ 'latest' => '2017-07-01', '2017-07-01' => '2017-07-01', ], 'serviceIdentifier' => 'healthlake', ], 'iam' => [ 'namespace' => 'Iam', 'versions' => [ 'latest' => '2010-05-08', '2010-05-08' => '2010-05-08', ], 'serviceIdentifier' => 'iam', ], 'identitystore' => [ 'namespace' => 'IdentityStore', 'versions' => [ 'latest' => '2020-06-15', '2020-06-15' => '2020-06-15', ], 'serviceIdentifier' => 'identitystore', ], 'imagebuilder' => [ 'namespace' => 'imagebuilder', 'versions' => [ 'latest' => '2019-12-02', '2019-12-02' => '2019-12-02', ], 'serviceIdentifier' => 'imagebuilder', ], 'importexport' => [ 'namespace' => 'ImportExport', 'versions' => [ 'latest' => '2010-06-01', '2010-06-01' => '2010-06-01', ], 'serviceIdentifier' => 'importexport', ], 'inspector-scan' => [ 'namespace' => 'InspectorScan', 'versions' => [ 'latest' => '2023-08-08', '2023-08-08' => '2023-08-08', ], 'serviceIdentifier' => 'inspector_scan', ], 'inspector' => [ 'namespace' => 'Inspector', 'versions' => [ 'latest' => '2016-02-16', '2016-02-16' => '2016-02-16', '2015-08-18' => '2016-02-16', ], 'serviceIdentifier' => 'inspector', ], 'inspector2' => [ 'namespace' => 'Inspector2', 'versions' => [ 'latest' => '2020-06-08', '2020-06-08' => '2020-06-08', ], 'serviceIdentifier' => 'inspector2', ], 'internetmonitor' => [ 'namespace' => 'InternetMonitor', 'versions' => [ 'latest' => '2021-06-03', '2021-06-03' => '2021-06-03', ], 'serviceIdentifier' => 'internetmonitor', ], 'invoicing' => [ 'namespace' => 'Invoicing', 'versions' => [ 'latest' => '2024-12-01', '2024-12-01' => '2024-12-01', ], 'serviceIdentifier' => 'invoicing', ], 'iot-jobs-data' => [ 'namespace' => 'IoTJobsDataPlane', 'versions' => [ 'latest' => '2017-09-29', '2017-09-29' => '2017-09-29', ], 'serviceIdentifier' => 'iot_jobs_data_plane', ], 'iot-managed-integrations' => [ 'namespace' => 'IoTManagedIntegrations', 'versions' => [ 'latest' => '2025-03-03', '2025-03-03' => '2025-03-03', ], 'serviceIdentifier' => 'iot_managed_integrations', ], 'iot' => [ 'namespace' => 'Iot', 'versions' => [ 'latest' => '2015-05-28', '2015-05-28' => '2015-05-28', ], 'serviceIdentifier' => 'iot', ], 'iotanalytics' => [ 'namespace' => 'IoTAnalytics', 'versions' => [ 'latest' => '2017-11-27', '2017-11-27' => '2017-11-27', ], 'serviceIdentifier' => 'iotanalytics', ], 'iotdeviceadvisor' => [ 'namespace' => 'IoTDeviceAdvisor', 'versions' => [ 'latest' => '2020-09-18', '2020-09-18' => '2020-09-18', ], 'serviceIdentifier' => 'iotdeviceadvisor', ], 'iotevents-data' => [ 'namespace' => 'IoTEventsData', 'versions' => [ 'latest' => '2018-10-23', '2018-10-23' => '2018-10-23', ], 'serviceIdentifier' => 'iot_events_data', ], 'iotevents' => [ 'namespace' => 'IoTEvents', 'versions' => [ 'latest' => '2018-07-27', '2018-07-27' => '2018-07-27', ], 'serviceIdentifier' => 'iot_events', ], 'iotfleethub' => [ 'namespace' => 'IoTFleetHub', 'versions' => [ 'latest' => '2020-11-03', '2020-11-03' => '2020-11-03', ], 'serviceIdentifier' => 'iotfleethub', ], 'iotfleetwise' => [ 'namespace' => 'IoTFleetWise', 'versions' => [ 'latest' => '2021-06-17', '2021-06-17' => '2021-06-17', ], 'serviceIdentifier' => 'iotfleetwise', ], 'iotsecuretunneling' => [ 'namespace' => 'IoTSecureTunneling', 'versions' => [ 'latest' => '2018-10-05', '2018-10-05' => '2018-10-05', ], 'serviceIdentifier' => 'iotsecuretunneling', ], 'iotsitewise' => [ 'namespace' => 'IoTSiteWise', 'versions' => [ 'latest' => '2019-12-02', '2019-12-02' => '2019-12-02', ], 'serviceIdentifier' => 'iotsitewise', ], 'iotthingsgraph' => [ 'namespace' => 'IoTThingsGraph', 'versions' => [ 'latest' => '2018-09-06', '2018-09-06' => '2018-09-06', ], 'serviceIdentifier' => 'iotthingsgraph', ], 'iottwinmaker' => [ 'namespace' => 'IoTTwinMaker', 'versions' => [ 'latest' => '2021-11-29', '2021-11-29' => '2021-11-29', ], 'serviceIdentifier' => 'iottwinmaker', ], 'iotwireless' => [ 'namespace' => 'IoTWireless', 'versions' => [ 'latest' => '2020-11-22', '2020-11-22' => '2020-11-22', ], 'serviceIdentifier' => 'iot_wireless', ], 'ivs-realtime' => [ 'namespace' => 'IVSRealTime', 'versions' => [ 'latest' => '2020-07-14', '2020-07-14' => '2020-07-14', ], 'serviceIdentifier' => 'ivs_realtime', ], 'ivs' => [ 'namespace' => 'IVS', 'versions' => [ 'latest' => '2020-07-14', '2020-07-14' => '2020-07-14', ], 'serviceIdentifier' => 'ivs', ], 'ivschat' => [ 'namespace' => 'ivschat', 'versions' => [ 'latest' => '2020-07-14', '2020-07-14' => '2020-07-14', ], 'serviceIdentifier' => 'ivschat', ], 'kafka' => [ 'namespace' => 'Kafka', 'versions' => [ 'latest' => '2018-11-14', '2018-11-14' => '2018-11-14', ], 'serviceIdentifier' => 'kafka', ], 'kafkaconnect' => [ 'namespace' => 'KafkaConnect', 'versions' => [ 'latest' => '2021-09-14', '2021-09-14' => '2021-09-14', ], 'serviceIdentifier' => 'kafkaconnect', ], 'kendra-ranking' => [ 'namespace' => 'KendraRanking', 'versions' => [ 'latest' => '2022-10-19', '2022-10-19' => '2022-10-19', ], 'serviceIdentifier' => 'kendra_ranking', ], 'kendra' => [ 'namespace' => 'kendra', 'versions' => [ 'latest' => '2019-02-03', '2019-02-03' => '2019-02-03', ], 'serviceIdentifier' => 'kendra', ], 'keyspaces' => [ 'namespace' => 'Keyspaces', 'versions' => [ 'latest' => '2022-02-10', '2022-02-10' => '2022-02-10', ], 'serviceIdentifier' => 'keyspaces', ], 'kinesis-video-archived-media' => [ 'namespace' => 'KinesisVideoArchivedMedia', 'versions' => [ 'latest' => '2017-09-30', '2017-09-30' => '2017-09-30', ], 'serviceIdentifier' => 'kinesis_video_archived_media', ], 'kinesis-video-media' => [ 'namespace' => 'KinesisVideoMedia', 'versions' => [ 'latest' => '2017-09-30', '2017-09-30' => '2017-09-30', ], 'serviceIdentifier' => 'kinesis_video_media', ], 'kinesis-video-signaling' => [ 'namespace' => 'KinesisVideoSignalingChannels', 'versions' => [ 'latest' => '2019-12-04', '2019-12-04' => '2019-12-04', ], 'serviceIdentifier' => 'kinesis_video_signaling', ], 'kinesis-video-webrtc-storage' => [ 'namespace' => 'KinesisVideoWebRTCStorage', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'kinesis_video_webrtc_storage', ], 'kinesis' => [ 'namespace' => 'Kinesis', 'versions' => [ 'latest' => '2013-12-02', '2013-12-02' => '2013-12-02', ], 'serviceIdentifier' => 'kinesis', ], 'kinesisanalytics' => [ 'namespace' => 'KinesisAnalytics', 'versions' => [ 'latest' => '2015-08-14', '2015-08-14' => '2015-08-14', ], 'serviceIdentifier' => 'kinesis_analytics', ], 'kinesisanalyticsv2' => [ 'namespace' => 'KinesisAnalyticsV2', 'versions' => [ 'latest' => '2018-05-23', '2018-05-23' => '2018-05-23', ], 'serviceIdentifier' => 'kinesis_analytics_v2', ], 'kinesisvideo' => [ 'namespace' => 'KinesisVideo', 'versions' => [ 'latest' => '2017-09-30', '2017-09-30' => '2017-09-30', ], 'serviceIdentifier' => 'kinesis_video', ], 'kms' => [ 'namespace' => 'Kms', 'versions' => [ 'latest' => '2014-11-01', '2014-11-01' => '2014-11-01', ], 'serviceIdentifier' => 'kms', ], 'lakeformation' => [ 'namespace' => 'LakeFormation', 'versions' => [ 'latest' => '2017-03-31', '2017-03-31' => '2017-03-31', ], 'serviceIdentifier' => 'lakeformation', ], 'lambda' => [ 'namespace' => 'Lambda', 'versions' => [ 'latest' => '2015-03-31', '2015-03-31' => '2015-03-31', ], 'serviceIdentifier' => 'lambda', ], 'launch-wizard' => [ 'namespace' => 'LaunchWizard', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'launch_wizard', ], 'lex-models' => [ 'namespace' => 'LexModelBuildingService', 'versions' => [ 'latest' => '2017-04-19', '2017-04-19' => '2017-04-19', ], 'serviceIdentifier' => 'lex_model_building_service', ], 'license-manager-linux-subscriptions' => [ 'namespace' => 'LicenseManagerLinuxSubscriptions', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'license_manager_linux_subscriptions', ], 'license-manager-user-subscriptions' => [ 'namespace' => 'LicenseManagerUserSubscriptions', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'license_manager_user_subscriptions', ], 'license-manager' => [ 'namespace' => 'LicenseManager', 'versions' => [ 'latest' => '2018-08-01', '2018-08-01' => '2018-08-01', ], 'serviceIdentifier' => 'license_manager', ], 'lightsail' => [ 'namespace' => 'Lightsail', 'versions' => [ 'latest' => '2016-11-28', '2016-11-28' => '2016-11-28', ], 'serviceIdentifier' => 'lightsail', ], 'location' => [ 'namespace' => 'LocationService', 'versions' => [ 'latest' => '2020-11-19', '2020-11-19' => '2020-11-19', ], 'serviceIdentifier' => 'location', ], 'logs' => [ 'namespace' => 'CloudWatchLogs', 'versions' => [ 'latest' => '2014-03-28', '2014-03-28' => '2014-03-28', ], 'serviceIdentifier' => 'cloudwatch_logs', ], 'lookoutequipment' => [ 'namespace' => 'LookoutEquipment', 'versions' => [ 'latest' => '2020-12-15', '2020-12-15' => '2020-12-15', ], 'serviceIdentifier' => 'lookoutequipment', ], 'lookoutmetrics' => [ 'namespace' => 'LookoutMetrics', 'versions' => [ 'latest' => '2017-07-25', '2017-07-25' => '2017-07-25', ], 'serviceIdentifier' => 'lookoutmetrics', ], 'lookoutvision' => [ 'namespace' => 'LookoutforVision', 'versions' => [ 'latest' => '2020-11-20', '2020-11-20' => '2020-11-20', ], 'serviceIdentifier' => 'lookoutvision', ], 'm2' => [ 'namespace' => 'MainframeModernization', 'versions' => [ 'latest' => '2021-04-28', '2021-04-28' => '2021-04-28', ], 'serviceIdentifier' => 'm2', ], 'machinelearning' => [ 'namespace' => 'MachineLearning', 'versions' => [ 'latest' => '2014-12-12', '2014-12-12' => '2014-12-12', ], 'serviceIdentifier' => 'machine_learning', ], 'macie2' => [ 'namespace' => 'Macie2', 'versions' => [ 'latest' => '2020-01-01', '2020-01-01' => '2020-01-01', ], 'serviceIdentifier' => 'macie2', ], 'mailmanager' => [ 'namespace' => 'MailManager', 'versions' => [ 'latest' => '2023-10-17', '2023-10-17' => '2023-10-17', ], 'serviceIdentifier' => 'mailmanager', ], 'managedblockchain-query' => [ 'namespace' => 'ManagedBlockchainQuery', 'versions' => [ 'latest' => '2023-05-04', '2023-05-04' => '2023-05-04', ], 'serviceIdentifier' => 'managedblockchain_query', ], 'managedblockchain' => [ 'namespace' => 'ManagedBlockchain', 'versions' => [ 'latest' => '2018-09-24', '2018-09-24' => '2018-09-24', ], 'serviceIdentifier' => 'managedblockchain', ], 'marketplace-agreement' => [ 'namespace' => 'MarketplaceAgreement', 'versions' => [ 'latest' => '2020-03-01', '2020-03-01' => '2020-03-01', ], 'serviceIdentifier' => 'marketplace_agreement', ], 'marketplace-catalog' => [ 'namespace' => 'MarketplaceCatalog', 'versions' => [ 'latest' => '2018-09-17', '2018-09-17' => '2018-09-17', ], 'serviceIdentifier' => 'marketplace_catalog', ], 'marketplace-deployment' => [ 'namespace' => 'MarketplaceDeployment', 'versions' => [ 'latest' => '2023-01-25', '2023-01-25' => '2023-01-25', ], 'serviceIdentifier' => 'marketplace_deployment', ], 'marketplace-reporting' => [ 'namespace' => 'MarketplaceReporting', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'marketplace_reporting', ], 'marketplacecommerceanalytics' => [ 'namespace' => 'MarketplaceCommerceAnalytics', 'versions' => [ 'latest' => '2015-07-01', '2015-07-01' => '2015-07-01', ], 'serviceIdentifier' => 'marketplace_commerce_analytics', ], 'mediaconnect' => [ 'namespace' => 'MediaConnect', 'versions' => [ 'latest' => '2018-11-14', '2018-11-14' => '2018-11-14', ], 'serviceIdentifier' => 'mediaconnect', ], 'mediaconvert' => [ 'namespace' => 'MediaConvert', 'versions' => [ 'latest' => '2017-08-29', '2017-08-29' => '2017-08-29', ], 'serviceIdentifier' => 'mediaconvert', ], 'medialive' => [ 'namespace' => 'MediaLive', 'versions' => [ 'latest' => '2017-10-14', '2017-10-14' => '2017-10-14', ], 'serviceIdentifier' => 'medialive', ], 'mediapackage-vod' => [ 'namespace' => 'MediaPackageVod', 'versions' => [ 'latest' => '2018-11-07', '2018-11-07' => '2018-11-07', ], 'serviceIdentifier' => 'mediapackage_vod', ], 'mediapackage' => [ 'namespace' => 'MediaPackage', 'versions' => [ 'latest' => '2017-10-12', '2017-10-12' => '2017-10-12', ], 'serviceIdentifier' => 'mediapackage', ], 'mediapackagev2' => [ 'namespace' => 'MediaPackageV2', 'versions' => [ 'latest' => '2022-12-25', '2022-12-25' => '2022-12-25', ], 'serviceIdentifier' => 'mediapackagev2', ], 'mediastore-data' => [ 'namespace' => 'MediaStoreData', 'versions' => [ 'latest' => '2017-09-01', '2017-09-01' => '2017-09-01', ], 'serviceIdentifier' => 'mediastore_data', ], 'mediastore' => [ 'namespace' => 'MediaStore', 'versions' => [ 'latest' => '2017-09-01', '2017-09-01' => '2017-09-01', ], 'serviceIdentifier' => 'mediastore', ], 'mediatailor' => [ 'namespace' => 'MediaTailor', 'versions' => [ 'latest' => '2018-04-23', '2018-04-23' => '2018-04-23', ], 'serviceIdentifier' => 'mediatailor', ], 'medical-imaging' => [ 'namespace' => 'MedicalImaging', 'versions' => [ 'latest' => '2023-07-19', '2023-07-19' => '2023-07-19', ], 'serviceIdentifier' => 'medical_imaging', ], 'memorydb' => [ 'namespace' => 'MemoryDB', 'versions' => [ 'latest' => '2021-01-01', '2021-01-01' => '2021-01-01', ], 'serviceIdentifier' => 'memorydb', ], 'metering.marketplace' => [ 'namespace' => 'MarketplaceMetering', 'versions' => [ 'latest' => '2016-01-14', '2016-01-14' => '2016-01-14', ], 'serviceIdentifier' => 'marketplace_metering', ], 'mgh' => [ 'namespace' => 'MigrationHub', 'versions' => [ 'latest' => '2017-05-31', '2017-05-31' => '2017-05-31', ], 'serviceIdentifier' => 'migration_hub', ], 'mgn' => [ 'namespace' => 'mgn', 'versions' => [ 'latest' => '2020-02-26', '2020-02-26' => '2020-02-26', ], 'serviceIdentifier' => 'mgn', ], 'migration-hub-refactor-spaces' => [ 'namespace' => 'MigrationHubRefactorSpaces', 'versions' => [ 'latest' => '2021-10-26', '2021-10-26' => '2021-10-26', ], 'serviceIdentifier' => 'migration_hub_refactor_spaces', ], 'migrationhub-config' => [ 'namespace' => 'MigrationHubConfig', 'versions' => [ 'latest' => '2019-06-30', '2019-06-30' => '2019-06-30', ], 'serviceIdentifier' => 'migrationhub_config', ], 'migrationhuborchestrator' => [ 'namespace' => 'MigrationHubOrchestrator', 'versions' => [ 'latest' => '2021-08-28', '2021-08-28' => '2021-08-28', ], 'serviceIdentifier' => 'migrationhuborchestrator', ], 'migrationhubstrategy' => [ 'namespace' => 'MigrationHubStrategyRecommendations', 'versions' => [ 'latest' => '2020-02-19', '2020-02-19' => '2020-02-19', ], 'serviceIdentifier' => 'migrationhubstrategy', ], 'models.lex.v2' => [ 'namespace' => 'LexModelsV2', 'versions' => [ 'latest' => '2020-08-07', '2020-08-07' => '2020-08-07', ], 'serviceIdentifier' => 'lex_models_v2', ], 'monitoring' => [ 'namespace' => 'CloudWatch', 'versions' => [ 'latest' => '2010-08-01', '2010-08-01' => '2010-08-01', ], 'serviceIdentifier' => 'cloudwatch', ], 'mq' => [ 'namespace' => 'MQ', 'versions' => [ 'latest' => '2017-11-27', '2017-11-27' => '2017-11-27', ], 'serviceIdentifier' => 'mq', ], 'mturk-requester' => [ 'namespace' => 'MTurk', 'versions' => [ 'latest' => '2017-01-17', '2017-01-17' => '2017-01-17', ], 'serviceIdentifier' => 'mturk', ], 'mwaa' => [ 'namespace' => 'MWAA', 'versions' => [ 'latest' => '2020-07-01', '2020-07-01' => '2020-07-01', ], 'serviceIdentifier' => 'mwaa', ], 'neptune-graph' => [ 'namespace' => 'NeptuneGraph', 'versions' => [ 'latest' => '2023-11-29', '2023-11-29' => '2023-11-29', ], 'serviceIdentifier' => 'neptune_graph', ], 'neptune' => [ 'namespace' => 'Neptune', 'versions' => [ 'latest' => '2014-10-31', '2014-10-31' => '2014-10-31', ], 'serviceIdentifier' => 'neptune', ], 'neptunedata' => [ 'namespace' => 'Neptunedata', 'versions' => [ 'latest' => '2023-08-01', '2023-08-01' => '2023-08-01', ], 'serviceIdentifier' => 'neptunedata', ], 'network-firewall' => [ 'namespace' => 'NetworkFirewall', 'versions' => [ 'latest' => '2020-11-12', '2020-11-12' => '2020-11-12', ], 'serviceIdentifier' => 'network_firewall', ], 'networkflowmonitor' => [ 'namespace' => 'NetworkFlowMonitor', 'versions' => [ 'latest' => '2023-04-19', '2023-04-19' => '2023-04-19', ], 'serviceIdentifier' => 'networkflowmonitor', ], 'networkmanager' => [ 'namespace' => 'NetworkManager', 'versions' => [ 'latest' => '2019-07-05', '2019-07-05' => '2019-07-05', ], 'serviceIdentifier' => 'networkmanager', ], 'networkmonitor' => [ 'namespace' => 'NetworkMonitor', 'versions' => [ 'latest' => '2023-08-01', '2023-08-01' => '2023-08-01', ], 'serviceIdentifier' => 'networkmonitor', ], 'notifications' => [ 'namespace' => 'Notifications', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'notifications', ], 'notificationscontacts' => [ 'namespace' => 'NotificationsContacts', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'notificationscontacts', ], 'oam' => [ 'namespace' => 'OAM', 'versions' => [ 'latest' => '2022-06-10', '2022-06-10' => '2022-06-10', ], 'serviceIdentifier' => 'oam', ], 'observabilityadmin' => [ 'namespace' => 'ObservabilityAdmin', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'observabilityadmin', ], 'omics' => [ 'namespace' => 'Omics', 'versions' => [ 'latest' => '2022-11-28', '2022-11-28' => '2022-11-28', ], 'serviceIdentifier' => 'omics', ], 'opensearch' => [ 'namespace' => 'OpenSearchService', 'versions' => [ 'latest' => '2021-01-01', '2021-01-01' => '2021-01-01', ], 'serviceIdentifier' => 'opensearch', ], 'opensearchserverless' => [ 'namespace' => 'OpenSearchServerless', 'versions' => [ 'latest' => '2021-11-01', '2021-11-01' => '2021-11-01', ], 'serviceIdentifier' => 'opensearchserverless', ], 'opsworks' => [ 'namespace' => 'OpsWorks', 'versions' => [ 'latest' => '2013-02-18', '2013-02-18' => '2013-02-18', ], 'serviceIdentifier' => 'opsworks', ], 'opsworkscm' => [ 'namespace' => 'OpsWorksCM', 'versions' => [ 'latest' => '2016-11-01', '2016-11-01' => '2016-11-01', ], 'serviceIdentifier' => 'opsworkscm', ], 'organizations' => [ 'namespace' => 'Organizations', 'versions' => [ 'latest' => '2016-11-28', '2016-11-28' => '2016-11-28', ], 'serviceIdentifier' => 'organizations', ], 'osis' => [ 'namespace' => 'OSIS', 'versions' => [ 'latest' => '2022-01-01', '2022-01-01' => '2022-01-01', ], 'serviceIdentifier' => 'osis', ], 'outposts' => [ 'namespace' => 'Outposts', 'versions' => [ 'latest' => '2019-12-03', '2019-12-03' => '2019-12-03', ], 'serviceIdentifier' => 'outposts', ], 'panorama' => [ 'namespace' => 'Panorama', 'versions' => [ 'latest' => '2019-07-24', '2019-07-24' => '2019-07-24', ], 'serviceIdentifier' => 'panorama', ], 'partnercentral-selling' => [ 'namespace' => 'PartnerCentralSelling', 'versions' => [ 'latest' => '2022-07-26', '2022-07-26' => '2022-07-26', ], 'serviceIdentifier' => 'partnercentral_selling', ], 'payment-cryptography-data' => [ 'namespace' => 'PaymentCryptographyData', 'versions' => [ 'latest' => '2022-02-03', '2022-02-03' => '2022-02-03', ], 'serviceIdentifier' => 'payment_cryptography_data', ], 'payment-cryptography' => [ 'namespace' => 'PaymentCryptography', 'versions' => [ 'latest' => '2021-09-14', '2021-09-14' => '2021-09-14', ], 'serviceIdentifier' => 'payment_cryptography', ], 'pca-connector-ad' => [ 'namespace' => 'PcaConnectorAd', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'pca_connector_ad', ], 'pca-connector-scep' => [ 'namespace' => 'PcaConnectorScep', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'pca_connector_scep', ], 'pcs' => [ 'namespace' => 'PCS', 'versions' => [ 'latest' => '2023-02-10', '2023-02-10' => '2023-02-10', ], 'serviceIdentifier' => 'pcs', ], 'personalize-events' => [ 'namespace' => 'PersonalizeEvents', 'versions' => [ 'latest' => '2018-03-22', '2018-03-22' => '2018-03-22', ], 'serviceIdentifier' => 'personalize_events', ], 'personalize-runtime' => [ 'namespace' => 'PersonalizeRuntime', 'versions' => [ 'latest' => '2018-05-22', '2018-05-22' => '2018-05-22', ], 'serviceIdentifier' => 'personalize_runtime', ], 'personalize' => [ 'namespace' => 'Personalize', 'versions' => [ 'latest' => '2018-05-22', '2018-05-22' => '2018-05-22', ], 'serviceIdentifier' => 'personalize', ], 'pi' => [ 'namespace' => 'PI', 'versions' => [ 'latest' => '2018-02-27', '2018-02-27' => '2018-02-27', ], 'serviceIdentifier' => 'pi', ], 'pinpoint-email' => [ 'namespace' => 'PinpointEmail', 'versions' => [ 'latest' => '2018-07-26', '2018-07-26' => '2018-07-26', ], 'serviceIdentifier' => 'pinpoint_email', ], 'pinpoint-sms-voice-v2' => [ 'namespace' => 'PinpointSMSVoiceV2', 'versions' => [ 'latest' => '2022-03-31', '2022-03-31' => '2022-03-31', ], 'serviceIdentifier' => 'pinpoint_sms_voice_v2', ], 'pinpoint' => [ 'namespace' => 'Pinpoint', 'versions' => [ 'latest' => '2016-12-01', '2016-12-01' => '2016-12-01', ], 'serviceIdentifier' => 'pinpoint', ], 'pipes' => [ 'namespace' => 'Pipes', 'versions' => [ 'latest' => '2015-10-07', '2015-10-07' => '2015-10-07', ], 'serviceIdentifier' => 'pipes', ], 'polly' => [ 'namespace' => 'Polly', 'versions' => [ 'latest' => '2016-06-10', '2016-06-10' => '2016-06-10', ], 'serviceIdentifier' => 'polly', ], 'pricing' => [ 'namespace' => 'Pricing', 'versions' => [ 'latest' => '2017-10-15', '2017-10-15' => '2017-10-15', ], 'serviceIdentifier' => 'pricing', ], 'proton' => [ 'namespace' => 'Proton', 'versions' => [ 'latest' => '2020-07-20', '2020-07-20' => '2020-07-20', ], 'serviceIdentifier' => 'proton', ], 'qapps' => [ 'namespace' => 'QApps', 'versions' => [ 'latest' => '2023-11-27', '2023-11-27' => '2023-11-27', ], 'serviceIdentifier' => 'qapps', ], 'qbusiness' => [ 'namespace' => 'QBusiness', 'versions' => [ 'latest' => '2023-11-27', '2023-11-27' => '2023-11-27', ], 'serviceIdentifier' => 'qbusiness', ], 'qconnect' => [ 'namespace' => 'QConnect', 'versions' => [ 'latest' => '2020-10-19', '2020-10-19' => '2020-10-19', ], 'serviceIdentifier' => 'qconnect', ], 'qldb-session' => [ 'namespace' => 'QLDBSession', 'versions' => [ 'latest' => '2019-07-11', '2019-07-11' => '2019-07-11', ], 'serviceIdentifier' => 'qldb_session', ], 'qldb' => [ 'namespace' => 'QLDB', 'versions' => [ 'latest' => '2019-01-02', '2019-01-02' => '2019-01-02', ], 'serviceIdentifier' => 'qldb', ], 'quicksight' => [ 'namespace' => 'QuickSight', 'versions' => [ 'latest' => '2018-04-01', '2018-04-01' => '2018-04-01', ], 'serviceIdentifier' => 'quicksight', ], 'ram' => [ 'namespace' => 'RAM', 'versions' => [ 'latest' => '2018-01-04', '2018-01-04' => '2018-01-04', ], 'serviceIdentifier' => 'ram', ], 'rbin' => [ 'namespace' => 'RecycleBin', 'versions' => [ 'latest' => '2021-06-15', '2021-06-15' => '2021-06-15', ], 'serviceIdentifier' => 'rbin', ], 'rds-data' => [ 'namespace' => 'RDSDataService', 'versions' => [ 'latest' => '2018-08-01', '2018-08-01' => '2018-08-01', ], 'serviceIdentifier' => 'rds_data', ], 'rds' => [ 'namespace' => 'Rds', 'versions' => [ 'latest' => '2014-10-31', '2014-10-31' => '2014-10-31', '2014-09-01' => '2014-09-01', ], 'serviceIdentifier' => 'rds', ], 'redshift-data' => [ 'namespace' => 'RedshiftDataAPIService', 'versions' => [ 'latest' => '2019-12-20', '2019-12-20' => '2019-12-20', ], 'serviceIdentifier' => 'redshift_data', ], 'redshift-serverless' => [ 'namespace' => 'RedshiftServerless', 'versions' => [ 'latest' => '2021-04-21', '2021-04-21' => '2021-04-21', ], 'serviceIdentifier' => 'redshift_serverless', ], 'redshift' => [ 'namespace' => 'Redshift', 'versions' => [ 'latest' => '2012-12-01', '2012-12-01' => '2012-12-01', ], 'serviceIdentifier' => 'redshift', ], 'rekognition' => [ 'namespace' => 'Rekognition', 'versions' => [ 'latest' => '2016-06-27', '2016-06-27' => '2016-06-27', ], 'serviceIdentifier' => 'rekognition', ], 'repostspace' => [ 'namespace' => 'Repostspace', 'versions' => [ 'latest' => '2022-05-13', '2022-05-13' => '2022-05-13', ], 'serviceIdentifier' => 'repostspace', ], 'resiliencehub' => [ 'namespace' => 'ResilienceHub', 'versions' => [ 'latest' => '2020-04-30', '2020-04-30' => '2020-04-30', ], 'serviceIdentifier' => 'resiliencehub', ], 'resource-explorer-2' => [ 'namespace' => 'ResourceExplorer2', 'versions' => [ 'latest' => '2022-07-28', '2022-07-28' => '2022-07-28', ], 'serviceIdentifier' => 'resource_explorer_2', ], 'resource-groups' => [ 'namespace' => 'ResourceGroups', 'versions' => [ 'latest' => '2017-11-27', '2017-11-27' => '2017-11-27', ], 'serviceIdentifier' => 'resource_groups', ], 'resourcegroupstaggingapi' => [ 'namespace' => 'ResourceGroupsTaggingAPI', 'versions' => [ 'latest' => '2017-01-26', '2017-01-26' => '2017-01-26', ], 'serviceIdentifier' => 'resource_groups_tagging_api', ], 'robomaker' => [ 'namespace' => 'RoboMaker', 'versions' => [ 'latest' => '2018-06-29', '2018-06-29' => '2018-06-29', ], 'serviceIdentifier' => 'robomaker', ], 'rolesanywhere' => [ 'namespace' => 'RolesAnywhere', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'rolesanywhere', ], 'route53-recovery-cluster' => [ 'namespace' => 'Route53RecoveryCluster', 'versions' => [ 'latest' => '2019-12-02', '2019-12-02' => '2019-12-02', ], 'serviceIdentifier' => 'route53_recovery_cluster', ], 'route53-recovery-control-config' => [ 'namespace' => 'Route53RecoveryControlConfig', 'versions' => [ 'latest' => '2020-11-02', '2020-11-02' => '2020-11-02', ], 'serviceIdentifier' => 'route53_recovery_control_config', ], 'route53-recovery-readiness' => [ 'namespace' => 'Route53RecoveryReadiness', 'versions' => [ 'latest' => '2019-12-02', '2019-12-02' => '2019-12-02', ], 'serviceIdentifier' => 'route53_recovery_readiness', ], 'route53' => [ 'namespace' => 'Route53', 'versions' => [ 'latest' => '2013-04-01', '2013-04-01' => '2013-04-01', ], 'serviceIdentifier' => 'route_53', ], 'route53domains' => [ 'namespace' => 'Route53Domains', 'versions' => [ 'latest' => '2014-05-15', '2014-05-15' => '2014-05-15', ], 'serviceIdentifier' => 'route_53_domains', ], 'route53profiles' => [ 'namespace' => 'Route53Profiles', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'route53profiles', ], 'route53resolver' => [ 'namespace' => 'Route53Resolver', 'versions' => [ 'latest' => '2018-04-01', '2018-04-01' => '2018-04-01', ], 'serviceIdentifier' => 'route53resolver', ], 'rum' => [ 'namespace' => 'CloudWatchRUM', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'rum', ], 'runtime.lex.v2' => [ 'namespace' => 'LexRuntimeV2', 'versions' => [ 'latest' => '2020-08-07', '2020-08-07' => '2020-08-07', ], 'serviceIdentifier' => 'lex_runtime_v2', ], 'runtime.lex' => [ 'namespace' => 'LexRuntimeService', 'versions' => [ 'latest' => '2016-11-28', '2016-11-28' => '2016-11-28', ], 'serviceIdentifier' => 'lex_runtime_service', ], 'runtime.sagemaker' => [ 'namespace' => 'SageMakerRuntime', 'versions' => [ 'latest' => '2017-05-13', '2017-05-13' => '2017-05-13', ], 'serviceIdentifier' => 'sagemaker_runtime', ], 's3' => [ 'namespace' => 'S3', 'versions' => [ 'latest' => '2006-03-01', '2006-03-01' => '2006-03-01', ], 'serviceIdentifier' => 's3', ], 's3control' => [ 'namespace' => 'S3Control', 'versions' => [ 'latest' => '2018-08-20', '2018-08-20' => '2018-08-20', ], 'serviceIdentifier' => 's3_control', ], 's3outposts' => [ 'namespace' => 'S3Outposts', 'versions' => [ 'latest' => '2017-07-25', '2017-07-25' => '2017-07-25', ], 'serviceIdentifier' => 's3outposts', ], 's3tables' => [ 'namespace' => 'S3Tables', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 's3tables', ], 'sagemaker-a2i-runtime' => [ 'namespace' => 'AugmentedAIRuntime', 'versions' => [ 'latest' => '2019-11-07', '2019-11-07' => '2019-11-07', ], 'serviceIdentifier' => 'sagemaker_a2i_runtime', ], 'sagemaker-edge' => [ 'namespace' => 'SagemakerEdgeManager', 'versions' => [ 'latest' => '2020-09-23', '2020-09-23' => '2020-09-23', ], 'serviceIdentifier' => 'sagemaker_edge', ], 'sagemaker-featurestore-runtime' => [ 'namespace' => 'SageMakerFeatureStoreRuntime', 'versions' => [ 'latest' => '2020-07-01', '2020-07-01' => '2020-07-01', ], 'serviceIdentifier' => 'sagemaker_featurestore_runtime', ], 'sagemaker-geospatial' => [ 'namespace' => 'SageMakerGeospatial', 'versions' => [ 'latest' => '2020-05-27', '2020-05-27' => '2020-05-27', ], 'serviceIdentifier' => 'sagemaker_geospatial', ], 'sagemaker-metrics' => [ 'namespace' => 'SageMakerMetrics', 'versions' => [ 'latest' => '2022-09-30', '2022-09-30' => '2022-09-30', ], 'serviceIdentifier' => 'sagemaker_metrics', ], 'sagemaker' => [ 'namespace' => 'SageMaker', 'versions' => [ 'latest' => '2017-07-24', '2017-07-24' => '2017-07-24', ], 'serviceIdentifier' => 'sagemaker', ], 'savingsplans' => [ 'namespace' => 'SavingsPlans', 'versions' => [ 'latest' => '2019-06-28', '2019-06-28' => '2019-06-28', ], 'serviceIdentifier' => 'savingsplans', ], 'scheduler' => [ 'namespace' => 'Scheduler', 'versions' => [ 'latest' => '2021-06-30', '2021-06-30' => '2021-06-30', ], 'serviceIdentifier' => 'scheduler', ], 'schemas' => [ 'namespace' => 'Schemas', 'versions' => [ 'latest' => '2019-12-02', '2019-12-02' => '2019-12-02', ], 'serviceIdentifier' => 'schemas', ], 'secretsmanager' => [ 'namespace' => 'SecretsManager', 'versions' => [ 'latest' => '2017-10-17', '2017-10-17' => '2017-10-17', ], 'serviceIdentifier' => 'secrets_manager', ], 'security-ir' => [ 'namespace' => 'SecurityIR', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'security_ir', ], 'securityhub' => [ 'namespace' => 'SecurityHub', 'versions' => [ 'latest' => '2018-10-26', '2018-10-26' => '2018-10-26', ], 'serviceIdentifier' => 'securityhub', ], 'securitylake' => [ 'namespace' => 'SecurityLake', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'securitylake', ], 'serverlessrepo' => [ 'namespace' => 'ServerlessApplicationRepository', 'versions' => [ 'latest' => '2017-09-08', '2017-09-08' => '2017-09-08', ], 'serviceIdentifier' => 'serverlessapplicationrepository', ], 'service-quotas' => [ 'namespace' => 'ServiceQuotas', 'versions' => [ 'latest' => '2019-06-24', '2019-06-24' => '2019-06-24', ], 'serviceIdentifier' => 'service_quotas', ], 'servicecatalog-appregistry' => [ 'namespace' => 'AppRegistry', 'versions' => [ 'latest' => '2020-06-24', '2020-06-24' => '2020-06-24', ], 'serviceIdentifier' => 'service_catalog_appregistry', ], 'servicecatalog' => [ 'namespace' => 'ServiceCatalog', 'versions' => [ 'latest' => '2015-12-10', '2015-12-10' => '2015-12-10', ], 'serviceIdentifier' => 'service_catalog', ], 'servicediscovery' => [ 'namespace' => 'ServiceDiscovery', 'versions' => [ 'latest' => '2017-03-14', '2017-03-14' => '2017-03-14', ], 'serviceIdentifier' => 'servicediscovery', ], 'sesv2' => [ 'namespace' => 'SesV2', 'versions' => [ 'latest' => '2019-09-27', '2019-09-27' => '2019-09-27', ], 'serviceIdentifier' => 'sesv2', ], 'shield' => [ 'namespace' => 'Shield', 'versions' => [ 'latest' => '2016-06-02', '2016-06-02' => '2016-06-02', ], 'serviceIdentifier' => 'shield', ], 'signer' => [ 'namespace' => 'signer', 'versions' => [ 'latest' => '2017-08-25', '2017-08-25' => '2017-08-25', ], 'serviceIdentifier' => 'signer', ], 'simspaceweaver' => [ 'namespace' => 'SimSpaceWeaver', 'versions' => [ 'latest' => '2022-10-28', '2022-10-28' => '2022-10-28', ], 'serviceIdentifier' => 'simspaceweaver', ], 'sms-voice' => [ 'namespace' => 'PinpointSMSVoice', 'versions' => [ 'latest' => '2018-09-05', '2018-09-05' => '2018-09-05', ], 'serviceIdentifier' => 'pinpoint_sms_voice', ], 'sms' => [ 'namespace' => 'Sms', 'versions' => [ 'latest' => '2016-10-24', '2016-10-24' => '2016-10-24', ], 'serviceIdentifier' => 'sms', ], 'snow-device-management' => [ 'namespace' => 'SnowDeviceManagement', 'versions' => [ 'latest' => '2021-08-04', '2021-08-04' => '2021-08-04', ], 'serviceIdentifier' => 'snow_device_management', ], 'snowball' => [ 'namespace' => 'SnowBall', 'versions' => [ 'latest' => '2016-06-30', '2016-06-30' => '2016-06-30', ], 'serviceIdentifier' => 'snowball', ], 'sns' => [ 'namespace' => 'Sns', 'versions' => [ 'latest' => '2010-03-31', '2010-03-31' => '2010-03-31', ], 'serviceIdentifier' => 'sns', ], 'socialmessaging' => [ 'namespace' => 'SocialMessaging', 'versions' => [ 'latest' => '2024-01-01', '2024-01-01' => '2024-01-01', ], 'serviceIdentifier' => 'socialmessaging', ], 'sqs' => [ 'namespace' => 'Sqs', 'versions' => [ 'latest' => '2012-11-05', '2012-11-05' => '2012-11-05', ], 'serviceIdentifier' => 'sqs', ], 'ssm-contacts' => [ 'namespace' => 'SSMContacts', 'versions' => [ 'latest' => '2021-05-03', '2021-05-03' => '2021-05-03', ], 'serviceIdentifier' => 'ssm_contacts', ], 'ssm-guiconnect' => [ 'namespace' => 'SSMGuiConnect', 'versions' => [ 'latest' => '2021-05-01', '2021-05-01' => '2021-05-01', ], 'serviceIdentifier' => 'ssm_guiconnect', ], 'ssm-incidents' => [ 'namespace' => 'SSMIncidents', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'ssm_incidents', ], 'ssm-quicksetup' => [ 'namespace' => 'SSMQuickSetup', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'ssm_quicksetup', ], 'ssm-sap' => [ 'namespace' => 'SsmSap', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'ssm_sap', ], 'ssm' => [ 'namespace' => 'Ssm', 'versions' => [ 'latest' => '2014-11-06', '2014-11-06' => '2014-11-06', ], 'serviceIdentifier' => 'ssm', ], 'sso-admin' => [ 'namespace' => 'SSOAdmin', 'versions' => [ 'latest' => '2020-07-20', '2020-07-20' => '2020-07-20', ], 'serviceIdentifier' => 'sso_admin', ], 'sso-oidc' => [ 'namespace' => 'SSOOIDC', 'versions' => [ 'latest' => '2019-06-10', '2019-06-10' => '2019-06-10', ], 'serviceIdentifier' => 'sso_oidc', ], 'sso' => [ 'namespace' => 'SSO', 'versions' => [ 'latest' => '2019-06-10', '2019-06-10' => '2019-06-10', ], 'serviceIdentifier' => 'sso', ], 'states' => [ 'namespace' => 'Sfn', 'versions' => [ 'latest' => '2016-11-23', '2016-11-23' => '2016-11-23', ], 'serviceIdentifier' => 'sfn', ], 'storagegateway' => [ 'namespace' => 'StorageGateway', 'versions' => [ 'latest' => '2013-06-30', '2013-06-30' => '2013-06-30', ], 'serviceIdentifier' => 'storage_gateway', ], 'streams.dynamodb' => [ 'namespace' => 'DynamoDbStreams', 'versions' => [ 'latest' => '2012-08-10', '2012-08-10' => '2012-08-10', ], 'serviceIdentifier' => 'dynamodb_streams', ], 'sts' => [ 'namespace' => 'Sts', 'versions' => [ 'latest' => '2011-06-15', '2011-06-15' => '2011-06-15', ], 'serviceIdentifier' => 'sts', ], 'supplychain' => [ 'namespace' => 'SupplyChain', 'versions' => [ 'latest' => '2024-01-01', '2024-01-01' => '2024-01-01', ], 'serviceIdentifier' => 'supplychain', ], 'support-app' => [ 'namespace' => 'SupportApp', 'versions' => [ 'latest' => '2021-08-20', '2021-08-20' => '2021-08-20', ], 'serviceIdentifier' => 'support_app', ], 'support' => [ 'namespace' => 'Support', 'versions' => [ 'latest' => '2013-04-15', '2013-04-15' => '2013-04-15', ], 'serviceIdentifier' => 'support', ], 'swf' => [ 'namespace' => 'Swf', 'versions' => [ 'latest' => '2012-01-25', '2012-01-25' => '2012-01-25', ], 'serviceIdentifier' => 'swf', ], 'synthetics' => [ 'namespace' => 'Synthetics', 'versions' => [ 'latest' => '2017-10-11', '2017-10-11' => '2017-10-11', ], 'serviceIdentifier' => 'synthetics', ], 'taxsettings' => [ 'namespace' => 'TaxSettings', 'versions' => [ 'latest' => '2018-05-10', '2018-05-10' => '2018-05-10', ], 'serviceIdentifier' => 'taxsettings', ], 'textract' => [ 'namespace' => 'Textract', 'versions' => [ 'latest' => '2018-06-27', '2018-06-27' => '2018-06-27', ], 'serviceIdentifier' => 'textract', ], 'timestream-influxdb' => [ 'namespace' => 'TimestreamInfluxDB', 'versions' => [ 'latest' => '2023-01-27', '2023-01-27' => '2023-01-27', ], 'serviceIdentifier' => 'timestream_influxdb', ], 'timestream-query' => [ 'namespace' => 'TimestreamQuery', 'versions' => [ 'latest' => '2018-11-01', '2018-11-01' => '2018-11-01', ], 'serviceIdentifier' => 'timestream_query', ], 'timestream-write' => [ 'namespace' => 'TimestreamWrite', 'versions' => [ 'latest' => '2018-11-01', '2018-11-01' => '2018-11-01', ], 'serviceIdentifier' => 'timestream_write', ], 'tnb' => [ 'namespace' => 'Tnb', 'versions' => [ 'latest' => '2008-10-21', '2008-10-21' => '2008-10-21', ], 'serviceIdentifier' => 'tnb', ], 'transcribe' => [ 'namespace' => 'TranscribeService', 'versions' => [ 'latest' => '2017-10-26', '2017-10-26' => '2017-10-26', ], 'serviceIdentifier' => 'transcribe', ], 'transfer' => [ 'namespace' => 'Transfer', 'versions' => [ 'latest' => '2018-11-05', '2018-11-05' => '2018-11-05', ], 'serviceIdentifier' => 'transfer', ], 'translate' => [ 'namespace' => 'Translate', 'versions' => [ 'latest' => '2017-07-01', '2017-07-01' => '2017-07-01', ], 'serviceIdentifier' => 'translate', ], 'trustedadvisor' => [ 'namespace' => 'TrustedAdvisor', 'versions' => [ 'latest' => '2022-09-15', '2022-09-15' => '2022-09-15', ], 'serviceIdentifier' => 'trustedadvisor', ], 'verifiedpermissions' => [ 'namespace' => 'VerifiedPermissions', 'versions' => [ 'latest' => '2021-12-01', '2021-12-01' => '2021-12-01', ], 'serviceIdentifier' => 'verifiedpermissions', ], 'voice-id' => [ 'namespace' => 'VoiceID', 'versions' => [ 'latest' => '2021-09-27', '2021-09-27' => '2021-09-27', ], 'serviceIdentifier' => 'voice_id', ], 'vpc-lattice' => [ 'namespace' => 'VPCLattice', 'versions' => [ 'latest' => '2022-11-30', '2022-11-30' => '2022-11-30', ], 'serviceIdentifier' => 'vpc_lattice', ], 'waf-regional' => [ 'namespace' => 'WafRegional', 'versions' => [ 'latest' => '2016-11-28', '2016-11-28' => '2016-11-28', ], 'serviceIdentifier' => 'waf_regional', ], 'waf' => [ 'namespace' => 'Waf', 'versions' => [ 'latest' => '2015-08-24', '2015-08-24' => '2015-08-24', ], 'serviceIdentifier' => 'waf', ], 'wafv2' => [ 'namespace' => 'WAFV2', 'versions' => [ 'latest' => '2019-07-29', '2019-07-29' => '2019-07-29', ], 'serviceIdentifier' => 'wafv2', ], 'wellarchitected' => [ 'namespace' => 'WellArchitected', 'versions' => [ 'latest' => '2020-03-31', '2020-03-31' => '2020-03-31', ], 'serviceIdentifier' => 'wellarchitected', ], 'wisdom' => [ 'namespace' => 'ConnectWisdomService', 'versions' => [ 'latest' => '2020-10-19', '2020-10-19' => '2020-10-19', ], 'serviceIdentifier' => 'wisdom', ], 'workdocs' => [ 'namespace' => 'WorkDocs', 'versions' => [ 'latest' => '2016-05-01', '2016-05-01' => '2016-05-01', ], 'serviceIdentifier' => 'workdocs', ], 'workmail' => [ 'namespace' => 'WorkMail', 'versions' => [ 'latest' => '2017-10-01', '2017-10-01' => '2017-10-01', ], 'serviceIdentifier' => 'workmail', ], 'workmailmessageflow' => [ 'namespace' => 'WorkMailMessageFlow', 'versions' => [ 'latest' => '2019-05-01', '2019-05-01' => '2019-05-01', ], 'serviceIdentifier' => 'workmailmessageflow', ], 'workspaces-thin-client' => [ 'namespace' => 'WorkSpacesThinClient', 'versions' => [ 'latest' => '2023-08-22', '2023-08-22' => '2023-08-22', ], 'serviceIdentifier' => 'workspaces_thin_client', ], 'workspaces-web' => [ 'namespace' => 'WorkSpacesWeb', 'versions' => [ 'latest' => '2020-07-08', '2020-07-08' => '2020-07-08', ], 'serviceIdentifier' => 'workspaces_web', ], 'workspaces' => [ 'namespace' => 'WorkSpaces', 'versions' => [ 'latest' => '2015-04-08', '2015-04-08' => '2015-04-08', ], 'serviceIdentifier' => 'workspaces', ], 'xray' => [ 'namespace' => 'XRay', 'versions' => [ 'latest' => '2016-04-12', '2016-04-12' => '2016-04-12', ], 'serviceIdentifier' => 'xray', ],];
