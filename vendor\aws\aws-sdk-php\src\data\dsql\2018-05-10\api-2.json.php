<?php
// This file was auto-generated from sdk-root/src/data/dsql/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'dsql', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon Aurora DSQL', 'serviceId' => 'DSQL', 'signatureVersion' => 'v4', 'signingName' => 'dsql', 'uid' => 'dsql-2018-05-10', ], 'operations' => [ 'CreateCluster' => [ 'name' => 'CreateCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/cluster', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateClusterInput', ], 'output' => [ 'shape' => 'CreateClusterOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteCluster' => [ 'name' => 'DeleteCluster', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/cluster/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteClusterInput', ], 'output' => [ 'shape' => 'DeleteClusterOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], 'idempotent' => true, ], 'GetCluster' => [ 'name' => 'GetCluster', 'http' => [ 'method' => 'GET', 'requestUri' => '/cluster/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetClusterInput', ], 'output' => [ 'shape' => 'GetClusterOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetVpcEndpointServiceName' => [ 'name' => 'GetVpcEndpointServiceName', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{identifier}/vpc-endpoint-service-name', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVpcEndpointServiceNameInput', ], 'output' => [ 'shape' => 'GetVpcEndpointServiceNameOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListClusters' => [ 'name' => 'ListClusters', 'http' => [ 'method' => 'GET', 'requestUri' => '/cluster', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListClustersInput', ], 'output' => [ 'shape' => 'ListClustersOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceInput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateCluster' => [ 'name' => 'UpdateCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/cluster/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateClusterInput', ], 'output' => [ 'shape' => 'UpdateClusterOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConflictException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Arn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:.+', ], 'ClientToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[!-~]+', ], 'ClusterArn' => [ 'type' => 'string', 'pattern' => 'arn:aws(-[^:]+)?:dsql:[a-z0-9-]{1,20}:[0-9]{12}:cluster/[a-z0-9]{26}', ], 'ClusterArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClusterArn', ], ], 'ClusterCreationTime' => [ 'type' => 'timestamp', ], 'ClusterId' => [ 'type' => 'string', 'pattern' => '[a-z0-9]{26}', ], 'ClusterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClusterSummary', ], ], 'ClusterStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'IDLE', 'INACTIVE', 'UPDATING', 'DELETING', 'DELETED', 'FAILED', 'PENDING_SETUP', 'PENDING_DELETE', ], ], 'ClusterSummary' => [ 'type' => 'structure', 'required' => [ 'identifier', 'arn', ], 'members' => [ 'identifier' => [ 'shape' => 'ClusterId', ], 'arn' => [ 'shape' => 'ClusterArn', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateClusterInput' => [ 'type' => 'structure', 'members' => [ 'deletionProtectionEnabled' => [ 'shape' => 'DeletionProtectionEnabled', ], 'kmsEncryptionKey' => [ 'shape' => 'KmsEncryptionKey', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'multiRegionProperties' => [ 'shape' => 'MultiRegionProperties', ], ], ], 'CreateClusterOutput' => [ 'type' => 'structure', 'required' => [ 'identifier', 'arn', 'status', 'creationTime', 'deletionProtectionEnabled', ], 'members' => [ 'identifier' => [ 'shape' => 'ClusterId', ], 'arn' => [ 'shape' => 'ClusterArn', ], 'status' => [ 'shape' => 'ClusterStatus', ], 'creationTime' => [ 'shape' => 'ClusterCreationTime', ], 'multiRegionProperties' => [ 'shape' => 'MultiRegionProperties', ], 'encryptionDetails' => [ 'shape' => 'EncryptionDetails', ], 'deletionProtectionEnabled' => [ 'shape' => 'DeletionProtectionEnabled', ], ], ], 'DeleteClusterInput' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ClusterId', 'location' => 'uri', 'locationName' => 'identifier', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'client-token', ], ], ], 'DeleteClusterOutput' => [ 'type' => 'structure', 'required' => [ 'identifier', 'arn', 'status', 'creationTime', ], 'members' => [ 'identifier' => [ 'shape' => 'ClusterId', ], 'arn' => [ 'shape' => 'ClusterArn', ], 'status' => [ 'shape' => 'ClusterStatus', ], 'creationTime' => [ 'shape' => 'ClusterCreationTime', ], ], ], 'DeletionProtectionEnabled' => [ 'type' => 'boolean', 'box' => true, ], 'EncryptionDetails' => [ 'type' => 'structure', 'required' => [ 'encryptionType', 'encryptionStatus', ], 'members' => [ 'encryptionType' => [ 'shape' => 'EncryptionType', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'encryptionStatus' => [ 'shape' => 'EncryptionStatus', ], ], ], 'EncryptionStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'UPDATING', 'KMS_KEY_INACCESSIBLE', 'ENABLING', ], ], 'EncryptionType' => [ 'type' => 'string', 'enum' => [ 'AWS_OWNED_KMS_KEY', 'CUSTOMER_MANAGED_KMS_KEY', ], ], 'GetClusterInput' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ClusterId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetClusterOutput' => [ 'type' => 'structure', 'required' => [ 'identifier', 'arn', 'status', 'creationTime', 'deletionProtectionEnabled', ], 'members' => [ 'identifier' => [ 'shape' => 'ClusterId', ], 'arn' => [ 'shape' => 'ClusterArn', ], 'status' => [ 'shape' => 'ClusterStatus', ], 'creationTime' => [ 'shape' => 'ClusterCreationTime', ], 'deletionProtectionEnabled' => [ 'shape' => 'DeletionProtectionEnabled', ], 'multiRegionProperties' => [ 'shape' => 'MultiRegionProperties', ], 'tags' => [ 'shape' => 'TagMap', ], 'encryptionDetails' => [ 'shape' => 'EncryptionDetails', ], ], ], 'GetVpcEndpointServiceNameInput' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ClusterId', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetVpcEndpointServiceNameOutput' => [ 'type' => 'structure', 'required' => [ 'serviceName', ], 'members' => [ 'serviceName' => [ 'shape' => 'ServiceName', ], ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'KmsEncryptionKey' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[a-zA-Z0-9:/_-]+', ], 'KmsKeyArn' => [ 'type' => 'string', ], 'ListClustersInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'max-results', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'next-token', ], ], ], 'ListClustersOutput' => [ 'type' => 'structure', 'required' => [ 'clusters', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'clusters' => [ 'shape' => 'ClusterList', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MultiRegionProperties' => [ 'type' => 'structure', 'members' => [ 'witnessRegion' => [ 'shape' => 'Region', ], 'clusters' => [ 'shape' => 'ClusterArnList', ], ], ], 'NextToken' => [ 'type' => 'string', ], 'Region' => [ 'type' => 'string', 'max' => 50, 'min' => 0, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ServiceName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'com\\.amazonaws\\.[a-z0-9-]+\\.dsql-[a-f0-9]{6}', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', 'serviceCode', 'quotaCode', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_.:/=+\\-@ ]*', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[a-zA-Z0-9_.:/=+\\-@ ]*', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateClusterInput' => [ 'type' => 'structure', 'required' => [ 'identifier', ], 'members' => [ 'identifier' => [ 'shape' => 'ClusterId', 'location' => 'uri', 'locationName' => 'identifier', ], 'deletionProtectionEnabled' => [ 'shape' => 'DeletionProtectionEnabled', ], 'kmsEncryptionKey' => [ 'shape' => 'KmsEncryptionKey', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'multiRegionProperties' => [ 'shape' => 'MultiRegionProperties', ], ], ], 'UpdateClusterOutput' => [ 'type' => 'structure', 'required' => [ 'identifier', 'arn', 'status', 'creationTime', ], 'members' => [ 'identifier' => [ 'shape' => 'ClusterId', ], 'arn' => [ 'shape' => 'ClusterArn', ], 'status' => [ 'shape' => 'ClusterStatus', ], 'creationTime' => [ 'shape' => 'ClusterCreationTime', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'deletionProtectionEnabled', 'other', ], ], ],];
