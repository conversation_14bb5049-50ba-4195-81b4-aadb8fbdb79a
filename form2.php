<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Growth Consultant - Application Form</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="style.css">
    <style>
        .step {
            display: none;
        }

        .step.active {
            display: block;
        }

        .progress-bar {
            transition: width 0.3s ease;
        }
    </style>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="max-w-4xl mx-auto mb-8">
            <h1 class="text-3xl font-bold text-slate-800 text-center mb-2">Digital Growth Consultant</h1>
            <p class="text-slate-200 text-center">Application Form</p>
        </div>

        <!-- Progress Bar -->
        <div class="max-w-4xl mx-auto mb-8">
            <div class="progress-container">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-sm font-medium text-slate-200">Progress</span>
                    <span class="text-sm font-medium text-slate-200" id="progress-text">Step 1 of 3</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="progress-bar h-2 rounded-full" id="progress-bar" style="width: 33.33%"></div>
                </div>
            </div>
        </div>

        <!-- Form Container -->
        <form id="multiStepForm" class="max-w-4xl mx-auto" action="process_form2.php" method="POST" enctype="multipart/form-data">

            <!-- Step 1 -->
            <div class="step" data-step="1">

                <div class="info-box">
                    <p class="text-neutral-700 leading-relaxed">
                        Buat kamu yang sudah pernah pegang Meta Ads, pasang pixel, atau terbiasa tracking — kita cari
                        partner yang siap naik level jadi Digital Growth Consultant yang nggak cuma bisa eksekusi, tapi
                        juga bisa bantu strategi klien dengan percaya diri. Kalau kamu ngerti teknis dan nyaman
                        ngejelasin ke orang awam, kamu di tempat yang tepat!
                    </p>
                </div>


                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">Nama Lengkap *</label>
                        <input type="text" name="full_name" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">Whatsapp Number *</label>
                        <input type="number" name="phone" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="081234567890">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">Email *</label>
                        <input type="email" name="email" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">Domisili Saat Ini *</label>
                        <input type="text" name="domicile" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-white mb-2">
                        Apakah Anda memiliki pengalaman sebagai Digital Growth Consultant atau posisi serupa minimal 1
                        tahun? *
                    </label>
                    <div class="flex space-x-4">
                        <label class="flex items-center space-x-2">
                            <input type="radio" name="has_experience" value="ya" class="text-blue-600"
                                onchange="toggleExperienceDetail(true)" required>
                            <span class="text-white">Ya</span>
                        </label>
                        <label class="flex items-center space-x-2">
                            <input type="radio" name="has_experience" value="tidak" class="text-blue-600"
                                onchange="toggleExperienceDetail(false)" required>
                            <span class="text-white">Tidak</span>
                        </label>
                    </div>
                </div>

                <div id="experience_detail" class="mb-6" style="display: none;">
                    <label class="block text-sm font-medium text-white mb-2">Jika Ya, ceritakan sedikit:</label>
                    <textarea name="experience_description" rows="4"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Ceritakan pengalaman Anda..."></textarea>
                </div>

                <div class="flex justify-between">
                    <button type="button" onclick="prevStep()" class="btn-secondary">
                        Kembali
                    </button>
                    <button type="button" onclick="nextStep()" class="btn-primary">
                        Lanjut
                    </button>
                </div>
            </div>

            <!-- Step 2 -->
            <div class="step active" data-step="2">




                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Seberapa sering Anda menggunakan Meta Ads? *
                        </label>
                        <select name="meta_ads_frequency" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Pilih frekuensi penggunaan...</option>
                            <option value="setiap_hari">Setiap hari</option>
                            <option value="beberapa_kali_seminggu">Beberapa kali seminggu</option>
                            <option value="seminggu_sekali">Seminggu sekali</option>
                            <option value="sebulan_sekali">Sebulan sekali</option>
                            <option value="jarang">Jarang, sesekali saja</option>
                            <option value="belum_pernah">Belum pernah</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Pernahkah Anda memasang dan mengatur Pixel untuk keperluan tracking iklan? *
                        </label>
                        <div class="space-y-3">
                            <label class="radio-option">
                                <input type="radio" name="pixel_experience" value="sering" required>
                                <span class="text-neutral-700">Ya, sering dan mahir melakukannya</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="pixel_experience" value="pernah" required>
                                <span class="text-neutral-700">Pernah beberapa kali, masih belajar</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="pixel_experience" value="belum" required>
                                <span class="text-neutral-700">Belum pernah, tapi tertarik belajar</span>
                            </label>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Apakah Anda terbiasa menggunakan AI tools? (Jika Ya, sebutkan) *
                        </label>
                        <textarea name="ai_tools_usage" rows="4" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Contoh: ChatGPT untuk copywriting, Claude untuk analisis data, dll. Atau tulis 'Tidak' jika belum pernah..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Seberapa nyaman Anda menjelaskan fitur teknis dalam bahasa sederhana ke orang awam? *
                        </label>
                        <select name="technical_explanation_comfort" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Pilih tingkat kenyamanan...</option>
                            <option value="sangat_nyaman">Sangat nyaman, sering melakukannya</option>
                            <option value="nyaman">Nyaman, bisa dilakukan dengan baik</option>
                            <option value="cukup_nyaman">Cukup nyaman, masih perlu latihan</option>
                            <option value="kurang_nyaman">Kurang nyaman, perlu banyak latihan</option>
                            <option value="tidak_nyaman">Tidak nyaman, lebih suka fokus teknis</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Tools apa yang pernah Anda gunakan secara aktif? *
                        </label>
                        <div class="checkbox-grid">
                            <label class="checkbox-item">
                                <input type="checkbox" name="tools_used" value="wa_business" class="text-blue-600">
                                <span class="text-neutral-700">WhatsApp Business</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="tools_used" value="google_sheets" class="text-blue-600">
                                <span class="text-neutral-700">Google Sheets</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="tools_used" value="notion" class="text-blue-600">
                                <span class="text-neutral-700">Notion</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="tools_used" value="trello" class="text-blue-600">
                                <span class="text-neutral-700">Trello</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="tools_used" value="slack" class="text-blue-600">
                                <span class="text-neutral-700">Slack</span>
                            </label>
                            <label class="checkbox-item">
                                <input type="checkbox" name="tools_used" value="asana" class="text-blue-600">
                                <span class="text-neutral-700">Asana</span>
                            </label>
                        </div>
                        <div class="mt-3">
                            <input type="text" name="other_tools" placeholder="Tools lainnya (pisahkan dengan koma)"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>
                </div>

                <div class="flex justify-end mt-8">
                    <button type="button" onclick="nextStep()" class="btn-primary">
                        Lanjut
                    </button>
                </div>
            </div>

            <!-- Step 3 -->
            <div class="step" data-step="3">


                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Pernahkah Anda gagal saat testing campaign atau tracking? Apa penyebabnya dan bagaimana Anda
                            menyelesaikannya? *
                        </label>
                        <textarea name="campaign_failure_experience" rows="4" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Ceritakan pengalaman gagal dan bagaimana Anda mengatasinya..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Pernahkah Anda menjelaskan hal teknis (seperti pixel, attribution, atau event tracking) ke
                            orang non-teknis? Ceritakan caranya agar mereka bisa paham. *
                        </label>
                        <textarea name="technical_explanation_experience" rows="4" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Ceritakan pengalaman dan metode Anda menjelaskan hal teknis..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Apa pendekatan Anda jika klien bingung soal funnel iklan dan tracking? *
                        </label>
                        <textarea name="client_confusion_approach" rows="4" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Jelaskan pendekatan dan strategi Anda..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Menurut Anda, peran utama seorang digital marketing consultant itu apa? Jelaskan alasannya.
                            *
                        </label>
                        <textarea name="consultant_role_opinion" rows="4" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Jelaskan pandangan Anda tentang peran digital marketing consultant..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            <strong>Studi Kasus:</strong> "Misalnya ada klien bilang: 'Saya sudah pasang iklan tapi
                            nggak ada yang chat.' Apa langkah Anda?" *
                        </label>
                        <textarea name="case_study_response" rows="5" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Jelaskan langkah-langkah sistematis yang akan Anda lakukan..."></textarea>
                    </div>
                </div>

                <div class="flex justify-between mt-8">
                    <button type="button" onclick="prevStep()" class="btn-secondary">
                        Kembali
                    </button>
                    <button type="button" onclick="nextStep()" class="btn-primary">
                        Lanjut
                    </button>
                </div>
            </div>

            <!-- Step 4 -->
            <div class="step" data-step="4">


                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Kenapa Anda tertarik bergabung di perusahaan ini? Apa yang membuat Anda yakin bisa
                            berkembang bareng kami? *
                        </label>
                        <textarea name="joining_motivation" rows="5" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Ceritakan motivasi dan keyakinan Anda..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Tambahkan portfolio, dashboard iklan, dokumentasi tracking, atau studi kasus *
                        </label>

                        <div class="file-upload-container">
                            <div class="file-upload-area" id="fileUploadArea" role="button" tabindex="0">
                                <div class="file-upload-content">
                                    <div class="file-upload-icon">
                                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12">
                                            </path>
                                        </svg>
                                    </div>
                                    <div class="file-upload-text">
                                        <p class="text-lg font-medium text-gray-700">Drag & drop files here</p>
                                        <p class="text-sm text-gray-500">atau klik untuk memilih file</p>
                                    </div>
                                    <button type="button" class="file-upload-button" tabindex="-1">
                                        Pilih File
                                    </button>
                                </div>
                                <input type="file" name="portfolio_files[]" multiple
                                    accept=".pdf,.jpg,.jpeg,.png,.mp3,.mp4,.wav,.doc,.docx,.xlsx,.pptx"
                                    class="file-input-hidden" id="fileInput" required>
                            </div>

                            <!-- File Preview Area -->
                            <div class="file-preview-container" id="filePreviewContainer" style="display: none;">
                                <h4 class="text-sm font-medium text-white mb-3">File yang dipilih:</h4>
                                <div class="file-preview-list" id="filePreviewList">
                                    <!-- File previews will be inserted here -->
                                </div>
                            </div>

                            <!-- Upload Progress -->
                            <div class="upload-progress-container" id="uploadProgressContainer" style="display: none;">
                                <div class="upload-progress-bar">
                                    <div class="upload-progress-fill" id="uploadProgressFill"></div>
                                </div>
                                <p class="upload-progress-text" id="uploadProgressText">Uploading...</p>
                            </div>
                        </div>

                        <p class="text-sm text-slate-400 mt-2">
                            Format yang diterima: PDF, gambar (JPG, PNG), audio/video (MP3, MP4, WAV), dokumen (DOC,
                            DOCX, XLSX, PPTX)
                            <br>Maksimal 10MB per file
                        </p>
                    </div>

                    <div class="alert-warning">
                        <p class="text-sm">
                            <strong>Tips Portfolio:</strong> Sertakan screenshot dashboard Meta Ads, hasil tracking yang
                            pernah Anda setup, atau dokumentasi case study yang menunjukkan kemampuan teknis dan
                            analisis Anda.
                        </p>
                    </div>

                    <div class="alert-success">
                        <p class="text-sm">
                            <strong>Bonus Points:</strong> Jika Anda punya pengalaman dengan Google Analytics, GTM, atau
                            tools tracking lainnya, jangan lupa disebutkan di portfolio!
                        </p>
                    </div>

                    <div class="alert-info">
                        <p class="text-sm">
                            <strong>Catatan:</strong> Pastikan semua data yang Anda masukkan sudah benar sebelum
                            mengirim formulir.
                            Setelah dikirim, data tidak dapat diubah.
                        </p>
                    </div>
                </div>

                <div class="flex justify-between mt-8">
                    <button type="button" onclick="prevStep()" class="btn-secondary">
                        Kembali
                    </button>
                    <button type="submit" class="btn-success">
                        Kirim Aplikasi
                    </button>
                </div>
            </div>

        </form>

        <!-- Success Message -->
        <div id="successMessage" class="max-w-4xl mx-auto hidden">
            <div class="text-center p-8">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                    <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-medium text-white mb-2">Aplikasi Berhasil Dikirim!</h3>
                <p class="text-slate-200 mb-4">
                    Terima kasih telah melamar posisi Digital Growth Consultant. Tim kami akan meninjau aplikasi Anda
                    dan menghubungi dalam 3-5 hari kerja.
                </p>
                <p class="text-sm text-slate-200">
                    Kami akan mengevaluasi pengalaman teknis dan portfolio yang Anda berikan.
                </p>
            </div>
        </div>
    </div>

    <script src="form2.js"></script>
    <script>
    // Tambahkan fungsi untuk handle submit form
    document.getElementById('multiStepForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(this);
            
            // Tampilkan loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = 'Mengirim...';
            
            // Tampilkan progress upload
            const uploadProgressContainer = document.getElementById('uploadProgressContainer');
            const uploadProgressFill = document.getElementById('uploadProgressFill');
            const uploadProgressText = document.getElementById('uploadProgressText');
            uploadProgressContainer.style.display = 'block';
            
            const response = await fetch('process_form2.php', {
                method: 'POST',
                body: formData,
                onUploadProgress: (progressEvent) => {
                    const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                    uploadProgressFill.style.width = percentCompleted + '%';
                    uploadProgressText.textContent = `Uploading... ${percentCompleted}%`;
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Tampilkan pesan sukses
                document.getElementById('multiStepForm').style.display = 'none';
                document.getElementById('successMessage').classList.remove('hidden');
            } else {
                throw new Error(result.error || 'Terjadi kesalahan saat mengirim aplikasi');
            }
            
        } catch (error) {
            alert(error.message);
        } finally {
            // Reset button state
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
            uploadProgressContainer.style.display = 'none';
        }
    });
    </script>
</body>

</html>