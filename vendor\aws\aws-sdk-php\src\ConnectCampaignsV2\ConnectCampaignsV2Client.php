<?php
namespace Aws\ConnectCampaignsV2;

use Aws\AwsClient;

/**
 * This client is used to interact with the **AmazonConnectCampaignServiceV2** service.
 * @method \Aws\Result createCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCampaignAsync(array $args = [])
 * @method \Aws\Result deleteCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCampaignAsync(array $args = [])
 * @method \Aws\Result deleteCampaignChannelSubtypeConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCampaignChannelSubtypeConfigAsync(array $args = [])
 * @method \Aws\Result deleteCampaignCommunicationLimits(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCampaignCommunicationLimitsAsync(array $args = [])
 * @method \Aws\Result deleteCampaignCommunicationTime(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCampaignCommunicationTimeAsync(array $args = [])
 * @method \Aws\Result deleteConnectInstanceConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteConnectInstanceConfigAsync(array $args = [])
 * @method \Aws\Result deleteConnectInstanceIntegration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteConnectInstanceIntegrationAsync(array $args = [])
 * @method \Aws\Result deleteInstanceOnboardingJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteInstanceOnboardingJobAsync(array $args = [])
 * @method \Aws\Result describeCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeCampaignAsync(array $args = [])
 * @method \Aws\Result getCampaignState(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCampaignStateAsync(array $args = [])
 * @method \Aws\Result getCampaignStateBatch(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCampaignStateBatchAsync(array $args = [])
 * @method \Aws\Result getConnectInstanceConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getConnectInstanceConfigAsync(array $args = [])
 * @method \Aws\Result getInstanceOnboardingJobStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getInstanceOnboardingJobStatusAsync(array $args = [])
 * @method \Aws\Result listCampaigns(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCampaignsAsync(array $args = [])
 * @method \Aws\Result listConnectInstanceIntegrations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listConnectInstanceIntegrationsAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result pauseCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise pauseCampaignAsync(array $args = [])
 * @method \Aws\Result putConnectInstanceIntegration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putConnectInstanceIntegrationAsync(array $args = [])
 * @method \Aws\Result putOutboundRequestBatch(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putOutboundRequestBatchAsync(array $args = [])
 * @method \Aws\Result putProfileOutboundRequestBatch(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putProfileOutboundRequestBatchAsync(array $args = [])
 * @method \Aws\Result resumeCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise resumeCampaignAsync(array $args = [])
 * @method \Aws\Result startCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startCampaignAsync(array $args = [])
 * @method \Aws\Result startInstanceOnboardingJob(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startInstanceOnboardingJobAsync(array $args = [])
 * @method \Aws\Result stopCampaign(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopCampaignAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateCampaignChannelSubtypeConfig(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCampaignChannelSubtypeConfigAsync(array $args = [])
 * @method \Aws\Result updateCampaignCommunicationLimits(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCampaignCommunicationLimitsAsync(array $args = [])
 * @method \Aws\Result updateCampaignCommunicationTime(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCampaignCommunicationTimeAsync(array $args = [])
 * @method \Aws\Result updateCampaignFlowAssociation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCampaignFlowAssociationAsync(array $args = [])
 * @method \Aws\Result updateCampaignName(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCampaignNameAsync(array $args = [])
 * @method \Aws\Result updateCampaignSchedule(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCampaignScheduleAsync(array $args = [])
 * @method \Aws\Result updateCampaignSource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCampaignSourceAsync(array $args = [])
 */
class ConnectCampaignsV2Client extends AwsClient {}
