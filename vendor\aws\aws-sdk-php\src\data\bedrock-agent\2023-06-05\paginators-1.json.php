<?php
// This file was auto-generated from sdk-root/src/data/bedrock-agent/2023-06-05/paginators-1.json
return [ 'pagination' => [ 'ListAgentActionGroups' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'actionGroupSummaries', ], 'ListAgentAliases' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'agentAliasSummaries', ], 'ListAgentCollaborators' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'agentCollaboratorSummaries', ], 'ListAgentKnowledgeBases' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'agentKnowledgeBaseSummaries', ], 'ListAgentVersions' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'agentVersionSummaries', ], 'ListAgents' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'agentSummaries', ], 'ListDataSources' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'dataSourceSummaries', ], 'ListFlowAliases' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'flowAliasSummaries', ], 'ListFlowVersions' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'flowVersionSummaries', ], 'ListFlows' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'flowSummaries', ], 'ListIngestionJobs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'ingestionJobSummaries', ], 'ListKnowledgeBaseDocuments' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'documentDetails', ], 'ListKnowledgeBases' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'knowledgeBaseSummaries', ], 'ListPrompts' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'promptSummaries', ], ],];
