<?php
// This file was auto-generated from sdk-root/src/data/connectcampaignsv2/2024-04-23/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2024-04-23', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'connect-campaigns', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AmazonConnectCampaignServiceV2', 'serviceId' => 'ConnectCampaignsV2', 'signatureVersion' => 'v4', 'signingName' => 'connect-campaigns', 'uid' => 'connectcampaignsv2-2024-04-23', ], 'operations' => [ 'CreateCampaign' => [ 'name' => 'CreateCampaign', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/campaigns', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateCampaignRequest', ], 'output' => [ 'shape' => 'CreateCampaignResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteCampaign' => [ 'name' => 'DeleteCampaign', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/campaigns/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteCampaignRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteCampaignChannelSubtypeConfig' => [ 'name' => 'DeleteCampaignChannelSubtypeConfig', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/campaigns/{id}/channel-subtype-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteCampaignChannelSubtypeConfigRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteCampaignCommunicationLimits' => [ 'name' => 'DeleteCampaignCommunicationLimits', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/campaigns/{id}/communication-limits', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteCampaignCommunicationLimitsRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidCampaignStateException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteCampaignCommunicationTime' => [ 'name' => 'DeleteCampaignCommunicationTime', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/campaigns/{id}/communication-time', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteCampaignCommunicationTimeRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidCampaignStateException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteConnectInstanceConfig' => [ 'name' => 'DeleteConnectInstanceConfig', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/connect-instance/{connectInstanceId}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConnectInstanceConfigRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidStateException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'DeleteConnectInstanceIntegration' => [ 'name' => 'DeleteConnectInstanceIntegration', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/connect-instance/{connectInstanceId}/integrations/delete', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConnectInstanceIntegrationRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteInstanceOnboardingJob' => [ 'name' => 'DeleteInstanceOnboardingJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/connect-instance/{connectInstanceId}/onboarding', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteInstanceOnboardingJobRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidStateException', ], ], 'idempotent' => true, ], 'DescribeCampaign' => [ 'name' => 'DescribeCampaign', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/campaigns/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeCampaignRequest', ], 'output' => [ 'shape' => 'DescribeCampaignResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetCampaignState' => [ 'name' => 'GetCampaignState', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/campaigns/{id}/state', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCampaignStateRequest', ], 'output' => [ 'shape' => 'GetCampaignStateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetCampaignStateBatch' => [ 'name' => 'GetCampaignStateBatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/campaigns-state', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCampaignStateBatchRequest', ], 'output' => [ 'shape' => 'GetCampaignStateBatchResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetConnectInstanceConfig' => [ 'name' => 'GetConnectInstanceConfig', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/connect-instance/{connectInstanceId}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConnectInstanceConfigRequest', ], 'output' => [ 'shape' => 'GetConnectInstanceConfigResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetInstanceOnboardingJobStatus' => [ 'name' => 'GetInstanceOnboardingJobStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/connect-instance/{connectInstanceId}/onboarding', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetInstanceOnboardingJobStatusRequest', ], 'output' => [ 'shape' => 'GetInstanceOnboardingJobStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCampaigns' => [ 'name' => 'ListCampaigns', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/campaigns-summary', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCampaignsRequest', ], 'output' => [ 'shape' => 'ListCampaignsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListConnectInstanceIntegrations' => [ 'name' => 'ListConnectInstanceIntegrations', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/connect-instance/{connectInstanceId}/integrations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConnectInstanceIntegrationsRequest', ], 'output' => [ 'shape' => 'ListConnectInstanceIntegrationsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/v2/tags/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'PauseCampaign' => [ 'name' => 'PauseCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/campaigns/{id}/pause', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PauseCampaignRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidCampaignStateException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'PutConnectInstanceIntegration' => [ 'name' => 'PutConnectInstanceIntegration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/connect-instance/{connectInstanceId}/integrations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutConnectInstanceIntegrationRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'PutOutboundRequestBatch' => [ 'name' => 'PutOutboundRequestBatch', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/campaigns/{id}/outbound-requests', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutOutboundRequestBatchRequest', ], 'output' => [ 'shape' => 'PutOutboundRequestBatchResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidCampaignStateException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'PutProfileOutboundRequestBatch' => [ 'name' => 'PutProfileOutboundRequestBatch', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/campaigns/{id}/profile-outbound-requests', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutProfileOutboundRequestBatchRequest', ], 'output' => [ 'shape' => 'PutProfileOutboundRequestBatchResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidCampaignStateException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'ResumeCampaign' => [ 'name' => 'ResumeCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/campaigns/{id}/resume', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ResumeCampaignRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidCampaignStateException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StartCampaign' => [ 'name' => 'StartCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/campaigns/{id}/start', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartCampaignRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidCampaignStateException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'StartInstanceOnboardingJob' => [ 'name' => 'StartInstanceOnboardingJob', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v2/connect-instance/{connectInstanceId}/onboarding', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartInstanceOnboardingJobRequest', ], 'output' => [ 'shape' => 'StartInstanceOnboardingJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'StopCampaign' => [ 'name' => 'StopCampaign', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/campaigns/{id}/stop', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopCampaignRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidCampaignStateException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/tags/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v2/tags/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'UpdateCampaignChannelSubtypeConfig' => [ 'name' => 'UpdateCampaignChannelSubtypeConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/campaigns/{id}/channel-subtype-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCampaignChannelSubtypeConfigRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateCampaignCommunicationLimits' => [ 'name' => 'UpdateCampaignCommunicationLimits', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/campaigns/{id}/communication-limits', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCampaignCommunicationLimitsRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidCampaignStateException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateCampaignCommunicationTime' => [ 'name' => 'UpdateCampaignCommunicationTime', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/campaigns/{id}/communication-time', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCampaignCommunicationTimeRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidCampaignStateException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateCampaignFlowAssociation' => [ 'name' => 'UpdateCampaignFlowAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/campaigns/{id}/flow', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCampaignFlowAssociationRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidCampaignStateException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateCampaignName' => [ 'name' => 'UpdateCampaignName', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/campaigns/{id}/name', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCampaignNameRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateCampaignSchedule' => [ 'name' => 'UpdateCampaignSchedule', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/campaigns/{id}/schedule', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCampaignScheduleRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidCampaignStateException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'UpdateCampaignSource' => [ 'name' => 'UpdateCampaignSource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v2/campaigns/{id}/source', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCampaignSourceRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidCampaignStateException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'xAmzErrorType' => [ 'shape' => 'XAmazonErrorType', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AgentlessConfig' => [ 'type' => 'structure', 'members' => [], ], 'AnswerMachineDetectionConfig' => [ 'type' => 'structure', 'required' => [ 'enableAnswerMachineDetection', ], 'members' => [ 'enableAnswerMachineDetection' => [ 'shape' => 'Boolean', ], 'awaitAnswerMachinePrompt' => [ 'shape' => 'Boolean', ], ], ], 'Arn' => [ 'type' => 'string', 'max' => 500, 'min' => 20, 'pattern' => 'arn:[a-zA-Z0-9-]+:[a-zA-Z0-9-]+:[a-z]{2}-[a-z]+-\\d{1,2}:[a-zA-Z0-9-]+:[^:]+(?:/[^:]+)*(?:/[^:]+)?(?:\\:[^:]+)?', ], 'AttributeName' => [ 'type' => 'string', 'max' => 32767, 'min' => 0, 'pattern' => '[a-zA-Z0-9\\-_]+', ], 'AttributeValue' => [ 'type' => 'string', 'max' => 32767, 'min' => 0, 'pattern' => '.*', ], 'Attributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeName', ], 'value' => [ 'shape' => 'AttributeValue', ], 'sensitive' => true, ], 'BandwidthAllocation' => [ 'type' => 'double', 'box' => true, 'max' => 1, 'min' => 0, ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'Campaign' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'name', 'connectInstanceId', 'channelSubtypeConfig', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', ], 'arn' => [ 'shape' => 'CampaignArn', ], 'name' => [ 'shape' => 'CampaignName', ], 'connectInstanceId' => [ 'shape' => 'InstanceId', ], 'channelSubtypeConfig' => [ 'shape' => 'ChannelSubtypeConfig', ], 'source' => [ 'shape' => 'Source', ], 'connectCampaignFlowArn' => [ 'shape' => 'Arn', ], 'schedule' => [ 'shape' => 'Schedule', ], 'communicationTimeConfig' => [ 'shape' => 'CommunicationTimeConfig', ], 'communicationLimitsOverride' => [ 'shape' => 'CommunicationLimitsConfig', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CampaignArn' => [ 'type' => 'string', 'max' => 500, 'min' => 20, ], 'CampaignDeletionPolicy' => [ 'type' => 'string', 'enum' => [ 'RETAIN_ALL', 'DELETE_ALL', ], ], 'CampaignFilters' => [ 'type' => 'structure', 'members' => [ 'instanceIdFilter' => [ 'shape' => 'InstanceIdFilter', ], ], ], 'CampaignId' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[a-zA-Z0-9\\-:/]*', ], 'CampaignName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, ], 'CampaignState' => [ 'type' => 'string', 'enum' => [ 'Initialized', 'Running', 'Paused', 'Stopped', 'Failed', 'Completed', ], ], 'CampaignSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'arn', 'name', 'connectInstanceId', 'channelSubtypes', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', ], 'arn' => [ 'shape' => 'CampaignArn', ], 'name' => [ 'shape' => 'CampaignName', ], 'connectInstanceId' => [ 'shape' => 'InstanceId', ], 'channelSubtypes' => [ 'shape' => 'ChannelSubtypeList', ], 'schedule' => [ 'shape' => 'Schedule', ], 'connectCampaignFlowArn' => [ 'shape' => 'Arn', ], ], ], 'CampaignSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CampaignSummary', ], ], 'Capacity' => [ 'type' => 'double', 'box' => true, 'max' => 1, 'min' => 0.01, ], 'ChannelSubtype' => [ 'type' => 'string', 'enum' => [ 'TELEPHONY', 'SMS', 'EMAIL', ], ], 'ChannelSubtypeConfig' => [ 'type' => 'structure', 'members' => [ 'telephony' => [ 'shape' => 'TelephonyChannelSubtypeConfig', ], 'sms' => [ 'shape' => 'SmsChannelSubtypeConfig', ], 'email' => [ 'shape' => 'EmailChannelSubtypeConfig', ], ], ], 'ChannelSubtypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelSubtype', ], ], 'ChannelSubtypeParameters' => [ 'type' => 'structure', 'members' => [ 'telephony' => [ 'shape' => 'TelephonyChannelSubtypeParameters', ], 'sms' => [ 'shape' => 'SmsChannelSubtypeParameters', ], 'email' => [ 'shape' => 'EmailChannelSubtypeParameters', ], ], 'union' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'pattern' => '[a-zA-Z0-9_\\-.]*', ], 'CommunicationLimit' => [ 'type' => 'structure', 'required' => [ 'maxCountPerRecipient', 'frequency', 'unit', ], 'members' => [ 'maxCountPerRecipient' => [ 'shape' => 'CommunicationLimitMaxCountPerRecipientInteger', ], 'frequency' => [ 'shape' => 'CommunicationLimitFrequencyInteger', ], 'unit' => [ 'shape' => 'CommunicationLimitTimeUnit', ], ], ], 'CommunicationLimitFrequencyInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 30, 'min' => 1, ], 'CommunicationLimitList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommunicationLimit', ], 'max' => 2, 'min' => 0, ], 'CommunicationLimitMaxCountPerRecipientInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'CommunicationLimitTimeUnit' => [ 'type' => 'string', 'enum' => [ 'DAY', ], ], 'CommunicationLimits' => [ 'type' => 'structure', 'members' => [ 'communicationLimitsList' => [ 'shape' => 'CommunicationLimitList', ], ], 'union' => true, ], 'CommunicationLimitsConfig' => [ 'type' => 'structure', 'members' => [ 'allChannelSubtypes' => [ 'shape' => 'CommunicationLimits', ], ], ], 'CommunicationLimitsConfigType' => [ 'type' => 'string', 'enum' => [ 'ALL_CHANNEL_SUBTYPES', ], ], 'CommunicationTimeConfig' => [ 'type' => 'structure', 'required' => [ 'localTimeZoneConfig', ], 'members' => [ 'localTimeZoneConfig' => [ 'shape' => 'LocalTimeZoneConfig', ], 'telephony' => [ 'shape' => 'TimeWindow', ], 'sms' => [ 'shape' => 'TimeWindow', ], 'email' => [ 'shape' => 'TimeWindow', ], ], ], 'CommunicationTimeConfigType' => [ 'type' => 'string', 'enum' => [ 'TELEPHONY', 'SMS', 'EMAIL', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'xAmzErrorType' => [ 'shape' => 'XAmazonErrorType', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ContactFlowId' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'CreateCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'connectInstanceId', 'channelSubtypeConfig', ], 'members' => [ 'name' => [ 'shape' => 'CampaignName', ], 'connectInstanceId' => [ 'shape' => 'InstanceId', ], 'channelSubtypeConfig' => [ 'shape' => 'ChannelSubtypeConfig', ], 'source' => [ 'shape' => 'Source', ], 'connectCampaignFlowArn' => [ 'shape' => 'Arn', ], 'schedule' => [ 'shape' => 'Schedule', ], 'communicationTimeConfig' => [ 'shape' => 'CommunicationTimeConfig', ], 'communicationLimitsOverride' => [ 'shape' => 'CommunicationLimitsConfig', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'CampaignId', ], 'arn' => [ 'shape' => 'CampaignArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CustomerProfilesIntegrationConfig' => [ 'type' => 'structure', 'required' => [ 'domainArn', 'objectTypeNames', ], 'members' => [ 'domainArn' => [ 'shape' => 'Arn', ], 'objectTypeNames' => [ 'shape' => 'ObjectTypeNamesMap', ], ], ], 'CustomerProfilesIntegrationIdentifier' => [ 'type' => 'structure', 'required' => [ 'domainArn', ], 'members' => [ 'domainArn' => [ 'shape' => 'Arn', ], ], ], 'CustomerProfilesIntegrationSummary' => [ 'type' => 'structure', 'required' => [ 'domainArn', 'objectTypeNames', ], 'members' => [ 'domainArn' => [ 'shape' => 'Arn', ], 'objectTypeNames' => [ 'shape' => 'ObjectTypeNamesMap', ], ], ], 'DailyHours' => [ 'type' => 'map', 'key' => [ 'shape' => 'DayOfWeek', ], 'value' => [ 'shape' => 'TimeRangeList', ], ], 'DayOfWeek' => [ 'type' => 'string', 'enum' => [ 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY', 'SUNDAY', ], ], 'DeleteCampaignChannelSubtypeConfigRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'channelSubtype', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], 'channelSubtype' => [ 'shape' => 'ChannelSubtype', 'location' => 'querystring', 'locationName' => 'channelSubtype', ], ], ], 'DeleteCampaignCommunicationLimitsRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'config', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], 'config' => [ 'shape' => 'CommunicationLimitsConfigType', 'location' => 'querystring', 'locationName' => 'config', ], ], ], 'DeleteCampaignCommunicationTimeRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'config', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], 'config' => [ 'shape' => 'CommunicationTimeConfigType', 'location' => 'querystring', 'locationName' => 'config', ], ], ], 'DeleteCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteConnectInstanceConfigRequest' => [ 'type' => 'structure', 'required' => [ 'connectInstanceId', ], 'members' => [ 'connectInstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'connectInstanceId', ], 'campaignDeletionPolicy' => [ 'shape' => 'CampaignDeletionPolicy', 'location' => 'querystring', 'locationName' => 'campaignDeletionPolicy', ], ], ], 'DeleteConnectInstanceIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'connectInstanceId', 'integrationIdentifier', ], 'members' => [ 'connectInstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'connectInstanceId', ], 'integrationIdentifier' => [ 'shape' => 'IntegrationIdentifier', ], ], ], 'DeleteInstanceOnboardingJobRequest' => [ 'type' => 'structure', 'required' => [ 'connectInstanceId', ], 'members' => [ 'connectInstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'connectInstanceId', ], ], ], 'DescribeCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DescribeCampaignResponse' => [ 'type' => 'structure', 'members' => [ 'campaign' => [ 'shape' => 'Campaign', ], ], ], 'DestinationPhoneNumber' => [ 'type' => 'string', 'max' => 20, 'min' => 0, 'pattern' => '[\\d\\-+]*', 'sensitive' => true, ], 'DialRequestId' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[a-zA-Z0-9_\\-.]*', ], 'EmailAddress' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.*[^\\s@]+@[^\\s@]+\\.[^\\s@]+.*', 'sensitive' => true, ], 'EmailChannelSubtypeConfig' => [ 'type' => 'structure', 'required' => [ 'outboundMode', 'defaultOutboundConfig', ], 'members' => [ 'capacity' => [ 'shape' => 'Capacity', ], 'outboundMode' => [ 'shape' => 'EmailOutboundMode', ], 'defaultOutboundConfig' => [ 'shape' => 'EmailOutboundConfig', ], ], ], 'EmailChannelSubtypeParameters' => [ 'type' => 'structure', 'required' => [ 'destinationEmailAddress', 'templateParameters', ], 'members' => [ 'destinationEmailAddress' => [ 'shape' => 'EmailAddress', ], 'connectSourceEmailAddress' => [ 'shape' => 'EmailAddress', ], 'templateArn' => [ 'shape' => 'Arn', ], 'templateParameters' => [ 'shape' => 'Attributes', ], ], ], 'EmailDisplayName' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'sensitive' => true, ], 'EmailOutboundConfig' => [ 'type' => 'structure', 'required' => [ 'connectSourceEmailAddress', 'wisdomTemplateArn', ], 'members' => [ 'connectSourceEmailAddress' => [ 'shape' => 'EmailAddress', ], 'sourceEmailAddressDisplayName' => [ 'shape' => 'EmailDisplayName', ], 'wisdomTemplateArn' => [ 'shape' => 'Arn', ], ], ], 'EmailOutboundMode' => [ 'type' => 'structure', 'members' => [ 'agentless' => [ 'shape' => 'AgentlessConfig', ], ], 'union' => true, ], 'Enabled' => [ 'type' => 'boolean', ], 'EncryptionConfig' => [ 'type' => 'structure', 'required' => [ 'enabled', ], 'members' => [ 'enabled' => [ 'shape' => 'Enabled', ], 'encryptionType' => [ 'shape' => 'EncryptionType', ], 'keyArn' => [ 'shape' => 'EncryptionKey', ], ], ], 'EncryptionKey' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'EncryptionType' => [ 'type' => 'string', 'enum' => [ 'KMS', ], ], 'EventTrigger' => [ 'type' => 'structure', 'members' => [ 'customerProfilesDomainArn' => [ 'shape' => 'Arn', ], ], ], 'EventType' => [ 'type' => 'string', 'enum' => [ 'Campaign-Email', 'Campaign-SMS', 'Campaign-Telephony', 'Campaign-Orchestration', ], ], 'FailedCampaignStateResponse' => [ 'type' => 'structure', 'members' => [ 'campaignId' => [ 'shape' => 'CampaignId', ], 'failureCode' => [ 'shape' => 'GetCampaignStateBatchFailureCode', ], ], ], 'FailedCampaignStateResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedCampaignStateResponse', ], 'max' => 25, 'min' => 0, ], 'FailedProfileOutboundRequest' => [ 'type' => 'structure', 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', ], 'id' => [ 'shape' => 'ProfileOutboundRequestId', ], 'failureCode' => [ 'shape' => 'ProfileOutboundRequestFailureCode', ], ], ], 'FailedProfileOutboundRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedProfileOutboundRequest', ], 'max' => 20, 'min' => 0, ], 'FailedRequest' => [ 'type' => 'structure', 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', ], 'id' => [ 'shape' => 'DialRequestId', ], 'failureCode' => [ 'shape' => 'FailureCode', ], ], ], 'FailedRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedRequest', ], 'max' => 25, 'min' => 0, ], 'FailureCode' => [ 'type' => 'string', 'enum' => [ 'InvalidInput', 'RequestThrottled', 'UnknownError', 'BufferLimitExceeded', ], ], 'GetCampaignStateBatchFailureCode' => [ 'type' => 'string', 'enum' => [ 'ResourceNotFound', 'UnknownError', ], ], 'GetCampaignStateBatchRequest' => [ 'type' => 'structure', 'required' => [ 'campaignIds', ], 'members' => [ 'campaignIds' => [ 'shape' => 'GetCampaignStateBatchRequestCampaignIdsList', ], ], ], 'GetCampaignStateBatchRequestCampaignIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CampaignId', ], 'max' => 25, 'min' => 1, ], 'GetCampaignStateBatchResponse' => [ 'type' => 'structure', 'members' => [ 'successfulRequests' => [ 'shape' => 'SuccessfulCampaignStateResponseList', ], 'failedRequests' => [ 'shape' => 'FailedCampaignStateResponseList', ], ], ], 'GetCampaignStateRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetCampaignStateResponse' => [ 'type' => 'structure', 'members' => [ 'state' => [ 'shape' => 'CampaignState', ], ], ], 'GetConnectInstanceConfigRequest' => [ 'type' => 'structure', 'required' => [ 'connectInstanceId', ], 'members' => [ 'connectInstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'connectInstanceId', ], ], ], 'GetConnectInstanceConfigResponse' => [ 'type' => 'structure', 'members' => [ 'connectInstanceConfig' => [ 'shape' => 'InstanceConfig', ], ], ], 'GetInstanceOnboardingJobStatusRequest' => [ 'type' => 'structure', 'required' => [ 'connectInstanceId', ], 'members' => [ 'connectInstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'connectInstanceId', ], ], ], 'GetInstanceOnboardingJobStatusResponse' => [ 'type' => 'structure', 'members' => [ 'connectInstanceOnboardingJobStatus' => [ 'shape' => 'InstanceOnboardingJobStatus', ], ], ], 'InstanceConfig' => [ 'type' => 'structure', 'required' => [ 'connectInstanceId', 'serviceLinkedRoleArn', 'encryptionConfig', ], 'members' => [ 'connectInstanceId' => [ 'shape' => 'InstanceId', ], 'serviceLinkedRoleArn' => [ 'shape' => 'ServiceLinkedRoleArn', ], 'encryptionConfig' => [ 'shape' => 'EncryptionConfig', ], ], ], 'InstanceId' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[a-zA-Z0-9_\\-.]*', ], 'InstanceIdFilter' => [ 'type' => 'structure', 'required' => [ 'value', 'operator', ], 'members' => [ 'value' => [ 'shape' => 'InstanceId', ], 'operator' => [ 'shape' => 'InstanceIdFilterOperator', ], ], ], 'InstanceIdFilterOperator' => [ 'type' => 'string', 'enum' => [ 'Eq', ], ], 'InstanceOnboardingJobFailureCode' => [ 'type' => 'string', 'enum' => [ 'EVENT_BRIDGE_ACCESS_DENIED', 'EVENT_BRIDGE_MANAGED_RULE_LIMIT_EXCEEDED', 'IAM_ACCESS_DENIED', 'KMS_ACCESS_DENIED', 'KMS_KEY_NOT_FOUND', 'INTERNAL_FAILURE', ], ], 'InstanceOnboardingJobStatus' => [ 'type' => 'structure', 'required' => [ 'connectInstanceId', 'status', ], 'members' => [ 'connectInstanceId' => [ 'shape' => 'InstanceId', ], 'status' => [ 'shape' => 'InstanceOnboardingJobStatusCode', ], 'failureCode' => [ 'shape' => 'InstanceOnboardingJobFailureCode', ], ], ], 'InstanceOnboardingJobStatusCode' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', ], ], 'IntegrationConfig' => [ 'type' => 'structure', 'members' => [ 'customerProfiles' => [ 'shape' => 'CustomerProfilesIntegrationConfig', ], 'qConnect' => [ 'shape' => 'QConnectIntegrationConfig', ], ], 'union' => true, ], 'IntegrationIdentifier' => [ 'type' => 'structure', 'members' => [ 'customerProfiles' => [ 'shape' => 'CustomerProfilesIntegrationIdentifier', ], 'qConnect' => [ 'shape' => 'QConnectIntegrationIdentifier', ], ], 'union' => true, ], 'IntegrationSummary' => [ 'type' => 'structure', 'members' => [ 'customerProfiles' => [ 'shape' => 'CustomerProfilesIntegrationSummary', ], 'qConnect' => [ 'shape' => 'QConnectIntegrationSummary', ], ], 'union' => true, ], 'IntegrationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IntegrationSummary', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'xAmzErrorType' => [ 'shape' => 'XAmazonErrorType', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'InvalidCampaignStateException' => [ 'type' => 'structure', 'required' => [ 'state', 'message', ], 'members' => [ 'state' => [ 'shape' => 'CampaignState', ], 'message' => [ 'shape' => 'String', ], 'xAmzErrorType' => [ 'shape' => 'XAmazonErrorType', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'InvalidStateException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'xAmzErrorType' => [ 'shape' => 'XAmazonErrorType', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'Iso8601Date' => [ 'type' => 'string', 'pattern' => '\\d{4}-\\d{2}-\\d{2}', ], 'Iso8601Duration' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => 'P(?:([-+]?[0-9]+)D)?(T(?:([-+]?[0-9]+)H)?(?:([-+]?[0-9]+)M)?(?:([-+]?[0-9]+)(?:[.,]([0-9]{0,9}))?S)?)?', ], 'Iso8601Time' => [ 'type' => 'string', 'pattern' => 'T\\d{2}:\\d{2}', ], 'ListCampaignsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'filters' => [ 'shape' => 'CampaignFilters', ], ], ], 'ListCampaignsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'campaignSummaryList' => [ 'shape' => 'CampaignSummaryList', ], ], ], 'ListConnectInstanceIntegrationsRequest' => [ 'type' => 'structure', 'required' => [ 'connectInstanceId', ], 'members' => [ 'connectInstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'connectInstanceId', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListConnectInstanceIntegrationsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'integrationSummaryList' => [ 'shape' => 'IntegrationSummaryList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'LocalTimeZoneConfig' => [ 'type' => 'structure', 'members' => [ 'defaultTimeZone' => [ 'shape' => 'TimeZone', ], 'localTimeZoneDetection' => [ 'shape' => 'LocalTimeZoneDetection', ], ], ], 'LocalTimeZoneDetection' => [ 'type' => 'list', 'member' => [ 'shape' => 'LocalTimeZoneDetectionType', ], ], 'LocalTimeZoneDetectionType' => [ 'type' => 'string', 'enum' => [ 'ZIP_CODE', 'AREA_CODE', ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'ObjectTypeName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ObjectTypeNamesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'EventType', ], 'value' => [ 'shape' => 'ObjectTypeName', ], ], 'OpenHours' => [ 'type' => 'structure', 'members' => [ 'dailyHours' => [ 'shape' => 'DailyHours', ], ], 'union' => true, ], 'OutboundRequest' => [ 'type' => 'structure', 'required' => [ 'clientToken', 'expirationTime', 'channelSubtypeParameters', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', ], 'expirationTime' => [ 'shape' => 'TimeStamp', ], 'channelSubtypeParameters' => [ 'shape' => 'ChannelSubtypeParameters', ], ], ], 'OutboundRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutboundRequest', ], 'max' => 25, 'min' => 1, ], 'PauseCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'PredictiveConfig' => [ 'type' => 'structure', 'required' => [ 'bandwidthAllocation', ], 'members' => [ 'bandwidthAllocation' => [ 'shape' => 'BandwidthAllocation', ], ], ], 'ProfileId' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{32}', ], 'ProfileOutboundRequest' => [ 'type' => 'structure', 'required' => [ 'clientToken', 'profileId', ], 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', ], 'profileId' => [ 'shape' => 'ProfileId', ], 'expirationTime' => [ 'shape' => 'TimeStamp', ], ], ], 'ProfileOutboundRequestFailureCode' => [ 'type' => 'string', 'enum' => [ 'UnknownError', 'ResourceNotFound', 'Conflict', 'RequestThrottled', 'InvalidInput', ], ], 'ProfileOutboundRequestId' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[a-zA-Z0-9_\\-.]*', ], 'ProfileOutboundRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProfileOutboundRequest', ], 'max' => 20, 'min' => 1, ], 'ProgressiveConfig' => [ 'type' => 'structure', 'required' => [ 'bandwidthAllocation', ], 'members' => [ 'bandwidthAllocation' => [ 'shape' => 'BandwidthAllocation', ], ], ], 'PutConnectInstanceIntegrationRequest' => [ 'type' => 'structure', 'required' => [ 'connectInstanceId', 'integrationConfig', ], 'members' => [ 'connectInstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'connectInstanceId', ], 'integrationConfig' => [ 'shape' => 'IntegrationConfig', ], ], ], 'PutOutboundRequestBatchRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'outboundRequests', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], 'outboundRequests' => [ 'shape' => 'OutboundRequestList', ], ], ], 'PutOutboundRequestBatchResponse' => [ 'type' => 'structure', 'members' => [ 'successfulRequests' => [ 'shape' => 'SuccessfulRequestList', ], 'failedRequests' => [ 'shape' => 'FailedRequestList', ], ], ], 'PutProfileOutboundRequestBatchRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'profileOutboundRequests', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], 'profileOutboundRequests' => [ 'shape' => 'ProfileOutboundRequestList', ], ], ], 'PutProfileOutboundRequestBatchResponse' => [ 'type' => 'structure', 'members' => [ 'successfulRequests' => [ 'shape' => 'SuccessfulProfileOutboundRequestList', ], 'failedRequests' => [ 'shape' => 'FailedProfileOutboundRequestList', ], ], ], 'QConnectIntegrationConfig' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseArn', ], 'members' => [ 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], ], ], 'QConnectIntegrationIdentifier' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseArn', ], 'members' => [ 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], ], ], 'QConnectIntegrationSummary' => [ 'type' => 'structure', 'required' => [ 'knowledgeBaseArn', ], 'members' => [ 'knowledgeBaseArn' => [ 'shape' => 'Arn', ], ], ], 'QueueId' => [ 'type' => 'string', 'max' => 500, 'min' => 0, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'xAmzErrorType' => [ 'shape' => 'XAmazonErrorType', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RestrictedPeriod' => [ 'type' => 'structure', 'required' => [ 'startDate', 'endDate', ], 'members' => [ 'name' => [ 'shape' => 'RestrictedPeriodName', ], 'startDate' => [ 'shape' => 'Iso8601Date', ], 'endDate' => [ 'shape' => 'Iso8601Date', ], ], ], 'RestrictedPeriodList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RestrictedPeriod', ], ], 'RestrictedPeriodName' => [ 'type' => 'string', 'max' => 127, 'min' => 1, ], 'RestrictedPeriods' => [ 'type' => 'structure', 'members' => [ 'restrictedPeriodList' => [ 'shape' => 'RestrictedPeriodList', ], ], 'union' => true, ], 'ResumeCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'Schedule' => [ 'type' => 'structure', 'required' => [ 'startTime', 'endTime', ], 'members' => [ 'startTime' => [ 'shape' => 'TimeStamp', ], 'endTime' => [ 'shape' => 'TimeStamp', ], 'refreshFrequency' => [ 'shape' => 'Iso8601Duration', ], ], ], 'ServiceLinkedRoleArn' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'xAmzErrorType' => [ 'shape' => 'XAmazonErrorType', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SmsChannelSubtypeConfig' => [ 'type' => 'structure', 'required' => [ 'outboundMode', 'defaultOutboundConfig', ], 'members' => [ 'capacity' => [ 'shape' => 'Capacity', ], 'outboundMode' => [ 'shape' => 'SmsOutboundMode', ], 'defaultOutboundConfig' => [ 'shape' => 'SmsOutboundConfig', ], ], ], 'SmsChannelSubtypeParameters' => [ 'type' => 'structure', 'required' => [ 'destinationPhoneNumber', 'templateParameters', ], 'members' => [ 'destinationPhoneNumber' => [ 'shape' => 'DestinationPhoneNumber', ], 'connectSourcePhoneNumberArn' => [ 'shape' => 'Arn', ], 'templateArn' => [ 'shape' => 'Arn', ], 'templateParameters' => [ 'shape' => 'Attributes', ], ], ], 'SmsOutboundConfig' => [ 'type' => 'structure', 'required' => [ 'connectSourcePhoneNumberArn', 'wisdomTemplateArn', ], 'members' => [ 'connectSourcePhoneNumberArn' => [ 'shape' => 'Arn', ], 'wisdomTemplateArn' => [ 'shape' => 'Arn', ], ], ], 'SmsOutboundMode' => [ 'type' => 'structure', 'members' => [ 'agentless' => [ 'shape' => 'AgentlessConfig', ], ], 'union' => true, ], 'Source' => [ 'type' => 'structure', 'members' => [ 'customerProfilesSegmentArn' => [ 'shape' => 'Arn', ], 'eventTrigger' => [ 'shape' => 'EventTrigger', ], ], 'union' => true, ], 'SourcePhoneNumber' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'StartCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'StartInstanceOnboardingJobRequest' => [ 'type' => 'structure', 'required' => [ 'connectInstanceId', 'encryptionConfig', ], 'members' => [ 'connectInstanceId' => [ 'shape' => 'InstanceId', 'location' => 'uri', 'locationName' => 'connectInstanceId', ], 'encryptionConfig' => [ 'shape' => 'EncryptionConfig', ], ], ], 'StartInstanceOnboardingJobResponse' => [ 'type' => 'structure', 'members' => [ 'connectInstanceOnboardingJobStatus' => [ 'shape' => 'InstanceOnboardingJobStatus', ], ], ], 'StopCampaignRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'String' => [ 'type' => 'string', ], 'SuccessfulCampaignStateResponse' => [ 'type' => 'structure', 'members' => [ 'campaignId' => [ 'shape' => 'CampaignId', ], 'state' => [ 'shape' => 'CampaignState', ], ], ], 'SuccessfulCampaignStateResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuccessfulCampaignStateResponse', ], 'max' => 25, 'min' => 0, ], 'SuccessfulProfileOutboundRequest' => [ 'type' => 'structure', 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', ], 'id' => [ 'shape' => 'ProfileOutboundRequestId', ], ], ], 'SuccessfulProfileOutboundRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuccessfulProfileOutboundRequest', ], 'max' => 20, 'min' => 0, ], 'SuccessfulRequest' => [ 'type' => 'structure', 'members' => [ 'clientToken' => [ 'shape' => 'ClientToken', ], 'id' => [ 'shape' => 'DialRequestId', ], ], ], 'SuccessfulRequestList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SuccessfulRequest', ], 'max' => 25, 'min' => 0, ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '(?!aws:)[a-zA-Z+-=._:/]+', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'tags', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'arn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TelephonyChannelSubtypeConfig' => [ 'type' => 'structure', 'required' => [ 'outboundMode', 'defaultOutboundConfig', ], 'members' => [ 'capacity' => [ 'shape' => 'Capacity', ], 'connectQueueId' => [ 'shape' => 'QueueId', ], 'outboundMode' => [ 'shape' => 'TelephonyOutboundMode', ], 'defaultOutboundConfig' => [ 'shape' => 'TelephonyOutboundConfig', ], ], ], 'TelephonyChannelSubtypeParameters' => [ 'type' => 'structure', 'required' => [ 'destinationPhoneNumber', 'attributes', ], 'members' => [ 'destinationPhoneNumber' => [ 'shape' => 'DestinationPhoneNumber', ], 'attributes' => [ 'shape' => 'Attributes', ], 'connectSourcePhoneNumber' => [ 'shape' => 'SourcePhoneNumber', ], 'answerMachineDetectionConfig' => [ 'shape' => 'AnswerMachineDetectionConfig', ], ], ], 'TelephonyOutboundConfig' => [ 'type' => 'structure', 'required' => [ 'connectContactFlowId', ], 'members' => [ 'connectContactFlowId' => [ 'shape' => 'ContactFlowId', ], 'connectSourcePhoneNumber' => [ 'shape' => 'SourcePhoneNumber', ], 'answerMachineDetectionConfig' => [ 'shape' => 'AnswerMachineDetectionConfig', ], ], ], 'TelephonyOutboundMode' => [ 'type' => 'structure', 'members' => [ 'progressive' => [ 'shape' => 'ProgressiveConfig', ], 'predictive' => [ 'shape' => 'PredictiveConfig', ], 'agentless' => [ 'shape' => 'AgentlessConfig', ], ], 'union' => true, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'xAmzErrorType' => [ 'shape' => 'XAmazonErrorType', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'TimeRange' => [ 'type' => 'structure', 'required' => [ 'startTime', 'endTime', ], 'members' => [ 'startTime' => [ 'shape' => 'Iso8601Time', ], 'endTime' => [ 'shape' => 'Iso8601Time', ], ], ], 'TimeRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TimeRange', ], ], 'TimeStamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TimeWindow' => [ 'type' => 'structure', 'required' => [ 'openHours', ], 'members' => [ 'openHours' => [ 'shape' => 'OpenHours', ], 'restrictedPeriods' => [ 'shape' => 'RestrictedPeriods', ], ], ], 'TimeZone' => [ 'type' => 'string', 'max' => 50, 'min' => 0, 'pattern' => '[a-zA-Z0-9_\\-/]*', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'arn', 'tagKeys', ], 'members' => [ 'arn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'arn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateCampaignChannelSubtypeConfigRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'channelSubtypeConfig', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], 'channelSubtypeConfig' => [ 'shape' => 'ChannelSubtypeConfig', ], ], ], 'UpdateCampaignCommunicationLimitsRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'communicationLimitsOverride', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], 'communicationLimitsOverride' => [ 'shape' => 'CommunicationLimitsConfig', ], ], ], 'UpdateCampaignCommunicationTimeRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'communicationTimeConfig', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], 'communicationTimeConfig' => [ 'shape' => 'CommunicationTimeConfig', ], ], ], 'UpdateCampaignFlowAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'connectCampaignFlowArn', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], 'connectCampaignFlowArn' => [ 'shape' => 'Arn', ], ], ], 'UpdateCampaignNameRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'name', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], 'name' => [ 'shape' => 'CampaignName', ], ], ], 'UpdateCampaignScheduleRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'schedule', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], 'schedule' => [ 'shape' => 'Schedule', ], ], ], 'UpdateCampaignSourceRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'source', ], 'members' => [ 'id' => [ 'shape' => 'CampaignId', 'location' => 'uri', 'locationName' => 'id', ], 'source' => [ 'shape' => 'Source', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'xAmzErrorType' => [ 'shape' => 'XAmazonErrorType', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'XAmazonErrorType' => [ 'type' => 'string', ], ],];
