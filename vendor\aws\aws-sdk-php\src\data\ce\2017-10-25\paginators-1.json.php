<?php
// This file was auto-generated from sdk-root/src/data/ce/2017-10-25/paginators-1.json
return [ 'pagination' => [ 'GetAnomalies' => [ 'input_token' => 'NextPageToken', 'output_token' => 'NextPageToken', 'limit_key' => 'MaxResults', 'result_key' => 'Anomalies', ], 'GetAnomalyMonitors' => [ 'input_token' => 'NextPageToken', 'output_token' => 'NextPageToken', 'limit_key' => 'MaxResults', 'result_key' => 'AnomalyMonitors', ], 'GetAnomalySubscriptions' => [ 'input_token' => 'NextPageToken', 'output_token' => 'NextPageToken', 'limit_key' => 'MaxResults', 'result_key' => 'AnomalySubscriptions', ], 'GetCostAndUsageComparisons' => [ 'input_token' => 'NextPageToken', 'output_token' => 'NextPageToken', 'limit_key' => 'MaxResults', 'result_key' => 'CostAndUsageComparisons', ], 'GetCostComparisonDrivers' => [ 'input_token' => 'NextPageToken', 'output_token' => 'NextPageToken', 'limit_key' => 'MaxResults', 'result_key' => 'CostComparisonDrivers', ], 'GetSavingsPlansCoverage' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'GetSavingsPlansUtilizationDetails' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListCostAllocationTagBackfillHistory' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListCostAllocationTags' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], 'ListCostCategoryDefinitions' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'limit_key' => 'MaxResults', ], ],];
