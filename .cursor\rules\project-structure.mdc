---
description: 
globs: 
alwaysApply: true
---
# Struktur Proyek Lowongan Digital Growth Consultant

## File Utama
- [form1.php](mdc:form1.php) - Form aplikasi utama dengan 6 langkah
- [process_form.php](mdc:process_form.php) - Handler untuk memproses form dan upload file
- [database.sql](mdc:database.sql) - Struktur database

## Konfigurasi
- [config/database.php](mdc:config/database.php) - Konfigurasi koneksi database
- [config/env.php](mdc:config/env.php) - Variabel environment

## Struktur Database
Terdapat 2 tabel utama:
1. `applications` - Menyimpan data aplikasi
2. `portfolio_files` - Menyimpan file portfolio (relasi one-to-many dengan applications)

## Direktori Upload
- `uploads/portfolio/` - Direktori untuk menyimpan file portfolio
- Format file yang didukung: PDF, JPG, PNG, MP3, WAV, MP4, DOC, DOCX
- Maksimal ukuran file: 10MB per file

## Validasi Form
- Email harus valid
- Nomor telepon: 10-15 digit
- Semua field wajib kecuali yang opsional
- File portfolio opsional, maksimal 10MB per file

