<?php
// This file was auto-generated from sdk-root/src/data/bedrock-data-automation/2023-07-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-07-26', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'bedrock-data-automation', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Data Automation for Amazon Bedrock', 'serviceId' => 'Bedrock Data Automation', 'signatureVersion' => 'v4', 'signingName' => 'bedrock', 'uid' => 'bedrock-data-automation-2023-07-26', ], 'operations' => [ 'CreateBlueprint' => [ 'name' => 'CreateBlueprint', 'http' => [ 'method' => 'PUT', 'requestUri' => '/blueprints/', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateBlueprintRequest', ], 'output' => [ 'shape' => 'CreateBlueprintResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'CreateBlueprintVersion' => [ 'name' => 'CreateBlueprintVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/blueprints/{blueprintArn}/versions/', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateBlueprintVersionRequest', ], 'output' => [ 'shape' => 'CreateBlueprintVersionResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateDataAutomationProject' => [ 'name' => 'CreateDataAutomationProject', 'http' => [ 'method' => 'PUT', 'requestUri' => '/data-automation-projects/', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDataAutomationProjectRequest', ], 'output' => [ 'shape' => 'CreateDataAutomationProjectResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteBlueprint' => [ 'name' => 'DeleteBlueprint', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/blueprints/{blueprintArn}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteBlueprintRequest', ], 'output' => [ 'shape' => 'DeleteBlueprintResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteDataAutomationProject' => [ 'name' => 'DeleteDataAutomationProject', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/data-automation-projects/{projectArn}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteDataAutomationProjectRequest', ], 'output' => [ 'shape' => 'DeleteDataAutomationProjectResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetBlueprint' => [ 'name' => 'GetBlueprint', 'http' => [ 'method' => 'POST', 'requestUri' => '/blueprints/{blueprintArn}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBlueprintRequest', ], 'output' => [ 'shape' => 'GetBlueprintResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetDataAutomationProject' => [ 'name' => 'GetDataAutomationProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/data-automation-projects/{projectArn}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDataAutomationProjectRequest', ], 'output' => [ 'shape' => 'GetDataAutomationProjectResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListBlueprints' => [ 'name' => 'ListBlueprints', 'http' => [ 'method' => 'POST', 'requestUri' => '/blueprints/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBlueprintsRequest', ], 'output' => [ 'shape' => 'ListBlueprintsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListDataAutomationProjects' => [ 'name' => 'ListDataAutomationProjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/data-automation-projects/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDataAutomationProjectsRequest', ], 'output' => [ 'shape' => 'ListDataAutomationProjectsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/listTagsForResource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tagResource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/untagResource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateBlueprint' => [ 'name' => 'UpdateBlueprint', 'http' => [ 'method' => 'PUT', 'requestUri' => '/blueprints/{blueprintArn}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBlueprintRequest', ], 'output' => [ 'shape' => 'UpdateBlueprintResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateDataAutomationProject' => [ 'name' => 'UpdateDataAutomationProject', 'http' => [ 'method' => 'PUT', 'requestUri' => '/data-automation-projects/{projectArn}/', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDataAutomationProjectRequest', ], 'output' => [ 'shape' => 'UpdateDataAutomationProjectResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AudioExtractionCategory' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], 'types' => [ 'shape' => 'AudioExtractionCategoryTypes', ], ], ], 'AudioExtractionCategoryType' => [ 'type' => 'string', 'enum' => [ 'AUDIO_CONTENT_MODERATION', 'TRANSCRIPT', 'TOPIC_CONTENT_MODERATION', ], ], 'AudioExtractionCategoryTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudioExtractionCategoryType', ], ], 'AudioOverrideConfiguration' => [ 'type' => 'structure', 'members' => [ 'modalityProcessing' => [ 'shape' => 'ModalityProcessingConfiguration', ], ], ], 'AudioStandardExtraction' => [ 'type' => 'structure', 'required' => [ 'category', ], 'members' => [ 'category' => [ 'shape' => 'AudioExtractionCategory', ], ], ], 'AudioStandardGenerativeField' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], 'types' => [ 'shape' => 'AudioStandardGenerativeFieldTypes', ], ], ], 'AudioStandardGenerativeFieldType' => [ 'type' => 'string', 'enum' => [ 'AUDIO_SUMMARY', 'IAB', 'TOPIC_SUMMARY', ], ], 'AudioStandardGenerativeFieldTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudioStandardGenerativeFieldType', ], ], 'AudioStandardOutputConfiguration' => [ 'type' => 'structure', 'members' => [ 'extraction' => [ 'shape' => 'AudioStandardExtraction', ], 'generativeField' => [ 'shape' => 'AudioStandardGenerativeField', ], ], ], 'Blueprint' => [ 'type' => 'structure', 'required' => [ 'blueprintArn', 'schema', 'type', 'creationTime', 'lastModifiedTime', 'blueprintName', ], 'members' => [ 'blueprintArn' => [ 'shape' => 'BlueprintArn', ], 'schema' => [ 'shape' => 'BlueprintSchema', ], 'type' => [ 'shape' => 'Type', ], 'creationTime' => [ 'shape' => 'DateTimestamp', ], 'lastModifiedTime' => [ 'shape' => 'DateTimestamp', ], 'blueprintName' => [ 'shape' => 'BlueprintName', ], 'blueprintVersion' => [ 'shape' => 'BlueprintVersion', ], 'blueprintStage' => [ 'shape' => 'BlueprintStage', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'kmsEncryptionContext' => [ 'shape' => 'KmsEncryptionContext', ], ], ], 'BlueprintArn' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => 'arn:aws(|-cn|-us-gov):bedrock:[a-zA-Z0-9-]*:(aws|[0-9]{12}):blueprint/(bedrock-data-automation-public-[a-zA-Z0-9-_]{1,30}|[a-zA-Z0-9-]{12,36})', ], 'BlueprintFilter' => [ 'type' => 'structure', 'required' => [ 'blueprintArn', ], 'members' => [ 'blueprintArn' => [ 'shape' => 'BlueprintArn', ], 'blueprintVersion' => [ 'shape' => 'BlueprintVersion', ], 'blueprintStage' => [ 'shape' => 'BlueprintStage', ], ], ], 'BlueprintItem' => [ 'type' => 'structure', 'required' => [ 'blueprintArn', ], 'members' => [ 'blueprintArn' => [ 'shape' => 'BlueprintArn', ], 'blueprintVersion' => [ 'shape' => 'BlueprintVersion', ], 'blueprintStage' => [ 'shape' => 'BlueprintStage', ], ], ], 'BlueprintItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlueprintItem', ], ], 'BlueprintName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_]+', 'sensitive' => true, ], 'BlueprintSchema' => [ 'type' => 'string', 'max' => 100000, 'min' => 1, 'sensitive' => true, ], 'BlueprintStage' => [ 'type' => 'string', 'enum' => [ 'DEVELOPMENT', 'LIVE', ], ], 'BlueprintStageFilter' => [ 'type' => 'string', 'enum' => [ 'DEVELOPMENT', 'LIVE', 'ALL', ], ], 'BlueprintSummary' => [ 'type' => 'structure', 'required' => [ 'blueprintArn', 'creationTime', ], 'members' => [ 'blueprintArn' => [ 'shape' => 'BlueprintArn', ], 'blueprintVersion' => [ 'shape' => 'BlueprintVersion', ], 'blueprintStage' => [ 'shape' => 'BlueprintStage', ], 'blueprintName' => [ 'shape' => 'BlueprintName', ], 'creationTime' => [ 'shape' => 'DateTimestamp', ], 'lastModifiedTime' => [ 'shape' => 'DateTimestamp', ], ], ], 'BlueprintVersion' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[0-9]*', ], 'Blueprints' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlueprintSummary', ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 256, 'min' => 33, 'pattern' => '[a-zA-Z0-9](-*[a-zA-Z0-9]){0,256}', ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateBlueprintRequest' => [ 'type' => 'structure', 'required' => [ 'blueprintName', 'type', 'schema', ], 'members' => [ 'blueprintName' => [ 'shape' => 'BlueprintName', ], 'type' => [ 'shape' => 'Type', ], 'blueprintStage' => [ 'shape' => 'BlueprintStage', ], 'schema' => [ 'shape' => 'BlueprintSchema', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateBlueprintResponse' => [ 'type' => 'structure', 'required' => [ 'blueprint', ], 'members' => [ 'blueprint' => [ 'shape' => 'Blueprint', ], ], ], 'CreateBlueprintVersionRequest' => [ 'type' => 'structure', 'required' => [ 'blueprintArn', ], 'members' => [ 'blueprintArn' => [ 'shape' => 'BlueprintArn', 'location' => 'uri', 'locationName' => 'blueprintArn', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], ], ], 'CreateBlueprintVersionResponse' => [ 'type' => 'structure', 'required' => [ 'blueprint', ], 'members' => [ 'blueprint' => [ 'shape' => 'Blueprint', ], ], ], 'CreateDataAutomationProjectRequest' => [ 'type' => 'structure', 'required' => [ 'projectName', 'standardOutputConfiguration', ], 'members' => [ 'projectName' => [ 'shape' => 'DataAutomationProjectName', ], 'projectDescription' => [ 'shape' => 'DataAutomationProjectDescription', ], 'projectStage' => [ 'shape' => 'DataAutomationProjectStage', ], 'standardOutputConfiguration' => [ 'shape' => 'StandardOutputConfiguration', ], 'customOutputConfiguration' => [ 'shape' => 'CustomOutputConfiguration', ], 'overrideConfiguration' => [ 'shape' => 'OverrideConfiguration', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDataAutomationProjectResponse' => [ 'type' => 'structure', 'required' => [ 'projectArn', ], 'members' => [ 'projectArn' => [ 'shape' => 'DataAutomationProjectArn', ], 'projectStage' => [ 'shape' => 'DataAutomationProjectStage', ], 'status' => [ 'shape' => 'DataAutomationProjectStatus', ], ], ], 'CustomOutputConfiguration' => [ 'type' => 'structure', 'members' => [ 'blueprints' => [ 'shape' => 'BlueprintItems', ], ], ], 'DataAutomationProject' => [ 'type' => 'structure', 'required' => [ 'projectArn', 'creationTime', 'lastModifiedTime', 'projectName', 'status', ], 'members' => [ 'projectArn' => [ 'shape' => 'DataAutomationProjectArn', ], 'creationTime' => [ 'shape' => 'DateTimestamp', ], 'lastModifiedTime' => [ 'shape' => 'DateTimestamp', ], 'projectName' => [ 'shape' => 'DataAutomationProjectName', ], 'projectStage' => [ 'shape' => 'DataAutomationProjectStage', ], 'projectDescription' => [ 'shape' => 'DataAutomationProjectDescription', ], 'standardOutputConfiguration' => [ 'shape' => 'StandardOutputConfiguration', ], 'customOutputConfiguration' => [ 'shape' => 'CustomOutputConfiguration', ], 'overrideConfiguration' => [ 'shape' => 'OverrideConfiguration', ], 'status' => [ 'shape' => 'DataAutomationProjectStatus', ], 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'kmsEncryptionContext' => [ 'shape' => 'KmsEncryptionContext', ], ], ], 'DataAutomationProjectArn' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => 'arn:aws(|-cn|-us-gov):bedrock:[a-zA-Z0-9-]*:(aws|[0-9]{12}):data-automation-project/[a-zA-Z0-9-]{12,36}', ], 'DataAutomationProjectDescription' => [ 'type' => 'string', 'max' => 300, 'min' => 0, 'sensitive' => true, ], 'DataAutomationProjectFilter' => [ 'type' => 'structure', 'required' => [ 'projectArn', ], 'members' => [ 'projectArn' => [ 'shape' => 'DataAutomationProjectArn', ], 'projectStage' => [ 'shape' => 'DataAutomationProjectStage', ], ], ], 'DataAutomationProjectName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_]+', 'sensitive' => true, ], 'DataAutomationProjectStage' => [ 'type' => 'string', 'enum' => [ 'DEVELOPMENT', 'LIVE', ], ], 'DataAutomationProjectStageFilter' => [ 'type' => 'string', 'enum' => [ 'DEVELOPMENT', 'LIVE', 'ALL', ], ], 'DataAutomationProjectStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETED', 'IN_PROGRESS', 'FAILED', ], ], 'DataAutomationProjectSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataAutomationProjectSummary', ], ], 'DataAutomationProjectSummary' => [ 'type' => 'structure', 'required' => [ 'projectArn', 'creationTime', ], 'members' => [ 'projectArn' => [ 'shape' => 'DataAutomationProjectArn', ], 'projectStage' => [ 'shape' => 'DataAutomationProjectStage', ], 'projectName' => [ 'shape' => 'DataAutomationProjectName', ], 'creationTime' => [ 'shape' => 'DateTimestamp', ], ], ], 'DateTimestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DeleteBlueprintRequest' => [ 'type' => 'structure', 'required' => [ 'blueprintArn', ], 'members' => [ 'blueprintArn' => [ 'shape' => 'BlueprintArn', 'location' => 'uri', 'locationName' => 'blueprintArn', ], 'blueprintVersion' => [ 'shape' => 'BlueprintVersion', 'location' => 'querystring', 'locationName' => 'blueprintVersion', ], ], ], 'DeleteBlueprintResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteDataAutomationProjectRequest' => [ 'type' => 'structure', 'required' => [ 'projectArn', ], 'members' => [ 'projectArn' => [ 'shape' => 'DataAutomationProjectArn', 'location' => 'uri', 'locationName' => 'projectArn', ], ], ], 'DeleteDataAutomationProjectResponse' => [ 'type' => 'structure', 'required' => [ 'projectArn', ], 'members' => [ 'projectArn' => [ 'shape' => 'DataAutomationProjectArn', ], 'status' => [ 'shape' => 'DataAutomationProjectStatus', ], ], ], 'DesiredModality' => [ 'type' => 'string', 'enum' => [ 'IMAGE', 'DOCUMENT', 'AUDIO', 'VIDEO', ], ], 'DocumentBoundingBox' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], ], ], 'DocumentExtractionGranularity' => [ 'type' => 'structure', 'members' => [ 'types' => [ 'shape' => 'DocumentExtractionGranularityTypes', ], ], ], 'DocumentExtractionGranularityType' => [ 'type' => 'string', 'enum' => [ 'DOCUMENT', 'PAGE', 'ELEMENT', 'WORD', 'LINE', ], ], 'DocumentExtractionGranularityTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentExtractionGranularityType', ], ], 'DocumentOutputAdditionalFileFormat' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], ], ], 'DocumentOutputFormat' => [ 'type' => 'structure', 'required' => [ 'textFormat', 'additionalFileFormat', ], 'members' => [ 'textFormat' => [ 'shape' => 'DocumentOutputTextFormat', ], 'additionalFileFormat' => [ 'shape' => 'DocumentOutputAdditionalFileFormat', ], ], ], 'DocumentOutputTextFormat' => [ 'type' => 'structure', 'members' => [ 'types' => [ 'shape' => 'DocumentOutputTextFormatTypes', ], ], ], 'DocumentOutputTextFormatType' => [ 'type' => 'string', 'enum' => [ 'PLAIN_TEXT', 'MARKDOWN', 'HTML', 'CSV', ], ], 'DocumentOutputTextFormatTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'DocumentOutputTextFormatType', ], ], 'DocumentOverrideConfiguration' => [ 'type' => 'structure', 'members' => [ 'splitter' => [ 'shape' => 'SplitterConfiguration', ], 'modalityProcessing' => [ 'shape' => 'ModalityProcessingConfiguration', ], ], ], 'DocumentStandardExtraction' => [ 'type' => 'structure', 'required' => [ 'granularity', 'boundingBox', ], 'members' => [ 'granularity' => [ 'shape' => 'DocumentExtractionGranularity', ], 'boundingBox' => [ 'shape' => 'DocumentBoundingBox', ], ], ], 'DocumentStandardGenerativeField' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], ], ], 'DocumentStandardOutputConfiguration' => [ 'type' => 'structure', 'members' => [ 'extraction' => [ 'shape' => 'DocumentStandardExtraction', ], 'generativeField' => [ 'shape' => 'DocumentStandardGenerativeField', ], 'outputFormat' => [ 'shape' => 'DocumentOutputFormat', ], ], ], 'EncryptionConfiguration' => [ 'type' => 'structure', 'required' => [ 'kmsKeyId', ], 'members' => [ 'kmsKeyId' => [ 'shape' => 'KmsKeyId', ], 'kmsEncryptionContext' => [ 'shape' => 'KmsEncryptionContext', ], ], ], 'EncryptionContextKey' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => '.*\\S.*', ], 'EncryptionContextValue' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => '.*\\S.*', ], 'GetBlueprintRequest' => [ 'type' => 'structure', 'required' => [ 'blueprintArn', ], 'members' => [ 'blueprintArn' => [ 'shape' => 'BlueprintArn', 'location' => 'uri', 'locationName' => 'blueprintArn', ], 'blueprintVersion' => [ 'shape' => 'BlueprintVersion', ], 'blueprintStage' => [ 'shape' => 'BlueprintStage', ], ], ], 'GetBlueprintResponse' => [ 'type' => 'structure', 'required' => [ 'blueprint', ], 'members' => [ 'blueprint' => [ 'shape' => 'Blueprint', ], ], ], 'GetDataAutomationProjectRequest' => [ 'type' => 'structure', 'required' => [ 'projectArn', ], 'members' => [ 'projectArn' => [ 'shape' => 'DataAutomationProjectArn', 'location' => 'uri', 'locationName' => 'projectArn', ], 'projectStage' => [ 'shape' => 'DataAutomationProjectStage', ], ], ], 'GetDataAutomationProjectResponse' => [ 'type' => 'structure', 'required' => [ 'project', ], 'members' => [ 'project' => [ 'shape' => 'DataAutomationProject', ], ], ], 'ImageBoundingBox' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], ], ], 'ImageExtractionCategory' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], 'types' => [ 'shape' => 'ImageExtractionCategoryTypes', ], ], ], 'ImageExtractionCategoryType' => [ 'type' => 'string', 'enum' => [ 'CONTENT_MODERATION', 'TEXT_DETECTION', 'LOGOS', ], ], 'ImageExtractionCategoryTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageExtractionCategoryType', ], ], 'ImageOverrideConfiguration' => [ 'type' => 'structure', 'members' => [ 'modalityProcessing' => [ 'shape' => 'ModalityProcessingConfiguration', ], ], ], 'ImageStandardExtraction' => [ 'type' => 'structure', 'required' => [ 'category', 'boundingBox', ], 'members' => [ 'category' => [ 'shape' => 'ImageExtractionCategory', ], 'boundingBox' => [ 'shape' => 'ImageBoundingBox', ], ], ], 'ImageStandardGenerativeField' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], 'types' => [ 'shape' => 'ImageStandardGenerativeFieldTypes', ], ], ], 'ImageStandardGenerativeFieldType' => [ 'type' => 'string', 'enum' => [ 'IMAGE_SUMMARY', 'IAB', ], ], 'ImageStandardGenerativeFieldTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImageStandardGenerativeFieldType', ], ], 'ImageStandardOutputConfiguration' => [ 'type' => 'structure', 'members' => [ 'extraction' => [ 'shape' => 'ImageStandardExtraction', ], 'generativeField' => [ 'shape' => 'ImageStandardGenerativeField', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'KmsEncryptionContext' => [ 'type' => 'map', 'key' => [ 'shape' => 'EncryptionContextKey', ], 'value' => [ 'shape' => 'EncryptionContextValue', ], 'min' => 1, ], 'KmsKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]+', ], 'ListBlueprintsRequest' => [ 'type' => 'structure', 'members' => [ 'blueprintArn' => [ 'shape' => 'BlueprintArn', ], 'resourceOwner' => [ 'shape' => 'ResourceOwner', ], 'blueprintStageFilter' => [ 'shape' => 'BlueprintStageFilter', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'projectFilter' => [ 'shape' => 'DataAutomationProjectFilter', ], ], ], 'ListBlueprintsResponse' => [ 'type' => 'structure', 'required' => [ 'blueprints', ], 'members' => [ 'blueprints' => [ 'shape' => 'Blueprints', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListDataAutomationProjectsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'MaxResults', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'projectStageFilter' => [ 'shape' => 'DataAutomationProjectStageFilter', ], 'blueprintFilter' => [ 'shape' => 'BlueprintFilter', ], 'resourceOwner' => [ 'shape' => 'ResourceOwner', ], ], ], 'ListDataAutomationProjectsResponse' => [ 'type' => 'structure', 'required' => [ 'projects', ], 'members' => [ 'projects' => [ 'shape' => 'DataAutomationProjectSummaries', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TaggableResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagList', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ModalityProcessingConfiguration' => [ 'type' => 'structure', 'members' => [ 'state' => [ 'shape' => 'State', ], ], ], 'ModalityRoutingConfiguration' => [ 'type' => 'structure', 'members' => [ 'jpeg' => [ 'shape' => 'DesiredModality', ], 'png' => [ 'shape' => 'DesiredModality', ], 'mp4' => [ 'shape' => 'DesiredModality', ], 'mov' => [ 'shape' => 'DesiredModality', ], ], ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '\\S*', ], 'NonBlankString' => [ 'type' => 'string', 'pattern' => '[\\s\\S]+', ], 'OverrideConfiguration' => [ 'type' => 'structure', 'members' => [ 'document' => [ 'shape' => 'DocumentOverrideConfiguration', ], 'image' => [ 'shape' => 'ImageOverrideConfiguration', ], 'video' => [ 'shape' => 'VideoOverrideConfiguration', ], 'audio' => [ 'shape' => 'AudioOverrideConfiguration', ], 'modalityRouting' => [ 'shape' => 'ModalityRoutingConfiguration', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceOwner' => [ 'type' => 'string', 'enum' => [ 'SERVICE', 'ACCOUNT', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SplitterConfiguration' => [ 'type' => 'structure', 'members' => [ 'state' => [ 'shape' => 'State', ], ], ], 'StandardOutputConfiguration' => [ 'type' => 'structure', 'members' => [ 'document' => [ 'shape' => 'DocumentStandardOutputConfiguration', ], 'image' => [ 'shape' => 'ImageStandardOutputConfiguration', ], 'video' => [ 'shape' => 'VideoStandardOutputConfiguration', ], 'audio' => [ 'shape' => 'AudioStandardOutputConfiguration', ], ], ], 'State' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tags', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TaggableResourceArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TaggableResourceArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => 'arn:aws(|-cn|-us-gov):bedrock:[a-z0-9-]*:[0-9]{12}:(blueprint|data-automation-project)/[a-zA-Z0-9-]{12,36}', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'Type' => [ 'type' => 'string', 'enum' => [ 'DOCUMENT', 'IMAGE', 'AUDIO', 'VIDEO', ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tagKeys', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TaggableResourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateBlueprintRequest' => [ 'type' => 'structure', 'required' => [ 'blueprintArn', 'schema', ], 'members' => [ 'blueprintArn' => [ 'shape' => 'BlueprintArn', 'location' => 'uri', 'locationName' => 'blueprintArn', ], 'schema' => [ 'shape' => 'BlueprintSchema', ], 'blueprintStage' => [ 'shape' => 'BlueprintStage', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], ], ], 'UpdateBlueprintResponse' => [ 'type' => 'structure', 'required' => [ 'blueprint', ], 'members' => [ 'blueprint' => [ 'shape' => 'Blueprint', ], ], ], 'UpdateDataAutomationProjectRequest' => [ 'type' => 'structure', 'required' => [ 'projectArn', 'standardOutputConfiguration', ], 'members' => [ 'projectArn' => [ 'shape' => 'DataAutomationProjectArn', 'location' => 'uri', 'locationName' => 'projectArn', ], 'projectStage' => [ 'shape' => 'DataAutomationProjectStage', ], 'projectDescription' => [ 'shape' => 'DataAutomationProjectDescription', ], 'standardOutputConfiguration' => [ 'shape' => 'StandardOutputConfiguration', ], 'customOutputConfiguration' => [ 'shape' => 'CustomOutputConfiguration', ], 'overrideConfiguration' => [ 'shape' => 'OverrideConfiguration', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], ], ], 'UpdateDataAutomationProjectResponse' => [ 'type' => 'structure', 'required' => [ 'projectArn', ], 'members' => [ 'projectArn' => [ 'shape' => 'DataAutomationProjectArn', ], 'projectStage' => [ 'shape' => 'DataAutomationProjectStage', ], 'status' => [ 'shape' => 'DataAutomationProjectStatus', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'NonBlankString', ], 'message' => [ 'shape' => 'NonBlankString', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'VideoBoundingBox' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], ], ], 'VideoExtractionCategory' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], 'types' => [ 'shape' => 'VideoExtractionCategoryTypes', ], ], ], 'VideoExtractionCategoryType' => [ 'type' => 'string', 'enum' => [ 'CONTENT_MODERATION', 'TEXT_DETECTION', 'TRANSCRIPT', 'LOGOS', ], ], 'VideoExtractionCategoryTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'VideoExtractionCategoryType', ], ], 'VideoOverrideConfiguration' => [ 'type' => 'structure', 'members' => [ 'modalityProcessing' => [ 'shape' => 'ModalityProcessingConfiguration', ], ], ], 'VideoStandardExtraction' => [ 'type' => 'structure', 'required' => [ 'category', 'boundingBox', ], 'members' => [ 'category' => [ 'shape' => 'VideoExtractionCategory', ], 'boundingBox' => [ 'shape' => 'VideoBoundingBox', ], ], ], 'VideoStandardGenerativeField' => [ 'type' => 'structure', 'required' => [ 'state', ], 'members' => [ 'state' => [ 'shape' => 'State', ], 'types' => [ 'shape' => 'VideoStandardGenerativeFieldTypes', ], ], ], 'VideoStandardGenerativeFieldType' => [ 'type' => 'string', 'enum' => [ 'VIDEO_SUMMARY', 'IAB', 'CHAPTER_SUMMARY', ], ], 'VideoStandardGenerativeFieldTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'VideoStandardGenerativeFieldType', ], ], 'VideoStandardOutputConfiguration' => [ 'type' => 'structure', 'members' => [ 'extraction' => [ 'shape' => 'VideoStandardExtraction', ], 'generativeField' => [ 'shape' => 'VideoStandardGenerativeField', ], ], ], ],];
