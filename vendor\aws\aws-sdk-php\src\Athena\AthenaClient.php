<?php
namespace Aws\Athena;

use Aws\AwsClient;

/**
 * This client is used to interact with the **Amazon Athena** service.
 * @method \Aws\Result batchGetNamedQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchGetNamedQueryAsync(array $args = [])
 * @method \Aws\Result batchGetPreparedStatement(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchGetPreparedStatementAsync(array $args = [])
 * @method \Aws\Result batchGetQueryExecution(array $args = [])
 * @method \GuzzleHttp\Promise\Promise batchGetQueryExecutionAsync(array $args = [])
 * @method \Aws\Result cancelCapacityReservation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise cancelCapacityReservationAsync(array $args = [])
 * @method \Aws\Result createCapacityReservation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCapacityReservationAsync(array $args = [])
 * @method \Aws\Result createDataCatalog(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createDataCatalogAsync(array $args = [])
 * @method \Aws\Result createNamedQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createNamedQueryAsync(array $args = [])
 * @method \Aws\Result createNotebook(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createNotebookAsync(array $args = [])
 * @method \Aws\Result createPreparedStatement(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPreparedStatementAsync(array $args = [])
 * @method \Aws\Result createPresignedNotebookUrl(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createPresignedNotebookUrlAsync(array $args = [])
 * @method \Aws\Result createWorkGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createWorkGroupAsync(array $args = [])
 * @method \Aws\Result deleteCapacityReservation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCapacityReservationAsync(array $args = [])
 * @method \Aws\Result deleteDataCatalog(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteDataCatalogAsync(array $args = [])
 * @method \Aws\Result deleteNamedQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteNamedQueryAsync(array $args = [])
 * @method \Aws\Result deleteNotebook(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteNotebookAsync(array $args = [])
 * @method \Aws\Result deletePreparedStatement(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deletePreparedStatementAsync(array $args = [])
 * @method \Aws\Result deleteWorkGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteWorkGroupAsync(array $args = [])
 * @method \Aws\Result exportNotebook(array $args = [])
 * @method \GuzzleHttp\Promise\Promise exportNotebookAsync(array $args = [])
 * @method \Aws\Result getCalculationExecution(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCalculationExecutionAsync(array $args = [])
 * @method \Aws\Result getCalculationExecutionCode(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCalculationExecutionCodeAsync(array $args = [])
 * @method \Aws\Result getCalculationExecutionStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCalculationExecutionStatusAsync(array $args = [])
 * @method \Aws\Result getCapacityAssignmentConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCapacityAssignmentConfigurationAsync(array $args = [])
 * @method \Aws\Result getCapacityReservation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCapacityReservationAsync(array $args = [])
 * @method \Aws\Result getDataCatalog(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDataCatalogAsync(array $args = [])
 * @method \Aws\Result getDatabase(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getDatabaseAsync(array $args = [])
 * @method \Aws\Result getNamedQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getNamedQueryAsync(array $args = [])
 * @method \Aws\Result getNotebookMetadata(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getNotebookMetadataAsync(array $args = [])
 * @method \Aws\Result getPreparedStatement(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getPreparedStatementAsync(array $args = [])
 * @method \Aws\Result getQueryExecution(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQueryExecutionAsync(array $args = [])
 * @method \Aws\Result getQueryResults(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQueryResultsAsync(array $args = [])
 * @method \Aws\Result getQueryRuntimeStatistics(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getQueryRuntimeStatisticsAsync(array $args = [])
 * @method \Aws\Result getSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSessionAsync(array $args = [])
 * @method \Aws\Result getSessionStatus(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getSessionStatusAsync(array $args = [])
 * @method \Aws\Result getTableMetadata(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getTableMetadataAsync(array $args = [])
 * @method \Aws\Result getWorkGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getWorkGroupAsync(array $args = [])
 * @method \Aws\Result importNotebook(array $args = [])
 * @method \GuzzleHttp\Promise\Promise importNotebookAsync(array $args = [])
 * @method \Aws\Result listApplicationDPUSizes(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listApplicationDPUSizesAsync(array $args = [])
 * @method \Aws\Result listCalculationExecutions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCalculationExecutionsAsync(array $args = [])
 * @method \Aws\Result listCapacityReservations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCapacityReservationsAsync(array $args = [])
 * @method \Aws\Result listDataCatalogs(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDataCatalogsAsync(array $args = [])
 * @method \Aws\Result listDatabases(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listDatabasesAsync(array $args = [])
 * @method \Aws\Result listEngineVersions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listEngineVersionsAsync(array $args = [])
 * @method \Aws\Result listExecutors(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listExecutorsAsync(array $args = [])
 * @method \Aws\Result listNamedQueries(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listNamedQueriesAsync(array $args = [])
 * @method \Aws\Result listNotebookMetadata(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listNotebookMetadataAsync(array $args = [])
 * @method \Aws\Result listNotebookSessions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listNotebookSessionsAsync(array $args = [])
 * @method \Aws\Result listPreparedStatements(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listPreparedStatementsAsync(array $args = [])
 * @method \Aws\Result listQueryExecutions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listQueryExecutionsAsync(array $args = [])
 * @method \Aws\Result listSessions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listSessionsAsync(array $args = [])
 * @method \Aws\Result listTableMetadata(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTableMetadataAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result listWorkGroups(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listWorkGroupsAsync(array $args = [])
 * @method \Aws\Result putCapacityAssignmentConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise putCapacityAssignmentConfigurationAsync(array $args = [])
 * @method \Aws\Result startCalculationExecution(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startCalculationExecutionAsync(array $args = [])
 * @method \Aws\Result startQueryExecution(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startQueryExecutionAsync(array $args = [])
 * @method \Aws\Result startSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise startSessionAsync(array $args = [])
 * @method \Aws\Result stopCalculationExecution(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopCalculationExecutionAsync(array $args = [])
 * @method \Aws\Result stopQueryExecution(array $args = [])
 * @method \GuzzleHttp\Promise\Promise stopQueryExecutionAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result terminateSession(array $args = [])
 * @method \GuzzleHttp\Promise\Promise terminateSessionAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateCapacityReservation(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCapacityReservationAsync(array $args = [])
 * @method \Aws\Result updateDataCatalog(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateDataCatalogAsync(array $args = [])
 * @method \Aws\Result updateNamedQuery(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateNamedQueryAsync(array $args = [])
 * @method \Aws\Result updateNotebook(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateNotebookAsync(array $args = [])
 * @method \Aws\Result updateNotebookMetadata(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateNotebookMetadataAsync(array $args = [])
 * @method \Aws\Result updatePreparedStatement(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updatePreparedStatementAsync(array $args = [])
 * @method \Aws\Result updateWorkGroup(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateWorkGroupAsync(array $args = [])
 */
class AthenaClient extends AwsClient {}
