<?php
// This file was auto-generated from sdk-root/src/data/partnercentral-selling/2022-07-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-07-26', 'endpointPrefix' => 'partnercentral-selling', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'Partner Central Selling API', 'serviceId' => 'PartnerCentral Selling', 'signatureVersion' => 'v4', 'signingName' => 'partnercentral-selling', 'targetPrefix' => 'AWSPartnerCentralSelling', 'uid' => 'partnercentral-selling-2022-07-26', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AcceptEngagementInvitation' => [ 'name' => 'AcceptEngagementInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AcceptEngagementInvitationRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'AssignOpportunity' => [ 'name' => 'AssignOpportunity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssignOpportunityRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'AssociateOpportunity' => [ 'name' => 'AssociateOpportunity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateOpportunityRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'CreateEngagement' => [ 'name' => 'CreateEngagement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEngagementRequest', ], 'output' => [ 'shape' => 'CreateEngagementResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateEngagementInvitation' => [ 'name' => 'CreateEngagementInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEngagementInvitationRequest', ], 'output' => [ 'shape' => 'CreateEngagementInvitationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateOpportunity' => [ 'name' => 'CreateOpportunity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateOpportunityRequest', ], 'output' => [ 'shape' => 'CreateOpportunityResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateResourceSnapshot' => [ 'name' => 'CreateResourceSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateResourceSnapshotRequest', ], 'output' => [ 'shape' => 'CreateResourceSnapshotResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateResourceSnapshotJob' => [ 'name' => 'CreateResourceSnapshotJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateResourceSnapshotJobRequest', ], 'output' => [ 'shape' => 'CreateResourceSnapshotJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteResourceSnapshotJob' => [ 'name' => 'DeleteResourceSnapshotJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourceSnapshotJobRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DisassociateOpportunity' => [ 'name' => 'DisassociateOpportunity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DisassociateOpportunityRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAwsOpportunitySummary' => [ 'name' => 'GetAwsOpportunitySummary', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAwsOpportunitySummaryRequest', ], 'output' => [ 'shape' => 'GetAwsOpportunitySummaryResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetEngagement' => [ 'name' => 'GetEngagement', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEngagementRequest', ], 'output' => [ 'shape' => 'GetEngagementResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetEngagementInvitation' => [ 'name' => 'GetEngagementInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEngagementInvitationRequest', ], 'output' => [ 'shape' => 'GetEngagementInvitationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetOpportunity' => [ 'name' => 'GetOpportunity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetOpportunityRequest', ], 'output' => [ 'shape' => 'GetOpportunityResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetResourceSnapshot' => [ 'name' => 'GetResourceSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourceSnapshotRequest', ], 'output' => [ 'shape' => 'GetResourceSnapshotResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetResourceSnapshotJob' => [ 'name' => 'GetResourceSnapshotJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourceSnapshotJobRequest', ], 'output' => [ 'shape' => 'GetResourceSnapshotJobResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetSellingSystemSettings' => [ 'name' => 'GetSellingSystemSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSellingSystemSettingsRequest', ], 'output' => [ 'shape' => 'GetSellingSystemSettingsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEngagementByAcceptingInvitationTasks' => [ 'name' => 'ListEngagementByAcceptingInvitationTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEngagementByAcceptingInvitationTasksRequest', ], 'output' => [ 'shape' => 'ListEngagementByAcceptingInvitationTasksResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEngagementFromOpportunityTasks' => [ 'name' => 'ListEngagementFromOpportunityTasks', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEngagementFromOpportunityTasksRequest', ], 'output' => [ 'shape' => 'ListEngagementFromOpportunityTasksResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEngagementInvitations' => [ 'name' => 'ListEngagementInvitations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEngagementInvitationsRequest', ], 'output' => [ 'shape' => 'ListEngagementInvitationsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEngagementMembers' => [ 'name' => 'ListEngagementMembers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEngagementMembersRequest', ], 'output' => [ 'shape' => 'ListEngagementMembersResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEngagementResourceAssociations' => [ 'name' => 'ListEngagementResourceAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEngagementResourceAssociationsRequest', ], 'output' => [ 'shape' => 'ListEngagementResourceAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListEngagements' => [ 'name' => 'ListEngagements', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListEngagementsRequest', ], 'output' => [ 'shape' => 'ListEngagementsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListOpportunities' => [ 'name' => 'ListOpportunities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListOpportunitiesRequest', ], 'output' => [ 'shape' => 'ListOpportunitiesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListResourceSnapshotJobs' => [ 'name' => 'ListResourceSnapshotJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourceSnapshotJobsRequest', ], 'output' => [ 'shape' => 'ListResourceSnapshotJobsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListResourceSnapshots' => [ 'name' => 'ListResourceSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourceSnapshotsRequest', ], 'output' => [ 'shape' => 'ListResourceSnapshotsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListSolutions' => [ 'name' => 'ListSolutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSolutionsRequest', ], 'output' => [ 'shape' => 'ListSolutionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'PutSellingSystemSettings' => [ 'name' => 'PutSellingSystemSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutSellingSystemSettingsRequest', ], 'output' => [ 'shape' => 'PutSellingSystemSettingsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'RejectEngagementInvitation' => [ 'name' => 'RejectEngagementInvitation', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RejectEngagementInvitationRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartEngagementByAcceptingInvitationTask' => [ 'name' => 'StartEngagementByAcceptingInvitationTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartEngagementByAcceptingInvitationTaskRequest', ], 'output' => [ 'shape' => 'StartEngagementByAcceptingInvitationTaskResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartEngagementFromOpportunityTask' => [ 'name' => 'StartEngagementFromOpportunityTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartEngagementFromOpportunityTaskRequest', ], 'output' => [ 'shape' => 'StartEngagementFromOpportunityTaskResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartResourceSnapshotJob' => [ 'name' => 'StartResourceSnapshotJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartResourceSnapshotJobRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'StopResourceSnapshotJob' => [ 'name' => 'StopResourceSnapshotJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopResourceSnapshotJobRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'SubmitOpportunity' => [ 'name' => 'SubmitOpportunity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SubmitOpportunityRequest', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateOpportunity' => [ 'name' => 'UpdateOpportunity', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateOpportunityRequest', ], 'output' => [ 'shape' => 'UpdateOpportunityResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], ], 'shapes' => [ 'AcceptEngagementInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'Identifier', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'Identifier' => [ 'shape' => 'EngagementInvitationArnOrIdentifier', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Account' => [ 'type' => 'structure', 'required' => [ 'CompanyName', ], 'members' => [ 'Address' => [ 'shape' => 'Address', ], 'AwsAccountId' => [ 'shape' => 'AwsAccount', ], 'CompanyName' => [ 'shape' => 'AccountCompanyNameString', ], 'Duns' => [ 'shape' => 'DunsNumber', ], 'Industry' => [ 'shape' => 'Industry', ], 'OtherIndustry' => [ 'shape' => 'AccountOtherIndustryString', ], 'WebsiteUrl' => [ 'shape' => 'WebsiteUrl', ], ], ], 'AccountCompanyNameString' => [ 'type' => 'string', 'max' => 120, 'min' => 0, 'sensitive' => true, ], 'AccountOtherIndustryString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'AccountReceiver' => [ 'type' => 'structure', 'required' => [ 'AwsAccountId', ], 'members' => [ 'Alias' => [ 'shape' => 'Alias', ], 'AwsAccountId' => [ 'shape' => 'AwsAccount', ], ], ], 'AccountSummary' => [ 'type' => 'structure', 'required' => [ 'CompanyName', ], 'members' => [ 'Address' => [ 'shape' => 'AddressSummary', ], 'CompanyName' => [ 'shape' => 'AccountSummaryCompanyNameString', ], 'Industry' => [ 'shape' => 'Industry', ], 'OtherIndustry' => [ 'shape' => 'AccountSummaryOtherIndustryString', ], 'WebsiteUrl' => [ 'shape' => 'WebsiteUrl', ], ], ], 'AccountSummaryCompanyNameString' => [ 'type' => 'string', 'max' => 120, 'min' => 0, 'sensitive' => true, ], 'AccountSummaryOtherIndustryString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'Address' => [ 'type' => 'structure', 'members' => [ 'City' => [ 'shape' => 'AddressCityString', ], 'CountryCode' => [ 'shape' => 'CountryCode', ], 'PostalCode' => [ 'shape' => 'AddressPostalCodeString', ], 'StateOrRegion' => [ 'shape' => 'AddressPart', ], 'StreetAddress' => [ 'shape' => 'AddressStreetAddressString', ], ], ], 'AddressCityString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'sensitive' => true, ], 'AddressPart' => [ 'type' => 'string', 'sensitive' => true, ], 'AddressPostalCodeString' => [ 'type' => 'string', 'max' => 20, 'min' => 0, 'sensitive' => true, ], 'AddressStreetAddressString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'sensitive' => true, ], 'AddressSummary' => [ 'type' => 'structure', 'members' => [ 'City' => [ 'shape' => 'AddressSummaryCityString', ], 'CountryCode' => [ 'shape' => 'CountryCode', ], 'PostalCode' => [ 'shape' => 'AddressSummaryPostalCodeString', ], 'StateOrRegion' => [ 'shape' => 'AddressPart', ], ], ], 'AddressSummaryCityString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'sensitive' => true, ], 'AddressSummaryPostalCodeString' => [ 'type' => 'string', 'max' => 20, 'min' => 0, 'sensitive' => true, ], 'Alias' => [ 'type' => 'string', 'max' => 80, 'min' => 0, 'sensitive' => true, ], 'ApnPrograms' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'AssignOpportunityRequest' => [ 'type' => 'structure', 'required' => [ 'Assignee', 'Catalog', 'Identifier', ], 'members' => [ 'Assignee' => [ 'shape' => 'AssigneeContact', ], 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'Identifier' => [ 'shape' => 'OpportunityIdentifier', ], ], ], 'AssigneeContact' => [ 'type' => 'structure', 'required' => [ 'BusinessTitle', 'Email', 'FirstName', 'LastName', ], 'members' => [ 'BusinessTitle' => [ 'shape' => 'JobTitle', ], 'Email' => [ 'shape' => 'Email', ], 'FirstName' => [ 'shape' => 'AssigneeContactFirstNameString', ], 'LastName' => [ 'shape' => 'AssigneeContactLastNameString', ], ], ], 'AssigneeContactFirstNameString' => [ 'type' => 'string', 'max' => 80, 'min' => 0, 'sensitive' => true, ], 'AssigneeContactLastNameString' => [ 'type' => 'string', 'max' => 80, 'min' => 0, 'sensitive' => true, ], 'AssociateOpportunityRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'OpportunityIdentifier', 'RelatedEntityIdentifier', 'RelatedEntityType', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'OpportunityIdentifier' => [ 'shape' => 'OpportunityIdentifier', ], 'RelatedEntityIdentifier' => [ 'shape' => 'AssociateOpportunityRequestRelatedEntityIdentifierString', ], 'RelatedEntityType' => [ 'shape' => 'RelatedEntityType', ], ], ], 'AssociateOpportunityRequestRelatedEntityIdentifierString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'AwsAccount' => [ 'type' => 'string', 'pattern' => '^([0-9]{12}|\\w{1,12})$', 'sensitive' => true, ], 'AwsAccountIdOrAliasList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAccount', ], 'max' => 10, 'min' => 1, ], 'AwsAccountList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAccount', ], 'max' => 10, 'min' => 1, ], 'AwsClosedLostReason' => [ 'type' => 'string', 'enum' => [ 'Administrative', 'Business Associate Agreement', 'Company Acquired/Dissolved', 'Competitive Offering', 'Customer Data Requirement', 'Customer Deficiency', 'Customer Experience', 'Delay / Cancellation of Project', 'Duplicate', 'Duplicate Opportunity', 'Executive Blocker', 'Failed Vetting', 'Feature Limitation', 'Financial/Commercial', 'Insufficient Amazon Value', 'Insufficient AWS Value', 'International Constraints', 'Legal / Tax / Regulatory', 'Legal Terms and Conditions', 'Lost to Competitor', 'Lost to Competitor - Google', 'Lost to Competitor - Microsoft', 'Lost to Competitor - Other', 'Lost to Competitor - Rackspace', 'Lost to Competitor - SoftLayer', 'Lost to Competitor - VMWare', 'No Customer Reference', 'No Integration Resources', 'No Opportunity', 'No Perceived Value of MP', 'No Response', 'Not Committed to AWS', 'No Update', 'On Premises Deployment', 'Other', 'Other (Details in Description)', 'Partner Gap', 'Past Due', 'People/Relationship/Governance', 'Platform Technology Limitation', 'Preference for Competitor', 'Price', 'Product/Technology', 'Product Not on AWS', 'Security / Compliance', 'Self-Service', 'Technical Limitations', 'Term Sheet Impasse', ], ], 'AwsFundingUsed' => [ 'type' => 'string', 'enum' => [ 'Yes', 'No', ], ], 'AwsMarketplaceOfferIdentifier' => [ 'type' => 'string', 'pattern' => '^arn:aws:aws-marketplace:[a-z]{1,2}-[a-z]*-\\d+:\\d{12}:AWSMarketplace/Offer/.*$', ], 'AwsMarketplaceOfferIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsMarketplaceOfferIdentifier', ], ], 'AwsMemberBusinessTitle' => [ 'type' => 'string', 'enum' => [ 'AWSSalesRep', 'AWSAccountOwner', 'WWPSPDM', 'PDM', 'PSM', 'ISVSM', ], ], 'AwsOpportunityCustomer' => [ 'type' => 'structure', 'members' => [ 'Contacts' => [ 'shape' => 'CustomerContactsList', ], ], ], 'AwsOpportunityInsights' => [ 'type' => 'structure', 'members' => [ 'EngagementScore' => [ 'shape' => 'EngagementScore', ], 'NextBestActions' => [ 'shape' => 'String', ], ], ], 'AwsOpportunityLifeCycle' => [ 'type' => 'structure', 'members' => [ 'ClosedLostReason' => [ 'shape' => 'AwsClosedLostReason', ], 'NextSteps' => [ 'shape' => 'AwsOpportunityLifeCycleNextStepsString', ], 'NextStepsHistory' => [ 'shape' => 'AwsOpportunityLifeCycleNextStepsHistoryList', ], 'Stage' => [ 'shape' => 'AwsOpportunityStage', ], 'TargetCloseDate' => [ 'shape' => 'Date', ], ], ], 'AwsOpportunityLifeCycleNextStepsHistoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProfileNextStepsHistory', ], 'max' => 50, 'min' => 0, ], 'AwsOpportunityLifeCycleNextStepsString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'sensitive' => true, ], 'AwsOpportunityProject' => [ 'type' => 'structure', 'members' => [ 'ExpectedCustomerSpend' => [ 'shape' => 'ExpectedCustomerSpendList', ], ], ], 'AwsOpportunityRelatedEntities' => [ 'type' => 'structure', 'members' => [ 'AwsProducts' => [ 'shape' => 'AwsProductIdentifiers', ], 'Solutions' => [ 'shape' => 'SolutionIdentifiers', ], ], ], 'AwsOpportunityStage' => [ 'type' => 'string', 'enum' => [ 'Not Started', 'In Progress', 'Prospect', 'Engaged', 'Identified', 'Qualify', 'Research', 'Seller Engaged', 'Evaluating', 'Seller Registered', 'Term Sheet Negotiation', 'Contract Negotiation', 'Onboarding', 'Building Integration', 'Qualified', 'On-hold', 'Technical Validation', 'Business Validation', 'Committed', 'Launched', 'Deferred to Partner', 'Closed Lost', 'Completed', 'Closed Incomplete', ], ], 'AwsOpportunityTeamMembersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsTeamMember', ], ], 'AwsProductIdentifier' => [ 'type' => 'string', ], 'AwsProductIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsProductIdentifier', ], ], 'AwsSubmission' => [ 'type' => 'structure', 'required' => [ 'InvolvementType', ], 'members' => [ 'InvolvementType' => [ 'shape' => 'SalesInvolvementType', ], 'Visibility' => [ 'shape' => 'Visibility', ], ], ], 'AwsTeamMember' => [ 'type' => 'structure', 'members' => [ 'BusinessTitle' => [ 'shape' => 'AwsMemberBusinessTitle', ], 'Email' => [ 'shape' => 'Email', ], 'FirstName' => [ 'shape' => 'AwsTeamMemberFirstNameString', ], 'LastName' => [ 'shape' => 'AwsTeamMemberLastNameString', ], ], ], 'AwsTeamMemberFirstNameString' => [ 'type' => 'string', 'max' => 80, 'min' => 0, 'sensitive' => true, ], 'AwsTeamMemberLastNameString' => [ 'type' => 'string', 'max' => 80, 'min' => 0, 'sensitive' => true, ], 'CatalogIdentifier' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z]+$', ], 'Channel' => [ 'type' => 'string', 'enum' => [ 'AWS Marketing Central', 'Content Syndication', 'Display', 'Email', 'Live Event', 'Out Of Home (OOH)', 'Print', 'Search', 'Social', 'Telemarketing', 'TV', 'Video', 'Virtual Event', ], ], 'Channels' => [ 'type' => 'list', 'member' => [ 'shape' => 'Channel', ], ], 'ClientToken' => [ 'type' => 'string', 'pattern' => '^[!-~]{1,64}$', ], 'ClosedLostReason' => [ 'type' => 'string', 'enum' => [ 'Customer Deficiency', 'Delay / Cancellation of Project', 'Legal / Tax / Regulatory', 'Lost to Competitor - Google', 'Lost to Competitor - Microsoft', 'Lost to Competitor - SoftLayer', 'Lost to Competitor - VMWare', 'Lost to Competitor - Other', 'No Opportunity', 'On Premises Deployment', 'Partner Gap', 'Price', 'Security / Compliance', 'Technical Limitations', 'Customer Experience', 'Other', 'People/Relationship/Governance', 'Product/Technology', 'Financial/Commercial', ], ], 'CompanyName' => [ 'type' => 'string', 'max' => 120, 'min' => 1, 'sensitive' => true, ], 'CompanyWebsiteUrl' => [ 'type' => 'string', 'max' => 255, 'min' => 4, 'pattern' => '^((http|https)://)??(www[.])??([a-zA-Z0-9]|-)+?([.][a-zA-Z0-9(-|/|=|?)??]+?)+?$', 'sensitive' => true, ], 'CompetitorName' => [ 'type' => 'string', 'enum' => [ 'Oracle Cloud', 'On-Prem', 'Co-location', 'Akamai', 'AliCloud', 'Google Cloud Platform', 'IBM Softlayer', 'Microsoft Azure', 'Other- Cost Optimization', 'No Competition', '*Other', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'Contact' => [ 'type' => 'structure', 'members' => [ 'BusinessTitle' => [ 'shape' => 'JobTitle', ], 'Email' => [ 'shape' => 'Email', ], 'FirstName' => [ 'shape' => 'ContactFirstNameString', ], 'LastName' => [ 'shape' => 'ContactLastNameString', ], 'Phone' => [ 'shape' => 'PhoneNumber', ], ], ], 'ContactFirstNameString' => [ 'type' => 'string', 'max' => 80, 'min' => 0, 'sensitive' => true, ], 'ContactLastNameString' => [ 'type' => 'string', 'max' => 80, 'min' => 0, 'sensitive' => true, ], 'CountryCode' => [ 'type' => 'string', 'enum' => [ 'US', 'AF', 'AX', 'AL', 'DZ', 'AS', 'AD', 'AO', 'AI', 'AQ', 'AG', 'AR', 'AM', 'AW', 'AU', 'AT', 'AZ', 'BS', 'BH', 'BD', 'BB', 'BY', 'BE', 'BZ', 'BJ', 'BM', 'BT', 'BO', 'BQ', 'BA', 'BW', 'BV', 'BR', 'IO', 'BN', 'BG', 'BF', 'BI', 'KH', 'CM', 'CA', 'CV', 'KY', 'CF', 'TD', 'CL', 'CN', 'CX', 'CC', 'CO', 'KM', 'CG', 'CK', 'CR', 'CI', 'HR', 'CU', 'CW', 'CY', 'CZ', 'CD', 'DK', 'DJ', 'DM', 'DO', 'EC', 'EG', 'SV', 'GQ', 'ER', 'EE', 'ET', 'FK', 'FO', 'FJ', 'FI', 'FR', 'GF', 'PF', 'TF', 'GA', 'GM', 'GE', 'DE', 'GH', 'GI', 'GR', 'GL', 'GD', 'GP', 'GU', 'GT', 'GG', 'GN', 'GW', 'GY', 'HT', 'HM', 'VA', 'HN', 'HK', 'HU', 'IS', 'IN', 'ID', 'IR', 'IQ', 'IE', 'IM', 'IL', 'IT', 'JM', 'JP', 'JE', 'JO', 'KZ', 'KE', 'KI', 'KR', 'KW', 'KG', 'LA', 'LV', 'LB', 'LS', 'LR', 'LY', 'LI', 'LT', 'LU', 'MO', 'MK', 'MG', 'MW', 'MY', 'MV', 'ML', 'MT', 'MH', 'MQ', 'MR', 'MU', 'YT', 'MX', 'FM', 'MD', 'MC', 'MN', 'ME', 'MS', 'MA', 'MZ', 'MM', 'NA', 'NR', 'NP', 'NL', 'AN', 'NC', 'NZ', 'NI', 'NE', 'NG', 'NU', 'NF', 'MP', 'NO', 'OM', 'PK', 'PW', 'PS', 'PA', 'PG', 'PY', 'PE', 'PH', 'PN', 'PL', 'PT', 'PR', 'QA', 'RE', 'RO', 'RU', 'RW', 'BL', 'SH', 'KN', 'LC', 'MF', 'PM', 'VC', 'WS', 'SM', 'ST', 'SA', 'SN', 'RS', 'SC', 'SL', 'SG', 'SX', 'SK', 'SI', 'SB', 'SO', 'ZA', 'GS', 'SS', 'ES', 'LK', 'SD', 'SR', 'SJ', 'SZ', 'SE', 'CH', 'SY', 'TW', 'TJ', 'TZ', 'TH', 'TL', 'TG', 'TK', 'TO', 'TT', 'TN', 'TR', 'TM', 'TC', 'TV', 'UG', 'UA', 'AE', 'GB', 'UM', 'UY', 'UZ', 'VU', 'VE', 'VN', 'VG', 'VI', 'WF', 'EH', 'YE', 'ZM', 'ZW', ], 'sensitive' => true, ], 'CreateEngagementInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'ClientToken', 'EngagementIdentifier', 'Invitation', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'EngagementIdentifier' => [ 'shape' => 'EngagementIdentifier', ], 'Invitation' => [ 'shape' => 'Invitation', ], ], ], 'CreateEngagementInvitationResponse' => [ 'type' => 'structure', 'required' => [ 'Arn', 'Id', ], 'members' => [ 'Arn' => [ 'shape' => 'EngagementInvitationArn', ], 'Id' => [ 'shape' => 'EngagementInvitationIdentifier', ], ], ], 'CreateEngagementRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'ClientToken', 'Description', 'Title', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'ClientToken' => [ 'shape' => 'CreateEngagementRequestClientTokenString', 'idempotencyToken' => true, ], 'Contexts' => [ 'shape' => 'EngagementContexts', ], 'Description' => [ 'shape' => 'EngagementDescription', ], 'Title' => [ 'shape' => 'EngagementTitle', ], ], ], 'CreateEngagementRequestClientTokenString' => [ 'type' => 'string', 'pattern' => '^[!-~]{1,64}$', ], 'CreateEngagementResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'EngagementArn', ], 'Id' => [ 'shape' => 'EngagementIdentifier', ], ], ], 'CreateOpportunityRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'ClientToken', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'ClientToken' => [ 'shape' => 'CreateOpportunityRequestClientTokenString', 'idempotencyToken' => true, ], 'Customer' => [ 'shape' => 'Customer', ], 'LifeCycle' => [ 'shape' => 'LifeCycle', ], 'Marketing' => [ 'shape' => 'Marketing', ], 'NationalSecurity' => [ 'shape' => 'NationalSecurity', ], 'OpportunityTeam' => [ 'shape' => 'PartnerOpportunityTeamMembersList', ], 'OpportunityType' => [ 'shape' => 'OpportunityType', ], 'Origin' => [ 'shape' => 'OpportunityOrigin', ], 'PartnerOpportunityIdentifier' => [ 'shape' => 'CreateOpportunityRequestPartnerOpportunityIdentifierString', ], 'PrimaryNeedsFromAws' => [ 'shape' => 'PrimaryNeedsFromAws', ], 'Project' => [ 'shape' => 'Project', ], 'SoftwareRevenue' => [ 'shape' => 'SoftwareRevenue', ], ], ], 'CreateOpportunityRequestClientTokenString' => [ 'type' => 'string', 'min' => 1, ], 'CreateOpportunityRequestPartnerOpportunityIdentifierString' => [ 'type' => 'string', 'max' => 64, 'min' => 0, ], 'CreateOpportunityResponse' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'OpportunityIdentifier', ], 'LastModifiedDate' => [ 'shape' => 'DateTime', ], 'PartnerOpportunityIdentifier' => [ 'shape' => 'String', ], ], ], 'CreateResourceSnapshotJobRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'ClientToken', 'EngagementIdentifier', 'ResourceIdentifier', 'ResourceSnapshotTemplateIdentifier', 'ResourceType', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'ClientToken' => [ 'shape' => 'CreateResourceSnapshotJobRequestClientTokenString', 'idempotencyToken' => true, ], 'EngagementIdentifier' => [ 'shape' => 'EngagementIdentifier', ], 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'ResourceSnapshotTemplateIdentifier' => [ 'shape' => 'ResourceTemplateName', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateResourceSnapshotJobRequestClientTokenString' => [ 'type' => 'string', 'pattern' => '^[!-~]{1,64}$', ], 'CreateResourceSnapshotJobResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ResourceSnapshotJobArn', ], 'Id' => [ 'shape' => 'ResourceSnapshotJobIdentifier', ], ], ], 'CreateResourceSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'ClientToken', 'EngagementIdentifier', 'ResourceIdentifier', 'ResourceSnapshotTemplateIdentifier', 'ResourceType', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'ClientToken' => [ 'shape' => 'CreateResourceSnapshotRequestClientTokenString', 'idempotencyToken' => true, ], 'EngagementIdentifier' => [ 'shape' => 'EngagementIdentifier', ], 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'ResourceSnapshotTemplateIdentifier' => [ 'shape' => 'ResourceTemplateName', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], ], 'CreateResourceSnapshotRequestClientTokenString' => [ 'type' => 'string', 'pattern' => '^[!-~]{1,64}$', ], 'CreateResourceSnapshotResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ResourceArn', ], 'Revision' => [ 'shape' => 'ResourceSnapshotRevision', ], ], ], 'CurrencyCode' => [ 'type' => 'string', 'enum' => [ 'USD', 'EUR', 'GBP', 'AUD', 'CAD', 'CNY', 'NZD', 'INR', 'JPY', 'CHF', 'SEK', 'AED', 'AFN', 'ALL', 'AMD', 'ANG', 'AOA', 'ARS', 'AWG', 'AZN', 'BAM', 'BBD', 'BDT', 'BGN', 'BHD', 'BIF', 'BMD', 'BND', 'BOB', 'BOV', 'BRL', 'BSD', 'BTN', 'BWP', 'BYN', 'BZD', 'CDF', 'CHE', 'CHW', 'CLF', 'CLP', 'COP', 'COU', 'CRC', 'CUC', 'CUP', 'CVE', 'CZK', 'DJF', 'DKK', 'DOP', 'DZD', 'EGP', 'ERN', 'ETB', 'FJD', 'FKP', 'GEL', 'GHS', 'GIP', 'GMD', 'GNF', 'GTQ', 'GYD', 'HKD', 'HNL', 'HRK', 'HTG', 'HUF', 'IDR', 'ILS', 'IQD', 'IRR', 'ISK', 'JMD', 'JOD', 'KES', 'KGS', 'KHR', 'KMF', 'KPW', 'KRW', 'KWD', 'KYD', 'KZT', 'LAK', 'LBP', 'LKR', 'LRD', 'LSL', 'LYD', 'MAD', 'MDL', 'MGA', 'MKD', 'MMK', 'MNT', 'MOP', 'MRU', 'MUR', 'MVR', 'MWK', 'MXN', 'MXV', 'MYR', 'MZN', 'NAD', 'NGN', 'NIO', 'NOK', 'NPR', 'OMR', 'PAB', 'PEN', 'PGK', 'PHP', 'PKR', 'PLN', 'PYG', 'QAR', 'RON', 'RSD', 'RUB', 'RWF', 'SAR', 'SBD', 'SCR', 'SDG', 'SGD', 'SHP', 'SLL', 'SOS', 'SRD', 'SSP', 'STN', 'SVC', 'SYP', 'SZL', 'THB', 'TJS', 'TMT', 'TND', 'TOP', 'TRY', 'TTD', 'TWD', 'TZS', 'UAH', 'UGX', 'USN', 'UYI', 'UYU', 'UZS', 'VEF', 'VND', 'VUV', 'WST', 'XAF', 'XCD', 'XDR', 'XOF', 'XPF', 'XSU', 'XUA', 'YER', 'ZAR', 'ZMW', 'ZWL', ], 'sensitive' => true, ], 'Customer' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'Account', ], 'Contacts' => [ 'shape' => 'CustomerContactsList', ], ], ], 'CustomerContactsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Contact', ], ], 'CustomerProjectsContext' => [ 'type' => 'structure', 'members' => [ 'Customer' => [ 'shape' => 'EngagementCustomer', ], 'Project' => [ 'shape' => 'EngagementCustomerProjectDetails', ], ], ], 'CustomerSummary' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'AccountSummary', ], ], ], 'Date' => [ 'type' => 'string', 'pattern' => '^[1-9][0-9]{3}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$', ], 'DateTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DeleteResourceSnapshotJobRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'ResourceSnapshotJobIdentifier', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'ResourceSnapshotJobIdentifier' => [ 'shape' => 'ResourceSnapshotJobIdentifier', ], ], ], 'DeliveryModel' => [ 'type' => 'string', 'enum' => [ 'SaaS or PaaS', 'BYOL or AMI', 'Managed Services', 'Professional Services', 'Resell', 'Other', ], ], 'DeliveryModels' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeliveryModel', ], ], 'DisassociateOpportunityRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'OpportunityIdentifier', 'RelatedEntityIdentifier', 'RelatedEntityType', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'OpportunityIdentifier' => [ 'shape' => 'OpportunityIdentifier', ], 'RelatedEntityIdentifier' => [ 'shape' => 'DisassociateOpportunityRequestRelatedEntityIdentifierString', ], 'RelatedEntityType' => [ 'shape' => 'RelatedEntityType', ], ], ], 'DisassociateOpportunityRequestRelatedEntityIdentifierString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'DunsNumber' => [ 'type' => 'string', 'pattern' => '^[0-9]{9}$', 'sensitive' => true, ], 'Email' => [ 'type' => 'string', 'max' => 80, 'min' => 0, 'pattern' => '^[a-z0-9!#$%&\'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&\'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$', 'sensitive' => true, ], 'EngagementArn' => [ 'type' => 'string', 'pattern' => '^arn:.*', ], 'EngagementArnOrIdentifier' => [ 'type' => 'string', 'pattern' => '^(arn:.*|eng-[0-9a-z]{14})$', ], 'EngagementContextDetails' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Payload' => [ 'shape' => 'EngagementContextPayload', ], 'Type' => [ 'shape' => 'EngagementContextType', ], ], ], 'EngagementContextPayload' => [ 'type' => 'structure', 'members' => [ 'CustomerProject' => [ 'shape' => 'CustomerProjectsContext', ], ], 'union' => true, ], 'EngagementContextType' => [ 'type' => 'string', 'enum' => [ 'CustomerProject', ], ], 'EngagementContexts' => [ 'type' => 'list', 'member' => [ 'shape' => 'EngagementContextDetails', ], 'max' => 5, 'min' => 0, ], 'EngagementCustomer' => [ 'type' => 'structure', 'required' => [ 'CompanyName', 'CountryCode', 'Industry', 'WebsiteUrl', ], 'members' => [ 'CompanyName' => [ 'shape' => 'CompanyName', ], 'CountryCode' => [ 'shape' => 'CountryCode', ], 'Industry' => [ 'shape' => 'Industry', ], 'WebsiteUrl' => [ 'shape' => 'CompanyWebsiteUrl', ], ], ], 'EngagementCustomerBusinessProblem' => [ 'type' => 'string', 'max' => 255, 'min' => 20, 'sensitive' => true, ], 'EngagementCustomerProjectDetails' => [ 'type' => 'structure', 'required' => [ 'BusinessProblem', 'TargetCompletionDate', 'Title', ], 'members' => [ 'BusinessProblem' => [ 'shape' => 'EngagementCustomerBusinessProblem', ], 'TargetCompletionDate' => [ 'shape' => 'EngagementCustomerProjectDetailsTargetCompletionDateString', ], 'Title' => [ 'shape' => 'EngagementCustomerProjectTitle', ], ], ], 'EngagementCustomerProjectDetailsTargetCompletionDateString' => [ 'type' => 'string', 'pattern' => '^[1-9][0-9]{3}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])$', ], 'EngagementCustomerProjectTitle' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'EngagementDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'EngagementIdentifier' => [ 'type' => 'string', 'pattern' => '^eng-[0-9a-z]{14}$', ], 'EngagementIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'EngagementArnOrIdentifier', ], 'max' => 10, 'min' => 1, ], 'EngagementInvitationArn' => [ 'type' => 'string', 'pattern' => '^arn:aws:partnercentral::[0-9]{12}:[a-zA-Z]+/engagement-invitation/engi-[0-9,a-z]{13}$', ], 'EngagementInvitationArnOrIdentifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '^(arn:.*|engi-[0-9a-z]{13})$', ], 'EngagementInvitationIdentifier' => [ 'type' => 'string', 'pattern' => '^engi-[0-9,a-z]{13}$', ], 'EngagementInvitationIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'EngagementInvitationArnOrIdentifier', ], 'max' => 10, 'min' => 1, ], 'EngagementInvitationPayloadType' => [ 'type' => 'string', 'enum' => [ 'OpportunityInvitation', ], ], 'EngagementInvitationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'EngagementInvitationSummary', ], ], 'EngagementInvitationSummary' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'Id', ], 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'EngagementId' => [ 'shape' => 'EngagementIdentifier', ], 'EngagementTitle' => [ 'shape' => 'EngagementTitle', ], 'ExpirationDate' => [ 'shape' => 'DateTime', ], 'Id' => [ 'shape' => 'EngagementInvitationArnOrIdentifier', ], 'InvitationDate' => [ 'shape' => 'DateTime', ], 'ParticipantType' => [ 'shape' => 'ParticipantType', ], 'PayloadType' => [ 'shape' => 'EngagementInvitationPayloadType', ], 'Receiver' => [ 'shape' => 'Receiver', ], 'SenderAwsAccountId' => [ 'shape' => 'AwsAccount', ], 'SenderCompanyName' => [ 'shape' => 'EngagementInvitationSummarySenderCompanyNameString', ], 'Status' => [ 'shape' => 'InvitationStatus', ], ], ], 'EngagementInvitationSummarySenderCompanyNameString' => [ 'type' => 'string', 'max' => 120, 'min' => 0, ], 'EngagementInvitationsPayloadType' => [ 'type' => 'list', 'member' => [ 'shape' => 'EngagementInvitationPayloadType', ], ], 'EngagementMember' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AwsAccount', ], 'CompanyName' => [ 'shape' => 'MemberCompanyName', ], 'WebsiteUrl' => [ 'shape' => 'String', ], ], ], 'EngagementMemberSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'EngagementMemberSummary', ], ], 'EngagementMemberSummary' => [ 'type' => 'structure', 'members' => [ 'CompanyName' => [ 'shape' => 'MemberCompanyName', ], 'WebsiteUrl' => [ 'shape' => 'String', ], ], ], 'EngagementMembers' => [ 'type' => 'list', 'member' => [ 'shape' => 'EngagementMember', ], 'max' => 10, 'min' => 0, ], 'EngagementPageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'EngagementResourceAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'CreatedBy' => [ 'shape' => 'AwsAccount', ], 'EngagementId' => [ 'shape' => 'EngagementIdentifier', ], 'ResourceId' => [ 'shape' => 'ResourceIdentifier', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], ], 'EngagementResourceAssociationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EngagementResourceAssociationSummary', ], ], 'EngagementScore' => [ 'type' => 'string', 'enum' => [ 'High', 'Medium', 'Low', ], ], 'EngagementSort' => [ 'type' => 'structure', 'required' => [ 'SortBy', 'SortOrder', ], 'members' => [ 'SortBy' => [ 'shape' => 'EngagementSortName', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'EngagementSortName' => [ 'type' => 'string', 'enum' => [ 'CreatedDate', ], ], 'EngagementSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'EngagementArn', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'CreatedBy' => [ 'shape' => 'AwsAccount', ], 'Id' => [ 'shape' => 'EngagementIdentifier', ], 'MemberCount' => [ 'shape' => 'Integer', ], 'Title' => [ 'shape' => 'EngagementTitle', ], ], ], 'EngagementSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EngagementSummary', ], ], 'EngagementTitle' => [ 'type' => 'string', 'max' => 40, 'min' => 1, ], 'ExpectedCustomerSpend' => [ 'type' => 'structure', 'required' => [ 'Amount', 'CurrencyCode', 'Frequency', 'TargetCompany', ], 'members' => [ 'Amount' => [ 'shape' => 'String', ], 'CurrencyCode' => [ 'shape' => 'ExpectedCustomerSpendCurrencyCodeEnum', ], 'EstimationUrl' => [ 'shape' => 'WebsiteUrl', ], 'Frequency' => [ 'shape' => 'PaymentFrequency', ], 'TargetCompany' => [ 'shape' => 'ExpectedCustomerSpendTargetCompanyString', ], ], ], 'ExpectedCustomerSpendCurrencyCodeEnum' => [ 'type' => 'string', 'enum' => [ 'USD', 'EUR', 'GBP', 'AUD', 'CAD', 'CNY', 'NZD', 'INR', 'JPY', 'CHF', 'SEK', 'AED', 'AFN', 'ALL', 'AMD', 'ANG', 'AOA', 'ARS', 'AWG', 'AZN', 'BAM', 'BBD', 'BDT', 'BGN', 'BHD', 'BIF', 'BMD', 'BND', 'BOB', 'BOV', 'BRL', 'BSD', 'BTN', 'BWP', 'BYN', 'BZD', 'CDF', 'CHE', 'CHW', 'CLF', 'CLP', 'COP', 'COU', 'CRC', 'CUC', 'CUP', 'CVE', 'CZK', 'DJF', 'DKK', 'DOP', 'DZD', 'EGP', 'ERN', 'ETB', 'FJD', 'FKP', 'GEL', 'GHS', 'GIP', 'GMD', 'GNF', 'GTQ', 'GYD', 'HKD', 'HNL', 'HRK', 'HTG', 'HUF', 'IDR', 'ILS', 'IQD', 'IRR', 'ISK', 'JMD', 'JOD', 'KES', 'KGS', 'KHR', 'KMF', 'KPW', 'KRW', 'KWD', 'KYD', 'KZT', 'LAK', 'LBP', 'LKR', 'LRD', 'LSL', 'LYD', 'MAD', 'MDL', 'MGA', 'MKD', 'MMK', 'MNT', 'MOP', 'MRU', 'MUR', 'MVR', 'MWK', 'MXN', 'MXV', 'MYR', 'MZN', 'NAD', 'NGN', 'NIO', 'NOK', 'NPR', 'OMR', 'PAB', 'PEN', 'PGK', 'PHP', 'PKR', 'PLN', 'PYG', 'QAR', 'RON', 'RSD', 'RUB', 'RWF', 'SAR', 'SBD', 'SCR', 'SDG', 'SGD', 'SHP', 'SLL', 'SOS', 'SRD', 'SSP', 'STN', 'SVC', 'SYP', 'SZL', 'THB', 'TJS', 'TMT', 'TND', 'TOP', 'TRY', 'TTD', 'TWD', 'TZS', 'UAH', 'UGX', 'USN', 'UYI', 'UYU', 'UZS', 'VEF', 'VND', 'VUV', 'WST', 'XAF', 'XCD', 'XDR', 'XOF', 'XPF', 'XSU', 'XUA', 'YER', 'ZAR', 'ZMW', 'ZWL', ], 'pattern' => '^USD$', 'sensitive' => true, ], 'ExpectedCustomerSpendList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExpectedCustomerSpend', ], 'max' => 10, 'min' => 0, ], 'ExpectedCustomerSpendTargetCompanyString' => [ 'type' => 'string', 'max' => 80, 'min' => 1, ], 'GetAwsOpportunitySummaryRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'RelatedOpportunityIdentifier', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'RelatedOpportunityIdentifier' => [ 'shape' => 'OpportunityIdentifier', ], ], ], 'GetAwsOpportunitySummaryResponse' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'Customer' => [ 'shape' => 'AwsOpportunityCustomer', ], 'Insights' => [ 'shape' => 'AwsOpportunityInsights', ], 'InvolvementType' => [ 'shape' => 'SalesInvolvementType', ], 'InvolvementTypeChangeReason' => [ 'shape' => 'InvolvementTypeChangeReason', ], 'LifeCycle' => [ 'shape' => 'AwsOpportunityLifeCycle', ], 'OpportunityTeam' => [ 'shape' => 'AwsOpportunityTeamMembersList', ], 'Origin' => [ 'shape' => 'OpportunityOrigin', ], 'Project' => [ 'shape' => 'AwsOpportunityProject', ], 'RelatedEntityIds' => [ 'shape' => 'AwsOpportunityRelatedEntities', ], 'RelatedOpportunityId' => [ 'shape' => 'OpportunityIdentifier', ], 'Visibility' => [ 'shape' => 'Visibility', ], ], ], 'GetEngagementInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'Identifier', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'Identifier' => [ 'shape' => 'EngagementInvitationArnOrIdentifier', ], ], ], 'GetEngagementInvitationResponse' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'Id', ], 'members' => [ 'Arn' => [ 'shape' => 'String', ], 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'EngagementDescription' => [ 'shape' => 'EngagementDescription', ], 'EngagementId' => [ 'shape' => 'EngagementIdentifier', ], 'EngagementTitle' => [ 'shape' => 'EngagementTitle', ], 'ExistingMembers' => [ 'shape' => 'EngagementMemberSummaries', ], 'ExpirationDate' => [ 'shape' => 'DateTime', ], 'Id' => [ 'shape' => 'EngagementInvitationArnOrIdentifier', ], 'InvitationDate' => [ 'shape' => 'DateTime', ], 'InvitationMessage' => [ 'shape' => 'InvitationMessage', ], 'Payload' => [ 'shape' => 'Payload', ], 'PayloadType' => [ 'shape' => 'EngagementInvitationPayloadType', ], 'Receiver' => [ 'shape' => 'Receiver', ], 'RejectionReason' => [ 'shape' => 'RejectionReasonString', ], 'SenderAwsAccountId' => [ 'shape' => 'AwsAccount', ], 'SenderCompanyName' => [ 'shape' => 'GetEngagementInvitationResponseSenderCompanyNameString', ], 'Status' => [ 'shape' => 'InvitationStatus', ], ], ], 'GetEngagementInvitationResponseSenderCompanyNameString' => [ 'type' => 'string', 'max' => 120, 'min' => 0, ], 'GetEngagementRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'Identifier', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'Identifier' => [ 'shape' => 'EngagementArnOrIdentifier', ], ], ], 'GetEngagementResponse' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'EngagementArn', ], 'Contexts' => [ 'shape' => 'EngagementContexts', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'CreatedBy' => [ 'shape' => 'AwsAccount', ], 'Description' => [ 'shape' => 'EngagementDescription', ], 'Id' => [ 'shape' => 'EngagementIdentifier', ], 'MemberCount' => [ 'shape' => 'Integer', ], 'Title' => [ 'shape' => 'EngagementTitle', ], ], ], 'GetOpportunityRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'Identifier', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'Identifier' => [ 'shape' => 'OpportunityIdentifier', ], ], ], 'GetOpportunityResponse' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'CreatedDate', 'Id', 'LastModifiedDate', 'RelatedEntityIdentifiers', ], 'members' => [ 'Arn' => [ 'shape' => 'OpportunityArn', ], 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'CreatedDate' => [ 'shape' => 'DateTime', ], 'Customer' => [ 'shape' => 'Customer', ], 'Id' => [ 'shape' => 'OpportunityIdentifier', ], 'LastModifiedDate' => [ 'shape' => 'DateTime', ], 'LifeCycle' => [ 'shape' => 'LifeCycle', ], 'Marketing' => [ 'shape' => 'Marketing', ], 'NationalSecurity' => [ 'shape' => 'NationalSecurity', ], 'OpportunityTeam' => [ 'shape' => 'PartnerOpportunityTeamMembersList', ], 'OpportunityType' => [ 'shape' => 'OpportunityType', ], 'PartnerOpportunityIdentifier' => [ 'shape' => 'GetOpportunityResponsePartnerOpportunityIdentifierString', ], 'PrimaryNeedsFromAws' => [ 'shape' => 'PrimaryNeedsFromAws', ], 'Project' => [ 'shape' => 'Project', ], 'RelatedEntityIdentifiers' => [ 'shape' => 'RelatedEntityIdentifiers', ], 'SoftwareRevenue' => [ 'shape' => 'SoftwareRevenue', ], ], ], 'GetOpportunityResponsePartnerOpportunityIdentifierString' => [ 'type' => 'string', 'max' => 64, 'min' => 0, ], 'GetResourceSnapshotJobRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'ResourceSnapshotJobIdentifier', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'ResourceSnapshotJobIdentifier' => [ 'shape' => 'ResourceSnapshotJobIdentifier', ], ], ], 'GetResourceSnapshotJobResponse' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Arn' => [ 'shape' => 'ResourceSnapshotJobArn', ], 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'EngagementId' => [ 'shape' => 'EngagementIdentifier', ], 'Id' => [ 'shape' => 'ResourceSnapshotJobIdentifier', ], 'LastFailure' => [ 'shape' => 'String', ], 'LastSuccessfulExecutionDate' => [ 'shape' => 'DateTime', ], 'ResourceArn' => [ 'shape' => 'ResourceArn', ], 'ResourceId' => [ 'shape' => 'ResourceIdentifier', ], 'ResourceSnapshotTemplateName' => [ 'shape' => 'ResourceTemplateName', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Status' => [ 'shape' => 'ResourceSnapshotJobStatus', ], ], ], 'GetResourceSnapshotRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'EngagementIdentifier', 'ResourceIdentifier', 'ResourceSnapshotTemplateIdentifier', 'ResourceType', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'EngagementIdentifier' => [ 'shape' => 'EngagementIdentifier', ], 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'ResourceSnapshotTemplateIdentifier' => [ 'shape' => 'ResourceTemplateName', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Revision' => [ 'shape' => 'ResourceSnapshotRevision', ], ], ], 'GetResourceSnapshotResponse' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Arn' => [ 'shape' => 'ResourceArn', ], 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'CreatedAt' => [ 'shape' => 'DateTime', ], 'CreatedBy' => [ 'shape' => 'AwsAccount', ], 'EngagementId' => [ 'shape' => 'EngagementIdentifier', ], 'Payload' => [ 'shape' => 'ResourceSnapshotPayload', ], 'ResourceId' => [ 'shape' => 'ResourceIdentifier', ], 'ResourceSnapshotTemplateName' => [ 'shape' => 'ResourceTemplateName', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Revision' => [ 'shape' => 'ResourceSnapshotRevision', ], ], ], 'GetSellingSystemSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], ], ], 'GetSellingSystemSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'ResourceSnapshotJobRoleArn' => [ 'shape' => 'ResourceSnapshotJobRoleArn', ], ], ], 'Industry' => [ 'type' => 'string', 'enum' => [ 'Aerospace', 'Agriculture', 'Automotive', 'Computers and Electronics', 'Consumer Goods', 'Education', 'Energy - Oil and Gas', 'Energy - Power and Utilities', 'Financial Services', 'Gaming', 'Government', 'Healthcare', 'Hospitality', 'Life Sciences', 'Manufacturing', 'Marketing and Advertising', 'Media and Entertainment', 'Mining', 'Non-Profit Organization', 'Professional Services', 'Real Estate and Construction', 'Retail', 'Software and Internet', 'Telecommunications', 'Transportation and Logistics', 'Travel', 'Wholesale and Distribution', 'Other', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, 'fault' => true, ], 'Invitation' => [ 'type' => 'structure', 'required' => [ 'Message', 'Payload', 'Receiver', ], 'members' => [ 'Message' => [ 'shape' => 'InvitationMessage', ], 'Payload' => [ 'shape' => 'Payload', ], 'Receiver' => [ 'shape' => 'Receiver', ], ], ], 'InvitationMessage' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'sensitive' => true, ], 'InvitationStatus' => [ 'type' => 'string', 'enum' => [ 'ACCEPTED', 'PENDING', 'REJECTED', 'EXPIRED', ], ], 'InvitationStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvitationStatus', ], 'max' => 10, 'min' => 1, ], 'InvolvementTypeChangeReason' => [ 'type' => 'string', 'enum' => [ 'Expansion Opportunity', 'Change in Deal Information', 'Customer Requested', 'Technical Complexity', 'Risk Mitigation', ], ], 'JobTitle' => [ 'type' => 'string', 'max' => 80, 'min' => 0, 'sensitive' => true, ], 'LastModifiedDate' => [ 'type' => 'structure', 'members' => [ 'AfterLastModifiedDate' => [ 'shape' => 'DateTime', ], 'BeforeLastModifiedDate' => [ 'shape' => 'DateTime', ], ], ], 'LifeCycle' => [ 'type' => 'structure', 'members' => [ 'ClosedLostReason' => [ 'shape' => 'ClosedLostReason', ], 'NextSteps' => [ 'shape' => 'LifeCycleNextStepsString', ], 'NextStepsHistory' => [ 'shape' => 'LifeCycleNextStepsHistoryList', ], 'ReviewComments' => [ 'shape' => 'String', ], 'ReviewStatus' => [ 'shape' => 'ReviewStatus', ], 'ReviewStatusReason' => [ 'shape' => 'String', ], 'Stage' => [ 'shape' => 'Stage', ], 'TargetCloseDate' => [ 'shape' => 'Date', ], ], ], 'LifeCycleForView' => [ 'type' => 'structure', 'members' => [ 'NextSteps' => [ 'shape' => 'LifeCycleForViewNextStepsString', ], 'ReviewStatus' => [ 'shape' => 'ReviewStatus', ], 'Stage' => [ 'shape' => 'Stage', ], 'TargetCloseDate' => [ 'shape' => 'Date', ], ], ], 'LifeCycleForViewNextStepsString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'sensitive' => true, ], 'LifeCycleNextStepsHistoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'NextStepsHistory', ], 'max' => 50, 'min' => 0, ], 'LifeCycleNextStepsString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'sensitive' => true, ], 'LifeCycleSummary' => [ 'type' => 'structure', 'members' => [ 'ClosedLostReason' => [ 'shape' => 'ClosedLostReason', ], 'NextSteps' => [ 'shape' => 'LifeCycleSummaryNextStepsString', ], 'ReviewComments' => [ 'shape' => 'String', ], 'ReviewStatus' => [ 'shape' => 'ReviewStatus', ], 'ReviewStatusReason' => [ 'shape' => 'String', ], 'Stage' => [ 'shape' => 'Stage', ], 'TargetCloseDate' => [ 'shape' => 'Date', ], ], ], 'LifeCycleSummaryNextStepsString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'sensitive' => true, ], 'ListEngagementByAcceptingInvitationTaskSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListEngagementByAcceptingInvitationTaskSummary', ], ], 'ListEngagementByAcceptingInvitationTaskSummary' => [ 'type' => 'structure', 'members' => [ 'EngagementInvitationId' => [ 'shape' => 'EngagementInvitationIdentifier', ], 'Message' => [ 'shape' => 'String', ], 'OpportunityId' => [ 'shape' => 'OpportunityIdentifier', ], 'ReasonCode' => [ 'shape' => 'ReasonCode', ], 'ResourceSnapshotJobId' => [ 'shape' => 'ResourceSnapshotJobIdentifier', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'TaskArn' => [ 'shape' => 'TaskArn', ], 'TaskId' => [ 'shape' => 'TaskIdentifier', ], 'TaskStatus' => [ 'shape' => 'TaskStatus', ], ], ], 'ListEngagementByAcceptingInvitationTasksRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'EngagementInvitationIdentifier' => [ 'shape' => 'EngagementInvitationIdentifiers', ], 'MaxResults' => [ 'shape' => 'ListEngagementByAcceptingInvitationTasksRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'ListEngagementByAcceptingInvitationTasksRequestNextTokenString', ], 'OpportunityIdentifier' => [ 'shape' => 'OpportunityIdentifiers', ], 'Sort' => [ 'shape' => 'ListTasksSortBase', ], 'TaskIdentifier' => [ 'shape' => 'TaskIdentifiers', ], 'TaskStatus' => [ 'shape' => 'TaskStatuses', ], ], ], 'ListEngagementByAcceptingInvitationTasksRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListEngagementByAcceptingInvitationTasksRequestNextTokenString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ListEngagementByAcceptingInvitationTasksResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'TaskSummaries' => [ 'shape' => 'ListEngagementByAcceptingInvitationTaskSummaries', ], ], ], 'ListEngagementFromOpportunityTaskSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListEngagementFromOpportunityTaskSummary', ], ], 'ListEngagementFromOpportunityTaskSummary' => [ 'type' => 'structure', 'members' => [ 'EngagementId' => [ 'shape' => 'EngagementIdentifier', ], 'EngagementInvitationId' => [ 'shape' => 'EngagementInvitationIdentifier', ], 'Message' => [ 'shape' => 'String', ], 'OpportunityId' => [ 'shape' => 'OpportunityIdentifier', ], 'ReasonCode' => [ 'shape' => 'ReasonCode', ], 'ResourceSnapshotJobId' => [ 'shape' => 'ResourceSnapshotJobIdentifier', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'TaskArn' => [ 'shape' => 'TaskArn', ], 'TaskId' => [ 'shape' => 'TaskIdentifier', ], 'TaskStatus' => [ 'shape' => 'TaskStatus', ], ], ], 'ListEngagementFromOpportunityTasksRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'EngagementIdentifier' => [ 'shape' => 'EngagementIdentifiers', ], 'MaxResults' => [ 'shape' => 'ListEngagementFromOpportunityTasksRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'ListEngagementFromOpportunityTasksRequestNextTokenString', ], 'OpportunityIdentifier' => [ 'shape' => 'OpportunityIdentifiers', ], 'Sort' => [ 'shape' => 'ListTasksSortBase', ], 'TaskIdentifier' => [ 'shape' => 'TaskIdentifiers', ], 'TaskStatus' => [ 'shape' => 'TaskStatuses', ], ], ], 'ListEngagementFromOpportunityTasksRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListEngagementFromOpportunityTasksRequestNextTokenString' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ListEngagementFromOpportunityTasksResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'TaskSummaries' => [ 'shape' => 'ListEngagementFromOpportunityTaskSummaries', ], ], ], 'ListEngagementInvitationsRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'ParticipantType', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'EngagementIdentifier' => [ 'shape' => 'EngagementIdentifiers', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'String', ], 'ParticipantType' => [ 'shape' => 'ParticipantType', ], 'PayloadType' => [ 'shape' => 'EngagementInvitationsPayloadType', ], 'SenderAwsAccountId' => [ 'shape' => 'AwsAccountIdOrAliasList', ], 'Sort' => [ 'shape' => 'OpportunityEngagementInvitationSort', ], 'Status' => [ 'shape' => 'InvitationStatusList', ], ], ], 'ListEngagementInvitationsResponse' => [ 'type' => 'structure', 'members' => [ 'EngagementInvitationSummaries' => [ 'shape' => 'EngagementInvitationSummaries', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListEngagementMembersRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'Identifier', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'Identifier' => [ 'shape' => 'EngagementArnOrIdentifier', ], 'MaxResults' => [ 'shape' => 'MemberPageSize', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListEngagementMembersResponse' => [ 'type' => 'structure', 'required' => [ 'EngagementMemberList', ], 'members' => [ 'EngagementMemberList' => [ 'shape' => 'EngagementMembers', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListEngagementResourceAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'CreatedBy' => [ 'shape' => 'AwsAccount', ], 'EngagementIdentifier' => [ 'shape' => 'EngagementIdentifier', ], 'MaxResults' => [ 'shape' => 'ListEngagementResourceAssociationsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'String', ], 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], ], 'ListEngagementResourceAssociationsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListEngagementResourceAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'EngagementResourceAssociationSummaries', ], 'members' => [ 'EngagementResourceAssociationSummaries' => [ 'shape' => 'EngagementResourceAssociationSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListEngagementsRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'CreatedBy' => [ 'shape' => 'AwsAccountList', ], 'EngagementIdentifier' => [ 'shape' => 'EngagementIdentifiers', ], 'ExcludeCreatedBy' => [ 'shape' => 'AwsAccountList', ], 'MaxResults' => [ 'shape' => 'EngagementPageSize', ], 'NextToken' => [ 'shape' => 'String', ], 'Sort' => [ 'shape' => 'EngagementSort', ], ], ], 'ListEngagementsResponse' => [ 'type' => 'structure', 'required' => [ 'EngagementSummaryList', ], 'members' => [ 'EngagementSummaryList' => [ 'shape' => 'EngagementSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListOpportunitiesRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'CustomerCompanyName' => [ 'shape' => 'ListOpportunitiesRequestCustomerCompanyNameList', ], 'Identifier' => [ 'shape' => 'ListOpportunitiesRequestIdentifierList', ], 'LastModifiedDate' => [ 'shape' => 'LastModifiedDate', ], 'LifeCycleReviewStatus' => [ 'shape' => 'ListOpportunitiesRequestLifeCycleReviewStatusList', ], 'LifeCycleStage' => [ 'shape' => 'ListOpportunitiesRequestLifeCycleStageList', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'String', ], 'Sort' => [ 'shape' => 'OpportunitySort', ], ], ], 'ListOpportunitiesRequestCustomerCompanyNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 10, 'min' => 0, ], 'ListOpportunitiesRequestIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpportunityIdentifier', ], 'max' => 20, 'min' => 0, ], 'ListOpportunitiesRequestLifeCycleReviewStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReviewStatus', ], 'max' => 10, 'min' => 0, ], 'ListOpportunitiesRequestLifeCycleStageList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Stage', ], 'max' => 10, 'min' => 0, ], 'ListOpportunitiesResponse' => [ 'type' => 'structure', 'required' => [ 'OpportunitySummaries', ], 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'OpportunitySummaries' => [ 'shape' => 'OpportunitySummaries', ], ], ], 'ListResourceSnapshotJobsRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'EngagementIdentifier' => [ 'shape' => 'EngagementIdentifier', ], 'MaxResults' => [ 'shape' => 'ListResourceSnapshotJobsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'String', ], 'Sort' => [ 'shape' => 'SortObject', ], 'Status' => [ 'shape' => 'ResourceSnapshotJobStatus', ], ], ], 'ListResourceSnapshotJobsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListResourceSnapshotJobsResponse' => [ 'type' => 'structure', 'required' => [ 'ResourceSnapshotJobSummaries', ], 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'ResourceSnapshotJobSummaries' => [ 'shape' => 'ResourceSnapshotJobSummaryList', ], ], ], 'ListResourceSnapshotsRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'EngagementIdentifier', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'CreatedBy' => [ 'shape' => 'AwsAccount', ], 'EngagementIdentifier' => [ 'shape' => 'EngagementIdentifier', ], 'MaxResults' => [ 'shape' => 'ListResourceSnapshotsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'String', ], 'ResourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'ResourceSnapshotTemplateIdentifier' => [ 'shape' => 'ResourceTemplateName', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], ], ], 'ListResourceSnapshotsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'ListResourceSnapshotsResponse' => [ 'type' => 'structure', 'required' => [ 'ResourceSnapshotSummaries', ], 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'ResourceSnapshotSummaries' => [ 'shape' => 'ResourceSnapshotSummaryList', ], ], ], 'ListSolutionsRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'Category' => [ 'shape' => 'ListSolutionsRequestCategoryList', ], 'Identifier' => [ 'shape' => 'ListSolutionsRequestIdentifierList', ], 'MaxResults' => [ 'shape' => 'PageSize', ], 'NextToken' => [ 'shape' => 'String', ], 'Sort' => [ 'shape' => 'SolutionSort', ], 'Status' => [ 'shape' => 'ListSolutionsRequestStatusList', ], ], ], 'ListSolutionsRequestCategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 10, 'min' => 0, ], 'ListSolutionsRequestIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SolutionIdentifier', ], 'max' => 20, 'min' => 0, ], 'ListSolutionsRequestStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SolutionStatus', ], 'max' => 10, 'min' => 0, ], 'ListSolutionsResponse' => [ 'type' => 'structure', 'required' => [ 'SolutionSummaries', ], 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'SolutionSummaries' => [ 'shape' => 'SolutionList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TaggableResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'Tags', ], 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListTasksSortBase' => [ 'type' => 'structure', 'required' => [ 'SortBy', 'SortOrder', ], 'members' => [ 'SortBy' => [ 'shape' => 'ListTasksSortName', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'ListTasksSortName' => [ 'type' => 'string', 'enum' => [ 'StartTime', ], ], 'Marketing' => [ 'type' => 'structure', 'members' => [ 'AwsFundingUsed' => [ 'shape' => 'AwsFundingUsed', ], 'CampaignName' => [ 'shape' => 'String', ], 'Channels' => [ 'shape' => 'Channels', ], 'Source' => [ 'shape' => 'MarketingSource', ], 'UseCases' => [ 'shape' => 'UseCases', ], ], ], 'MarketingSource' => [ 'type' => 'string', 'enum' => [ 'Marketing Activity', 'None', ], ], 'MemberCompanyName' => [ 'type' => 'string', 'max' => 120, 'min' => 1, 'sensitive' => true, ], 'MemberPageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'MonetaryValue' => [ 'type' => 'structure', 'required' => [ 'Amount', 'CurrencyCode', ], 'members' => [ 'Amount' => [ 'shape' => 'MonetaryValueAmountString', ], 'CurrencyCode' => [ 'shape' => 'CurrencyCode', ], ], ], 'MonetaryValueAmountString' => [ 'type' => 'string', 'pattern' => '^(0|([1-9][0-9]{0,30}))(\\.[0-9]{0,2})?$', ], 'Name' => [ 'type' => 'string', 'max' => 80, 'min' => 0, 'sensitive' => true, ], 'NationalSecurity' => [ 'type' => 'string', 'enum' => [ 'Yes', 'No', ], ], 'NextStepsHistory' => [ 'type' => 'structure', 'required' => [ 'Time', 'Value', ], 'members' => [ 'Time' => [ 'shape' => 'DateTime', ], 'Value' => [ 'shape' => 'String', ], ], ], 'OpportunityArn' => [ 'type' => 'string', 'pattern' => '^arn:.*$', ], 'OpportunityEngagementInvitationSort' => [ 'type' => 'structure', 'required' => [ 'SortBy', 'SortOrder', ], 'members' => [ 'SortBy' => [ 'shape' => 'OpportunityEngagementInvitationSortName', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'OpportunityEngagementInvitationSortName' => [ 'type' => 'string', 'enum' => [ 'InvitationDate', ], ], 'OpportunityIdentifier' => [ 'type' => 'string', 'pattern' => '^O[0-9]{1,19}$', ], 'OpportunityIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpportunityIdentifier', ], 'max' => 10, 'min' => 1, ], 'OpportunityInvitationPayload' => [ 'type' => 'structure', 'required' => [ 'Customer', 'Project', 'ReceiverResponsibilities', ], 'members' => [ 'Customer' => [ 'shape' => 'EngagementCustomer', ], 'Project' => [ 'shape' => 'ProjectDetails', ], 'ReceiverResponsibilities' => [ 'shape' => 'ReceiverResponsibilityList', ], 'SenderContacts' => [ 'shape' => 'SenderContactList', ], ], ], 'OpportunityOrigin' => [ 'type' => 'string', 'enum' => [ 'AWS Referral', 'Partner Referral', ], ], 'OpportunitySort' => [ 'type' => 'structure', 'required' => [ 'SortBy', 'SortOrder', ], 'members' => [ 'SortBy' => [ 'shape' => 'OpportunitySortName', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'OpportunitySortName' => [ 'type' => 'string', 'enum' => [ 'LastModifiedDate', 'Identifier', 'CustomerCompanyName', ], ], 'OpportunitySummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'OpportunitySummary', ], ], 'OpportunitySummary' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Arn' => [ 'shape' => 'OpportunityArn', ], 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'CreatedDate' => [ 'shape' => 'DateTime', ], 'Customer' => [ 'shape' => 'CustomerSummary', ], 'Id' => [ 'shape' => 'OpportunityIdentifier', ], 'LastModifiedDate' => [ 'shape' => 'DateTime', ], 'LifeCycle' => [ 'shape' => 'LifeCycleSummary', ], 'OpportunityType' => [ 'shape' => 'OpportunityType', ], 'PartnerOpportunityIdentifier' => [ 'shape' => 'String', ], 'Project' => [ 'shape' => 'ProjectSummary', ], ], ], 'OpportunitySummaryView' => [ 'type' => 'structure', 'members' => [ 'Customer' => [ 'shape' => 'Customer', ], 'Lifecycle' => [ 'shape' => 'LifeCycleForView', ], 'OpportunityTeam' => [ 'shape' => 'PartnerOpportunityTeamMembersList', ], 'OpportunityType' => [ 'shape' => 'OpportunityType', ], 'PrimaryNeedsFromAws' => [ 'shape' => 'PrimaryNeedsFromAws', ], 'Project' => [ 'shape' => 'ProjectView', ], 'RelatedEntityIdentifiers' => [ 'shape' => 'RelatedEntityIdentifiers', ], ], ], 'OpportunityType' => [ 'type' => 'string', 'enum' => [ 'Net New Business', 'Flat Renewal', 'Expansion', ], ], 'PageSize' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ParticipantType' => [ 'type' => 'string', 'enum' => [ 'SENDER', 'RECEIVER', ], ], 'PartnerOpportunityTeamMembersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Contact', ], 'max' => 1, 'min' => 0, ], 'Payload' => [ 'type' => 'structure', 'members' => [ 'OpportunityInvitation' => [ 'shape' => 'OpportunityInvitationPayload', ], ], 'union' => true, ], 'PaymentFrequency' => [ 'type' => 'string', 'enum' => [ 'Monthly', ], ], 'PhoneNumber' => [ 'type' => 'string', 'max' => 40, 'min' => 0, 'pattern' => '^\\+[1-9]\\d{1,14}$', 'sensitive' => true, ], 'PrimaryNeedFromAws' => [ 'type' => 'string', 'enum' => [ 'Co-Sell - Architectural Validation', 'Co-Sell - Business Presentation', 'Co-Sell - Competitive Information', 'Co-Sell - Pricing Assistance', 'Co-Sell - Technical Consultation', 'Co-Sell - Total Cost of Ownership Evaluation', 'Co-Sell - Deal Support', 'Co-Sell - Support for Public Tender / RFx', ], ], 'PrimaryNeedsFromAws' => [ 'type' => 'list', 'member' => [ 'shape' => 'PrimaryNeedFromAws', ], ], 'ProfileNextStepsHistory' => [ 'type' => 'structure', 'required' => [ 'Time', 'Value', ], 'members' => [ 'Time' => [ 'shape' => 'DateTime', ], 'Value' => [ 'shape' => 'String', ], ], ], 'Project' => [ 'type' => 'structure', 'members' => [ 'AdditionalComments' => [ 'shape' => 'ProjectAdditionalCommentsString', ], 'ApnPrograms' => [ 'shape' => 'ApnPrograms', ], 'CompetitorName' => [ 'shape' => 'CompetitorName', ], 'CustomerBusinessProblem' => [ 'shape' => 'ProjectCustomerBusinessProblemString', ], 'CustomerUseCase' => [ 'shape' => 'String', ], 'DeliveryModels' => [ 'shape' => 'DeliveryModels', ], 'ExpectedCustomerSpend' => [ 'shape' => 'ExpectedCustomerSpendList', ], 'OtherCompetitorNames' => [ 'shape' => 'ProjectOtherCompetitorNamesString', ], 'OtherSolutionDescription' => [ 'shape' => 'ProjectOtherSolutionDescriptionString', ], 'RelatedOpportunityIdentifier' => [ 'shape' => 'OpportunityIdentifier', ], 'SalesActivities' => [ 'shape' => 'SalesActivities', ], 'Title' => [ 'shape' => 'ProjectTitleString', ], ], ], 'ProjectAdditionalCommentsString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ProjectCustomerBusinessProblemString' => [ 'type' => 'string', 'max' => 2000, 'min' => 20, 'sensitive' => true, ], 'ProjectDetails' => [ 'type' => 'structure', 'required' => [ 'BusinessProblem', 'ExpectedCustomerSpend', 'TargetCompletionDate', 'Title', ], 'members' => [ 'BusinessProblem' => [ 'shape' => 'EngagementCustomerBusinessProblem', ], 'ExpectedCustomerSpend' => [ 'shape' => 'ExpectedCustomerSpendList', ], 'TargetCompletionDate' => [ 'shape' => 'Date', ], 'Title' => [ 'shape' => 'ProjectDetailsTitleString', ], ], ], 'ProjectDetailsTitleString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ProjectOtherCompetitorNamesString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'ProjectOtherSolutionDescriptionString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'sensitive' => true, ], 'ProjectSummary' => [ 'type' => 'structure', 'members' => [ 'DeliveryModels' => [ 'shape' => 'DeliveryModels', ], 'ExpectedCustomerSpend' => [ 'shape' => 'ExpectedCustomerSpendList', ], ], ], 'ProjectTitleString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'sensitive' => true, ], 'ProjectView' => [ 'type' => 'structure', 'members' => [ 'CustomerUseCase' => [ 'shape' => 'String', ], 'DeliveryModels' => [ 'shape' => 'DeliveryModels', ], 'ExpectedCustomerSpend' => [ 'shape' => 'ExpectedCustomerSpendList', ], 'OtherSolutionDescription' => [ 'shape' => 'ProjectViewOtherSolutionDescriptionString', ], 'SalesActivities' => [ 'shape' => 'SalesActivities', ], ], ], 'ProjectViewOtherSolutionDescriptionString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'sensitive' => true, ], 'PutSellingSystemSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'ResourceSnapshotJobRoleIdentifier' => [ 'shape' => 'ResourceSnapshotJobRoleIdentifier', ], ], ], 'PutSellingSystemSettingsResponse' => [ 'type' => 'structure', 'required' => [ 'Catalog', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'ResourceSnapshotJobRoleArn' => [ 'shape' => 'ResourceSnapshotJobRoleArn', ], ], ], 'ReasonCode' => [ 'type' => 'string', 'enum' => [ 'InvitationAccessDenied', 'InvitationValidationFailed', 'EngagementAccessDenied', 'OpportunityAccessDenied', 'ResourceSnapshotJobAccessDenied', 'ResourceSnapshotJobValidationFailed', 'ResourceSnapshotJobConflict', 'EngagementValidationFailed', 'EngagementConflict', 'OpportunitySubmissionFailed', 'EngagementInvitationConflict', 'InternalError', 'OpportunityValidationFailed', 'OpportunityConflict', 'ResourceSnapshotAccessDenied', 'ResourceSnapshotValidationFailed', 'ResourceSnapshotConflict', 'ServiceQuotaExceeded', 'RequestThrottled', ], ], 'Receiver' => [ 'type' => 'structure', 'members' => [ 'Account' => [ 'shape' => 'AccountReceiver', ], ], 'union' => true, ], 'ReceiverResponsibility' => [ 'type' => 'string', 'enum' => [ 'Distributor', 'Reseller', 'Hardware Partner', 'Managed Service Provider', 'Software Partner', 'Services Partner', 'Training Partner', 'Co-Sell Facilitator', 'Facilitator', ], ], 'ReceiverResponsibilityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReceiverResponsibility', ], ], 'RejectEngagementInvitationRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'Identifier', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'Identifier' => [ 'shape' => 'EngagementInvitationArnOrIdentifier', ], 'RejectionReason' => [ 'shape' => 'RejectionReasonString', ], ], ], 'RejectionReasonString' => [ 'type' => 'string', 'pattern' => '^[\\u0020-\\u007E\\u00A0-\\uD7FF\\uE000-\\uFFFD]{1,80}$', ], 'RelatedEntityIdentifiers' => [ 'type' => 'structure', 'members' => [ 'AwsMarketplaceOffers' => [ 'shape' => 'AwsMarketplaceOfferIdentifiers', ], 'AwsProducts' => [ 'shape' => 'AwsProductIdentifiers', ], 'Solutions' => [ 'shape' => 'SolutionIdentifiers', ], ], ], 'RelatedEntityType' => [ 'type' => 'string', 'enum' => [ 'Solutions', 'AwsProducts', 'AwsMarketplaceOffers', ], ], 'ResourceArn' => [ 'type' => 'string', 'pattern' => '^arn:.*', ], 'ResourceIdentifier' => [ 'type' => 'string', 'pattern' => '^O[0-9]{1,19}$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'ResourceSnapshotArn' => [ 'type' => 'string', 'pattern' => '^arn:.*', ], 'ResourceSnapshotJobArn' => [ 'type' => 'string', 'pattern' => '^arn:.*', ], 'ResourceSnapshotJobIdentifier' => [ 'type' => 'string', 'pattern' => '^job-[0-9a-z]{13}$', ], 'ResourceSnapshotJobRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^arn:aws:iam::\\d{12}:role/([-+=,.@_a-zA-Z0-9]+/)*[-+=,.@_a-zA-Z0-9]{1,64}$', ], 'ResourceSnapshotJobRoleIdentifier' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '^(arn:aws:iam::\\d{12}:role/([-+=,.@_a-zA-Z0-9]+/)*)?[-+=,.@_a-zA-Z0-9]{1,64}$', ], 'ResourceSnapshotJobStatus' => [ 'type' => 'string', 'enum' => [ 'Running', 'Stopped', ], ], 'ResourceSnapshotJobSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ResourceSnapshotJobArn', ], 'EngagementId' => [ 'shape' => 'EngagementIdentifier', ], 'Id' => [ 'shape' => 'ResourceSnapshotJobIdentifier', ], 'Status' => [ 'shape' => 'ResourceSnapshotJobStatus', ], ], ], 'ResourceSnapshotJobSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceSnapshotJobSummary', ], ], 'ResourceSnapshotPayload' => [ 'type' => 'structure', 'members' => [ 'OpportunitySummary' => [ 'shape' => 'OpportunitySummaryView', ], ], 'union' => true, ], 'ResourceSnapshotRevision' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'ResourceSnapshotSummary' => [ 'type' => 'structure', 'members' => [ 'Arn' => [ 'shape' => 'ResourceSnapshotArn', ], 'CreatedBy' => [ 'shape' => 'AwsAccount', ], 'ResourceId' => [ 'shape' => 'ResourceIdentifier', ], 'ResourceSnapshotTemplateName' => [ 'shape' => 'ResourceTemplateName', ], 'ResourceType' => [ 'shape' => 'ResourceType', ], 'Revision' => [ 'shape' => 'ResourceSnapshotRevision', ], ], ], 'ResourceSnapshotSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceSnapshotSummary', ], ], 'ResourceTemplateName' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9]{3,80}$', ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'Opportunity', ], ], 'RevenueModel' => [ 'type' => 'string', 'enum' => [ 'Contract', 'Pay-as-you-go', 'Subscription', ], ], 'ReviewStatus' => [ 'type' => 'string', 'enum' => [ 'Pending Submission', 'Submitted', 'In review', 'Approved', 'Rejected', 'Action Required', ], ], 'SalesActivities' => [ 'type' => 'list', 'member' => [ 'shape' => 'SalesActivity', ], ], 'SalesActivity' => [ 'type' => 'string', 'enum' => [ 'Initialized discussions with customer', 'Customer has shown interest in solution', 'Conducted POC / Demo', 'In evaluation / planning stage', 'Agreed on solution to Business Problem', 'Completed Action Plan', 'Finalized Deployment Need', 'SOW Signed', ], ], 'SalesInvolvementType' => [ 'type' => 'string', 'enum' => [ 'For Visibility Only', 'Co-Sell', ], ], 'SenderContact' => [ 'type' => 'structure', 'required' => [ 'Email', ], 'members' => [ 'BusinessTitle' => [ 'shape' => 'JobTitle', ], 'Email' => [ 'shape' => 'SenderContactEmail', ], 'FirstName' => [ 'shape' => 'Name', ], 'LastName' => [ 'shape' => 'Name', ], 'Phone' => [ 'shape' => 'PhoneNumber', ], ], ], 'SenderContactEmail' => [ 'type' => 'string', 'max' => 80, 'min' => 0, 'pattern' => '^[a-zA-Z0-9.!#$%&\'*+/=?^_{|}~-]+@[a-zA-Z0-9-]+(?:.[a-zA-Z0-9-]+)*$', 'sensitive' => true, ], 'SenderContactList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SenderContact', ], 'max' => 3, 'min' => 1, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'SoftwareRevenue' => [ 'type' => 'structure', 'members' => [ 'DeliveryModel' => [ 'shape' => 'RevenueModel', ], 'EffectiveDate' => [ 'shape' => 'Date', ], 'ExpirationDate' => [ 'shape' => 'Date', ], 'Value' => [ 'shape' => 'MonetaryValue', ], ], ], 'SolutionArn' => [ 'type' => 'string', 'pattern' => '^S-[0-9]{1,19}$', ], 'SolutionBase' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'Category', 'CreatedDate', 'Id', 'Name', 'Status', ], 'members' => [ 'Arn' => [ 'shape' => 'SolutionArn', ], 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'Category' => [ 'shape' => 'String', ], 'CreatedDate' => [ 'shape' => 'DateTime', ], 'Id' => [ 'shape' => 'SolutionIdentifier', ], 'Name' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'SolutionStatus', ], ], ], 'SolutionIdentifier' => [ 'type' => 'string', 'pattern' => '^S-[0-9]{1,19}$', ], 'SolutionIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'SolutionIdentifier', ], ], 'SolutionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SolutionBase', ], ], 'SolutionSort' => [ 'type' => 'structure', 'required' => [ 'SortBy', 'SortOrder', ], 'members' => [ 'SortBy' => [ 'shape' => 'SolutionSortName', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'SolutionSortName' => [ 'type' => 'string', 'enum' => [ 'Identifier', 'Name', 'Status', 'Category', 'CreatedDate', ], ], 'SolutionStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'Inactive', 'Draft', ], ], 'SortBy' => [ 'type' => 'string', 'enum' => [ 'CreatedDate', ], ], 'SortObject' => [ 'type' => 'structure', 'members' => [ 'SortBy' => [ 'shape' => 'SortBy', ], 'SortOrder' => [ 'shape' => 'SortOrder', ], ], ], 'SortOrder' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'Stage' => [ 'type' => 'string', 'enum' => [ 'Prospect', 'Qualified', 'Technical Validation', 'Business Validation', 'Committed', 'Launched', 'Closed Lost', ], ], 'StartEngagementByAcceptingInvitationTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'ClientToken', 'Identifier', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'ClientToken' => [ 'shape' => 'StartEngagementByAcceptingInvitationTaskRequestClientTokenString', 'idempotencyToken' => true, ], 'Identifier' => [ 'shape' => 'EngagementInvitationArnOrIdentifier', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'StartEngagementByAcceptingInvitationTaskRequestClientTokenString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '^[!-~]{1,64}$', ], 'StartEngagementByAcceptingInvitationTaskResponse' => [ 'type' => 'structure', 'members' => [ 'EngagementInvitationId' => [ 'shape' => 'EngagementInvitationIdentifier', ], 'Message' => [ 'shape' => 'String', ], 'OpportunityId' => [ 'shape' => 'OpportunityIdentifier', ], 'ReasonCode' => [ 'shape' => 'ReasonCode', ], 'ResourceSnapshotJobId' => [ 'shape' => 'ResourceSnapshotJobIdentifier', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'TaskArn' => [ 'shape' => 'TaskArn', ], 'TaskId' => [ 'shape' => 'TaskIdentifier', ], 'TaskStatus' => [ 'shape' => 'TaskStatus', ], ], ], 'StartEngagementFromOpportunityTaskRequest' => [ 'type' => 'structure', 'required' => [ 'AwsSubmission', 'Catalog', 'ClientToken', 'Identifier', ], 'members' => [ 'AwsSubmission' => [ 'shape' => 'AwsSubmission', ], 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'ClientToken' => [ 'shape' => 'StartEngagementFromOpportunityTaskRequestClientTokenString', 'idempotencyToken' => true, ], 'Identifier' => [ 'shape' => 'OpportunityIdentifier', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'StartEngagementFromOpportunityTaskRequestClientTokenString' => [ 'type' => 'string', 'min' => 1, 'pattern' => '^[!-~]{1,64}$', ], 'StartEngagementFromOpportunityTaskResponse' => [ 'type' => 'structure', 'members' => [ 'EngagementId' => [ 'shape' => 'EngagementIdentifier', ], 'EngagementInvitationId' => [ 'shape' => 'EngagementInvitationIdentifier', ], 'Message' => [ 'shape' => 'String', ], 'OpportunityId' => [ 'shape' => 'OpportunityIdentifier', ], 'ReasonCode' => [ 'shape' => 'ReasonCode', ], 'ResourceSnapshotJobId' => [ 'shape' => 'ResourceSnapshotJobIdentifier', ], 'StartTime' => [ 'shape' => 'DateTime', ], 'TaskArn' => [ 'shape' => 'TaskArn', ], 'TaskId' => [ 'shape' => 'TaskIdentifier', ], 'TaskStatus' => [ 'shape' => 'TaskStatus', ], ], ], 'StartResourceSnapshotJobRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'ResourceSnapshotJobIdentifier', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'ResourceSnapshotJobIdentifier' => [ 'shape' => 'ResourceSnapshotJobIdentifier', ], ], ], 'StopResourceSnapshotJobRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'ResourceSnapshotJobIdentifier', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'ResourceSnapshotJobIdentifier' => [ 'shape' => 'ResourceSnapshotJobIdentifier', ], ], ], 'String' => [ 'type' => 'string', ], 'SubmitOpportunityRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'Identifier', 'InvolvementType', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'Identifier' => [ 'shape' => 'OpportunityIdentifier', ], 'InvolvementType' => [ 'shape' => 'SalesInvolvementType', ], 'Visibility' => [ 'shape' => 'Visibility', ], ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TaggableResourceArn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$', ], 'TaggableResourceArn' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '^arn:[\\w+=/,.@-]+:partnercentral:[\\w+=/,.@-]*:[0-9]{12}:catalog/([a-zA-Z]+)/[\\w+=,.@-]+(/[\\w+=,.@-]+)*$', ], 'TaskArn' => [ 'type' => 'string', 'pattern' => '^arn:.*', ], 'TaskArnOrIdentifier' => [ 'type' => 'string', 'pattern' => '^(arn:.*|task-[0-9a-z]{13})$', ], 'TaskIdentifier' => [ 'type' => 'string', 'pattern' => 'task-[0-9a-z]{13}$', ], 'TaskIdentifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskArnOrIdentifier', ], 'max' => 10, 'min' => 1, ], 'TaskStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'COMPLETE', 'FAILED', ], ], 'TaskStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'TaskStatus', ], 'max' => 3, 'min' => 1, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'TaggableResourceArn', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateOpportunityRequest' => [ 'type' => 'structure', 'required' => [ 'Catalog', 'Identifier', 'LastModifiedDate', ], 'members' => [ 'Catalog' => [ 'shape' => 'CatalogIdentifier', ], 'Customer' => [ 'shape' => 'Customer', ], 'Identifier' => [ 'shape' => 'OpportunityIdentifier', ], 'LastModifiedDate' => [ 'shape' => 'DateTime', ], 'LifeCycle' => [ 'shape' => 'LifeCycle', ], 'Marketing' => [ 'shape' => 'Marketing', ], 'NationalSecurity' => [ 'shape' => 'NationalSecurity', ], 'OpportunityType' => [ 'shape' => 'OpportunityType', ], 'PartnerOpportunityIdentifier' => [ 'shape' => 'UpdateOpportunityRequestPartnerOpportunityIdentifierString', ], 'PrimaryNeedsFromAws' => [ 'shape' => 'PrimaryNeedsFromAws', ], 'Project' => [ 'shape' => 'Project', ], 'SoftwareRevenue' => [ 'shape' => 'SoftwareRevenue', ], ], ], 'UpdateOpportunityRequestPartnerOpportunityIdentifierString' => [ 'type' => 'string', 'max' => 64, 'min' => 0, ], 'UpdateOpportunityResponse' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedDate', ], 'members' => [ 'Id' => [ 'shape' => 'OpportunityIdentifier', ], 'LastModifiedDate' => [ 'shape' => 'DateTime', ], ], ], 'UseCases' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', 'Reason', ], 'members' => [ 'ErrorList' => [ 'shape' => 'ValidationExceptionErrorList', ], 'Message' => [ 'shape' => 'String', ], 'Reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'exception' => true, ], 'ValidationExceptionError' => [ 'type' => 'structure', 'required' => [ 'Code', 'Message', ], 'members' => [ 'Code' => [ 'shape' => 'ValidationExceptionErrorCode', ], 'FieldName' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionErrorCode' => [ 'type' => 'string', 'enum' => [ 'REQUIRED_FIELD_MISSING', 'INVALID_ENUM_VALUE', 'INVALID_STRING_FORMAT', 'INVALID_VALUE', 'TOO_MANY_VALUES', 'INVALID_RESOURCE_STATE', 'DUPLICATE_KEY_VALUE', 'VALUE_OUT_OF_RANGE', 'ACTION_NOT_PERMITTED', ], ], 'ValidationExceptionErrorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionError', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'REQUEST_VALIDATION_FAILED', 'BUSINESS_VALIDATION_FAILED', ], ], 'Visibility' => [ 'type' => 'string', 'enum' => [ 'Full', 'Limited', ], ], 'WebsiteUrl' => [ 'type' => 'string', 'max' => 255, 'min' => 4, 'sensitive' => true, ], ],];
