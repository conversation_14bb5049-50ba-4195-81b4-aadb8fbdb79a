<?php
// This file was auto-generated from sdk-root/src/data/cleanroomsml/2023-09-06/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-09-06', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'cleanrooms-ml', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Clean Rooms ML', 'serviceId' => 'CleanRoomsML', 'signatureVersion' => 'v4', 'signingName' => 'cleanrooms-ml', 'uid' => 'cleanroomsml-2023-09-06', ], 'operations' => [ 'CancelTrainedModel' => [ 'name' => 'CancelTrainedModel', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/memberships/{membershipIdentifier}/trained-models/{trainedModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelTrainedModelRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CancelTrainedModelInferenceJob' => [ 'name' => 'CancelTrainedModelInferenceJob', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/memberships/{membershipIdentifier}/trained-model-inference-jobs/{trainedModelInferenceJobArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelTrainedModelInferenceJobRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'CreateAudienceModel' => [ 'name' => 'CreateAudienceModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/audience-model', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateAudienceModelRequest', ], 'output' => [ 'shape' => 'CreateAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateConfiguredAudienceModel' => [ 'name' => 'CreateConfiguredAudienceModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/configured-audience-model', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateConfiguredAudienceModelRequest', ], 'output' => [ 'shape' => 'CreateConfiguredAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateConfiguredModelAlgorithm' => [ 'name' => 'CreateConfiguredModelAlgorithm', 'http' => [ 'method' => 'POST', 'requestUri' => '/configured-model-algorithms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateConfiguredModelAlgorithmRequest', ], 'output' => [ 'shape' => 'CreateConfiguredModelAlgorithmResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateConfiguredModelAlgorithmAssociation' => [ 'name' => 'CreateConfiguredModelAlgorithmAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/configured-model-algorithm-associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateConfiguredModelAlgorithmAssociationRequest', ], 'output' => [ 'shape' => 'CreateConfiguredModelAlgorithmAssociationResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateMLInputChannel' => [ 'name' => 'CreateMLInputChannel', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/ml-input-channels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMLInputChannelRequest', ], 'output' => [ 'shape' => 'CreateMLInputChannelResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateTrainedModel' => [ 'name' => 'CreateTrainedModel', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/trained-models', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTrainedModelRequest', ], 'output' => [ 'shape' => 'CreateTrainedModelResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'CreateTrainingDataset' => [ 'name' => 'CreateTrainingDataset', 'http' => [ 'method' => 'POST', 'requestUri' => '/training-dataset', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTrainingDatasetRequest', ], 'output' => [ 'shape' => 'CreateTrainingDatasetResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'DeleteAudienceGenerationJob' => [ 'name' => 'DeleteAudienceGenerationJob', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/audience-generation-job/{audienceGenerationJobArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAudienceGenerationJobRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteAudienceModel' => [ 'name' => 'DeleteAudienceModel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/audience-model/{audienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAudienceModelRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteConfiguredAudienceModel' => [ 'name' => 'DeleteConfiguredAudienceModel', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConfiguredAudienceModelRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteConfiguredAudienceModelPolicy' => [ 'name' => 'DeleteConfiguredAudienceModelPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConfiguredAudienceModelPolicyRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteConfiguredModelAlgorithm' => [ 'name' => 'DeleteConfiguredModelAlgorithm', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/configured-model-algorithms/{configuredModelAlgorithmArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConfiguredModelAlgorithmRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteConfiguredModelAlgorithmAssociation' => [ 'name' => 'DeleteConfiguredModelAlgorithmAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/memberships/{membershipIdentifier}/configured-model-algorithm-associations/{configuredModelAlgorithmAssociationArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteConfiguredModelAlgorithmAssociationRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteMLConfiguration' => [ 'name' => 'DeleteMLConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/memberships/{membershipIdentifier}/ml-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMLConfigurationRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteMLInputChannelData' => [ 'name' => 'DeleteMLInputChannelData', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/memberships/{membershipIdentifier}/ml-input-channels/{mlInputChannelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMLInputChannelDataRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteTrainedModelOutput' => [ 'name' => 'DeleteTrainedModelOutput', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/memberships/{membershipIdentifier}/trained-models/{trainedModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTrainedModelOutputRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteTrainingDataset' => [ 'name' => 'DeleteTrainingDataset', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/training-dataset/{trainingDatasetArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTrainingDatasetRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'GetAudienceGenerationJob' => [ 'name' => 'GetAudienceGenerationJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-generation-job/{audienceGenerationJobArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAudienceGenerationJobRequest', ], 'output' => [ 'shape' => 'GetAudienceGenerationJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetAudienceModel' => [ 'name' => 'GetAudienceModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-model/{audienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAudienceModelRequest', ], 'output' => [ 'shape' => 'GetAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetCollaborationConfiguredModelAlgorithmAssociation' => [ 'name' => 'GetCollaborationConfiguredModelAlgorithmAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/configured-model-algorithm-associations/{configuredModelAlgorithmAssociationArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCollaborationConfiguredModelAlgorithmAssociationRequest', ], 'output' => [ 'shape' => 'GetCollaborationConfiguredModelAlgorithmAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetCollaborationMLInputChannel' => [ 'name' => 'GetCollaborationMLInputChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/ml-input-channels/{mlInputChannelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCollaborationMLInputChannelRequest', ], 'output' => [ 'shape' => 'GetCollaborationMLInputChannelResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetCollaborationTrainedModel' => [ 'name' => 'GetCollaborationTrainedModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/trained-models/{trainedModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCollaborationTrainedModelRequest', ], 'output' => [ 'shape' => 'GetCollaborationTrainedModelResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetConfiguredAudienceModel' => [ 'name' => 'GetConfiguredAudienceModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfiguredAudienceModelRequest', ], 'output' => [ 'shape' => 'GetConfiguredAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetConfiguredAudienceModelPolicy' => [ 'name' => 'GetConfiguredAudienceModelPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfiguredAudienceModelPolicyRequest', ], 'output' => [ 'shape' => 'GetConfiguredAudienceModelPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetConfiguredModelAlgorithm' => [ 'name' => 'GetConfiguredModelAlgorithm', 'http' => [ 'method' => 'GET', 'requestUri' => '/configured-model-algorithms/{configuredModelAlgorithmArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfiguredModelAlgorithmRequest', ], 'output' => [ 'shape' => 'GetConfiguredModelAlgorithmResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetConfiguredModelAlgorithmAssociation' => [ 'name' => 'GetConfiguredModelAlgorithmAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/configured-model-algorithm-associations/{configuredModelAlgorithmAssociationArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConfiguredModelAlgorithmAssociationRequest', ], 'output' => [ 'shape' => 'GetConfiguredModelAlgorithmAssociationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetMLConfiguration' => [ 'name' => 'GetMLConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/ml-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMLConfigurationRequest', ], 'output' => [ 'shape' => 'GetMLConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetMLInputChannel' => [ 'name' => 'GetMLInputChannel', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/ml-input-channels/{mlInputChannelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMLInputChannelRequest', ], 'output' => [ 'shape' => 'GetMLInputChannelResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetTrainedModel' => [ 'name' => 'GetTrainedModel', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/trained-models/{trainedModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTrainedModelRequest', ], 'output' => [ 'shape' => 'GetTrainedModelResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetTrainedModelInferenceJob' => [ 'name' => 'GetTrainedModelInferenceJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/trained-model-inference-jobs/{trainedModelInferenceJobArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTrainedModelInferenceJobRequest', ], 'output' => [ 'shape' => 'GetTrainedModelInferenceJobResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetTrainingDataset' => [ 'name' => 'GetTrainingDataset', 'http' => [ 'method' => 'GET', 'requestUri' => '/training-dataset/{trainingDatasetArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTrainingDatasetRequest', ], 'output' => [ 'shape' => 'GetTrainingDatasetResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListAudienceExportJobs' => [ 'name' => 'ListAudienceExportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-export-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAudienceExportJobsRequest', ], 'output' => [ 'shape' => 'ListAudienceExportJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAudienceGenerationJobs' => [ 'name' => 'ListAudienceGenerationJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-generation-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAudienceGenerationJobsRequest', ], 'output' => [ 'shape' => 'ListAudienceGenerationJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListAudienceModels' => [ 'name' => 'ListAudienceModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/audience-model', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAudienceModelsRequest', ], 'output' => [ 'shape' => 'ListAudienceModelsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCollaborationConfiguredModelAlgorithmAssociations' => [ 'name' => 'ListCollaborationConfiguredModelAlgorithmAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/configured-model-algorithm-associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCollaborationConfiguredModelAlgorithmAssociationsRequest', ], 'output' => [ 'shape' => 'ListCollaborationConfiguredModelAlgorithmAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCollaborationMLInputChannels' => [ 'name' => 'ListCollaborationMLInputChannels', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/ml-input-channels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCollaborationMLInputChannelsRequest', ], 'output' => [ 'shape' => 'ListCollaborationMLInputChannelsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCollaborationTrainedModelExportJobs' => [ 'name' => 'ListCollaborationTrainedModelExportJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/trained-models/{trainedModelArn}/export-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCollaborationTrainedModelExportJobsRequest', ], 'output' => [ 'shape' => 'ListCollaborationTrainedModelExportJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCollaborationTrainedModelInferenceJobs' => [ 'name' => 'ListCollaborationTrainedModelInferenceJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/trained-model-inference-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCollaborationTrainedModelInferenceJobsRequest', ], 'output' => [ 'shape' => 'ListCollaborationTrainedModelInferenceJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListCollaborationTrainedModels' => [ 'name' => 'ListCollaborationTrainedModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/collaborations/{collaborationIdentifier}/trained-models', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCollaborationTrainedModelsRequest', ], 'output' => [ 'shape' => 'ListCollaborationTrainedModelsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListConfiguredAudienceModels' => [ 'name' => 'ListConfiguredAudienceModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/configured-audience-model', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConfiguredAudienceModelsRequest', ], 'output' => [ 'shape' => 'ListConfiguredAudienceModelsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListConfiguredModelAlgorithmAssociations' => [ 'name' => 'ListConfiguredModelAlgorithmAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/configured-model-algorithm-associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConfiguredModelAlgorithmAssociationsRequest', ], 'output' => [ 'shape' => 'ListConfiguredModelAlgorithmAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListConfiguredModelAlgorithms' => [ 'name' => 'ListConfiguredModelAlgorithms', 'http' => [ 'method' => 'GET', 'requestUri' => '/configured-model-algorithms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConfiguredModelAlgorithmsRequest', ], 'output' => [ 'shape' => 'ListConfiguredModelAlgorithmsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListMLInputChannels' => [ 'name' => 'ListMLInputChannels', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/ml-input-channels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMLInputChannelsRequest', ], 'output' => [ 'shape' => 'ListMLInputChannelsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListTrainedModelInferenceJobs' => [ 'name' => 'ListTrainedModelInferenceJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/trained-model-inference-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTrainedModelInferenceJobsRequest', ], 'output' => [ 'shape' => 'ListTrainedModelInferenceJobsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTrainedModels' => [ 'name' => 'ListTrainedModels', 'http' => [ 'method' => 'GET', 'requestUri' => '/memberships/{membershipIdentifier}/trained-models', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTrainedModelsRequest', ], 'output' => [ 'shape' => 'ListTrainedModelsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListTrainingDatasets' => [ 'name' => 'ListTrainingDatasets', 'http' => [ 'method' => 'GET', 'requestUri' => '/training-dataset', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTrainingDatasetsRequest', ], 'output' => [ 'shape' => 'ListTrainingDatasetsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'PutConfiguredAudienceModelPolicy' => [ 'name' => 'PutConfiguredAudienceModelPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}/policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutConfiguredAudienceModelPolicyRequest', ], 'output' => [ 'shape' => 'PutConfiguredAudienceModelPolicyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'PutMLConfiguration' => [ 'name' => 'PutMLConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/memberships/{membershipIdentifier}/ml-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutMLConfigurationRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], 'idempotent' => true, ], 'StartAudienceExportJob' => [ 'name' => 'StartAudienceExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/audience-export-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartAudienceExportJobRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'StartAudienceGenerationJob' => [ 'name' => 'StartAudienceGenerationJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/audience-generation-job', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartAudienceGenerationJobRequest', ], 'output' => [ 'shape' => 'StartAudienceGenerationJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'StartTrainedModelExportJob' => [ 'name' => 'StartTrainedModelExportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/trained-models/{trainedModelArn}/export-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartTrainedModelExportJobRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'StartTrainedModelInferenceJob' => [ 'name' => 'StartTrainedModelInferenceJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/memberships/{membershipIdentifier}/trained-model-inference-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartTrainedModelInferenceJobRequest', ], 'output' => [ 'shape' => 'StartTrainedModelInferenceJobResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateConfiguredAudienceModel' => [ 'name' => 'UpdateConfiguredAudienceModel', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/configured-audience-model/{configuredAudienceModelArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConfiguredAudienceModelRequest', ], 'output' => [ 'shape' => 'UpdateConfiguredAudienceModelResponse', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '[0-9]{12}', ], 'AccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 5, 'min' => 1, ], 'AlgorithmImage' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.*', ], 'AnalysisTemplateArn' => [ 'type' => 'string', 'max' => 200, 'min' => 0, 'pattern' => 'arn:aws[-a-z]*:cleanrooms:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:membership/[\\d\\w-]+/analysistemplate/[\\d\\w-]+', ], 'AudienceDestination' => [ 'type' => 'structure', 'required' => [ 's3Destination', ], 'members' => [ 's3Destination' => [ 'shape' => 'S3ConfigMap', ], ], ], 'AudienceExportJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudienceExportJobSummary', ], ], 'AudienceExportJobStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', ], ], 'AudienceExportJobSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'name', 'audienceGenerationJobArn', 'audienceSize', 'status', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'name' => [ 'shape' => 'NameString', ], 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], 'audienceSize' => [ 'shape' => 'AudienceSize', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'status' => [ 'shape' => 'AudienceExportJobStatus', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'outputLocation' => [ 'shape' => 'S3Path', ], ], ], 'AudienceGenerationJobArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:audience-generation-job/[-a-zA-Z0-9_/.]+', ], 'AudienceGenerationJobDataSource' => [ 'type' => 'structure', 'required' => [ 'roleArn', ], 'members' => [ 'dataSource' => [ 'shape' => 'S3ConfigMap', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'sqlParameters' => [ 'shape' => 'ProtectedQuerySQLParameters', ], 'sqlComputeConfiguration' => [ 'shape' => 'ComputeConfiguration', ], ], ], 'AudienceGenerationJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudienceGenerationJobSummary', ], ], 'AudienceGenerationJobStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_PENDING', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', ], ], 'AudienceGenerationJobSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'audienceGenerationJobArn', 'name', 'status', 'configuredAudienceModelArn', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'status' => [ 'shape' => 'AudienceGenerationJobStatus', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'startedBy' => [ 'shape' => 'AccountId', ], ], ], 'AudienceModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:audience-model/[-a-zA-Z0-9_/.]+', ], 'AudienceModelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudienceModelSummary', ], ], 'AudienceModelStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_PENDING', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', ], ], 'AudienceModelSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'audienceModelArn', 'name', 'trainingDatasetArn', 'status', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'name' => [ 'shape' => 'NameString', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], 'status' => [ 'shape' => 'AudienceModelStatus', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'AudienceQualityMetrics' => [ 'type' => 'structure', 'required' => [ 'relevanceMetrics', ], 'members' => [ 'relevanceMetrics' => [ 'shape' => 'RelevanceMetrics', ], 'recallMetric' => [ 'shape' => 'AudienceQualityMetricsRecallMetricDouble', ], ], ], 'AudienceQualityMetricsRecallMetricDouble' => [ 'type' => 'double', 'box' => true, 'max' => 1.0, 'min' => 0.0, ], 'AudienceSize' => [ 'type' => 'structure', 'required' => [ 'type', 'value', ], 'members' => [ 'type' => [ 'shape' => 'AudienceSizeType', ], 'value' => [ 'shape' => 'AudienceSizeValue', ], ], ], 'AudienceSizeBins' => [ 'type' => 'list', 'member' => [ 'shape' => 'AudienceSizeValue', ], 'max' => 25, 'min' => 1, ], 'AudienceSizeConfig' => [ 'type' => 'structure', 'required' => [ 'audienceSizeType', 'audienceSizeBins', ], 'members' => [ 'audienceSizeType' => [ 'shape' => 'AudienceSizeType', ], 'audienceSizeBins' => [ 'shape' => 'AudienceSizeBins', ], ], ], 'AudienceSizeType' => [ 'type' => 'string', 'enum' => [ 'ABSOLUTE', 'PERCENTAGE', ], ], 'AudienceSizeValue' => [ 'type' => 'integer', 'box' => true, 'max' => 20000000, 'min' => 1, ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CancelTrainedModelInferenceJobRequest' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'trainedModelInferenceJobArn', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'trainedModelInferenceJobArn' => [ 'shape' => 'TrainedModelInferenceJobArn', 'location' => 'uri', 'locationName' => 'trainedModelInferenceJobArn', ], ], ], 'CancelTrainedModelRequest' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'trainedModelArn', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', 'location' => 'uri', 'locationName' => 'trainedModelArn', ], ], ], 'CollaborationConfiguredModelAlgorithmAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollaborationConfiguredModelAlgorithmAssociationSummary', ], ], 'CollaborationConfiguredModelAlgorithmAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'configuredModelAlgorithmAssociationArn', 'name', 'membershipIdentifier', 'collaborationIdentifier', 'configuredModelAlgorithmArn', 'creatorAccountId', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], 'configuredModelAlgorithmArn' => [ 'shape' => 'ConfiguredModelAlgorithmArn', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], ], ], 'CollaborationMLInputChannelSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'membershipIdentifier', 'collaborationIdentifier', 'name', 'configuredModelAlgorithmAssociations', 'mlInputChannelArn', 'status', 'creatorAccountId', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], 'name' => [ 'shape' => 'NameString', ], 'configuredModelAlgorithmAssociations' => [ 'shape' => 'CollaborationMLInputChannelSummaryConfiguredModelAlgorithmAssociationsList', ], 'mlInputChannelArn' => [ 'shape' => 'MLInputChannelArn', ], 'status' => [ 'shape' => 'MLInputChannelStatus', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'CollaborationMLInputChannelSummaryConfiguredModelAlgorithmAssociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'max' => 1, 'min' => 1, ], 'CollaborationMLInputChannelsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollaborationMLInputChannelSummary', ], ], 'CollaborationTrainedModelExportJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollaborationTrainedModelExportJobSummary', ], ], 'CollaborationTrainedModelExportJobSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'name', 'outputConfiguration', 'status', 'creatorAccountId', 'trainedModelArn', 'membershipIdentifier', 'collaborationIdentifier', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'name' => [ 'shape' => 'NameString', ], 'outputConfiguration' => [ 'shape' => 'TrainedModelExportOutputConfiguration', ], 'status' => [ 'shape' => 'TrainedModelExportJobStatus', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', ], 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], ], ], 'CollaborationTrainedModelInferenceJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollaborationTrainedModelInferenceJobSummary', ], ], 'CollaborationTrainedModelInferenceJobSummary' => [ 'type' => 'structure', 'required' => [ 'trainedModelInferenceJobArn', 'membershipIdentifier', 'trainedModelArn', 'collaborationIdentifier', 'status', 'outputConfiguration', 'name', 'createTime', 'updateTime', 'creatorAccountId', ], 'members' => [ 'trainedModelInferenceJobArn' => [ 'shape' => 'TrainedModelInferenceJobArn', ], 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], 'status' => [ 'shape' => 'TrainedModelInferenceJobStatus', ], 'outputConfiguration' => [ 'shape' => 'InferenceOutputConfiguration', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'metricsStatus' => [ 'shape' => 'MetricsStatus', ], 'metricsStatusDetails' => [ 'shape' => 'String', ], 'logsStatus' => [ 'shape' => 'LogsStatus', ], 'logsStatusDetails' => [ 'shape' => 'String', ], 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], ], ], 'CollaborationTrainedModelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CollaborationTrainedModelSummary', ], ], 'CollaborationTrainedModelSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'trainedModelArn', 'name', 'membershipIdentifier', 'collaborationIdentifier', 'status', 'configuredModelAlgorithmAssociationArn', 'creatorAccountId', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], 'status' => [ 'shape' => 'TrainedModelStatus', ], 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], ], ], 'ColumnName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_](([a-zA-Z0-9_ ]+-)*([a-zA-Z0-9_ ]+))?', ], 'ColumnSchema' => [ 'type' => 'structure', 'required' => [ 'columnName', 'columnTypes', ], 'members' => [ 'columnName' => [ 'shape' => 'ColumnName', ], 'columnTypes' => [ 'shape' => 'ColumnTypeList', ], ], ], 'ColumnType' => [ 'type' => 'string', 'enum' => [ 'USER_ID', 'ITEM_ID', 'TIMESTAMP', 'CATEGORICAL_FEATURE', 'NUMERICAL_FEATURE', ], ], 'ColumnTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnType', ], 'max' => 1, 'min' => 1, ], 'ComputeConfiguration' => [ 'type' => 'structure', 'members' => [ 'worker' => [ 'shape' => 'WorkerComputeConfiguration', ], ], 'union' => true, ], 'ConfiguredAudienceModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:configured-audience-model/[-a-zA-Z0-9_/.]+', ], 'ConfiguredAudienceModelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredAudienceModelSummary', ], ], 'ConfiguredAudienceModelOutputConfig' => [ 'type' => 'structure', 'required' => [ 'destination', 'roleArn', ], 'members' => [ 'destination' => [ 'shape' => 'AudienceDestination', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'ConfiguredAudienceModelStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', ], ], 'ConfiguredAudienceModelSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'name', 'audienceModelArn', 'outputConfig', 'configuredAudienceModelArn', 'status', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'name' => [ 'shape' => 'NameString', ], 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'outputConfig' => [ 'shape' => 'ConfiguredAudienceModelOutputConfig', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'status' => [ 'shape' => 'ConfiguredAudienceModelStatus', ], ], ], 'ConfiguredModelAlgorithmArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:configured-model-algorithm/[-a-zA-Z0-9_/.]+', ], 'ConfiguredModelAlgorithmAssociationArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:(membership/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/)?configured-model-algorithm-association/[-a-zA-Z0-9_/.]+', ], 'ConfiguredModelAlgorithmAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationSummary', ], ], 'ConfiguredModelAlgorithmAssociationSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'configuredModelAlgorithmAssociationArn', 'configuredModelAlgorithmArn', 'name', 'membershipIdentifier', 'collaborationIdentifier', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'configuredModelAlgorithmArn' => [ 'shape' => 'ConfiguredModelAlgorithmArn', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], ], ], 'ConfiguredModelAlgorithmList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredModelAlgorithmSummary', ], ], 'ConfiguredModelAlgorithmSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'configuredModelAlgorithmArn', 'name', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'configuredModelAlgorithmArn' => [ 'shape' => 'ConfiguredModelAlgorithmArn', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ContainerArgument' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*', ], 'ContainerArguments' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerArgument', ], 'max' => 100, 'min' => 1, ], 'ContainerConfig' => [ 'type' => 'structure', 'required' => [ 'imageUri', ], 'members' => [ 'imageUri' => [ 'shape' => 'AlgorithmImage', ], 'entrypoint' => [ 'shape' => 'ContainerEntrypoint', ], 'arguments' => [ 'shape' => 'ContainerArguments', ], 'metricDefinitions' => [ 'shape' => 'MetricDefinitionList', ], ], ], 'ContainerEntrypoint' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerEntrypointString', ], 'max' => 100, 'min' => 1, ], 'ContainerEntrypointString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*', ], 'CreateAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'trainingDatasetArn', ], 'members' => [ 'trainingDataStartTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainingDataEndTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'name' => [ 'shape' => 'NameString', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'CreateAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'audienceModelArn', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], ], ], 'CreateConfiguredAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'audienceModelArn', 'outputConfig', 'sharedAudienceMetrics', ], 'members' => [ 'name' => [ 'shape' => 'NameString', ], 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'outputConfig' => [ 'shape' => 'ConfiguredAudienceModelOutputConfig', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'sharedAudienceMetrics' => [ 'shape' => 'MetricsList', ], 'minMatchingSeedSize' => [ 'shape' => 'MinMatchingSeedSize', ], 'audienceSizeConfig' => [ 'shape' => 'AudienceSizeConfig', ], 'tags' => [ 'shape' => 'TagMap', ], 'childResourceTagOnCreatePolicy' => [ 'shape' => 'TagOnCreatePolicy', ], ], ], 'CreateConfiguredAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], ], ], 'CreateConfiguredModelAlgorithmAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'configuredModelAlgorithmArn', 'name', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'configuredModelAlgorithmArn' => [ 'shape' => 'ConfiguredModelAlgorithmArn', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'privacyConfiguration' => [ 'shape' => 'PrivacyConfiguration', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateConfiguredModelAlgorithmAssociationResponse' => [ 'type' => 'structure', 'required' => [ 'configuredModelAlgorithmAssociationArn', ], 'members' => [ 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], ], ], 'CreateConfiguredModelAlgorithmRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'roleArn', ], 'members' => [ 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'trainingContainerConfig' => [ 'shape' => 'ContainerConfig', ], 'inferenceContainerConfig' => [ 'shape' => 'InferenceContainerConfig', ], 'tags' => [ 'shape' => 'TagMap', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'CreateConfiguredModelAlgorithmResponse' => [ 'type' => 'structure', 'required' => [ 'configuredModelAlgorithmArn', ], 'members' => [ 'configuredModelAlgorithmArn' => [ 'shape' => 'ConfiguredModelAlgorithmArn', ], ], ], 'CreateMLInputChannelRequest' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'configuredModelAlgorithmAssociations', 'inputChannel', 'name', 'retentionInDays', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'configuredModelAlgorithmAssociations' => [ 'shape' => 'CreateMLInputChannelRequestConfiguredModelAlgorithmAssociationsList', ], 'inputChannel' => [ 'shape' => 'InputChannel', ], 'name' => [ 'shape' => 'NameString', ], 'retentionInDays' => [ 'shape' => 'CreateMLInputChannelRequestRetentionInDaysInteger', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateMLInputChannelRequestConfiguredModelAlgorithmAssociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'max' => 1, 'min' => 1, ], 'CreateMLInputChannelRequestRetentionInDaysInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 30, 'min' => 1, ], 'CreateMLInputChannelResponse' => [ 'type' => 'structure', 'required' => [ 'mlInputChannelArn', ], 'members' => [ 'mlInputChannelArn' => [ 'shape' => 'MLInputChannelArn', ], ], ], 'CreateTrainedModelRequest' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'name', 'configuredModelAlgorithmAssociationArn', 'resourceConfig', 'dataChannels', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'name' => [ 'shape' => 'NameString', ], 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'hyperparameters' => [ 'shape' => 'HyperParameters', ], 'environment' => [ 'shape' => 'Environment', ], 'resourceConfig' => [ 'shape' => 'ResourceConfig', ], 'stoppingCondition' => [ 'shape' => 'StoppingCondition', ], 'dataChannels' => [ 'shape' => 'ModelTrainingDataChannels', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateTrainedModelResponse' => [ 'type' => 'structure', 'required' => [ 'trainedModelArn', ], 'members' => [ 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', ], ], ], 'CreateTrainingDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'roleArn', 'trainingData', ], 'members' => [ 'name' => [ 'shape' => 'NameString', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'trainingData' => [ 'shape' => 'CreateTrainingDatasetRequestTrainingDataList', ], 'tags' => [ 'shape' => 'TagMap', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'CreateTrainingDatasetRequestTrainingDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dataset', ], 'max' => 1, 'min' => 1, ], 'CreateTrainingDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'trainingDatasetArn', ], 'members' => [ 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], ], ], 'DataSource' => [ 'type' => 'structure', 'required' => [ 'glueDataSource', ], 'members' => [ 'glueDataSource' => [ 'shape' => 'GlueDataSource', ], ], ], 'Dataset' => [ 'type' => 'structure', 'required' => [ 'type', 'inputConfig', ], 'members' => [ 'type' => [ 'shape' => 'DatasetType', ], 'inputConfig' => [ 'shape' => 'DatasetInputConfig', ], ], ], 'DatasetInputConfig' => [ 'type' => 'structure', 'required' => [ 'schema', 'dataSource', ], 'members' => [ 'schema' => [ 'shape' => 'DatasetInputConfigSchemaList', ], 'dataSource' => [ 'shape' => 'DataSource', ], ], ], 'DatasetInputConfigSchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ColumnSchema', ], 'max' => 100, 'min' => 1, ], 'DatasetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Dataset', ], ], 'DatasetType' => [ 'type' => 'string', 'enum' => [ 'INTERACTIONS', ], ], 'DeleteAudienceGenerationJobRequest' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobArn', ], 'members' => [ 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', 'location' => 'uri', 'locationName' => 'audienceGenerationJobArn', ], ], ], 'DeleteAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'audienceModelArn', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', 'location' => 'uri', 'locationName' => 'audienceModelArn', ], ], ], 'DeleteConfiguredAudienceModelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], ], ], 'DeleteConfiguredAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], ], ], 'DeleteConfiguredModelAlgorithmAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'configuredModelAlgorithmAssociationArn', 'membershipIdentifier', ], 'members' => [ 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', 'location' => 'uri', 'locationName' => 'configuredModelAlgorithmAssociationArn', ], 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'DeleteConfiguredModelAlgorithmRequest' => [ 'type' => 'structure', 'required' => [ 'configuredModelAlgorithmArn', ], 'members' => [ 'configuredModelAlgorithmArn' => [ 'shape' => 'ConfiguredModelAlgorithmArn', 'location' => 'uri', 'locationName' => 'configuredModelAlgorithmArn', ], ], ], 'DeleteMLConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'DeleteMLInputChannelDataRequest' => [ 'type' => 'structure', 'required' => [ 'mlInputChannelArn', 'membershipIdentifier', ], 'members' => [ 'mlInputChannelArn' => [ 'shape' => 'MLInputChannelArn', 'location' => 'uri', 'locationName' => 'mlInputChannelArn', ], 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'DeleteTrainedModelOutputRequest' => [ 'type' => 'structure', 'required' => [ 'trainedModelArn', 'membershipIdentifier', ], 'members' => [ 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', 'location' => 'uri', 'locationName' => 'trainedModelArn', ], 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'DeleteTrainingDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'trainingDatasetArn', ], 'members' => [ 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', 'location' => 'uri', 'locationName' => 'trainingDatasetArn', ], ], ], 'Destination' => [ 'type' => 'structure', 'required' => [ 's3Destination', ], 'members' => [ 's3Destination' => [ 'shape' => 'S3ConfigMap', ], ], ], 'Environment' => [ 'type' => 'map', 'key' => [ 'shape' => 'EnvironmentKeyString', ], 'value' => [ 'shape' => 'EnvironmentValueString', ], 'max' => 100, 'min' => 0, ], 'EnvironmentKeyString' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[a-zA-Z_][a-zA-Z0-9_]*', ], 'EnvironmentValueString' => [ 'type' => 'string', 'max' => 512, 'min' => 1, 'pattern' => '[\\S\\s]*', ], 'GetAudienceGenerationJobRequest' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobArn', ], 'members' => [ 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', 'location' => 'uri', 'locationName' => 'audienceGenerationJobArn', ], ], ], 'GetAudienceGenerationJobResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'audienceGenerationJobArn', 'name', 'status', 'configuredAudienceModelArn', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'status' => [ 'shape' => 'AudienceGenerationJobStatus', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'seedAudience' => [ 'shape' => 'AudienceGenerationJobDataSource', ], 'includeSeedInOutput' => [ 'shape' => 'Boolean', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'metrics' => [ 'shape' => 'AudienceQualityMetrics', ], 'startedBy' => [ 'shape' => 'AccountId', ], 'tags' => [ 'shape' => 'TagMap', ], 'protectedQueryIdentifier' => [ 'shape' => 'String', ], ], ], 'GetAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'audienceModelArn', ], 'members' => [ 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', 'location' => 'uri', 'locationName' => 'audienceModelArn', ], ], ], 'GetAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'audienceModelArn', 'name', 'trainingDatasetArn', 'status', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainingDataStartTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainingDataEndTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'name' => [ 'shape' => 'NameString', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], 'status' => [ 'shape' => 'AudienceModelStatus', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'GetCollaborationConfiguredModelAlgorithmAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'configuredModelAlgorithmAssociationArn', 'collaborationIdentifier', ], 'members' => [ 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', 'location' => 'uri', 'locationName' => 'configuredModelAlgorithmAssociationArn', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], ], ], 'GetCollaborationConfiguredModelAlgorithmAssociationResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'configuredModelAlgorithmAssociationArn', 'membershipIdentifier', 'collaborationIdentifier', 'configuredModelAlgorithmArn', 'name', 'creatorAccountId', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], 'configuredModelAlgorithmArn' => [ 'shape' => 'ConfiguredModelAlgorithmArn', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'privacyConfiguration' => [ 'shape' => 'PrivacyConfiguration', ], ], ], 'GetCollaborationMLInputChannelRequest' => [ 'type' => 'structure', 'required' => [ 'mlInputChannelArn', 'collaborationIdentifier', ], 'members' => [ 'mlInputChannelArn' => [ 'shape' => 'MLInputChannelArn', 'location' => 'uri', 'locationName' => 'mlInputChannelArn', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], ], ], 'GetCollaborationMLInputChannelResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'creatorAccountId', 'membershipIdentifier', 'collaborationIdentifier', 'mlInputChannelArn', 'name', 'configuredModelAlgorithmAssociations', 'status', 'retentionInDays', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], 'mlInputChannelArn' => [ 'shape' => 'MLInputChannelArn', ], 'name' => [ 'shape' => 'NameString', ], 'configuredModelAlgorithmAssociations' => [ 'shape' => 'GetCollaborationMLInputChannelResponseConfiguredModelAlgorithmAssociationsList', ], 'status' => [ 'shape' => 'MLInputChannelStatus', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'retentionInDays' => [ 'shape' => 'GetCollaborationMLInputChannelResponseRetentionInDaysInteger', ], 'numberOfRecords' => [ 'shape' => 'GetCollaborationMLInputChannelResponseNumberOfRecordsLong', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'GetCollaborationMLInputChannelResponseConfiguredModelAlgorithmAssociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'max' => 1, 'min' => 1, ], 'GetCollaborationMLInputChannelResponseNumberOfRecordsLong' => [ 'type' => 'long', 'box' => true, 'max' => 100000000000, 'min' => 0, ], 'GetCollaborationMLInputChannelResponseRetentionInDaysInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 30, 'min' => 1, ], 'GetCollaborationTrainedModelRequest' => [ 'type' => 'structure', 'required' => [ 'trainedModelArn', 'collaborationIdentifier', ], 'members' => [ 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', 'location' => 'uri', 'locationName' => 'trainedModelArn', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], ], ], 'GetCollaborationTrainedModelResponse' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'collaborationIdentifier', 'trainedModelArn', 'name', 'status', 'configuredModelAlgorithmAssociationArn', 'createTime', 'updateTime', 'creatorAccountId', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'status' => [ 'shape' => 'TrainedModelStatus', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'resourceConfig' => [ 'shape' => 'ResourceConfig', ], 'stoppingCondition' => [ 'shape' => 'StoppingCondition', ], 'metricsStatus' => [ 'shape' => 'MetricsStatus', ], 'metricsStatusDetails' => [ 'shape' => 'String', ], 'logsStatus' => [ 'shape' => 'LogsStatus', ], 'logsStatusDetails' => [ 'shape' => 'String', ], 'trainingContainerImageDigest' => [ 'shape' => 'String', ], 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'creatorAccountId' => [ 'shape' => 'AccountId', ], ], ], 'GetConfiguredAudienceModelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], ], ], 'GetConfiguredAudienceModelPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', 'configuredAudienceModelPolicy', 'policyHash', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'configuredAudienceModelPolicy' => [ 'shape' => 'ResourcePolicy', ], 'policyHash' => [ 'shape' => 'Hash', ], ], ], 'GetConfiguredAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], ], ], 'GetConfiguredAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'configuredAudienceModelArn', 'name', 'audienceModelArn', 'outputConfig', 'status', 'sharedAudienceMetrics', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'name' => [ 'shape' => 'NameString', ], 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'outputConfig' => [ 'shape' => 'ConfiguredAudienceModelOutputConfig', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'status' => [ 'shape' => 'ConfiguredAudienceModelStatus', ], 'sharedAudienceMetrics' => [ 'shape' => 'MetricsList', ], 'minMatchingSeedSize' => [ 'shape' => 'MinMatchingSeedSize', ], 'audienceSizeConfig' => [ 'shape' => 'AudienceSizeConfig', ], 'tags' => [ 'shape' => 'TagMap', ], 'childResourceTagOnCreatePolicy' => [ 'shape' => 'TagOnCreatePolicy', ], ], ], 'GetConfiguredModelAlgorithmAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'configuredModelAlgorithmAssociationArn', 'membershipIdentifier', ], 'members' => [ 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', 'location' => 'uri', 'locationName' => 'configuredModelAlgorithmAssociationArn', ], 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'GetConfiguredModelAlgorithmAssociationResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'configuredModelAlgorithmAssociationArn', 'membershipIdentifier', 'collaborationIdentifier', 'configuredModelAlgorithmArn', 'name', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], 'configuredModelAlgorithmArn' => [ 'shape' => 'ConfiguredModelAlgorithmArn', ], 'name' => [ 'shape' => 'NameString', ], 'privacyConfiguration' => [ 'shape' => 'PrivacyConfiguration', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'GetConfiguredModelAlgorithmRequest' => [ 'type' => 'structure', 'required' => [ 'configuredModelAlgorithmArn', ], 'members' => [ 'configuredModelAlgorithmArn' => [ 'shape' => 'ConfiguredModelAlgorithmArn', 'location' => 'uri', 'locationName' => 'configuredModelAlgorithmArn', ], ], ], 'GetConfiguredModelAlgorithmResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'configuredModelAlgorithmArn', 'name', 'roleArn', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'configuredModelAlgorithmArn' => [ 'shape' => 'ConfiguredModelAlgorithmArn', ], 'name' => [ 'shape' => 'NameString', ], 'trainingContainerConfig' => [ 'shape' => 'ContainerConfig', ], 'inferenceContainerConfig' => [ 'shape' => 'InferenceContainerConfig', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'tags' => [ 'shape' => 'TagMap', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], ], ], 'GetMLConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'GetMLConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'defaultOutputLocation', 'createTime', 'updateTime', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'defaultOutputLocation' => [ 'shape' => 'MLOutputConfiguration', ], 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'GetMLInputChannelRequest' => [ 'type' => 'structure', 'required' => [ 'mlInputChannelArn', 'membershipIdentifier', ], 'members' => [ 'mlInputChannelArn' => [ 'shape' => 'MLInputChannelArn', 'location' => 'uri', 'locationName' => 'mlInputChannelArn', ], 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'GetMLInputChannelResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'membershipIdentifier', 'collaborationIdentifier', 'inputChannel', 'mlInputChannelArn', 'name', 'configuredModelAlgorithmAssociations', 'status', 'retentionInDays', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], 'inputChannel' => [ 'shape' => 'InputChannel', ], 'protectedQueryIdentifier' => [ 'shape' => 'UUID', ], 'mlInputChannelArn' => [ 'shape' => 'MLInputChannelArn', ], 'name' => [ 'shape' => 'NameString', ], 'configuredModelAlgorithmAssociations' => [ 'shape' => 'GetMLInputChannelResponseConfiguredModelAlgorithmAssociationsList', ], 'status' => [ 'shape' => 'MLInputChannelStatus', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'retentionInDays' => [ 'shape' => 'GetMLInputChannelResponseRetentionInDaysInteger', ], 'numberOfRecords' => [ 'shape' => 'GetMLInputChannelResponseNumberOfRecordsLong', ], 'numberOfFiles' => [ 'shape' => 'GetMLInputChannelResponseNumberOfFilesDouble', ], 'sizeInGb' => [ 'shape' => 'GetMLInputChannelResponseSizeInGbDouble', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'GetMLInputChannelResponseConfiguredModelAlgorithmAssociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'max' => 1, 'min' => 1, ], 'GetMLInputChannelResponseNumberOfFilesDouble' => [ 'type' => 'double', 'box' => true, 'max' => 1000000, 'min' => 0, ], 'GetMLInputChannelResponseNumberOfRecordsLong' => [ 'type' => 'long', 'box' => true, 'max' => 100000000000, 'min' => 0, ], 'GetMLInputChannelResponseRetentionInDaysInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 30, 'min' => 1, ], 'GetMLInputChannelResponseSizeInGbDouble' => [ 'type' => 'double', 'box' => true, 'max' => 1000000, 'min' => 0, ], 'GetTrainedModelInferenceJobRequest' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'trainedModelInferenceJobArn', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'trainedModelInferenceJobArn' => [ 'shape' => 'TrainedModelInferenceJobArn', 'location' => 'uri', 'locationName' => 'trainedModelInferenceJobArn', ], ], ], 'GetTrainedModelInferenceJobResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'trainedModelInferenceJobArn', 'name', 'status', 'trainedModelArn', 'resourceConfig', 'outputConfiguration', 'membershipIdentifier', 'dataSource', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainedModelInferenceJobArn' => [ 'shape' => 'TrainedModelInferenceJobArn', ], 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'name' => [ 'shape' => 'NameString', ], 'status' => [ 'shape' => 'TrainedModelInferenceJobStatus', ], 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', ], 'resourceConfig' => [ 'shape' => 'InferenceResourceConfig', ], 'outputConfiguration' => [ 'shape' => 'InferenceOutputConfiguration', ], 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'dataSource' => [ 'shape' => 'ModelInferenceDataSource', ], 'containerExecutionParameters' => [ 'shape' => 'InferenceContainerExecutionParameters', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'inferenceContainerImageDigest' => [ 'shape' => 'String', ], 'environment' => [ 'shape' => 'InferenceEnvironmentMap', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'metricsStatus' => [ 'shape' => 'MetricsStatus', ], 'metricsStatusDetails' => [ 'shape' => 'String', ], 'logsStatus' => [ 'shape' => 'LogsStatus', ], 'logsStatusDetails' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'GetTrainedModelRequest' => [ 'type' => 'structure', 'required' => [ 'trainedModelArn', 'membershipIdentifier', ], 'members' => [ 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', 'location' => 'uri', 'locationName' => 'trainedModelArn', ], 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'GetTrainedModelResponse' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'collaborationIdentifier', 'trainedModelArn', 'name', 'status', 'configuredModelAlgorithmAssociationArn', 'createTime', 'updateTime', 'dataChannels', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'status' => [ 'shape' => 'TrainedModelStatus', ], 'statusDetails' => [ 'shape' => 'StatusDetails', ], 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'resourceConfig' => [ 'shape' => 'ResourceConfig', ], 'stoppingCondition' => [ 'shape' => 'StoppingCondition', ], 'metricsStatus' => [ 'shape' => 'MetricsStatus', ], 'metricsStatusDetails' => [ 'shape' => 'String', ], 'logsStatus' => [ 'shape' => 'LogsStatus', ], 'logsStatusDetails' => [ 'shape' => 'String', ], 'trainingContainerImageDigest' => [ 'shape' => 'String', ], 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'hyperparameters' => [ 'shape' => 'HyperParameters', ], 'environment' => [ 'shape' => 'Environment', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'dataChannels' => [ 'shape' => 'ModelTrainingDataChannels', ], ], ], 'GetTrainingDatasetRequest' => [ 'type' => 'structure', 'required' => [ 'trainingDatasetArn', ], 'members' => [ 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', 'location' => 'uri', 'locationName' => 'trainingDatasetArn', ], ], ], 'GetTrainingDatasetResponse' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'trainingDatasetArn', 'name', 'trainingData', 'status', 'roleArn', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], 'name' => [ 'shape' => 'NameString', ], 'trainingData' => [ 'shape' => 'DatasetList', ], 'status' => [ 'shape' => 'TrainingDatasetStatus', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'GlueDataSource' => [ 'type' => 'structure', 'required' => [ 'tableName', 'databaseName', ], 'members' => [ 'tableName' => [ 'shape' => 'GlueTableName', ], 'databaseName' => [ 'shape' => 'GlueDatabaseName', ], 'catalogId' => [ 'shape' => 'AccountId', ], ], ], 'GlueDatabaseName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_](([a-zA-Z0-9_]+-)*([a-zA-Z0-9_]+))?', ], 'GlueTableName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9_](([a-zA-Z0-9_ ]+-)*([a-zA-Z0-9_ ]+))?', ], 'Hash' => [ 'type' => 'string', 'max' => 128, 'min' => 64, 'pattern' => '[0-9a-f]+', ], 'HyperParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'HyperParametersKeyString', ], 'value' => [ 'shape' => 'HyperParametersValueString', ], 'max' => 100, 'min' => 0, ], 'HyperParametersKeyString' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '.*', ], 'HyperParametersValueString' => [ 'type' => 'string', 'max' => 2500, 'min' => 1, 'pattern' => '.*', ], 'IamRoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:iam::[0-9]{12}:role/.+', ], 'InferenceContainerConfig' => [ 'type' => 'structure', 'required' => [ 'imageUri', ], 'members' => [ 'imageUri' => [ 'shape' => 'AlgorithmImage', ], ], ], 'InferenceContainerExecutionParameters' => [ 'type' => 'structure', 'members' => [ 'maxPayloadInMB' => [ 'shape' => 'InferenceContainerExecutionParametersMaxPayloadInMBInteger', ], ], ], 'InferenceContainerExecutionParametersMaxPayloadInMBInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'InferenceEnvironmentMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'InferenceEnvironmentMapKeyString', ], 'value' => [ 'shape' => 'InferenceEnvironmentMapValueString', ], 'max' => 16, 'min' => 0, ], 'InferenceEnvironmentMapKeyString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[a-zA-Z_][a-zA-Z0-9_]*', ], 'InferenceEnvironmentMapValueString' => [ 'type' => 'string', 'max' => 10240, 'min' => 1, 'pattern' => '[\\S\\s]*', ], 'InferenceInstanceType' => [ 'type' => 'string', 'enum' => [ 'ml.r7i.48xlarge', 'ml.r6i.16xlarge', 'ml.m6i.xlarge', 'ml.m5.4xlarge', 'ml.p2.xlarge', 'ml.m4.16xlarge', 'ml.r7i.16xlarge', 'ml.m7i.xlarge', 'ml.m6i.12xlarge', 'ml.r7i.8xlarge', 'ml.r7i.large', 'ml.m7i.12xlarge', 'ml.m6i.24xlarge', 'ml.m7i.24xlarge', 'ml.r6i.8xlarge', 'ml.r6i.large', 'ml.g5.2xlarge', 'ml.m5.large', 'ml.p3.16xlarge', 'ml.m7i.48xlarge', 'ml.m6i.16xlarge', 'ml.p2.16xlarge', 'ml.g5.4xlarge', 'ml.m7i.16xlarge', 'ml.c4.2xlarge', 'ml.c5.2xlarge', 'ml.c6i.32xlarge', 'ml.c4.4xlarge', 'ml.g5.8xlarge', 'ml.c6i.xlarge', 'ml.c5.4xlarge', 'ml.g4dn.xlarge', 'ml.c7i.xlarge', 'ml.c6i.12xlarge', 'ml.g4dn.12xlarge', 'ml.c7i.12xlarge', 'ml.c6i.24xlarge', 'ml.g4dn.2xlarge', 'ml.c7i.24xlarge', 'ml.c7i.2xlarge', 'ml.c4.8xlarge', 'ml.c6i.2xlarge', 'ml.g4dn.4xlarge', 'ml.c7i.48xlarge', 'ml.c7i.4xlarge', 'ml.c6i.16xlarge', 'ml.c5.9xlarge', 'ml.g4dn.16xlarge', 'ml.c7i.16xlarge', 'ml.c6i.4xlarge', 'ml.c5.xlarge', 'ml.c4.xlarge', 'ml.g4dn.8xlarge', 'ml.c7i.8xlarge', 'ml.c7i.large', 'ml.g5.xlarge', 'ml.c6i.8xlarge', 'ml.c6i.large', 'ml.g5.12xlarge', 'ml.g5.24xlarge', 'ml.m7i.2xlarge', 'ml.c5.18xlarge', 'ml.g5.48xlarge', 'ml.m6i.2xlarge', 'ml.g5.16xlarge', 'ml.m7i.4xlarge', 'ml.p3.2xlarge', 'ml.r6i.32xlarge', 'ml.m6i.4xlarge', 'ml.m5.xlarge', 'ml.m4.10xlarge', 'ml.r6i.xlarge', 'ml.m5.12xlarge', 'ml.m4.xlarge', 'ml.r7i.2xlarge', 'ml.r7i.xlarge', 'ml.r6i.12xlarge', 'ml.m5.24xlarge', 'ml.r7i.12xlarge', 'ml.m7i.8xlarge', 'ml.m7i.large', 'ml.r6i.24xlarge', 'ml.r6i.2xlarge', 'ml.m4.2xlarge', 'ml.r7i.24xlarge', 'ml.r7i.4xlarge', 'ml.m6i.8xlarge', 'ml.m6i.large', 'ml.m5.2xlarge', 'ml.p2.8xlarge', 'ml.r6i.4xlarge', 'ml.m6i.32xlarge', 'ml.p3.8xlarge', 'ml.m4.4xlarge', ], ], 'InferenceOutputConfiguration' => [ 'type' => 'structure', 'required' => [ 'members', ], 'members' => [ 'accept' => [ 'shape' => 'InferenceOutputConfigurationAcceptString', ], 'members' => [ 'shape' => 'InferenceReceiverMembers', ], ], ], 'InferenceOutputConfigurationAcceptString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '.*', ], 'InferenceReceiverMember' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'InferenceReceiverMembers' => [ 'type' => 'list', 'member' => [ 'shape' => 'InferenceReceiverMember', ], 'max' => 1, 'min' => 1, ], 'InferenceResourceConfig' => [ 'type' => 'structure', 'required' => [ 'instanceType', ], 'members' => [ 'instanceType' => [ 'shape' => 'InferenceInstanceType', ], 'instanceCount' => [ 'shape' => 'InferenceResourceConfigInstanceCountInteger', ], ], ], 'InferenceResourceConfigInstanceCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 1, ], 'InputChannel' => [ 'type' => 'structure', 'required' => [ 'dataSource', 'roleArn', ], 'members' => [ 'dataSource' => [ 'shape' => 'InputChannelDataSource', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'InputChannelDataSource' => [ 'type' => 'structure', 'members' => [ 'protectedQueryInputParameters' => [ 'shape' => 'ProtectedQueryInputParameters', ], ], 'union' => true, ], 'InstanceType' => [ 'type' => 'string', 'enum' => [ 'ml.m4.xlarge', 'ml.m4.2xlarge', 'ml.m4.4xlarge', 'ml.m4.10xlarge', 'ml.m4.16xlarge', 'ml.g4dn.xlarge', 'ml.g4dn.2xlarge', 'ml.g4dn.4xlarge', 'ml.g4dn.8xlarge', 'ml.g4dn.12xlarge', 'ml.g4dn.16xlarge', 'ml.m5.large', 'ml.m5.xlarge', 'ml.m5.2xlarge', 'ml.m5.4xlarge', 'ml.m5.12xlarge', 'ml.m5.24xlarge', 'ml.c4.xlarge', 'ml.c4.2xlarge', 'ml.c4.4xlarge', 'ml.c4.8xlarge', 'ml.p2.xlarge', 'ml.p2.8xlarge', 'ml.p2.16xlarge', 'ml.p3.2xlarge', 'ml.p3.8xlarge', 'ml.p3.16xlarge', 'ml.p3dn.24xlarge', 'ml.p4d.24xlarge', 'ml.p4de.24xlarge', 'ml.p5.48xlarge', 'ml.c5.xlarge', 'ml.c5.2xlarge', 'ml.c5.4xlarge', 'ml.c5.9xlarge', 'ml.c5.18xlarge', 'ml.c5n.xlarge', 'ml.c5n.2xlarge', 'ml.c5n.4xlarge', 'ml.c5n.9xlarge', 'ml.c5n.18xlarge', 'ml.g5.xlarge', 'ml.g5.2xlarge', 'ml.g5.4xlarge', 'ml.g5.8xlarge', 'ml.g5.16xlarge', 'ml.g5.12xlarge', 'ml.g5.24xlarge', 'ml.g5.48xlarge', 'ml.trn1.2xlarge', 'ml.trn1.32xlarge', 'ml.trn1n.32xlarge', 'ml.m6i.large', 'ml.m6i.xlarge', 'ml.m6i.2xlarge', 'ml.m6i.4xlarge', 'ml.m6i.8xlarge', 'ml.m6i.12xlarge', 'ml.m6i.16xlarge', 'ml.m6i.24xlarge', 'ml.m6i.32xlarge', 'ml.c6i.xlarge', 'ml.c6i.2xlarge', 'ml.c6i.8xlarge', 'ml.c6i.4xlarge', 'ml.c6i.12xlarge', 'ml.c6i.16xlarge', 'ml.c6i.24xlarge', 'ml.c6i.32xlarge', 'ml.r5d.large', 'ml.r5d.xlarge', 'ml.r5d.2xlarge', 'ml.r5d.4xlarge', 'ml.r5d.8xlarge', 'ml.r5d.12xlarge', 'ml.r5d.16xlarge', 'ml.r5d.24xlarge', 'ml.t3.medium', 'ml.t3.large', 'ml.t3.xlarge', 'ml.t3.2xlarge', 'ml.r5.large', 'ml.r5.xlarge', 'ml.r5.2xlarge', 'ml.r5.4xlarge', 'ml.r5.8xlarge', 'ml.r5.12xlarge', 'ml.r5.16xlarge', 'ml.r5.24xlarge', ], ], 'KmsKeyArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:kms:[-a-z0-9]+:[0-9]{12}:key/.+', ], 'ListAudienceExportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', 'location' => 'querystring', 'locationName' => 'audienceGenerationJobArn', ], ], ], 'ListAudienceExportJobsResponse' => [ 'type' => 'structure', 'required' => [ 'audienceExportJobs', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'audienceExportJobs' => [ 'shape' => 'AudienceExportJobList', ], ], ], 'ListAudienceGenerationJobsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'querystring', 'locationName' => 'configuredAudienceModelArn', ], 'collaborationId' => [ 'shape' => 'UUID', 'location' => 'querystring', 'locationName' => 'collaborationId', ], ], ], 'ListAudienceGenerationJobsResponse' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobs', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'audienceGenerationJobs' => [ 'shape' => 'AudienceGenerationJobList', ], ], ], 'ListAudienceModelsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAudienceModelsResponse' => [ 'type' => 'structure', 'required' => [ 'audienceModels', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'audienceModels' => [ 'shape' => 'AudienceModelList', ], ], ], 'ListCollaborationConfiguredModelAlgorithmAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], ], ], 'ListCollaborationConfiguredModelAlgorithmAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'collaborationConfiguredModelAlgorithmAssociations', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'collaborationConfiguredModelAlgorithmAssociations' => [ 'shape' => 'CollaborationConfiguredModelAlgorithmAssociationList', ], ], ], 'ListCollaborationMLInputChannelsRequest' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], ], ], 'ListCollaborationMLInputChannelsResponse' => [ 'type' => 'structure', 'required' => [ 'collaborationMLInputChannelsList', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'collaborationMLInputChannelsList' => [ 'shape' => 'CollaborationMLInputChannelsList', ], ], ], 'ListCollaborationTrainedModelExportJobsRequest' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', 'trainedModelArn', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', 'location' => 'uri', 'locationName' => 'trainedModelArn', ], ], ], 'ListCollaborationTrainedModelExportJobsResponse' => [ 'type' => 'structure', 'required' => [ 'collaborationTrainedModelExportJobs', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'collaborationTrainedModelExportJobs' => [ 'shape' => 'CollaborationTrainedModelExportJobList', ], ], ], 'ListCollaborationTrainedModelInferenceJobsRequest' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', 'location' => 'querystring', 'locationName' => 'trainedModelArn', ], ], ], 'ListCollaborationTrainedModelInferenceJobsResponse' => [ 'type' => 'structure', 'required' => [ 'collaborationTrainedModelInferenceJobs', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'collaborationTrainedModelInferenceJobs' => [ 'shape' => 'CollaborationTrainedModelInferenceJobList', ], ], ], 'ListCollaborationTrainedModelsRequest' => [ 'type' => 'structure', 'required' => [ 'collaborationIdentifier', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'collaborationIdentifier', ], ], ], 'ListCollaborationTrainedModelsResponse' => [ 'type' => 'structure', 'required' => [ 'collaborationTrainedModels', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'collaborationTrainedModels' => [ 'shape' => 'CollaborationTrainedModelList', ], ], ], 'ListConfiguredAudienceModelsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListConfiguredAudienceModelsResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModels', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'configuredAudienceModels' => [ 'shape' => 'ConfiguredAudienceModelList', ], ], ], 'ListConfiguredModelAlgorithmAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'ListConfiguredModelAlgorithmAssociationsResponse' => [ 'type' => 'structure', 'required' => [ 'configuredModelAlgorithmAssociations', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'configuredModelAlgorithmAssociations' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationList', ], ], ], 'ListConfiguredModelAlgorithmsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListConfiguredModelAlgorithmsResponse' => [ 'type' => 'structure', 'required' => [ 'configuredModelAlgorithms', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'configuredModelAlgorithms' => [ 'shape' => 'ConfiguredModelAlgorithmList', ], ], ], 'ListMLInputChannelsRequest' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'ListMLInputChannelsResponse' => [ 'type' => 'structure', 'required' => [ 'mlInputChannelsList', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'mlInputChannelsList' => [ 'shape' => 'MLInputChannelsList', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTrainedModelInferenceJobsRequest' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', 'location' => 'querystring', 'locationName' => 'trainedModelArn', ], ], ], 'ListTrainedModelInferenceJobsResponse' => [ 'type' => 'structure', 'required' => [ 'trainedModelInferenceJobs', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'trainedModelInferenceJobs' => [ 'shape' => 'TrainedModelInferenceJobList', ], ], ], 'ListTrainedModelsRequest' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], ], ], 'ListTrainedModelsResponse' => [ 'type' => 'structure', 'required' => [ 'trainedModels', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'trainedModels' => [ 'shape' => 'TrainedModelList', ], ], ], 'ListTrainingDatasetsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListTrainingDatasetsResponse' => [ 'type' => 'structure', 'required' => [ 'trainingDatasets', ], 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'trainingDatasets' => [ 'shape' => 'TrainingDatasetList', ], ], ], 'LogsConfigurationPolicy' => [ 'type' => 'structure', 'required' => [ 'allowedAccountIds', ], 'members' => [ 'allowedAccountIds' => [ 'shape' => 'AccountIdList', ], 'filterPattern' => [ 'shape' => 'LogsConfigurationPolicyFilterPatternString', ], ], ], 'LogsConfigurationPolicyFilterPatternString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'LogsConfigurationPolicyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogsConfigurationPolicy', ], 'max' => 5, 'min' => 1, ], 'LogsStatus' => [ 'type' => 'string', 'enum' => [ 'PUBLISH_SUCCEEDED', 'PUBLISH_FAILED', ], ], 'MLInputChannelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:membership/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/ml-input-channel/[-a-zA-Z0-9_/.]+', ], 'MLInputChannelStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_PENDING', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'INACTIVE', ], ], 'MLInputChannelSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'membershipIdentifier', 'collaborationIdentifier', 'name', 'configuredModelAlgorithmAssociations', 'mlInputChannelArn', 'status', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], 'name' => [ 'shape' => 'NameString', ], 'configuredModelAlgorithmAssociations' => [ 'shape' => 'MLInputChannelSummaryConfiguredModelAlgorithmAssociationsList', ], 'protectedQueryIdentifier' => [ 'shape' => 'UUID', ], 'mlInputChannelArn' => [ 'shape' => 'MLInputChannelArn', ], 'status' => [ 'shape' => 'MLInputChannelStatus', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'MLInputChannelSummaryConfiguredModelAlgorithmAssociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'max' => 1, 'min' => 1, ], 'MLInputChannelsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MLInputChannelSummary', ], ], 'MLOutputConfiguration' => [ 'type' => 'structure', 'required' => [ 'roleArn', ], 'members' => [ 'destination' => [ 'shape' => 'Destination', ], 'roleArn' => [ 'shape' => 'IamRoleArn', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MetricDefinition' => [ 'type' => 'structure', 'required' => [ 'name', 'regex', ], 'members' => [ 'name' => [ 'shape' => 'MetricName', ], 'regex' => [ 'shape' => 'MetricRegex', ], ], ], 'MetricDefinitionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricDefinition', ], 'max' => 40, 'min' => 0, ], 'MetricName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '.+', ], 'MetricRegex' => [ 'type' => 'string', 'max' => 500, 'min' => 1, 'pattern' => '.+', ], 'MetricsConfigurationPolicy' => [ 'type' => 'structure', 'required' => [ 'noiseLevel', ], 'members' => [ 'noiseLevel' => [ 'shape' => 'NoiseLevelType', ], ], ], 'MetricsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SharedAudienceMetrics', ], 'max' => 1, 'min' => 1, ], 'MetricsStatus' => [ 'type' => 'string', 'enum' => [ 'PUBLISH_SUCCEEDED', 'PUBLISH_FAILED', ], ], 'MinMatchingSeedSize' => [ 'type' => 'integer', 'box' => true, 'max' => 500000, 'min' => 25, ], 'ModelInferenceDataSource' => [ 'type' => 'structure', 'required' => [ 'mlInputChannelArn', ], 'members' => [ 'mlInputChannelArn' => [ 'shape' => 'MLInputChannelArn', ], ], ], 'ModelTrainingDataChannel' => [ 'type' => 'structure', 'required' => [ 'mlInputChannelArn', 'channelName', ], 'members' => [ 'mlInputChannelArn' => [ 'shape' => 'MLInputChannelArn', ], 'channelName' => [ 'shape' => 'ModelTrainingDataChannelName', ], ], ], 'ModelTrainingDataChannelName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[A-Za-z0-9\\.\\-_]+', ], 'ModelTrainingDataChannels' => [ 'type' => 'list', 'member' => [ 'shape' => 'ModelTrainingDataChannel', ], 'max' => 20, 'min' => 1, ], 'NameString' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '(?!\\s*$)[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t]*', ], 'NextToken' => [ 'type' => 'string', 'max' => 10240, 'min' => 1, ], 'NoiseLevelType' => [ 'type' => 'string', 'enum' => [ 'HIGH', 'MEDIUM', 'LOW', 'NONE', ], ], 'ParameterKey' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[0-9a-zA-Z_]+', ], 'ParameterMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'ParameterKey', ], 'value' => [ 'shape' => 'ParameterValue', ], ], 'ParameterValue' => [ 'type' => 'string', 'max' => 250, 'min' => 0, ], 'PolicyExistenceCondition' => [ 'type' => 'string', 'enum' => [ 'POLICY_MUST_EXIST', 'POLICY_MUST_NOT_EXIST', ], ], 'PrivacyConfiguration' => [ 'type' => 'structure', 'required' => [ 'policies', ], 'members' => [ 'policies' => [ 'shape' => 'PrivacyConfigurationPolicies', ], ], ], 'PrivacyConfigurationPolicies' => [ 'type' => 'structure', 'members' => [ 'trainedModels' => [ 'shape' => 'TrainedModelsConfigurationPolicy', ], 'trainedModelExports' => [ 'shape' => 'TrainedModelExportsConfigurationPolicy', ], 'trainedModelInferenceJobs' => [ 'shape' => 'TrainedModelInferenceJobsConfigurationPolicy', ], ], ], 'ProtectedQueryInputParameters' => [ 'type' => 'structure', 'required' => [ 'sqlParameters', ], 'members' => [ 'sqlParameters' => [ 'shape' => 'ProtectedQuerySQLParameters', ], 'computeConfiguration' => [ 'shape' => 'ComputeConfiguration', ], ], ], 'ProtectedQuerySQLParameters' => [ 'type' => 'structure', 'members' => [ 'queryString' => [ 'shape' => 'ProtectedQuerySQLParametersQueryStringString', ], 'analysisTemplateArn' => [ 'shape' => 'AnalysisTemplateArn', ], 'parameters' => [ 'shape' => 'ParameterMap', ], ], 'sensitive' => true, ], 'ProtectedQuerySQLParametersQueryStringString' => [ 'type' => 'string', 'max' => 500000, 'min' => 0, ], 'PutConfiguredAudienceModelPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', 'configuredAudienceModelPolicy', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], 'configuredAudienceModelPolicy' => [ 'shape' => 'ResourcePolicy', ], 'previousPolicyHash' => [ 'shape' => 'Hash', ], 'policyExistenceCondition' => [ 'shape' => 'PolicyExistenceCondition', ], ], ], 'PutConfiguredAudienceModelPolicyResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelPolicy', 'policyHash', ], 'members' => [ 'configuredAudienceModelPolicy' => [ 'shape' => 'ResourcePolicy', ], 'policyHash' => [ 'shape' => 'Hash', ], ], ], 'PutMLConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'defaultOutputLocation', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'defaultOutputLocation' => [ 'shape' => 'MLOutputConfiguration', ], ], ], 'RelevanceMetric' => [ 'type' => 'structure', 'required' => [ 'audienceSize', ], 'members' => [ 'audienceSize' => [ 'shape' => 'AudienceSize', ], 'score' => [ 'shape' => 'RelevanceMetricScoreDouble', ], ], ], 'RelevanceMetricScoreDouble' => [ 'type' => 'double', 'box' => true, 'max' => 10.0, 'min' => 0.0, ], 'RelevanceMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'RelevanceMetric', ], ], 'ResourceConfig' => [ 'type' => 'structure', 'required' => [ 'instanceType', 'volumeSizeInGB', ], 'members' => [ 'instanceCount' => [ 'shape' => 'ResourceConfigInstanceCountInteger', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'volumeSizeInGB' => [ 'shape' => 'ResourceConfigVolumeSizeInGBInteger', ], ], ], 'ResourceConfigInstanceCountInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 1, 'min' => 1, ], 'ResourceConfigVolumeSizeInGBInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 8192, 'min' => 1, ], 'ResourceDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t\\r\\n]*', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourcePolicy' => [ 'type' => 'string', 'max' => 20480, 'min' => 1, ], 'S3ConfigMap' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3Uri' => [ 'shape' => 'S3Path', ], ], ], 'S3Path' => [ 'type' => 'string', 'max' => 1285, 'min' => 1, 'pattern' => 's3://.+', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SharedAudienceMetrics' => [ 'type' => 'string', 'enum' => [ 'ALL', 'NONE', ], ], 'StartAudienceExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'audienceGenerationJobArn', 'audienceSize', ], 'members' => [ 'name' => [ 'shape' => 'NameString', ], 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], 'audienceSize' => [ 'shape' => 'AudienceSize', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'StartAudienceGenerationJobRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'configuredAudienceModelArn', 'seedAudience', ], 'members' => [ 'name' => [ 'shape' => 'NameString', ], 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], 'seedAudience' => [ 'shape' => 'AudienceGenerationJobDataSource', ], 'includeSeedInOutput' => [ 'shape' => 'Boolean', ], 'collaborationId' => [ 'shape' => 'UUID', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'StartAudienceGenerationJobResponse' => [ 'type' => 'structure', 'required' => [ 'audienceGenerationJobArn', ], 'members' => [ 'audienceGenerationJobArn' => [ 'shape' => 'AudienceGenerationJobArn', ], ], ], 'StartTrainedModelExportJobRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'trainedModelArn', 'membershipIdentifier', 'outputConfiguration', ], 'members' => [ 'name' => [ 'shape' => 'NameString', ], 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', 'location' => 'uri', 'locationName' => 'trainedModelArn', ], 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'outputConfiguration' => [ 'shape' => 'TrainedModelExportOutputConfiguration', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'StartTrainedModelInferenceJobRequest' => [ 'type' => 'structure', 'required' => [ 'membershipIdentifier', 'name', 'trainedModelArn', 'resourceConfig', 'outputConfiguration', 'dataSource', ], 'members' => [ 'membershipIdentifier' => [ 'shape' => 'UUID', 'location' => 'uri', 'locationName' => 'membershipIdentifier', ], 'name' => [ 'shape' => 'NameString', ], 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', ], 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'resourceConfig' => [ 'shape' => 'InferenceResourceConfig', ], 'outputConfiguration' => [ 'shape' => 'InferenceOutputConfiguration', ], 'dataSource' => [ 'shape' => 'ModelInferenceDataSource', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'containerExecutionParameters' => [ 'shape' => 'InferenceContainerExecutionParameters', ], 'environment' => [ 'shape' => 'InferenceEnvironmentMap', ], 'kmsKeyArn' => [ 'shape' => 'KmsKeyArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'StartTrainedModelInferenceJobResponse' => [ 'type' => 'structure', 'required' => [ 'trainedModelInferenceJobArn', ], 'members' => [ 'trainedModelInferenceJobArn' => [ 'shape' => 'TrainedModelInferenceJobArn', ], ], ], 'StatusDetails' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'StoppingCondition' => [ 'type' => 'structure', 'members' => [ 'maxRuntimeInSeconds' => [ 'shape' => 'StoppingConditionMaxRuntimeInSecondsInteger', ], ], ], 'StoppingConditionMaxRuntimeInSecondsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 2419200, 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagOnCreatePolicy' => [ 'type' => 'string', 'enum' => [ 'FROM_PARENT_RESOURCE', 'NONE', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TaggableArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:(membership/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/(configured-model-algorithm-association|trained-model|trained-model-inference-job|ml-input-channel)|training-dataset|audience-model|configured-audience-model|audience-generation-job|configured-model-algorithm|configured-model-algorithm-association|trained-model|trained-model-inference-job)/[-a-zA-Z0-9_/.]+', ], 'TrainedModelArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:(membership/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/)?trained-model/[-a-zA-Z0-9_/.]+', ], 'TrainedModelExportFileType' => [ 'type' => 'string', 'enum' => [ 'MODEL', 'OUTPUT', ], ], 'TrainedModelExportFileTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrainedModelExportFileType', ], 'max' => 2, 'min' => 1, ], 'TrainedModelExportJobStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', ], ], 'TrainedModelExportOutputConfiguration' => [ 'type' => 'structure', 'required' => [ 'members', ], 'members' => [ 'members' => [ 'shape' => 'TrainedModelExportReceiverMembers', ], ], ], 'TrainedModelExportReceiverMember' => [ 'type' => 'structure', 'required' => [ 'accountId', ], 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], ], ], 'TrainedModelExportReceiverMembers' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrainedModelExportReceiverMember', ], 'max' => 1, 'min' => 1, ], 'TrainedModelExportsConfigurationPolicy' => [ 'type' => 'structure', 'required' => [ 'maxSize', 'filesToExport', ], 'members' => [ 'maxSize' => [ 'shape' => 'TrainedModelExportsMaxSize', ], 'filesToExport' => [ 'shape' => 'TrainedModelExportFileTypeList', ], ], ], 'TrainedModelExportsMaxSize' => [ 'type' => 'structure', 'required' => [ 'unit', 'value', ], 'members' => [ 'unit' => [ 'shape' => 'TrainedModelExportsMaxSizeUnitType', ], 'value' => [ 'shape' => 'TrainedModelExportsMaxSizeValue', ], ], ], 'TrainedModelExportsMaxSizeUnitType' => [ 'type' => 'string', 'enum' => [ 'GB', ], ], 'TrainedModelExportsMaxSizeValue' => [ 'type' => 'double', 'box' => true, 'max' => 10.0, 'min' => 0.01, ], 'TrainedModelInferenceJobArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:(membership/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/)?trained-model-inference-job/[-a-zA-Z0-9_/.]+', ], 'TrainedModelInferenceJobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrainedModelInferenceJobSummary', ], ], 'TrainedModelInferenceJobStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'CANCEL_PENDING', 'CANCEL_IN_PROGRESS', 'CANCEL_FAILED', 'INACTIVE', ], ], 'TrainedModelInferenceJobSummary' => [ 'type' => 'structure', 'required' => [ 'trainedModelInferenceJobArn', 'membershipIdentifier', 'trainedModelArn', 'collaborationIdentifier', 'status', 'outputConfiguration', 'name', 'createTime', 'updateTime', ], 'members' => [ 'trainedModelInferenceJobArn' => [ 'shape' => 'TrainedModelInferenceJobArn', ], 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], 'status' => [ 'shape' => 'TrainedModelInferenceJobStatus', ], 'outputConfiguration' => [ 'shape' => 'InferenceOutputConfiguration', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'metricsStatus' => [ 'shape' => 'MetricsStatus', ], 'metricsStatusDetails' => [ 'shape' => 'String', ], 'logsStatus' => [ 'shape' => 'LogsStatus', ], 'logsStatusDetails' => [ 'shape' => 'String', ], 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'TrainedModelInferenceJobsConfigurationPolicy' => [ 'type' => 'structure', 'members' => [ 'containerLogs' => [ 'shape' => 'LogsConfigurationPolicyList', ], 'maxOutputSize' => [ 'shape' => 'TrainedModelInferenceMaxOutputSize', ], ], ], 'TrainedModelInferenceMaxOutputSize' => [ 'type' => 'structure', 'required' => [ 'unit', 'value', ], 'members' => [ 'unit' => [ 'shape' => 'TrainedModelInferenceMaxOutputSizeUnitType', ], 'value' => [ 'shape' => 'TrainedModelInferenceMaxOutputSizeValue', ], ], ], 'TrainedModelInferenceMaxOutputSizeUnitType' => [ 'type' => 'string', 'enum' => [ 'GB', ], ], 'TrainedModelInferenceMaxOutputSizeValue' => [ 'type' => 'double', 'box' => true, 'max' => 10.0, 'min' => 0.01, ], 'TrainedModelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrainedModelSummary', ], ], 'TrainedModelStatus' => [ 'type' => 'string', 'enum' => [ 'CREATE_PENDING', 'CREATE_IN_PROGRESS', 'CREATE_FAILED', 'ACTIVE', 'DELETE_PENDING', 'DELETE_IN_PROGRESS', 'DELETE_FAILED', 'INACTIVE', 'CANCEL_PENDING', 'CANCEL_IN_PROGRESS', 'CANCEL_FAILED', ], ], 'TrainedModelSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'trainedModelArn', 'name', 'membershipIdentifier', 'collaborationIdentifier', 'status', 'configuredModelAlgorithmAssociationArn', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainedModelArn' => [ 'shape' => 'TrainedModelArn', ], 'name' => [ 'shape' => 'NameString', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'membershipIdentifier' => [ 'shape' => 'UUID', ], 'collaborationIdentifier' => [ 'shape' => 'UUID', ], 'status' => [ 'shape' => 'TrainedModelStatus', ], 'configuredModelAlgorithmAssociationArn' => [ 'shape' => 'ConfiguredModelAlgorithmAssociationArn', ], ], ], 'TrainedModelsConfigurationPolicy' => [ 'type' => 'structure', 'members' => [ 'containerLogs' => [ 'shape' => 'LogsConfigurationPolicyList', ], 'containerMetrics' => [ 'shape' => 'MetricsConfigurationPolicy', ], ], ], 'TrainingDatasetArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:aws[-a-z]*:cleanrooms-ml:[-a-z0-9]+:[0-9]{12}:training-dataset/[-a-zA-Z0-9_/.]+', ], 'TrainingDatasetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TrainingDatasetSummary', ], ], 'TrainingDatasetStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', ], ], 'TrainingDatasetSummary' => [ 'type' => 'structure', 'required' => [ 'createTime', 'updateTime', 'trainingDatasetArn', 'name', 'status', ], 'members' => [ 'createTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'updateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'trainingDatasetArn' => [ 'shape' => 'TrainingDatasetArn', ], 'name' => [ 'shape' => 'NameString', ], 'status' => [ 'shape' => 'TrainingDatasetStatus', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'UUID' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'TaggableArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateConfiguredAudienceModelRequest' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', 'location' => 'uri', 'locationName' => 'configuredAudienceModelArn', ], 'outputConfig' => [ 'shape' => 'ConfiguredAudienceModelOutputConfig', ], 'audienceModelArn' => [ 'shape' => 'AudienceModelArn', ], 'sharedAudienceMetrics' => [ 'shape' => 'MetricsList', ], 'minMatchingSeedSize' => [ 'shape' => 'MinMatchingSeedSize', ], 'audienceSizeConfig' => [ 'shape' => 'AudienceSizeConfig', ], 'description' => [ 'shape' => 'ResourceDescription', ], ], ], 'UpdateConfiguredAudienceModelResponse' => [ 'type' => 'structure', 'required' => [ 'configuredAudienceModelArn', ], 'members' => [ 'configuredAudienceModelArn' => [ 'shape' => 'ConfiguredAudienceModelArn', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'WorkerComputeConfiguration' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'WorkerComputeType', ], 'number' => [ 'shape' => 'WorkerComputeConfigurationNumberInteger', ], ], ], 'WorkerComputeConfigurationNumberInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 400, 'min' => 2, ], 'WorkerComputeType' => [ 'type' => 'string', 'enum' => [ 'CR.1X', 'CR.4X', ], ], ],];
