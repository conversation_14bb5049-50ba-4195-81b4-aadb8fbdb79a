<?php
// This file was auto-generated from sdk-root/src/data/dsql/2018-05-10/waiters-2.json
return [ 'version' => 2, 'waiters' => [ 'ClusterActive' => [ 'description' => 'Wait until a Cluster is ACTIVE', 'delay' => 2, 'maxAttempts' => 60, 'operation' => 'GetCluster', 'acceptors' => [ [ 'matcher' => 'path', 'argument' => 'status', 'state' => 'success', 'expected' => 'ACTIVE', ], ], ], 'ClusterNotExists' => [ 'description' => 'Wait until a Cluster is gone', 'delay' => 2, 'maxAttempts' => 60, 'operation' => 'GetCluster', 'acceptors' => [ [ 'matcher' => 'error', 'state' => 'success', 'expected' => 'ResourceNotFoundException', ], ], ], ],];
