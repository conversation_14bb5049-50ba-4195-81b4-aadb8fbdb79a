<?php
// This file was auto-generated from sdk-root/src/data/cognito-idp/2016-04-18/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2016-04-18', 'endpointPrefix' => 'cognito-idp', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'Amazon Cognito Identity Provider', 'serviceId' => 'Cognito Identity Provider', 'signatureVersion' => 'v4', 'targetPrefix' => 'AWSCognitoIdentityProviderService', 'uid' => 'cognito-idp-2016-04-18', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AddCustomAttributes' => [ 'name' => 'AddCustomAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddCustomAttributesRequest', ], 'output' => [ 'shape' => 'AddCustomAttributesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserImportInProgressException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminAddUserToGroup' => [ 'name' => 'AdminAddUserToGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminAddUserToGroupRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminConfirmSignUp' => [ 'name' => 'AdminConfirmSignUp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminConfirmSignUpRequest', ], 'output' => [ 'shape' => 'AdminConfirmSignUpResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyFailedAttemptsException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminCreateUser' => [ 'name' => 'AdminCreateUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminCreateUserRequest', ], 'output' => [ 'shape' => 'AdminCreateUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UsernameExistsException', ], [ 'shape' => 'InvalidPasswordException', ], [ 'shape' => 'CodeDeliveryFailureException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UnsupportedUserStateException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminDeleteUser' => [ 'name' => 'AdminDeleteUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminDeleteUserRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminDeleteUserAttributes' => [ 'name' => 'AdminDeleteUserAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminDeleteUserAttributesRequest', ], 'output' => [ 'shape' => 'AdminDeleteUserAttributesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminDisableProviderForUser' => [ 'name' => 'AdminDisableProviderForUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminDisableProviderForUserRequest', ], 'output' => [ 'shape' => 'AdminDisableProviderForUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'AliasExistsException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminDisableUser' => [ 'name' => 'AdminDisableUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminDisableUserRequest', ], 'output' => [ 'shape' => 'AdminDisableUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminEnableUser' => [ 'name' => 'AdminEnableUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminEnableUserRequest', ], 'output' => [ 'shape' => 'AdminEnableUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminForgetDevice' => [ 'name' => 'AdminForgetDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminForgetDeviceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidUserPoolConfigurationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminGetDevice' => [ 'name' => 'AdminGetDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminGetDeviceRequest', ], 'output' => [ 'shape' => 'AdminGetDeviceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidUserPoolConfigurationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'AdminGetUser' => [ 'name' => 'AdminGetUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminGetUserRequest', ], 'output' => [ 'shape' => 'AdminGetUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminInitiateAuth' => [ 'name' => 'AdminInitiateAuth', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminInitiateAuthRequest', ], 'output' => [ 'shape' => 'AdminInitiateAuthResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'InvalidUserPoolConfigurationException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'MFAMethodNotFoundException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidEmailRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], ], ], 'AdminLinkProviderForUser' => [ 'name' => 'AdminLinkProviderForUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminLinkProviderForUserRequest', ], 'output' => [ 'shape' => 'AdminLinkProviderForUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'AliasExistsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminListDevices' => [ 'name' => 'AdminListDevices', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminListDevicesRequest', ], 'output' => [ 'shape' => 'AdminListDevicesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidUserPoolConfigurationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'AdminListGroupsForUser' => [ 'name' => 'AdminListGroupsForUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminListGroupsForUserRequest', ], 'output' => [ 'shape' => 'AdminListGroupsForUserResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminListUserAuthEvents' => [ 'name' => 'AdminListUserAuthEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminListUserAuthEventsRequest', ], 'output' => [ 'shape' => 'AdminListUserAuthEventsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserPoolAddOnNotEnabledException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminRemoveUserFromGroup' => [ 'name' => 'AdminRemoveUserFromGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminRemoveUserFromGroupRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminResetUserPassword' => [ 'name' => 'AdminResetUserPassword', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminResetUserPasswordRequest', ], 'output' => [ 'shape' => 'AdminResetUserPasswordResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidEmailRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminRespondToAuthChallenge' => [ 'name' => 'AdminRespondToAuthChallenge', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminRespondToAuthChallengeRequest', ], 'output' => [ 'shape' => 'AdminRespondToAuthChallengeResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'CodeMismatchException', ], [ 'shape' => 'ExpiredCodeException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'InvalidPasswordException', ], [ 'shape' => 'PasswordHistoryPolicyViolationException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidUserPoolConfigurationException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'MFAMethodNotFoundException', ], [ 'shape' => 'InvalidEmailRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], [ 'shape' => 'AliasExistsException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'SoftwareTokenMFANotFoundException', ], ], ], 'AdminSetUserMFAPreference' => [ 'name' => 'AdminSetUserMFAPreference', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminSetUserMFAPreferenceRequest', ], 'output' => [ 'shape' => 'AdminSetUserMFAPreferenceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminSetUserPassword' => [ 'name' => 'AdminSetUserPassword', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminSetUserPasswordRequest', ], 'output' => [ 'shape' => 'AdminSetUserPasswordResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidPasswordException', ], [ 'shape' => 'PasswordHistoryPolicyViolationException', ], ], ], 'AdminSetUserSettings' => [ 'name' => 'AdminSetUserSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminSetUserSettingsRequest', ], 'output' => [ 'shape' => 'AdminSetUserSettingsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminUpdateAuthEventFeedback' => [ 'name' => 'AdminUpdateAuthEventFeedback', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminUpdateAuthEventFeedbackRequest', ], 'output' => [ 'shape' => 'AdminUpdateAuthEventFeedbackResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserPoolAddOnNotEnabledException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminUpdateDeviceStatus' => [ 'name' => 'AdminUpdateDeviceStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminUpdateDeviceStatusRequest', ], 'output' => [ 'shape' => 'AdminUpdateDeviceStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidUserPoolConfigurationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AdminUpdateUserAttributes' => [ 'name' => 'AdminUpdateUserAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminUpdateUserAttributesRequest', ], 'output' => [ 'shape' => 'AdminUpdateUserAttributesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'AliasExistsException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidEmailRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], ], ], 'AdminUserGlobalSignOut' => [ 'name' => 'AdminUserGlobalSignOut', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AdminUserGlobalSignOutRequest', ], 'output' => [ 'shape' => 'AdminUserGlobalSignOutResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'AssociateSoftwareToken' => [ 'name' => 'AssociateSoftwareToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AssociateSoftwareTokenRequest', ], 'output' => [ 'shape' => 'AssociateSoftwareTokenResponse', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'SoftwareTokenMFANotFoundException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'ChangePassword' => [ 'name' => 'ChangePassword', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ChangePasswordRequest', ], 'output' => [ 'shape' => 'ChangePasswordResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidPasswordException', ], [ 'shape' => 'PasswordHistoryPolicyViolationException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'CompleteWebAuthnRegistration' => [ 'name' => 'CompleteWebAuthnRegistration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CompleteWebAuthnRegistrationRequest', ], 'output' => [ 'shape' => 'CompleteWebAuthnRegistrationResponse', ], 'errors' => [ [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'WebAuthnNotEnabledException', ], [ 'shape' => 'WebAuthnChallengeNotFoundException', ], [ 'shape' => 'WebAuthnRelyingPartyMismatchException', ], [ 'shape' => 'WebAuthnClientMismatchException', ], [ 'shape' => 'WebAuthnOriginNotAllowedException', ], [ 'shape' => 'WebAuthnCredentialNotSupportedException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'ConfirmDevice' => [ 'name' => 'ConfirmDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ConfirmDeviceRequest', ], 'output' => [ 'shape' => 'ConfirmDeviceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InvalidPasswordException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'UsernameExistsException', ], [ 'shape' => 'InvalidUserPoolConfigurationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'DeviceKeyExistsException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'ConfirmForgotPassword' => [ 'name' => 'ConfirmForgotPassword', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ConfirmForgotPasswordRequest', ], 'output' => [ 'shape' => 'ConfirmForgotPasswordResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidPasswordException', ], [ 'shape' => 'PasswordHistoryPolicyViolationException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'CodeMismatchException', ], [ 'shape' => 'ExpiredCodeException', ], [ 'shape' => 'TooManyFailedAttemptsException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'ConfirmSignUp' => [ 'name' => 'ConfirmSignUp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ConfirmSignUpRequest', ], 'output' => [ 'shape' => 'ConfirmSignUpResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyFailedAttemptsException', ], [ 'shape' => 'CodeMismatchException', ], [ 'shape' => 'ExpiredCodeException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'AliasExistsException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'CreateGroup' => [ 'name' => 'CreateGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGroupRequest', ], 'output' => [ 'shape' => 'CreateGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'GroupExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'CreateIdentityProvider' => [ 'name' => 'CreateIdentityProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateIdentityProviderRequest', ], 'output' => [ 'shape' => 'CreateIdentityProviderResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DuplicateProviderException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'CreateManagedLoginBranding' => [ 'name' => 'CreateManagedLoginBranding', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateManagedLoginBrandingRequest', ], 'output' => [ 'shape' => 'CreateManagedLoginBrandingResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ManagedLoginBrandingExistsException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'CreateResourceServer' => [ 'name' => 'CreateResourceServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateResourceServerRequest', ], 'output' => [ 'shape' => 'CreateResourceServerResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'CreateUserImportJob' => [ 'name' => 'CreateUserImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserImportJobRequest', ], 'output' => [ 'shape' => 'CreateUserImportJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'CreateUserPool' => [ 'name' => 'CreateUserPool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserPoolRequest', ], 'output' => [ 'shape' => 'CreateUserPoolResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], [ 'shape' => 'InvalidEmailRoleAccessPolicyException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserPoolTaggingException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'TierChangeNotAllowedException', ], [ 'shape' => 'FeatureUnavailableInTierException', ], ], ], 'CreateUserPoolClient' => [ 'name' => 'CreateUserPoolClient', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserPoolClientRequest', ], 'output' => [ 'shape' => 'CreateUserPoolClientResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'ScopeDoesNotExistException', ], [ 'shape' => 'InvalidOAuthFlowException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'FeatureUnavailableInTierException', ], ], ], 'CreateUserPoolDomain' => [ 'name' => 'CreateUserPoolDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateUserPoolDomainRequest', ], 'output' => [ 'shape' => 'CreateUserPoolDomainResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'FeatureUnavailableInTierException', ], ], ], 'DeleteGroup' => [ 'name' => 'DeleteGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteGroupRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DeleteIdentityProvider' => [ 'name' => 'DeleteIdentityProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteIdentityProviderRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedIdentityProviderException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DeleteManagedLoginBranding' => [ 'name' => 'DeleteManagedLoginBranding', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteManagedLoginBrandingRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DeleteResourceServer' => [ 'name' => 'DeleteResourceServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourceServerRequest', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DeleteUser' => [ 'name' => 'DeleteUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'DeleteUserAttributes' => [ 'name' => 'DeleteUserAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserAttributesRequest', ], 'output' => [ 'shape' => 'DeleteUserAttributesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'DeleteUserPool' => [ 'name' => 'DeleteUserPool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserPoolRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserImportInProgressException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DeleteUserPoolClient' => [ 'name' => 'DeleteUserPoolClient', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserPoolClientRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DeleteUserPoolDomain' => [ 'name' => 'DeleteUserPoolDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteUserPoolDomainRequest', ], 'output' => [ 'shape' => 'DeleteUserPoolDomainResponse', ], 'errors' => [ [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DeleteWebAuthnCredential' => [ 'name' => 'DeleteWebAuthnCredential', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWebAuthnCredentialRequest', ], 'output' => [ 'shape' => 'DeleteWebAuthnCredentialResponse', ], 'errors' => [ [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'DescribeIdentityProvider' => [ 'name' => 'DescribeIdentityProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeIdentityProviderRequest', ], 'output' => [ 'shape' => 'DescribeIdentityProviderResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DescribeManagedLoginBranding' => [ 'name' => 'DescribeManagedLoginBranding', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeManagedLoginBrandingRequest', ], 'output' => [ 'shape' => 'DescribeManagedLoginBrandingResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DescribeManagedLoginBrandingByClient' => [ 'name' => 'DescribeManagedLoginBrandingByClient', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeManagedLoginBrandingByClientRequest', ], 'output' => [ 'shape' => 'DescribeManagedLoginBrandingByClientResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DescribeResourceServer' => [ 'name' => 'DescribeResourceServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeResourceServerRequest', ], 'output' => [ 'shape' => 'DescribeResourceServerResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DescribeRiskConfiguration' => [ 'name' => 'DescribeRiskConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRiskConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeRiskConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserPoolAddOnNotEnabledException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DescribeUserImportJob' => [ 'name' => 'DescribeUserImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUserImportJobRequest', ], 'output' => [ 'shape' => 'DescribeUserImportJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DescribeUserPool' => [ 'name' => 'DescribeUserPool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUserPoolRequest', ], 'output' => [ 'shape' => 'DescribeUserPoolResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserPoolTaggingException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DescribeUserPoolClient' => [ 'name' => 'DescribeUserPoolClient', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUserPoolClientRequest', ], 'output' => [ 'shape' => 'DescribeUserPoolClientResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'DescribeUserPoolDomain' => [ 'name' => 'DescribeUserPoolDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeUserPoolDomainRequest', ], 'output' => [ 'shape' => 'DescribeUserPoolDomainResponse', ], 'errors' => [ [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ForgetDevice' => [ 'name' => 'ForgetDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ForgetDeviceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidUserPoolConfigurationException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'ForgotPassword' => [ 'name' => 'ForgotPassword', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ForgotPasswordRequest', ], 'output' => [ 'shape' => 'ForgotPasswordResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], [ 'shape' => 'InvalidEmailRoleAccessPolicyException', ], [ 'shape' => 'CodeDeliveryFailureException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'GetCSVHeader' => [ 'name' => 'GetCSVHeader', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCSVHeaderRequest', ], 'output' => [ 'shape' => 'GetCSVHeaderResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'GetDevice' => [ 'name' => 'GetDevice', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDeviceRequest', ], 'output' => [ 'shape' => 'GetDeviceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidUserPoolConfigurationException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'GetGroup' => [ 'name' => 'GetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetGroupRequest', ], 'output' => [ 'shape' => 'GetGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'GetIdentityProviderByIdentifier' => [ 'name' => 'GetIdentityProviderByIdentifier', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetIdentityProviderByIdentifierRequest', ], 'output' => [ 'shape' => 'GetIdentityProviderByIdentifierResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'GetLogDeliveryConfiguration' => [ 'name' => 'GetLogDeliveryConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLogDeliveryConfigurationRequest', ], 'output' => [ 'shape' => 'GetLogDeliveryConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetSigningCertificate' => [ 'name' => 'GetSigningCertificate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetSigningCertificateRequest', ], 'output' => [ 'shape' => 'GetSigningCertificateResponse', ], 'errors' => [ [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetTokensFromRefreshToken' => [ 'name' => 'GetTokensFromRefreshToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTokensFromRefreshTokenRequest', ], 'output' => [ 'shape' => 'GetTokensFromRefreshTokenResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'RefreshTokenReuseException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'GetUICustomization' => [ 'name' => 'GetUICustomization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUICustomizationRequest', ], 'output' => [ 'shape' => 'GetUICustomizationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'GetUser' => [ 'name' => 'GetUser', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUserRequest', ], 'output' => [ 'shape' => 'GetUserResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'GetUserAttributeVerificationCode' => [ 'name' => 'GetUserAttributeVerificationCode', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUserAttributeVerificationCodeRequest', ], 'output' => [ 'shape' => 'GetUserAttributeVerificationCodeResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], [ 'shape' => 'InvalidEmailRoleAccessPolicyException', ], [ 'shape' => 'CodeDeliveryFailureException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'GetUserAuthFactors' => [ 'name' => 'GetUserAuthFactors', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUserAuthFactorsRequest', ], 'output' => [ 'shape' => 'GetUserAuthFactorsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'GetUserPoolMfaConfig' => [ 'name' => 'GetUserPoolMfaConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetUserPoolMfaConfigRequest', ], 'output' => [ 'shape' => 'GetUserPoolMfaConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'GlobalSignOut' => [ 'name' => 'GlobalSignOut', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GlobalSignOutRequest', ], 'output' => [ 'shape' => 'GlobalSignOutResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'InitiateAuth' => [ 'name' => 'InitiateAuth', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'InitiateAuthRequest', ], 'output' => [ 'shape' => 'InitiateAuthResponse', ], 'errors' => [ [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'InvalidUserPoolConfigurationException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidEmailRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'ListDevices' => [ 'name' => 'ListDevices', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListDevicesRequest', ], 'output' => [ 'shape' => 'ListDevicesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InvalidUserPoolConfigurationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'ListGroups' => [ 'name' => 'ListGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListGroupsRequest', ], 'output' => [ 'shape' => 'ListGroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListIdentityProviders' => [ 'name' => 'ListIdentityProviders', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListIdentityProvidersRequest', ], 'output' => [ 'shape' => 'ListIdentityProvidersResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListResourceServers' => [ 'name' => 'ListResourceServers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourceServersRequest', ], 'output' => [ 'shape' => 'ListResourceServersResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListUserImportJobs' => [ 'name' => 'ListUserImportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListUserImportJobsRequest', ], 'output' => [ 'shape' => 'ListUserImportJobsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListUserPoolClients' => [ 'name' => 'ListUserPoolClients', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListUserPoolClientsRequest', ], 'output' => [ 'shape' => 'ListUserPoolClientsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListUserPools' => [ 'name' => 'ListUserPools', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListUserPoolsRequest', ], 'output' => [ 'shape' => 'ListUserPoolsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListUsers' => [ 'name' => 'ListUsers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListUsersRequest', ], 'output' => [ 'shape' => 'ListUsersResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListUsersInGroup' => [ 'name' => 'ListUsersInGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListUsersInGroupRequest', ], 'output' => [ 'shape' => 'ListUsersInGroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'ListWebAuthnCredentials' => [ 'name' => 'ListWebAuthnCredentials', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListWebAuthnCredentialsRequest', ], 'output' => [ 'shape' => 'ListWebAuthnCredentialsResponse', ], 'errors' => [ [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'NotAuthorizedException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'ResendConfirmationCode' => [ 'name' => 'ResendConfirmationCode', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResendConfirmationCodeRequest', ], 'output' => [ 'shape' => 'ResendConfirmationCodeResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], [ 'shape' => 'InvalidEmailRoleAccessPolicyException', ], [ 'shape' => 'CodeDeliveryFailureException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'RespondToAuthChallenge' => [ 'name' => 'RespondToAuthChallenge', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RespondToAuthChallengeRequest', ], 'output' => [ 'shape' => 'RespondToAuthChallengeResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'CodeMismatchException', ], [ 'shape' => 'ExpiredCodeException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'InvalidPasswordException', ], [ 'shape' => 'PasswordHistoryPolicyViolationException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidUserPoolConfigurationException', ], [ 'shape' => 'MFAMethodNotFoundException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], [ 'shape' => 'InvalidEmailRoleAccessPolicyException', ], [ 'shape' => 'AliasExistsException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'SoftwareTokenMFANotFoundException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'RevokeToken' => [ 'name' => 'RevokeToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RevokeTokenRequest', ], 'output' => [ 'shape' => 'RevokeTokenResponse', ], 'errors' => [ [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedOperationException', ], [ 'shape' => 'UnsupportedTokenTypeException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'SetLogDeliveryConfiguration' => [ 'name' => 'SetLogDeliveryConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetLogDeliveryConfigurationRequest', ], 'output' => [ 'shape' => 'SetLogDeliveryConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'FeatureUnavailableInTierException', ], ], ], 'SetRiskConfiguration' => [ 'name' => 'SetRiskConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetRiskConfigurationRequest', ], 'output' => [ 'shape' => 'SetRiskConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserPoolAddOnNotEnabledException', ], [ 'shape' => 'CodeDeliveryFailureException', ], [ 'shape' => 'InvalidEmailRoleAccessPolicyException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'SetUICustomization' => [ 'name' => 'SetUICustomization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetUICustomizationRequest', ], 'output' => [ 'shape' => 'SetUICustomizationResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'SetUserMFAPreference' => [ 'name' => 'SetUserMFAPreference', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetUserMFAPreferenceRequest', ], 'output' => [ 'shape' => 'SetUserMFAPreferenceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'SetUserPoolMfaConfig' => [ 'name' => 'SetUserPoolMfaConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetUserPoolMfaConfigRequest', ], 'output' => [ 'shape' => 'SetUserPoolMfaConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'FeatureUnavailableInTierException', ], ], ], 'SetUserSettings' => [ 'name' => 'SetUserSettings', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SetUserSettingsRequest', ], 'output' => [ 'shape' => 'SetUserSettingsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'SignUp' => [ 'name' => 'SignUp', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SignUpRequest', ], 'output' => [ 'shape' => 'SignUpResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InvalidPasswordException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'UsernameExistsException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], [ 'shape' => 'InvalidEmailRoleAccessPolicyException', ], [ 'shape' => 'CodeDeliveryFailureException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'StartUserImportJob' => [ 'name' => 'StartUserImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartUserImportJobRequest', ], 'output' => [ 'shape' => 'StartUserImportJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'StartWebAuthnRegistration' => [ 'name' => 'StartWebAuthnRegistration', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartWebAuthnRegistrationRequest', ], 'output' => [ 'shape' => 'StartWebAuthnRegistrationResponse', ], 'errors' => [ [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'WebAuthnNotEnabledException', ], [ 'shape' => 'WebAuthnConfigurationMissingException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'StopUserImportJob' => [ 'name' => 'StopUserImportJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopUserImportJobRequest', ], 'output' => [ 'shape' => 'StopUserImportJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'PreconditionNotMetException', ], [ 'shape' => 'NotAuthorizedException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'UpdateAuthEventFeedback' => [ 'name' => 'UpdateAuthEventFeedback', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateAuthEventFeedbackRequest', ], 'output' => [ 'shape' => 'UpdateAuthEventFeedbackResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserPoolAddOnNotEnabledException', ], [ 'shape' => 'InternalErrorException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'UpdateDeviceStatus' => [ 'name' => 'UpdateDeviceStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateDeviceStatusRequest', ], 'output' => [ 'shape' => 'UpdateDeviceStatusResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InvalidUserPoolConfigurationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'UpdateGroup' => [ 'name' => 'UpdateGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateGroupRequest', ], 'output' => [ 'shape' => 'UpdateGroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'UpdateIdentityProvider' => [ 'name' => 'UpdateIdentityProvider', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateIdentityProviderRequest', ], 'output' => [ 'shape' => 'UpdateIdentityProviderResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'UnsupportedIdentityProviderException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'UpdateManagedLoginBranding' => [ 'name' => 'UpdateManagedLoginBranding', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateManagedLoginBrandingRequest', ], 'output' => [ 'shape' => 'UpdateManagedLoginBrandingResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'UpdateResourceServer' => [ 'name' => 'UpdateResourceServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateResourceServerRequest', ], 'output' => [ 'shape' => 'UpdateResourceServerResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], ], ], 'UpdateUserAttributes' => [ 'name' => 'UpdateUserAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUserAttributesRequest', ], 'output' => [ 'shape' => 'UpdateUserAttributesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'CodeMismatchException', ], [ 'shape' => 'ExpiredCodeException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UnexpectedLambdaException', ], [ 'shape' => 'UserLambdaValidationException', ], [ 'shape' => 'InvalidLambdaResponseException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'AliasExistsException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], [ 'shape' => 'InvalidEmailRoleAccessPolicyException', ], [ 'shape' => 'CodeDeliveryFailureException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'UpdateUserPool' => [ 'name' => 'UpdateUserPool', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUserPoolRequest', ], 'output' => [ 'shape' => 'UpdateUserPoolResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'UserImportInProgressException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'InvalidSmsRoleAccessPolicyException', ], [ 'shape' => 'InvalidSmsRoleTrustRelationshipException', ], [ 'shape' => 'UserPoolTaggingException', ], [ 'shape' => 'InvalidEmailRoleAccessPolicyException', ], [ 'shape' => 'TierChangeNotAllowedException', ], [ 'shape' => 'FeatureUnavailableInTierException', ], ], ], 'UpdateUserPoolClient' => [ 'name' => 'UpdateUserPoolClient', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUserPoolClientRequest', ], 'output' => [ 'shape' => 'UpdateUserPoolClientResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'ScopeDoesNotExistException', ], [ 'shape' => 'InvalidOAuthFlowException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'FeatureUnavailableInTierException', ], ], ], 'UpdateUserPoolDomain' => [ 'name' => 'UpdateUserPoolDomain', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateUserPoolDomainRequest', ], 'output' => [ 'shape' => 'UpdateUserPoolDomainResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'FeatureUnavailableInTierException', ], ], ], 'VerifySoftwareToken' => [ 'name' => 'VerifySoftwareToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'VerifySoftwareTokenRequest', ], 'output' => [ 'shape' => 'VerifySoftwareTokenResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidUserPoolConfigurationException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'EnableSoftwareTokenMFAException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'SoftwareTokenMFANotFoundException', ], [ 'shape' => 'CodeMismatchException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], 'VerifyUserAttribute' => [ 'name' => 'VerifyUserAttribute', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'VerifyUserAttributeRequest', ], 'output' => [ 'shape' => 'VerifyUserAttributeResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'CodeMismatchException', ], [ 'shape' => 'ExpiredCodeException', ], [ 'shape' => 'NotAuthorizedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'PasswordResetRequiredException', ], [ 'shape' => 'UserNotFoundException', ], [ 'shape' => 'UserNotConfirmedException', ], [ 'shape' => 'InternalErrorException', ], [ 'shape' => 'AliasExistsException', ], [ 'shape' => 'ForbiddenException', ], ], 'authtype' => 'none', 'auth' => [ 'smithy.api#noAuth', ], ], ], 'shapes' => [ 'AWSAccountIdType' => [ 'type' => 'string', 'max' => 12, 'pattern' => '[0-9]+', ], 'AccessTokenValidityType' => [ 'type' => 'integer', 'max' => 86400, 'min' => 1, ], 'AccountRecoverySettingType' => [ 'type' => 'structure', 'members' => [ 'RecoveryMechanisms' => [ 'shape' => 'RecoveryMechanismsType', ], ], ], 'AccountTakeoverActionNotifyType' => [ 'type' => 'boolean', ], 'AccountTakeoverActionType' => [ 'type' => 'structure', 'required' => [ 'Notify', 'EventAction', ], 'members' => [ 'Notify' => [ 'shape' => 'AccountTakeoverActionNotifyType', ], 'EventAction' => [ 'shape' => 'AccountTakeoverEventActionType', ], ], ], 'AccountTakeoverActionsType' => [ 'type' => 'structure', 'members' => [ 'LowAction' => [ 'shape' => 'AccountTakeoverActionType', ], 'MediumAction' => [ 'shape' => 'AccountTakeoverActionType', ], 'HighAction' => [ 'shape' => 'AccountTakeoverActionType', ], ], ], 'AccountTakeoverEventActionType' => [ 'type' => 'string', 'enum' => [ 'BLOCK', 'MFA_IF_CONFIGURED', 'MFA_REQUIRED', 'NO_ACTION', ], ], 'AccountTakeoverRiskConfigurationType' => [ 'type' => 'structure', 'required' => [ 'Actions', ], 'members' => [ 'NotifyConfiguration' => [ 'shape' => 'NotifyConfigurationType', ], 'Actions' => [ 'shape' => 'AccountTakeoverActionsType', ], ], ], 'AddCustomAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'CustomAttributes', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'CustomAttributes' => [ 'shape' => 'CustomAttributesListType', ], ], ], 'AddCustomAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdminAddUserToGroupRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', 'GroupName', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'GroupName' => [ 'shape' => 'GroupNameType', ], ], ], 'AdminConfirmSignUpRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], ], ], 'AdminConfirmSignUpResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdminCreateUserConfigType' => [ 'type' => 'structure', 'members' => [ 'AllowAdminCreateUserOnly' => [ 'shape' => 'BooleanType', ], 'UnusedAccountValidityDays' => [ 'shape' => 'AdminCreateUserUnusedAccountValidityDaysType', ], 'InviteMessageTemplate' => [ 'shape' => 'MessageTemplateType', ], ], ], 'AdminCreateUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'UserAttributes' => [ 'shape' => 'AttributeListType', ], 'ValidationData' => [ 'shape' => 'AttributeListType', ], 'TemporaryPassword' => [ 'shape' => 'PasswordType', ], 'ForceAliasCreation' => [ 'shape' => 'ForceAliasCreation', ], 'MessageAction' => [ 'shape' => 'MessageActionType', ], 'DesiredDeliveryMediums' => [ 'shape' => 'DeliveryMediumListType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], ], ], 'AdminCreateUserResponse' => [ 'type' => 'structure', 'members' => [ 'User' => [ 'shape' => 'UserType', ], ], ], 'AdminCreateUserUnusedAccountValidityDaysType' => [ 'type' => 'integer', 'max' => 365, 'min' => 0, ], 'AdminDeleteUserAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', 'UserAttributeNames', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'UserAttributeNames' => [ 'shape' => 'AttributeNameListType', ], ], ], 'AdminDeleteUserAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdminDeleteUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], ], ], 'AdminDisableProviderForUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'User', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'StringType', ], 'User' => [ 'shape' => 'ProviderUserIdentifierType', ], ], ], 'AdminDisableProviderForUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdminDisableUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], ], ], 'AdminDisableUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdminEnableUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], ], ], 'AdminEnableUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdminForgetDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', 'DeviceKey', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'DeviceKey' => [ 'shape' => 'DeviceKeyType', ], ], ], 'AdminGetDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceKey', 'UserPoolId', 'Username', ], 'members' => [ 'DeviceKey' => [ 'shape' => 'DeviceKeyType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], ], ], 'AdminGetDeviceResponse' => [ 'type' => 'structure', 'required' => [ 'Device', ], 'members' => [ 'Device' => [ 'shape' => 'DeviceType', ], ], ], 'AdminGetUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], ], ], 'AdminGetUserResponse' => [ 'type' => 'structure', 'required' => [ 'Username', ], 'members' => [ 'Username' => [ 'shape' => 'UsernameType', ], 'UserAttributes' => [ 'shape' => 'AttributeListType', ], 'UserCreateDate' => [ 'shape' => 'DateType', ], 'UserLastModifiedDate' => [ 'shape' => 'DateType', ], 'Enabled' => [ 'shape' => 'BooleanType', ], 'UserStatus' => [ 'shape' => 'UserStatusType', ], 'MFAOptions' => [ 'shape' => 'MFAOptionListType', ], 'PreferredMfaSetting' => [ 'shape' => 'StringType', ], 'UserMFASettingList' => [ 'shape' => 'UserMFASettingListType', ], ], ], 'AdminInitiateAuthRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'ClientId', 'AuthFlow', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], 'AuthFlow' => [ 'shape' => 'AuthFlowType', ], 'AuthParameters' => [ 'shape' => 'AuthParametersType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], 'AnalyticsMetadata' => [ 'shape' => 'AnalyticsMetadataType', ], 'ContextData' => [ 'shape' => 'ContextDataType', ], 'Session' => [ 'shape' => 'SessionType', ], ], ], 'AdminInitiateAuthResponse' => [ 'type' => 'structure', 'members' => [ 'ChallengeName' => [ 'shape' => 'ChallengeNameType', ], 'Session' => [ 'shape' => 'SessionType', ], 'ChallengeParameters' => [ 'shape' => 'ChallengeParametersType', ], 'AuthenticationResult' => [ 'shape' => 'AuthenticationResultType', ], 'AvailableChallenges' => [ 'shape' => 'AvailableChallengeListType', ], ], ], 'AdminLinkProviderForUserRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'DestinationUser', 'SourceUser', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'StringType', ], 'DestinationUser' => [ 'shape' => 'ProviderUserIdentifierType', ], 'SourceUser' => [ 'shape' => 'ProviderUserIdentifierType', ], ], ], 'AdminLinkProviderForUserResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdminListDevicesRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'Limit' => [ 'shape' => 'QueryLimitType', ], 'PaginationToken' => [ 'shape' => 'SearchPaginationTokenType', ], ], ], 'AdminListDevicesResponse' => [ 'type' => 'structure', 'members' => [ 'Devices' => [ 'shape' => 'DeviceListType', ], 'PaginationToken' => [ 'shape' => 'SearchPaginationTokenType', ], ], ], 'AdminListGroupsForUserRequest' => [ 'type' => 'structure', 'required' => [ 'Username', 'UserPoolId', ], 'members' => [ 'Username' => [ 'shape' => 'UsernameType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Limit' => [ 'shape' => 'QueryLimitType', ], 'NextToken' => [ 'shape' => 'PaginationKey', ], ], ], 'AdminListGroupsForUserResponse' => [ 'type' => 'structure', 'members' => [ 'Groups' => [ 'shape' => 'GroupListType', ], 'NextToken' => [ 'shape' => 'PaginationKey', ], ], ], 'AdminListUserAuthEventsRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'MaxResults' => [ 'shape' => 'QueryLimitType', ], 'NextToken' => [ 'shape' => 'PaginationKey', ], ], ], 'AdminListUserAuthEventsResponse' => [ 'type' => 'structure', 'members' => [ 'AuthEvents' => [ 'shape' => 'AuthEventsType', ], 'NextToken' => [ 'shape' => 'PaginationKey', ], ], ], 'AdminRemoveUserFromGroupRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', 'GroupName', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'GroupName' => [ 'shape' => 'GroupNameType', ], ], ], 'AdminResetUserPasswordRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], ], ], 'AdminResetUserPasswordResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdminRespondToAuthChallengeRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'ClientId', 'ChallengeName', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], 'ChallengeName' => [ 'shape' => 'ChallengeNameType', ], 'ChallengeResponses' => [ 'shape' => 'ChallengeResponsesType', ], 'Session' => [ 'shape' => 'SessionType', ], 'AnalyticsMetadata' => [ 'shape' => 'AnalyticsMetadataType', ], 'ContextData' => [ 'shape' => 'ContextDataType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], ], ], 'AdminRespondToAuthChallengeResponse' => [ 'type' => 'structure', 'members' => [ 'ChallengeName' => [ 'shape' => 'ChallengeNameType', ], 'Session' => [ 'shape' => 'SessionType', ], 'ChallengeParameters' => [ 'shape' => 'ChallengeParametersType', ], 'AuthenticationResult' => [ 'shape' => 'AuthenticationResultType', ], ], ], 'AdminSetUserMFAPreferenceRequest' => [ 'type' => 'structure', 'required' => [ 'Username', 'UserPoolId', ], 'members' => [ 'SMSMfaSettings' => [ 'shape' => 'SMSMfaSettingsType', ], 'SoftwareTokenMfaSettings' => [ 'shape' => 'SoftwareTokenMfaSettingsType', ], 'EmailMfaSettings' => [ 'shape' => 'EmailMfaSettingsType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], ], ], 'AdminSetUserMFAPreferenceResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdminSetUserPasswordRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', 'Password', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'Password' => [ 'shape' => 'PasswordType', ], 'Permanent' => [ 'shape' => 'BooleanType', ], ], ], 'AdminSetUserPasswordResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdminSetUserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', 'MFAOptions', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'MFAOptions' => [ 'shape' => 'MFAOptionListType', ], ], ], 'AdminSetUserSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdminUpdateAuthEventFeedbackRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', 'EventId', 'FeedbackValue', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'EventId' => [ 'shape' => 'EventIdType', ], 'FeedbackValue' => [ 'shape' => 'FeedbackValueType', ], ], ], 'AdminUpdateAuthEventFeedbackResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdminUpdateDeviceStatusRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', 'DeviceKey', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'DeviceKey' => [ 'shape' => 'DeviceKeyType', ], 'DeviceRememberedStatus' => [ 'shape' => 'DeviceRememberedStatusType', ], ], ], 'AdminUpdateDeviceStatusResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdminUpdateUserAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', 'UserAttributes', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'UserAttributes' => [ 'shape' => 'AttributeListType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], ], ], 'AdminUpdateUserAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdminUserGlobalSignOutRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], ], ], 'AdminUserGlobalSignOutResponse' => [ 'type' => 'structure', 'members' => [], ], 'AdvancedSecurityAdditionalFlowsType' => [ 'type' => 'structure', 'members' => [ 'CustomAuthMode' => [ 'shape' => 'AdvancedSecurityEnabledModeType', ], ], ], 'AdvancedSecurityEnabledModeType' => [ 'type' => 'string', 'enum' => [ 'AUDIT', 'ENFORCED', ], ], 'AdvancedSecurityModeType' => [ 'type' => 'string', 'enum' => [ 'OFF', 'AUDIT', 'ENFORCED', ], ], 'AliasAttributeType' => [ 'type' => 'string', 'enum' => [ 'phone_number', 'email', 'preferred_username', ], ], 'AliasAttributesListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'AliasAttributeType', ], ], 'AliasExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'AllowedFirstAuthFactorsListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthFactorType', ], 'max' => 4, 'min' => 1, ], 'AnalyticsConfigurationType' => [ 'type' => 'structure', 'members' => [ 'ApplicationId' => [ 'shape' => 'HexStringType', ], 'ApplicationArn' => [ 'shape' => 'ArnType', ], 'RoleArn' => [ 'shape' => 'ArnType', ], 'ExternalId' => [ 'shape' => 'StringType', ], 'UserDataShared' => [ 'shape' => 'BooleanType', ], ], ], 'AnalyticsMetadataType' => [ 'type' => 'structure', 'members' => [ 'AnalyticsEndpointId' => [ 'shape' => 'StringType', ], ], ], 'ArnType' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:[\\w+=/,.@-]+:[\\w+=/,.@-]+:([\\w+=/,.@-]*)?:[0-9]+:[\\w+=/,.@-]+(:[\\w+=/,.@-]+)?(:[\\w+=/,.@-]+)?', ], 'AssetBytesType' => [ 'type' => 'blob', 'max' => 1000000, ], 'AssetCategoryType' => [ 'type' => 'string', 'enum' => [ 'FAVICON_ICO', 'FAVICON_SVG', 'EMAIL_GRAPHIC', 'SMS_GRAPHIC', 'AUTH_APP_GRAPHIC', 'PASSWORD_GRAPHIC', 'PASSKEY_GRAPHIC', 'PAGE_HEADER_LOGO', 'PAGE_HEADER_BACKGROUND', 'PAGE_FOOTER_LOGO', 'PAGE_FOOTER_BACKGROUND', 'PAGE_BACKGROUND', 'FORM_BACKGROUND', 'FORM_LOGO', 'IDP_BUTTON_ICON', ], ], 'AssetExtensionType' => [ 'type' => 'string', 'enum' => [ 'ICO', 'JPEG', 'PNG', 'SVG', 'WEBP', ], ], 'AssetListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssetType', ], 'max' => 40, 'min' => 0, ], 'AssetType' => [ 'type' => 'structure', 'required' => [ 'Category', 'ColorMode', 'Extension', ], 'members' => [ 'Category' => [ 'shape' => 'AssetCategoryType', ], 'ColorMode' => [ 'shape' => 'ColorSchemeModeType', ], 'Extension' => [ 'shape' => 'AssetExtensionType', ], 'Bytes' => [ 'shape' => 'AssetBytesType', ], 'ResourceId' => [ 'shape' => 'ResourceIdType', ], ], ], 'AssociateSoftwareTokenRequest' => [ 'type' => 'structure', 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], 'Session' => [ 'shape' => 'SessionType', ], ], ], 'AssociateSoftwareTokenResponse' => [ 'type' => 'structure', 'members' => [ 'SecretCode' => [ 'shape' => 'SecretCodeType', ], 'Session' => [ 'shape' => 'SessionType', ], ], ], 'AttributeDataType' => [ 'type' => 'string', 'enum' => [ 'String', 'Number', 'DateTime', 'Boolean', ], ], 'AttributeListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeType', ], ], 'AttributeMappingKeyType' => [ 'type' => 'string', 'max' => 32, 'min' => 1, ], 'AttributeMappingType' => [ 'type' => 'map', 'key' => [ 'shape' => 'AttributeMappingKeyType', ], 'value' => [ 'shape' => 'StringType', ], ], 'AttributeNameListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeNameType', ], ], 'AttributeNameType' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}]+', ], 'AttributeType' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'AttributeNameType', ], 'Value' => [ 'shape' => 'AttributeValueType', ], ], ], 'AttributeValueType' => [ 'type' => 'string', 'max' => 2048, 'sensitive' => true, ], 'AttributesRequireVerificationBeforeUpdateType' => [ 'type' => 'list', 'member' => [ 'shape' => 'VerifiedAttributeType', ], ], 'AuthEventType' => [ 'type' => 'structure', 'members' => [ 'EventId' => [ 'shape' => 'StringType', ], 'EventType' => [ 'shape' => 'EventType', ], 'CreationDate' => [ 'shape' => 'DateType', ], 'EventResponse' => [ 'shape' => 'EventResponseType', ], 'EventRisk' => [ 'shape' => 'EventRiskType', ], 'ChallengeResponses' => [ 'shape' => 'ChallengeResponseListType', ], 'EventContextData' => [ 'shape' => 'EventContextDataType', ], 'EventFeedback' => [ 'shape' => 'EventFeedbackType', ], ], ], 'AuthEventsType' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthEventType', ], ], 'AuthFactorType' => [ 'type' => 'string', 'enum' => [ 'PASSWORD', 'EMAIL_OTP', 'SMS_OTP', 'WEB_AUTHN', ], ], 'AuthFlowType' => [ 'type' => 'string', 'enum' => [ 'USER_SRP_AUTH', 'REFRESH_TOKEN_AUTH', 'REFRESH_TOKEN', 'CUSTOM_AUTH', 'ADMIN_NO_SRP_AUTH', 'USER_PASSWORD_AUTH', 'ADMIN_USER_PASSWORD_AUTH', 'USER_AUTH', ], ], 'AuthParametersType' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringType', ], 'value' => [ 'shape' => 'StringType', ], 'sensitive' => true, ], 'AuthSessionValidityType' => [ 'type' => 'integer', 'max' => 15, 'min' => 3, ], 'AuthenticationResultType' => [ 'type' => 'structure', 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], 'ExpiresIn' => [ 'shape' => 'IntegerType', ], 'TokenType' => [ 'shape' => 'StringType', ], 'RefreshToken' => [ 'shape' => 'TokenModelType', ], 'IdToken' => [ 'shape' => 'TokenModelType', ], 'NewDeviceMetadata' => [ 'shape' => 'NewDeviceMetadataType', ], ], ], 'AvailableChallengeListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChallengeNameType', ], ], 'BlockedIPRangeListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringType', ], 'max' => 200, ], 'BooleanType' => [ 'type' => 'boolean', ], 'CSSType' => [ 'type' => 'string', 'max' => 131072, 'min' => 0, ], 'CSSVersionType' => [ 'type' => 'string', ], 'CallbackURLsListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'RedirectUrlType', ], 'max' => 100, 'min' => 0, ], 'ChallengeName' => [ 'type' => 'string', 'enum' => [ 'Password', 'Mfa', ], ], 'ChallengeNameType' => [ 'type' => 'string', 'enum' => [ 'SMS_MFA', 'EMAIL_OTP', 'SOFTWARE_TOKEN_MFA', 'SELECT_MFA_TYPE', 'MFA_SETUP', 'PASSWORD_VERIFIER', 'CUSTOM_CHALLENGE', 'SELECT_CHALLENGE', 'DEVICE_SRP_AUTH', 'DEVICE_PASSWORD_VERIFIER', 'ADMIN_NO_SRP_AUTH', 'NEW_PASSWORD_REQUIRED', 'SMS_OTP', 'PASSWORD', 'WEB_AUTHN', 'PASSWORD_SRP', ], ], 'ChallengeParametersType' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringType', ], 'value' => [ 'shape' => 'StringType', ], ], 'ChallengeResponse' => [ 'type' => 'string', 'enum' => [ 'Success', 'Failure', ], ], 'ChallengeResponseListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChallengeResponseType', ], ], 'ChallengeResponseType' => [ 'type' => 'structure', 'members' => [ 'ChallengeName' => [ 'shape' => 'ChallengeName', ], 'ChallengeResponse' => [ 'shape' => 'ChallengeResponse', ], ], ], 'ChallengeResponsesType' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringType', ], 'value' => [ 'shape' => 'StringType', ], 'sensitive' => true, ], 'ChangePasswordRequest' => [ 'type' => 'structure', 'required' => [ 'ProposedPassword', 'AccessToken', ], 'members' => [ 'PreviousPassword' => [ 'shape' => 'PasswordType', ], 'ProposedPassword' => [ 'shape' => 'PasswordType', ], 'AccessToken' => [ 'shape' => 'TokenModelType', ], ], ], 'ChangePasswordResponse' => [ 'type' => 'structure', 'members' => [], ], 'ClientIdType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w+]+', 'sensitive' => true, ], 'ClientMetadataType' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringType', ], 'value' => [ 'shape' => 'StringType', ], ], 'ClientNameType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w\\s+=,.@-]+', ], 'ClientPermissionListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClientPermissionType', ], ], 'ClientPermissionType' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ClientSecretType' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[\\w+]+', 'sensitive' => true, ], 'CloudWatchLogsConfigurationType' => [ 'type' => 'structure', 'members' => [ 'LogGroupArn' => [ 'shape' => 'ArnType', ], ], ], 'CodeDeliveryDetailsListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeDeliveryDetailsType', ], ], 'CodeDeliveryDetailsType' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => 'StringType', ], 'DeliveryMedium' => [ 'shape' => 'DeliveryMediumType', ], 'AttributeName' => [ 'shape' => 'AttributeNameType', ], ], ], 'CodeDeliveryFailureException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'CodeMismatchException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'ColorSchemeModeType' => [ 'type' => 'string', 'enum' => [ 'LIGHT', 'DARK', 'DYNAMIC', ], ], 'CompleteWebAuthnRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', 'Credential', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], 'Credential' => [ 'shape' => 'Document', ], ], ], 'CompleteWebAuthnRegistrationResponse' => [ 'type' => 'structure', 'members' => [], ], 'CompletionMessageType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w]+', ], 'CompromisedCredentialsActionsType' => [ 'type' => 'structure', 'required' => [ 'EventAction', ], 'members' => [ 'EventAction' => [ 'shape' => 'CompromisedCredentialsEventActionType', ], ], ], 'CompromisedCredentialsEventActionType' => [ 'type' => 'string', 'enum' => [ 'BLOCK', 'NO_ACTION', ], ], 'CompromisedCredentialsRiskConfigurationType' => [ 'type' => 'structure', 'required' => [ 'Actions', ], 'members' => [ 'EventFilter' => [ 'shape' => 'EventFiltersType', ], 'Actions' => [ 'shape' => 'CompromisedCredentialsActionsType', ], ], ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'ConfiguredUserAuthFactorsListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthFactorType', ], 'max' => 8, 'min' => 0, ], 'ConfirmDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', 'DeviceKey', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], 'DeviceKey' => [ 'shape' => 'DeviceKeyType', ], 'DeviceSecretVerifierConfig' => [ 'shape' => 'DeviceSecretVerifierConfigType', ], 'DeviceName' => [ 'shape' => 'DeviceNameType', ], ], ], 'ConfirmDeviceResponse' => [ 'type' => 'structure', 'members' => [ 'UserConfirmationNecessary' => [ 'shape' => 'BooleanType', ], ], ], 'ConfirmForgotPasswordRequest' => [ 'type' => 'structure', 'required' => [ 'ClientId', 'Username', 'ConfirmationCode', 'Password', ], 'members' => [ 'ClientId' => [ 'shape' => 'ClientIdType', ], 'SecretHash' => [ 'shape' => 'SecretHashType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'ConfirmationCode' => [ 'shape' => 'ConfirmationCodeType', ], 'Password' => [ 'shape' => 'PasswordType', ], 'AnalyticsMetadata' => [ 'shape' => 'AnalyticsMetadataType', ], 'UserContextData' => [ 'shape' => 'UserContextDataType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], ], ], 'ConfirmForgotPasswordResponse' => [ 'type' => 'structure', 'members' => [], ], 'ConfirmSignUpRequest' => [ 'type' => 'structure', 'required' => [ 'ClientId', 'Username', 'ConfirmationCode', ], 'members' => [ 'ClientId' => [ 'shape' => 'ClientIdType', ], 'SecretHash' => [ 'shape' => 'SecretHashType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'ConfirmationCode' => [ 'shape' => 'ConfirmationCodeType', ], 'ForceAliasCreation' => [ 'shape' => 'ForceAliasCreation', ], 'AnalyticsMetadata' => [ 'shape' => 'AnalyticsMetadataType', ], 'UserContextData' => [ 'shape' => 'UserContextDataType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], 'Session' => [ 'shape' => 'SessionType', ], ], ], 'ConfirmSignUpResponse' => [ 'type' => 'structure', 'members' => [ 'Session' => [ 'shape' => 'SessionType', ], ], ], 'ConfirmationCodeType' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[\\S]+', ], 'ContextDataType' => [ 'type' => 'structure', 'required' => [ 'IpAddress', 'ServerName', 'ServerPath', 'HttpHeaders', ], 'members' => [ 'IpAddress' => [ 'shape' => 'StringType', ], 'ServerName' => [ 'shape' => 'StringType', ], 'ServerPath' => [ 'shape' => 'StringType', ], 'HttpHeaders' => [ 'shape' => 'HttpHeaderList', ], 'EncodedData' => [ 'shape' => 'StringType', ], ], ], 'CreateGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GroupName', 'UserPoolId', ], 'members' => [ 'GroupName' => [ 'shape' => 'GroupNameType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Description' => [ 'shape' => 'DescriptionType', ], 'RoleArn' => [ 'shape' => 'ArnType', ], 'Precedence' => [ 'shape' => 'PrecedenceType', ], ], ], 'CreateGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'GroupType', ], ], ], 'CreateIdentityProviderRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'ProviderName', 'ProviderType', 'ProviderDetails', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ProviderName' => [ 'shape' => 'ProviderNameTypeV2', ], 'ProviderType' => [ 'shape' => 'IdentityProviderTypeType', ], 'ProviderDetails' => [ 'shape' => 'ProviderDetailsType', ], 'AttributeMapping' => [ 'shape' => 'AttributeMappingType', ], 'IdpIdentifiers' => [ 'shape' => 'IdpIdentifiersListType', ], ], ], 'CreateIdentityProviderResponse' => [ 'type' => 'structure', 'required' => [ 'IdentityProvider', ], 'members' => [ 'IdentityProvider' => [ 'shape' => 'IdentityProviderType', ], ], ], 'CreateManagedLoginBrandingRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'ClientId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], 'UseCognitoProvidedValues' => [ 'shape' => 'BooleanType', ], 'Settings' => [ 'shape' => 'Document', ], 'Assets' => [ 'shape' => 'AssetListType', ], ], ], 'CreateManagedLoginBrandingResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedLoginBranding' => [ 'shape' => 'ManagedLoginBrandingType', ], ], ], 'CreateResourceServerRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Identifier', 'Name', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Identifier' => [ 'shape' => 'ResourceServerIdentifierType', ], 'Name' => [ 'shape' => 'ResourceServerNameType', ], 'Scopes' => [ 'shape' => 'ResourceServerScopeListType', ], ], ], 'CreateResourceServerResponse' => [ 'type' => 'structure', 'required' => [ 'ResourceServer', ], 'members' => [ 'ResourceServer' => [ 'shape' => 'ResourceServerType', ], ], ], 'CreateUserImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'JobName', 'UserPoolId', 'CloudWatchLogsRoleArn', ], 'members' => [ 'JobName' => [ 'shape' => 'UserImportJobNameType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'CloudWatchLogsRoleArn' => [ 'shape' => 'ArnType', ], ], ], 'CreateUserImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'UserImportJob' => [ 'shape' => 'UserImportJobType', ], ], ], 'CreateUserPoolClientRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'ClientName', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientName' => [ 'shape' => 'ClientNameType', ], 'GenerateSecret' => [ 'shape' => 'GenerateSecret', ], 'RefreshTokenValidity' => [ 'shape' => 'RefreshTokenValidityType', ], 'AccessTokenValidity' => [ 'shape' => 'AccessTokenValidityType', ], 'IdTokenValidity' => [ 'shape' => 'IdTokenValidityType', ], 'TokenValidityUnits' => [ 'shape' => 'TokenValidityUnitsType', ], 'ReadAttributes' => [ 'shape' => 'ClientPermissionListType', ], 'WriteAttributes' => [ 'shape' => 'ClientPermissionListType', ], 'ExplicitAuthFlows' => [ 'shape' => 'ExplicitAuthFlowsListType', ], 'SupportedIdentityProviders' => [ 'shape' => 'SupportedIdentityProvidersListType', ], 'CallbackURLs' => [ 'shape' => 'CallbackURLsListType', ], 'LogoutURLs' => [ 'shape' => 'LogoutURLsListType', ], 'DefaultRedirectURI' => [ 'shape' => 'RedirectUrlType', ], 'AllowedOAuthFlows' => [ 'shape' => 'OAuthFlowsType', ], 'AllowedOAuthScopes' => [ 'shape' => 'ScopeListType', ], 'AllowedOAuthFlowsUserPoolClient' => [ 'shape' => 'BooleanType', ], 'AnalyticsConfiguration' => [ 'shape' => 'AnalyticsConfigurationType', ], 'PreventUserExistenceErrors' => [ 'shape' => 'PreventUserExistenceErrorTypes', ], 'EnableTokenRevocation' => [ 'shape' => 'WrappedBooleanType', ], 'EnablePropagateAdditionalUserContextData' => [ 'shape' => 'WrappedBooleanType', ], 'AuthSessionValidity' => [ 'shape' => 'AuthSessionValidityType', ], 'RefreshTokenRotation' => [ 'shape' => 'RefreshTokenRotationType', ], ], ], 'CreateUserPoolClientResponse' => [ 'type' => 'structure', 'members' => [ 'UserPoolClient' => [ 'shape' => 'UserPoolClientType', ], ], ], 'CreateUserPoolDomainRequest' => [ 'type' => 'structure', 'required' => [ 'Domain', 'UserPoolId', ], 'members' => [ 'Domain' => [ 'shape' => 'DomainType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ManagedLoginVersion' => [ 'shape' => 'WrappedIntegerType', ], 'CustomDomainConfig' => [ 'shape' => 'CustomDomainConfigType', ], ], ], 'CreateUserPoolDomainResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedLoginVersion' => [ 'shape' => 'WrappedIntegerType', ], 'CloudFrontDomain' => [ 'shape' => 'DomainType', ], ], ], 'CreateUserPoolRequest' => [ 'type' => 'structure', 'required' => [ 'PoolName', ], 'members' => [ 'PoolName' => [ 'shape' => 'UserPoolNameType', ], 'Policies' => [ 'shape' => 'UserPoolPolicyType', ], 'DeletionProtection' => [ 'shape' => 'DeletionProtectionType', ], 'LambdaConfig' => [ 'shape' => 'LambdaConfigType', ], 'AutoVerifiedAttributes' => [ 'shape' => 'VerifiedAttributesListType', ], 'AliasAttributes' => [ 'shape' => 'AliasAttributesListType', ], 'UsernameAttributes' => [ 'shape' => 'UsernameAttributesListType', ], 'SmsVerificationMessage' => [ 'shape' => 'SmsVerificationMessageType', ], 'EmailVerificationMessage' => [ 'shape' => 'EmailVerificationMessageType', ], 'EmailVerificationSubject' => [ 'shape' => 'EmailVerificationSubjectType', ], 'VerificationMessageTemplate' => [ 'shape' => 'VerificationMessageTemplateType', ], 'SmsAuthenticationMessage' => [ 'shape' => 'SmsVerificationMessageType', ], 'MfaConfiguration' => [ 'shape' => 'UserPoolMfaType', ], 'UserAttributeUpdateSettings' => [ 'shape' => 'UserAttributeUpdateSettingsType', ], 'DeviceConfiguration' => [ 'shape' => 'DeviceConfigurationType', ], 'EmailConfiguration' => [ 'shape' => 'EmailConfigurationType', ], 'SmsConfiguration' => [ 'shape' => 'SmsConfigurationType', ], 'UserPoolTags' => [ 'shape' => 'UserPoolTagsType', ], 'AdminCreateUserConfig' => [ 'shape' => 'AdminCreateUserConfigType', ], 'Schema' => [ 'shape' => 'SchemaAttributesListType', ], 'UserPoolAddOns' => [ 'shape' => 'UserPoolAddOnsType', ], 'UsernameConfiguration' => [ 'shape' => 'UsernameConfigurationType', ], 'AccountRecoverySetting' => [ 'shape' => 'AccountRecoverySettingType', ], 'UserPoolTier' => [ 'shape' => 'UserPoolTierType', ], ], ], 'CreateUserPoolResponse' => [ 'type' => 'structure', 'members' => [ 'UserPool' => [ 'shape' => 'UserPoolType', ], ], ], 'CustomAttributeNameType' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}]+', ], 'CustomAttributesListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaAttributeType', ], 'max' => 25, 'min' => 1, ], 'CustomDomainConfigType' => [ 'type' => 'structure', 'required' => [ 'CertificateArn', ], 'members' => [ 'CertificateArn' => [ 'shape' => 'ArnType', ], ], ], 'CustomEmailLambdaVersionConfigType' => [ 'type' => 'structure', 'required' => [ 'LambdaVersion', 'LambdaArn', ], 'members' => [ 'LambdaVersion' => [ 'shape' => 'CustomEmailSenderLambdaVersionType', ], 'LambdaArn' => [ 'shape' => 'ArnType', ], ], ], 'CustomEmailSenderLambdaVersionType' => [ 'type' => 'string', 'enum' => [ 'V1_0', ], ], 'CustomSMSLambdaVersionConfigType' => [ 'type' => 'structure', 'required' => [ 'LambdaVersion', 'LambdaArn', ], 'members' => [ 'LambdaVersion' => [ 'shape' => 'CustomSMSSenderLambdaVersionType', ], 'LambdaArn' => [ 'shape' => 'ArnType', ], ], ], 'CustomSMSSenderLambdaVersionType' => [ 'type' => 'string', 'enum' => [ 'V1_0', ], ], 'DateType' => [ 'type' => 'timestamp', ], 'DefaultEmailOptionType' => [ 'type' => 'string', 'enum' => [ 'CONFIRM_WITH_LINK', 'CONFIRM_WITH_CODE', ], ], 'DeleteGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GroupName', 'UserPoolId', ], 'members' => [ 'GroupName' => [ 'shape' => 'GroupNameType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], ], ], 'DeleteIdentityProviderRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'ProviderName', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ProviderName' => [ 'shape' => 'ProviderNameType', ], ], ], 'DeleteManagedLoginBrandingRequest' => [ 'type' => 'structure', 'required' => [ 'ManagedLoginBrandingId', 'UserPoolId', ], 'members' => [ 'ManagedLoginBrandingId' => [ 'shape' => 'ManagedLoginBrandingIdType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], ], ], 'DeleteResourceServerRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Identifier', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Identifier' => [ 'shape' => 'ResourceServerIdentifierType', ], ], ], 'DeleteUserAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'UserAttributeNames', 'AccessToken', ], 'members' => [ 'UserAttributeNames' => [ 'shape' => 'AttributeNameListType', ], 'AccessToken' => [ 'shape' => 'TokenModelType', ], ], ], 'DeleteUserAttributesResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUserPoolClientRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'ClientId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], ], ], 'DeleteUserPoolDomainRequest' => [ 'type' => 'structure', 'required' => [ 'Domain', 'UserPoolId', ], 'members' => [ 'Domain' => [ 'shape' => 'DomainType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], ], ], 'DeleteUserPoolDomainResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteUserPoolRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], ], ], 'DeleteUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], ], ], 'DeleteWebAuthnCredentialRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', 'CredentialId', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], 'CredentialId' => [ 'shape' => 'StringType', ], ], ], 'DeleteWebAuthnCredentialResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletionProtectionType' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'DeliveryMediumListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeliveryMediumType', ], ], 'DeliveryMediumType' => [ 'type' => 'string', 'enum' => [ 'SMS', 'EMAIL', ], ], 'DescribeIdentityProviderRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'ProviderName', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ProviderName' => [ 'shape' => 'ProviderNameType', ], ], ], 'DescribeIdentityProviderResponse' => [ 'type' => 'structure', 'required' => [ 'IdentityProvider', ], 'members' => [ 'IdentityProvider' => [ 'shape' => 'IdentityProviderType', ], ], ], 'DescribeManagedLoginBrandingByClientRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'ClientId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], 'ReturnMergedResources' => [ 'shape' => 'BooleanType', ], ], ], 'DescribeManagedLoginBrandingByClientResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedLoginBranding' => [ 'shape' => 'ManagedLoginBrandingType', ], ], ], 'DescribeManagedLoginBrandingRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'ManagedLoginBrandingId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ManagedLoginBrandingId' => [ 'shape' => 'ManagedLoginBrandingIdType', ], 'ReturnMergedResources' => [ 'shape' => 'BooleanType', ], ], ], 'DescribeManagedLoginBrandingResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedLoginBranding' => [ 'shape' => 'ManagedLoginBrandingType', ], ], ], 'DescribeResourceServerRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Identifier', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Identifier' => [ 'shape' => 'ResourceServerIdentifierType', ], ], ], 'DescribeResourceServerResponse' => [ 'type' => 'structure', 'required' => [ 'ResourceServer', ], 'members' => [ 'ResourceServer' => [ 'shape' => 'ResourceServerType', ], ], ], 'DescribeRiskConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], ], ], 'DescribeRiskConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'RiskConfiguration', ], 'members' => [ 'RiskConfiguration' => [ 'shape' => 'RiskConfigurationType', ], ], ], 'DescribeUserImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'JobId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'JobId' => [ 'shape' => 'UserImportJobIdType', ], ], ], 'DescribeUserImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'UserImportJob' => [ 'shape' => 'UserImportJobType', ], ], ], 'DescribeUserPoolClientRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'ClientId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], ], ], 'DescribeUserPoolClientResponse' => [ 'type' => 'structure', 'members' => [ 'UserPoolClient' => [ 'shape' => 'UserPoolClientType', ], ], ], 'DescribeUserPoolDomainRequest' => [ 'type' => 'structure', 'required' => [ 'Domain', ], 'members' => [ 'Domain' => [ 'shape' => 'DomainType', ], ], ], 'DescribeUserPoolDomainResponse' => [ 'type' => 'structure', 'members' => [ 'DomainDescription' => [ 'shape' => 'DomainDescriptionType', ], ], ], 'DescribeUserPoolRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], ], ], 'DescribeUserPoolResponse' => [ 'type' => 'structure', 'members' => [ 'UserPool' => [ 'shape' => 'UserPoolType', ], ], ], 'DescriptionType' => [ 'type' => 'string', 'max' => 2048, ], 'DeviceConfigurationType' => [ 'type' => 'structure', 'members' => [ 'ChallengeRequiredOnNewDevice' => [ 'shape' => 'BooleanType', ], 'DeviceOnlyRememberedOnUserPrompt' => [ 'shape' => 'BooleanType', ], ], ], 'DeviceKeyExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'DeviceKeyType' => [ 'type' => 'string', 'max' => 55, 'min' => 1, 'pattern' => '[\\w-]+_[0-9a-f-]+', ], 'DeviceListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeviceType', ], ], 'DeviceNameType' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'DeviceRememberedStatusType' => [ 'type' => 'string', 'enum' => [ 'remembered', 'not_remembered', ], ], 'DeviceSecretVerifierConfigType' => [ 'type' => 'structure', 'members' => [ 'PasswordVerifier' => [ 'shape' => 'StringType', ], 'Salt' => [ 'shape' => 'StringType', ], ], ], 'DeviceType' => [ 'type' => 'structure', 'members' => [ 'DeviceKey' => [ 'shape' => 'DeviceKeyType', ], 'DeviceAttributes' => [ 'shape' => 'AttributeListType', ], 'DeviceCreateDate' => [ 'shape' => 'DateType', ], 'DeviceLastModifiedDate' => [ 'shape' => 'DateType', ], 'DeviceLastAuthenticatedDate' => [ 'shape' => 'DateType', ], ], ], 'Document' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'DomainDescriptionType' => [ 'type' => 'structure', 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'AWSAccountId' => [ 'shape' => 'AWSAccountIdType', ], 'Domain' => [ 'shape' => 'DomainType', ], 'S3Bucket' => [ 'shape' => 'S3BucketType', ], 'CloudFrontDistribution' => [ 'shape' => 'StringType', ], 'Version' => [ 'shape' => 'DomainVersionType', ], 'Status' => [ 'shape' => 'DomainStatusType', ], 'CustomDomainConfig' => [ 'shape' => 'CustomDomainConfigType', ], 'ManagedLoginVersion' => [ 'shape' => 'WrappedIntegerType', ], ], ], 'DomainStatusType' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'DELETING', 'UPDATING', 'ACTIVE', 'FAILED', ], ], 'DomainType' => [ 'type' => 'string', 'max' => 63, 'min' => 1, 'pattern' => '^[a-z0-9](?:[a-z0-9\\-]{0,61}[a-z0-9])?$', ], 'DomainVersionType' => [ 'type' => 'string', 'max' => 20, 'min' => 1, ], 'DuplicateProviderException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'EmailAddressType' => [ 'type' => 'string', 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}]+@[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}]+', ], 'EmailConfigurationType' => [ 'type' => 'structure', 'members' => [ 'SourceArn' => [ 'shape' => 'ArnType', ], 'ReplyToEmailAddress' => [ 'shape' => 'EmailAddressType', ], 'EmailSendingAccount' => [ 'shape' => 'EmailSendingAccountType', ], 'From' => [ 'shape' => 'StringType', ], 'ConfigurationSet' => [ 'shape' => 'SESConfigurationSet', ], ], ], 'EmailInviteMessageType' => [ 'type' => 'string', 'max' => 20000, 'min' => 6, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\s*]*', ], 'EmailMfaConfigType' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'EmailMfaMessageType', ], 'Subject' => [ 'shape' => 'EmailMfaSubjectType', ], ], ], 'EmailMfaMessageType' => [ 'type' => 'string', 'max' => 20000, 'min' => 6, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\s*]*\\{####\\}[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\s*]*', ], 'EmailMfaSettingsType' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'BooleanType', ], 'PreferredMfa' => [ 'shape' => 'BooleanType', ], ], ], 'EmailMfaSubjectType' => [ 'type' => 'string', 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\s]+', ], 'EmailNotificationBodyType' => [ 'type' => 'string', 'max' => 20000, 'min' => 6, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\s*]+', ], 'EmailNotificationSubjectType' => [ 'type' => 'string', 'max' => 140, 'min' => 1, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\s]+', ], 'EmailSendingAccountType' => [ 'type' => 'string', 'enum' => [ 'COGNITO_DEFAULT', 'DEVELOPER', ], ], 'EmailVerificationMessageByLinkType' => [ 'type' => 'string', 'max' => 20000, 'min' => 6, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\s*]*\\{##[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\s*]*##\\}[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\s*]*', ], 'EmailVerificationMessageType' => [ 'type' => 'string', 'max' => 20000, 'min' => 6, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\s*]*\\{####\\}[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\s*]*', ], 'EmailVerificationSubjectByLinkType' => [ 'type' => 'string', 'max' => 140, 'min' => 1, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\s]+', ], 'EmailVerificationSubjectType' => [ 'type' => 'string', 'max' => 140, 'min' => 1, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\s]+', ], 'EnableSoftwareTokenMFAException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'EventContextDataType' => [ 'type' => 'structure', 'members' => [ 'IpAddress' => [ 'shape' => 'StringType', ], 'DeviceName' => [ 'shape' => 'StringType', ], 'Timezone' => [ 'shape' => 'StringType', ], 'City' => [ 'shape' => 'StringType', ], 'Country' => [ 'shape' => 'StringType', ], ], ], 'EventFeedbackType' => [ 'type' => 'structure', 'required' => [ 'FeedbackValue', 'Provider', ], 'members' => [ 'FeedbackValue' => [ 'shape' => 'FeedbackValueType', ], 'Provider' => [ 'shape' => 'StringType', ], 'FeedbackDate' => [ 'shape' => 'DateType', ], ], ], 'EventFilterType' => [ 'type' => 'string', 'enum' => [ 'SIGN_IN', 'PASSWORD_CHANGE', 'SIGN_UP', ], ], 'EventFiltersType' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventFilterType', ], ], 'EventIdType' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '[\\w+-]+', ], 'EventResponseType' => [ 'type' => 'string', 'enum' => [ 'Pass', 'Fail', 'InProgress', ], ], 'EventRiskType' => [ 'type' => 'structure', 'members' => [ 'RiskDecision' => [ 'shape' => 'RiskDecisionType', ], 'RiskLevel' => [ 'shape' => 'RiskLevelType', ], 'CompromisedCredentialsDetected' => [ 'shape' => 'WrappedBooleanType', ], ], ], 'EventSourceName' => [ 'type' => 'string', 'enum' => [ 'userNotification', 'userAuthEvents', ], ], 'EventType' => [ 'type' => 'string', 'enum' => [ 'SignIn', 'SignUp', 'ForgotPassword', 'PasswordChange', 'ResendCode', ], ], 'ExpiredCodeException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'ExplicitAuthFlowsListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExplicitAuthFlowsType', ], ], 'ExplicitAuthFlowsType' => [ 'type' => 'string', 'enum' => [ 'ADMIN_NO_SRP_AUTH', 'CUSTOM_AUTH_FLOW_ONLY', 'USER_PASSWORD_AUTH', 'ALLOW_ADMIN_USER_PASSWORD_AUTH', 'ALLOW_CUSTOM_AUTH', 'ALLOW_USER_PASSWORD_AUTH', 'ALLOW_USER_SRP_AUTH', 'ALLOW_REFRESH_TOKEN_AUTH', 'ALLOW_USER_AUTH', ], ], 'FeatureType' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'FeatureUnavailableInTierException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'FeedbackValueType' => [ 'type' => 'string', 'enum' => [ 'Valid', 'Invalid', ], ], 'FirehoseConfigurationType' => [ 'type' => 'structure', 'members' => [ 'StreamArn' => [ 'shape' => 'ArnType', ], ], ], 'ForbiddenException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'ForceAliasCreation' => [ 'type' => 'boolean', ], 'ForgetDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceKey', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], 'DeviceKey' => [ 'shape' => 'DeviceKeyType', ], ], ], 'ForgotPasswordRequest' => [ 'type' => 'structure', 'required' => [ 'ClientId', 'Username', ], 'members' => [ 'ClientId' => [ 'shape' => 'ClientIdType', ], 'SecretHash' => [ 'shape' => 'SecretHashType', ], 'UserContextData' => [ 'shape' => 'UserContextDataType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'AnalyticsMetadata' => [ 'shape' => 'AnalyticsMetadataType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], ], ], 'ForgotPasswordResponse' => [ 'type' => 'structure', 'members' => [ 'CodeDeliveryDetails' => [ 'shape' => 'CodeDeliveryDetailsType', ], ], ], 'GenerateSecret' => [ 'type' => 'boolean', ], 'GetCSVHeaderRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], ], ], 'GetCSVHeaderResponse' => [ 'type' => 'structure', 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'CSVHeader' => [ 'shape' => 'ListOfStringTypes', ], ], ], 'GetDeviceRequest' => [ 'type' => 'structure', 'required' => [ 'DeviceKey', ], 'members' => [ 'DeviceKey' => [ 'shape' => 'DeviceKeyType', ], 'AccessToken' => [ 'shape' => 'TokenModelType', ], ], ], 'GetDeviceResponse' => [ 'type' => 'structure', 'required' => [ 'Device', ], 'members' => [ 'Device' => [ 'shape' => 'DeviceType', ], ], ], 'GetGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GroupName', 'UserPoolId', ], 'members' => [ 'GroupName' => [ 'shape' => 'GroupNameType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], ], ], 'GetGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'GroupType', ], ], ], 'GetIdentityProviderByIdentifierRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'IdpIdentifier', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'IdpIdentifier' => [ 'shape' => 'IdpIdentifierType', ], ], ], 'GetIdentityProviderByIdentifierResponse' => [ 'type' => 'structure', 'required' => [ 'IdentityProvider', ], 'members' => [ 'IdentityProvider' => [ 'shape' => 'IdentityProviderType', ], ], ], 'GetLogDeliveryConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], ], ], 'GetLogDeliveryConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'LogDeliveryConfiguration' => [ 'shape' => 'LogDeliveryConfigurationType', ], ], ], 'GetSigningCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], ], ], 'GetSigningCertificateResponse' => [ 'type' => 'structure', 'members' => [ 'Certificate' => [ 'shape' => 'StringType', ], ], ], 'GetTokensFromRefreshTokenRequest' => [ 'type' => 'structure', 'required' => [ 'RefreshToken', 'ClientId', ], 'members' => [ 'RefreshToken' => [ 'shape' => 'TokenModelType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], 'ClientSecret' => [ 'shape' => 'ClientSecretType', ], 'DeviceKey' => [ 'shape' => 'DeviceKeyType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], ], ], 'GetTokensFromRefreshTokenResponse' => [ 'type' => 'structure', 'members' => [ 'AuthenticationResult' => [ 'shape' => 'AuthenticationResultType', ], ], ], 'GetUICustomizationRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], ], ], 'GetUICustomizationResponse' => [ 'type' => 'structure', 'required' => [ 'UICustomization', ], 'members' => [ 'UICustomization' => [ 'shape' => 'UICustomizationType', ], ], ], 'GetUserAttributeVerificationCodeRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', 'AttributeName', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], 'AttributeName' => [ 'shape' => 'AttributeNameType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], ], ], 'GetUserAttributeVerificationCodeResponse' => [ 'type' => 'structure', 'members' => [ 'CodeDeliveryDetails' => [ 'shape' => 'CodeDeliveryDetailsType', ], ], ], 'GetUserAuthFactorsRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], ], ], 'GetUserAuthFactorsResponse' => [ 'type' => 'structure', 'required' => [ 'Username', ], 'members' => [ 'Username' => [ 'shape' => 'UsernameType', ], 'PreferredMfaSetting' => [ 'shape' => 'StringType', ], 'UserMFASettingList' => [ 'shape' => 'UserMFASettingListType', ], 'ConfiguredUserAuthFactors' => [ 'shape' => 'ConfiguredUserAuthFactorsListType', ], ], ], 'GetUserPoolMfaConfigRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], ], ], 'GetUserPoolMfaConfigResponse' => [ 'type' => 'structure', 'members' => [ 'SmsMfaConfiguration' => [ 'shape' => 'SmsMfaConfigType', ], 'SoftwareTokenMfaConfiguration' => [ 'shape' => 'SoftwareTokenMfaConfigType', ], 'EmailMfaConfiguration' => [ 'shape' => 'EmailMfaConfigType', ], 'MfaConfiguration' => [ 'shape' => 'UserPoolMfaType', ], 'WebAuthnConfiguration' => [ 'shape' => 'WebAuthnConfigurationType', ], ], ], 'GetUserRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], ], ], 'GetUserResponse' => [ 'type' => 'structure', 'required' => [ 'Username', 'UserAttributes', ], 'members' => [ 'Username' => [ 'shape' => 'UsernameType', ], 'UserAttributes' => [ 'shape' => 'AttributeListType', ], 'MFAOptions' => [ 'shape' => 'MFAOptionListType', ], 'PreferredMfaSetting' => [ 'shape' => 'StringType', ], 'UserMFASettingList' => [ 'shape' => 'UserMFASettingListType', ], ], ], 'GlobalSignOutRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], ], ], 'GlobalSignOutResponse' => [ 'type' => 'structure', 'members' => [], ], 'GroupExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'GroupListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'GroupType', ], ], 'GroupNameType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}]+', ], 'GroupType' => [ 'type' => 'structure', 'members' => [ 'GroupName' => [ 'shape' => 'GroupNameType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Description' => [ 'shape' => 'DescriptionType', ], 'RoleArn' => [ 'shape' => 'ArnType', ], 'Precedence' => [ 'shape' => 'PrecedenceType', ], 'LastModifiedDate' => [ 'shape' => 'DateType', ], 'CreationDate' => [ 'shape' => 'DateType', ], ], ], 'HexStringType' => [ 'type' => 'string', 'pattern' => '^[0-9a-fA-F]+$', ], 'HttpHeader' => [ 'type' => 'structure', 'members' => [ 'headerName' => [ 'shape' => 'StringType', ], 'headerValue' => [ 'shape' => 'StringType', ], ], ], 'HttpHeaderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HttpHeader', ], ], 'IdTokenValidityType' => [ 'type' => 'integer', 'max' => 86400, 'min' => 1, ], 'IdentityProviderType' => [ 'type' => 'structure', 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ProviderName' => [ 'shape' => 'ProviderNameType', ], 'ProviderType' => [ 'shape' => 'IdentityProviderTypeType', ], 'ProviderDetails' => [ 'shape' => 'ProviderDetailsType', ], 'AttributeMapping' => [ 'shape' => 'AttributeMappingType', ], 'IdpIdentifiers' => [ 'shape' => 'IdpIdentifiersListType', ], 'LastModifiedDate' => [ 'shape' => 'DateType', ], 'CreationDate' => [ 'shape' => 'DateType', ], ], ], 'IdentityProviderTypeType' => [ 'type' => 'string', 'enum' => [ 'SAML', 'Facebook', 'Google', 'LoginWithAmazon', 'SignInWithApple', 'OIDC', ], ], 'IdpIdentifierType' => [ 'type' => 'string', 'max' => 40, 'min' => 1, 'pattern' => '[\\w\\s+=.@-]+', ], 'IdpIdentifiersListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdpIdentifierType', ], 'max' => 50, 'min' => 0, ], 'ImageFileType' => [ 'type' => 'blob', 'max' => 131072, 'min' => 0, ], 'ImageUrlType' => [ 'type' => 'string', ], 'InitiateAuthRequest' => [ 'type' => 'structure', 'required' => [ 'AuthFlow', 'ClientId', ], 'members' => [ 'AuthFlow' => [ 'shape' => 'AuthFlowType', ], 'AuthParameters' => [ 'shape' => 'AuthParametersType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], 'AnalyticsMetadata' => [ 'shape' => 'AnalyticsMetadataType', ], 'UserContextData' => [ 'shape' => 'UserContextDataType', ], 'Session' => [ 'shape' => 'SessionType', ], ], ], 'InitiateAuthResponse' => [ 'type' => 'structure', 'members' => [ 'ChallengeName' => [ 'shape' => 'ChallengeNameType', ], 'Session' => [ 'shape' => 'SessionType', ], 'ChallengeParameters' => [ 'shape' => 'ChallengeParametersType', ], 'AuthenticationResult' => [ 'shape' => 'AuthenticationResultType', ], 'AvailableChallenges' => [ 'shape' => 'AvailableChallengeListType', ], ], ], 'IntegerType' => [ 'type' => 'integer', ], 'InternalErrorException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, 'fault' => true, ], 'InvalidEmailRoleAccessPolicyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'InvalidLambdaResponseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'InvalidOAuthFlowException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], 'reasonCode' => [ 'shape' => 'InvalidParameterExceptionReasonCodeType', ], ], 'exception' => true, ], 'InvalidParameterExceptionReasonCodeType' => [ 'type' => 'string', ], 'InvalidPasswordException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'InvalidSmsRoleAccessPolicyException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'InvalidSmsRoleTrustRelationshipException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'InvalidUserPoolConfigurationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'LambdaConfigType' => [ 'type' => 'structure', 'members' => [ 'PreSignUp' => [ 'shape' => 'ArnType', ], 'CustomMessage' => [ 'shape' => 'ArnType', ], 'PostConfirmation' => [ 'shape' => 'ArnType', ], 'PreAuthentication' => [ 'shape' => 'ArnType', ], 'PostAuthentication' => [ 'shape' => 'ArnType', ], 'DefineAuthChallenge' => [ 'shape' => 'ArnType', ], 'CreateAuthChallenge' => [ 'shape' => 'ArnType', ], 'VerifyAuthChallengeResponse' => [ 'shape' => 'ArnType', ], 'PreTokenGeneration' => [ 'shape' => 'ArnType', ], 'UserMigration' => [ 'shape' => 'ArnType', ], 'PreTokenGenerationConfig' => [ 'shape' => 'PreTokenGenerationVersionConfigType', ], 'CustomSMSSender' => [ 'shape' => 'CustomSMSLambdaVersionConfigType', ], 'CustomEmailSender' => [ 'shape' => 'CustomEmailLambdaVersionConfigType', ], 'KMSKeyID' => [ 'shape' => 'ArnType', ], ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'ListDevicesRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], 'Limit' => [ 'shape' => 'QueryLimitType', ], 'PaginationToken' => [ 'shape' => 'SearchPaginationTokenType', ], ], ], 'ListDevicesResponse' => [ 'type' => 'structure', 'members' => [ 'Devices' => [ 'shape' => 'DeviceListType', ], 'PaginationToken' => [ 'shape' => 'SearchPaginationTokenType', ], ], ], 'ListGroupsRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Limit' => [ 'shape' => 'QueryLimitType', ], 'NextToken' => [ 'shape' => 'PaginationKey', ], ], ], 'ListGroupsResponse' => [ 'type' => 'structure', 'members' => [ 'Groups' => [ 'shape' => 'GroupListType', ], 'NextToken' => [ 'shape' => 'PaginationKey', ], ], ], 'ListIdentityProvidersRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'MaxResults' => [ 'shape' => 'ListProvidersLimitType', ], 'NextToken' => [ 'shape' => 'PaginationKeyType', ], ], ], 'ListIdentityProvidersResponse' => [ 'type' => 'structure', 'required' => [ 'Providers', ], 'members' => [ 'Providers' => [ 'shape' => 'ProvidersListType', ], 'NextToken' => [ 'shape' => 'PaginationKeyType', ], ], ], 'ListOfStringTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringType', ], ], 'ListProvidersLimitType' => [ 'type' => 'integer', 'max' => 60, 'min' => 0, ], 'ListResourceServersLimitType' => [ 'type' => 'integer', 'max' => 50, 'min' => 1, ], 'ListResourceServersRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'MaxResults' => [ 'shape' => 'ListResourceServersLimitType', ], 'NextToken' => [ 'shape' => 'PaginationKeyType', ], ], ], 'ListResourceServersResponse' => [ 'type' => 'structure', 'required' => [ 'ResourceServers', ], 'members' => [ 'ResourceServers' => [ 'shape' => 'ResourceServersListType', ], 'NextToken' => [ 'shape' => 'PaginationKeyType', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ArnType', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'UserPoolTagsType', ], ], ], 'ListUserImportJobsRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'MaxResults', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'MaxResults' => [ 'shape' => 'PoolQueryLimitType', ], 'PaginationToken' => [ 'shape' => 'PaginationKeyType', ], ], ], 'ListUserImportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'UserImportJobs' => [ 'shape' => 'UserImportJobsListType', ], 'PaginationToken' => [ 'shape' => 'PaginationKeyType', ], ], ], 'ListUserPoolClientsRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'MaxResults' => [ 'shape' => 'QueryLimit', ], 'NextToken' => [ 'shape' => 'PaginationKey', ], ], ], 'ListUserPoolClientsResponse' => [ 'type' => 'structure', 'members' => [ 'UserPoolClients' => [ 'shape' => 'UserPoolClientListType', ], 'NextToken' => [ 'shape' => 'PaginationKey', ], ], ], 'ListUserPoolsRequest' => [ 'type' => 'structure', 'required' => [ 'MaxResults', ], 'members' => [ 'NextToken' => [ 'shape' => 'PaginationKeyType', ], 'MaxResults' => [ 'shape' => 'PoolQueryLimitType', ], ], ], 'ListUserPoolsResponse' => [ 'type' => 'structure', 'members' => [ 'UserPools' => [ 'shape' => 'UserPoolListType', ], 'NextToken' => [ 'shape' => 'PaginationKeyType', ], ], ], 'ListUsersInGroupRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'GroupName', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'GroupName' => [ 'shape' => 'GroupNameType', ], 'Limit' => [ 'shape' => 'QueryLimitType', ], 'NextToken' => [ 'shape' => 'PaginationKey', ], ], ], 'ListUsersInGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Users' => [ 'shape' => 'UsersListType', ], 'NextToken' => [ 'shape' => 'PaginationKey', ], ], ], 'ListUsersRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'AttributesToGet' => [ 'shape' => 'SearchedAttributeNamesListType', ], 'Limit' => [ 'shape' => 'QueryLimitType', ], 'PaginationToken' => [ 'shape' => 'SearchPaginationTokenType', ], 'Filter' => [ 'shape' => 'UserFilterType', ], ], ], 'ListUsersResponse' => [ 'type' => 'structure', 'members' => [ 'Users' => [ 'shape' => 'UsersListType', ], 'PaginationToken' => [ 'shape' => 'SearchPaginationTokenType', ], ], ], 'ListWebAuthnCredentialsRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], 'NextToken' => [ 'shape' => 'PaginationKey', ], 'MaxResults' => [ 'shape' => 'WebAuthnCredentialsQueryLimitType', ], ], ], 'ListWebAuthnCredentialsResponse' => [ 'type' => 'structure', 'required' => [ 'Credentials', ], 'members' => [ 'Credentials' => [ 'shape' => 'WebAuthnCredentialDescriptionListType', ], 'NextToken' => [ 'shape' => 'PaginationKey', ], ], ], 'LogConfigurationListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogConfigurationType', ], 'max' => 2, 'min' => 0, ], 'LogConfigurationType' => [ 'type' => 'structure', 'required' => [ 'LogLevel', 'EventSource', ], 'members' => [ 'LogLevel' => [ 'shape' => 'LogLevel', ], 'EventSource' => [ 'shape' => 'EventSourceName', ], 'CloudWatchLogsConfiguration' => [ 'shape' => 'CloudWatchLogsConfigurationType', ], 'S3Configuration' => [ 'shape' => 'S3ConfigurationType', ], 'FirehoseConfiguration' => [ 'shape' => 'FirehoseConfigurationType', ], ], ], 'LogDeliveryConfigurationType' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'LogConfigurations', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'LogConfigurations' => [ 'shape' => 'LogConfigurationListType', ], ], ], 'LogLevel' => [ 'type' => 'string', 'enum' => [ 'ERROR', 'INFO', ], ], 'LogoutURLsListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'RedirectUrlType', ], 'max' => 100, 'min' => 0, ], 'LongType' => [ 'type' => 'long', ], 'MFAMethodNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'MFAOptionListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'MFAOptionType', ], ], 'MFAOptionType' => [ 'type' => 'structure', 'members' => [ 'DeliveryMedium' => [ 'shape' => 'DeliveryMediumType', ], 'AttributeName' => [ 'shape' => 'AttributeNameType', ], ], ], 'ManagedLoginBrandingExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'ManagedLoginBrandingIdType' => [ 'type' => 'string', 'pattern' => '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[4][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}$', ], 'ManagedLoginBrandingType' => [ 'type' => 'structure', 'members' => [ 'ManagedLoginBrandingId' => [ 'shape' => 'ManagedLoginBrandingIdType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'UseCognitoProvidedValues' => [ 'shape' => 'BooleanType', ], 'Settings' => [ 'shape' => 'Document', ], 'Assets' => [ 'shape' => 'AssetListType', ], 'CreationDate' => [ 'shape' => 'DateType', ], 'LastModifiedDate' => [ 'shape' => 'DateType', ], ], ], 'MessageActionType' => [ 'type' => 'string', 'enum' => [ 'RESEND', 'SUPPRESS', ], ], 'MessageTemplateType' => [ 'type' => 'structure', 'members' => [ 'SMSMessage' => [ 'shape' => 'SmsInviteMessageType', ], 'EmailMessage' => [ 'shape' => 'EmailInviteMessageType', ], 'EmailSubject' => [ 'shape' => 'EmailVerificationSubjectType', ], ], ], 'MessageType' => [ 'type' => 'string', ], 'NewDeviceMetadataType' => [ 'type' => 'structure', 'members' => [ 'DeviceKey' => [ 'shape' => 'DeviceKeyType', ], 'DeviceGroupKey' => [ 'shape' => 'StringType', ], ], ], 'NotAuthorizedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'NotifyConfigurationType' => [ 'type' => 'structure', 'required' => [ 'SourceArn', ], 'members' => [ 'From' => [ 'shape' => 'StringType', ], 'ReplyTo' => [ 'shape' => 'StringType', ], 'SourceArn' => [ 'shape' => 'ArnType', ], 'BlockEmail' => [ 'shape' => 'NotifyEmailType', ], 'NoActionEmail' => [ 'shape' => 'NotifyEmailType', ], 'MfaEmail' => [ 'shape' => 'NotifyEmailType', ], ], ], 'NotifyEmailType' => [ 'type' => 'structure', 'required' => [ 'Subject', ], 'members' => [ 'Subject' => [ 'shape' => 'EmailNotificationSubjectType', ], 'HtmlBody' => [ 'shape' => 'EmailNotificationBodyType', ], 'TextBody' => [ 'shape' => 'EmailNotificationBodyType', ], ], ], 'NumberAttributeConstraintsType' => [ 'type' => 'structure', 'members' => [ 'MinValue' => [ 'shape' => 'StringType', ], 'MaxValue' => [ 'shape' => 'StringType', ], ], ], 'OAuthFlowType' => [ 'type' => 'string', 'enum' => [ 'code', 'implicit', 'client_credentials', ], ], 'OAuthFlowsType' => [ 'type' => 'list', 'member' => [ 'shape' => 'OAuthFlowType', ], 'max' => 3, 'min' => 0, ], 'PaginationKey' => [ 'type' => 'string', 'max' => 131072, 'min' => 1, 'pattern' => '[\\S]+', ], 'PaginationKeyType' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[\\S]+', ], 'PasswordHistoryPolicyViolationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'PasswordHistorySizeType' => [ 'type' => 'integer', 'max' => 24, 'min' => 0, ], 'PasswordPolicyMinLengthType' => [ 'type' => 'integer', 'max' => 99, 'min' => 6, ], 'PasswordPolicyType' => [ 'type' => 'structure', 'members' => [ 'MinimumLength' => [ 'shape' => 'PasswordPolicyMinLengthType', ], 'RequireUppercase' => [ 'shape' => 'BooleanType', ], 'RequireLowercase' => [ 'shape' => 'BooleanType', ], 'RequireNumbers' => [ 'shape' => 'BooleanType', ], 'RequireSymbols' => [ 'shape' => 'BooleanType', ], 'PasswordHistorySize' => [ 'shape' => 'PasswordHistorySizeType', ], 'TemporaryPasswordValidityDays' => [ 'shape' => 'TemporaryPasswordValidityDaysType', ], ], ], 'PasswordResetRequiredException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'PasswordType' => [ 'type' => 'string', 'max' => 256, 'pattern' => '[\\S]+', 'sensitive' => true, ], 'PoolQueryLimitType' => [ 'type' => 'integer', 'max' => 60, 'min' => 1, ], 'PreSignedUrlType' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'PreTokenGenerationLambdaVersionType' => [ 'type' => 'string', 'enum' => [ 'V1_0', 'V2_0', 'V3_0', ], ], 'PreTokenGenerationVersionConfigType' => [ 'type' => 'structure', 'required' => [ 'LambdaVersion', 'LambdaArn', ], 'members' => [ 'LambdaVersion' => [ 'shape' => 'PreTokenGenerationLambdaVersionType', ], 'LambdaArn' => [ 'shape' => 'ArnType', ], ], ], 'PrecedenceType' => [ 'type' => 'integer', 'min' => 0, ], 'PreconditionNotMetException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'PreventUserExistenceErrorTypes' => [ 'type' => 'string', 'enum' => [ 'LEGACY', 'ENABLED', ], ], 'PriorityType' => [ 'type' => 'integer', 'max' => 2, 'min' => 1, ], 'ProviderDescription' => [ 'type' => 'structure', 'members' => [ 'ProviderName' => [ 'shape' => 'ProviderNameType', ], 'ProviderType' => [ 'shape' => 'IdentityProviderTypeType', ], 'LastModifiedDate' => [ 'shape' => 'DateType', ], 'CreationDate' => [ 'shape' => 'DateType', ], ], ], 'ProviderDetailsType' => [ 'type' => 'map', 'key' => [ 'shape' => 'StringType', ], 'value' => [ 'shape' => 'StringType', ], ], 'ProviderNameType' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}\\p{Z}]+', ], 'ProviderNameTypeV2' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[^_\\p{Z}][\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}][^_\\p{Z}]+', ], 'ProviderUserIdentifierType' => [ 'type' => 'structure', 'members' => [ 'ProviderName' => [ 'shape' => 'ProviderNameType', ], 'ProviderAttributeName' => [ 'shape' => 'StringType', ], 'ProviderAttributeValue' => [ 'shape' => 'StringType', ], ], ], 'ProvidersListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProviderDescription', ], 'max' => 50, 'min' => 0, ], 'QueryLimit' => [ 'type' => 'integer', 'max' => 60, 'min' => 1, ], 'QueryLimitType' => [ 'type' => 'integer', 'max' => 60, 'min' => 0, ], 'RecoveryMechanismsType' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryOptionType', ], 'max' => 2, 'min' => 1, ], 'RecoveryOptionNameType' => [ 'type' => 'string', 'enum' => [ 'verified_email', 'verified_phone_number', 'admin_only', ], ], 'RecoveryOptionType' => [ 'type' => 'structure', 'required' => [ 'Priority', 'Name', ], 'members' => [ 'Priority' => [ 'shape' => 'PriorityType', ], 'Name' => [ 'shape' => 'RecoveryOptionNameType', ], ], ], 'RedirectUrlType' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}]+', ], 'RefreshTokenReuseException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'RefreshTokenRotationType' => [ 'type' => 'structure', 'required' => [ 'Feature', ], 'members' => [ 'Feature' => [ 'shape' => 'FeatureType', ], 'RetryGracePeriodSeconds' => [ 'shape' => 'RetryGracePeriodSecondsType', ], ], ], 'RefreshTokenValidityType' => [ 'type' => 'integer', 'max' => 315360000, 'min' => 0, ], 'RegionCodeType' => [ 'type' => 'string', 'max' => 32, 'min' => 5, ], 'RelyingPartyIdType' => [ 'type' => 'string', 'max' => 127, 'min' => 1, ], 'ResendConfirmationCodeRequest' => [ 'type' => 'structure', 'required' => [ 'ClientId', 'Username', ], 'members' => [ 'ClientId' => [ 'shape' => 'ClientIdType', ], 'SecretHash' => [ 'shape' => 'SecretHashType', ], 'UserContextData' => [ 'shape' => 'UserContextDataType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'AnalyticsMetadata' => [ 'shape' => 'AnalyticsMetadataType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], ], ], 'ResendConfirmationCodeResponse' => [ 'type' => 'structure', 'members' => [ 'CodeDeliveryDetails' => [ 'shape' => 'CodeDeliveryDetailsType', ], ], ], 'ResourceIdType' => [ 'type' => 'string', 'max' => 40, 'min' => 1, 'pattern' => '^[\\w\\- ]+$', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'ResourceServerIdentifierType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\x21\\x23-\\x5B\\x5D-\\x7E]+', ], 'ResourceServerNameType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\w\\s+=,.@-]+', ], 'ResourceServerScopeDescriptionType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ResourceServerScopeListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceServerScopeType', ], 'max' => 100, ], 'ResourceServerScopeNameType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\x21\\x23-\\x2E\\x30-\\x5B\\x5D-\\x7E]+', ], 'ResourceServerScopeType' => [ 'type' => 'structure', 'required' => [ 'ScopeName', 'ScopeDescription', ], 'members' => [ 'ScopeName' => [ 'shape' => 'ResourceServerScopeNameType', ], 'ScopeDescription' => [ 'shape' => 'ResourceServerScopeDescriptionType', ], ], ], 'ResourceServerType' => [ 'type' => 'structure', 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Identifier' => [ 'shape' => 'ResourceServerIdentifierType', ], 'Name' => [ 'shape' => 'ResourceServerNameType', ], 'Scopes' => [ 'shape' => 'ResourceServerScopeListType', ], ], ], 'ResourceServersListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceServerType', ], ], 'RespondToAuthChallengeRequest' => [ 'type' => 'structure', 'required' => [ 'ClientId', 'ChallengeName', ], 'members' => [ 'ClientId' => [ 'shape' => 'ClientIdType', ], 'ChallengeName' => [ 'shape' => 'ChallengeNameType', ], 'Session' => [ 'shape' => 'SessionType', ], 'ChallengeResponses' => [ 'shape' => 'ChallengeResponsesType', ], 'AnalyticsMetadata' => [ 'shape' => 'AnalyticsMetadataType', ], 'UserContextData' => [ 'shape' => 'UserContextDataType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], ], ], 'RespondToAuthChallengeResponse' => [ 'type' => 'structure', 'members' => [ 'ChallengeName' => [ 'shape' => 'ChallengeNameType', ], 'Session' => [ 'shape' => 'SessionType', ], 'ChallengeParameters' => [ 'shape' => 'ChallengeParametersType', ], 'AuthenticationResult' => [ 'shape' => 'AuthenticationResultType', ], ], ], 'RetryGracePeriodSecondsType' => [ 'type' => 'integer', 'max' => 60, 'min' => 0, ], 'RevokeTokenRequest' => [ 'type' => 'structure', 'required' => [ 'Token', 'ClientId', ], 'members' => [ 'Token' => [ 'shape' => 'TokenModelType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], 'ClientSecret' => [ 'shape' => 'ClientSecretType', ], ], ], 'RevokeTokenResponse' => [ 'type' => 'structure', 'members' => [], ], 'RiskConfigurationType' => [ 'type' => 'structure', 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], 'CompromisedCredentialsRiskConfiguration' => [ 'shape' => 'CompromisedCredentialsRiskConfigurationType', ], 'AccountTakeoverRiskConfiguration' => [ 'shape' => 'AccountTakeoverRiskConfigurationType', ], 'RiskExceptionConfiguration' => [ 'shape' => 'RiskExceptionConfigurationType', ], 'LastModifiedDate' => [ 'shape' => 'DateType', ], ], ], 'RiskDecisionType' => [ 'type' => 'string', 'enum' => [ 'NoRisk', 'AccountTakeover', 'Block', ], ], 'RiskExceptionConfigurationType' => [ 'type' => 'structure', 'members' => [ 'BlockedIPRangeList' => [ 'shape' => 'BlockedIPRangeListType', ], 'SkippedIPRangeList' => [ 'shape' => 'SkippedIPRangeListType', ], ], ], 'RiskLevelType' => [ 'type' => 'string', 'enum' => [ 'Low', 'Medium', 'High', ], ], 'S3ArnType' => [ 'type' => 'string', 'max' => 1024, 'min' => 3, 'pattern' => 'arn:[\\w+=/,.@-]+:[\\w+=/,.@-]+:::[\\w+=/,.@-]+(:[\\w+=/,.@-]+)?(:[\\w+=/,.@-]+)?', ], 'S3BucketType' => [ 'type' => 'string', 'max' => 1024, 'min' => 3, 'pattern' => '^[0-9A-Za-z\\.\\-_]*(?<!\\.)$', ], 'S3ConfigurationType' => [ 'type' => 'structure', 'members' => [ 'BucketArn' => [ 'shape' => 'S3ArnType', ], ], ], 'SESConfigurationSet' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[a-zA-Z0-9_-]+$', ], 'SMSMfaSettingsType' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'BooleanType', ], 'PreferredMfa' => [ 'shape' => 'BooleanType', ], ], ], 'SchemaAttributeType' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'CustomAttributeNameType', ], 'AttributeDataType' => [ 'shape' => 'AttributeDataType', ], 'DeveloperOnlyAttribute' => [ 'shape' => 'BooleanType', 'box' => true, ], 'Mutable' => [ 'shape' => 'BooleanType', 'box' => true, ], 'Required' => [ 'shape' => 'BooleanType', 'box' => true, ], 'NumberAttributeConstraints' => [ 'shape' => 'NumberAttributeConstraintsType', ], 'StringAttributeConstraints' => [ 'shape' => 'StringAttributeConstraintsType', ], ], ], 'SchemaAttributesListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaAttributeType', ], 'max' => 50, 'min' => 1, ], 'ScopeDoesNotExistException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'ScopeListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'ScopeType', ], 'max' => 50, ], 'ScopeType' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[\\x21\\x23-\\x5B\\x5D-\\x7E]+', ], 'SearchPaginationTokenType' => [ 'type' => 'string', 'min' => 1, 'pattern' => '[\\S]+', ], 'SearchedAttributeNamesListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeNameType', ], ], 'SecretCodeType' => [ 'type' => 'string', 'min' => 16, 'pattern' => '[A-Za-z0-9]+', 'sensitive' => true, ], 'SecretHashType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w+=/]+', 'sensitive' => true, ], 'SessionType' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'sensitive' => true, ], 'SetLogDeliveryConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'LogConfigurations', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'LogConfigurations' => [ 'shape' => 'LogConfigurationListType', ], ], ], 'SetLogDeliveryConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'LogDeliveryConfiguration' => [ 'shape' => 'LogDeliveryConfigurationType', ], ], ], 'SetRiskConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], 'CompromisedCredentialsRiskConfiguration' => [ 'shape' => 'CompromisedCredentialsRiskConfigurationType', ], 'AccountTakeoverRiskConfiguration' => [ 'shape' => 'AccountTakeoverRiskConfigurationType', ], 'RiskExceptionConfiguration' => [ 'shape' => 'RiskExceptionConfigurationType', ], ], ], 'SetRiskConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'RiskConfiguration', ], 'members' => [ 'RiskConfiguration' => [ 'shape' => 'RiskConfigurationType', ], ], ], 'SetUICustomizationRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], 'CSS' => [ 'shape' => 'CSSType', ], 'ImageFile' => [ 'shape' => 'ImageFileType', ], ], ], 'SetUICustomizationResponse' => [ 'type' => 'structure', 'required' => [ 'UICustomization', ], 'members' => [ 'UICustomization' => [ 'shape' => 'UICustomizationType', ], ], ], 'SetUserMFAPreferenceRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', ], 'members' => [ 'SMSMfaSettings' => [ 'shape' => 'SMSMfaSettingsType', ], 'SoftwareTokenMfaSettings' => [ 'shape' => 'SoftwareTokenMfaSettingsType', ], 'EmailMfaSettings' => [ 'shape' => 'EmailMfaSettingsType', ], 'AccessToken' => [ 'shape' => 'TokenModelType', ], ], ], 'SetUserMFAPreferenceResponse' => [ 'type' => 'structure', 'members' => [], ], 'SetUserPoolMfaConfigRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'SmsMfaConfiguration' => [ 'shape' => 'SmsMfaConfigType', ], 'SoftwareTokenMfaConfiguration' => [ 'shape' => 'SoftwareTokenMfaConfigType', ], 'EmailMfaConfiguration' => [ 'shape' => 'EmailMfaConfigType', ], 'MfaConfiguration' => [ 'shape' => 'UserPoolMfaType', ], 'WebAuthnConfiguration' => [ 'shape' => 'WebAuthnConfigurationType', ], ], ], 'SetUserPoolMfaConfigResponse' => [ 'type' => 'structure', 'members' => [ 'SmsMfaConfiguration' => [ 'shape' => 'SmsMfaConfigType', ], 'SoftwareTokenMfaConfiguration' => [ 'shape' => 'SoftwareTokenMfaConfigType', ], 'EmailMfaConfiguration' => [ 'shape' => 'EmailMfaConfigType', ], 'MfaConfiguration' => [ 'shape' => 'UserPoolMfaType', ], 'WebAuthnConfiguration' => [ 'shape' => 'WebAuthnConfigurationType', ], ], ], 'SetUserSettingsRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', 'MFAOptions', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], 'MFAOptions' => [ 'shape' => 'MFAOptionListType', ], ], ], 'SetUserSettingsResponse' => [ 'type' => 'structure', 'members' => [], ], 'SignInPolicyType' => [ 'type' => 'structure', 'members' => [ 'AllowedFirstAuthFactors' => [ 'shape' => 'AllowedFirstAuthFactorsListType', ], ], ], 'SignUpRequest' => [ 'type' => 'structure', 'required' => [ 'ClientId', 'Username', ], 'members' => [ 'ClientId' => [ 'shape' => 'ClientIdType', ], 'SecretHash' => [ 'shape' => 'SecretHashType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'Password' => [ 'shape' => 'PasswordType', ], 'UserAttributes' => [ 'shape' => 'AttributeListType', ], 'ValidationData' => [ 'shape' => 'AttributeListType', ], 'AnalyticsMetadata' => [ 'shape' => 'AnalyticsMetadataType', ], 'UserContextData' => [ 'shape' => 'UserContextDataType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], ], ], 'SignUpResponse' => [ 'type' => 'structure', 'required' => [ 'UserConfirmed', 'UserSub', ], 'members' => [ 'UserConfirmed' => [ 'shape' => 'BooleanType', ], 'CodeDeliveryDetails' => [ 'shape' => 'CodeDeliveryDetailsType', ], 'UserSub' => [ 'shape' => 'StringType', ], 'Session' => [ 'shape' => 'SessionType', ], ], ], 'SkippedIPRangeListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringType', ], 'max' => 200, ], 'SmsConfigurationType' => [ 'type' => 'structure', 'required' => [ 'SnsCallerArn', ], 'members' => [ 'SnsCallerArn' => [ 'shape' => 'ArnType', ], 'ExternalId' => [ 'shape' => 'StringType', ], 'SnsRegion' => [ 'shape' => 'RegionCodeType', ], ], ], 'SmsInviteMessageType' => [ 'type' => 'string', 'max' => 140, 'min' => 6, 'pattern' => '(?s).*', ], 'SmsMfaConfigType' => [ 'type' => 'structure', 'members' => [ 'SmsAuthenticationMessage' => [ 'shape' => 'SmsVerificationMessageType', ], 'SmsConfiguration' => [ 'shape' => 'SmsConfigurationType', ], ], ], 'SmsVerificationMessageType' => [ 'type' => 'string', 'max' => 140, 'min' => 6, 'pattern' => '.*\\{####\\}.*', ], 'SoftwareTokenMFANotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'SoftwareTokenMFAUserCodeType' => [ 'type' => 'string', 'max' => 6, 'min' => 6, 'pattern' => '[0-9]+', 'sensitive' => true, ], 'SoftwareTokenMfaConfigType' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'BooleanType', ], ], ], 'SoftwareTokenMfaSettingsType' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'BooleanType', ], 'PreferredMfa' => [ 'shape' => 'BooleanType', ], ], ], 'StartUserImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'JobId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'JobId' => [ 'shape' => 'UserImportJobIdType', ], ], ], 'StartUserImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'UserImportJob' => [ 'shape' => 'UserImportJobType', ], ], ], 'StartWebAuthnRegistrationRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], ], ], 'StartWebAuthnRegistrationResponse' => [ 'type' => 'structure', 'required' => [ 'CredentialCreationOptions', ], 'members' => [ 'CredentialCreationOptions' => [ 'shape' => 'Document', ], ], ], 'StatusType' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'StopUserImportJobRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'JobId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'JobId' => [ 'shape' => 'UserImportJobIdType', ], ], ], 'StopUserImportJobResponse' => [ 'type' => 'structure', 'members' => [ 'UserImportJob' => [ 'shape' => 'UserImportJobType', ], ], ], 'StringAttributeConstraintsType' => [ 'type' => 'structure', 'members' => [ 'MinLength' => [ 'shape' => 'StringType', ], 'MaxLength' => [ 'shape' => 'StringType', ], ], ], 'StringType' => [ 'type' => 'string', 'max' => 131072, 'min' => 0, ], 'SupportedIdentityProvidersListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProviderNameType', ], ], 'TagKeysType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'Tags', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ArnType', ], 'Tags' => [ 'shape' => 'UserPoolTagsType', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValueType' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TemporaryPasswordValidityDaysType' => [ 'type' => 'integer', 'max' => 365, 'min' => 0, ], 'TierChangeNotAllowedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'TimeUnitsType' => [ 'type' => 'string', 'enum' => [ 'seconds', 'minutes', 'hours', 'days', ], ], 'TokenModelType' => [ 'type' => 'string', 'pattern' => '[A-Za-z0-9-_=.]+', 'sensitive' => true, ], 'TokenValidityUnitsType' => [ 'type' => 'structure', 'members' => [ 'AccessToken' => [ 'shape' => 'TimeUnitsType', ], 'IdToken' => [ 'shape' => 'TimeUnitsType', ], 'RefreshToken' => [ 'shape' => 'TimeUnitsType', ], ], ], 'TooManyFailedAttemptsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'UICustomizationType' => [ 'type' => 'structure', 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], 'ImageUrl' => [ 'shape' => 'ImageUrlType', ], 'CSS' => [ 'shape' => 'CSSType', ], 'CSSVersion' => [ 'shape' => 'CSSVersionType', ], 'LastModifiedDate' => [ 'shape' => 'DateType', ], 'CreationDate' => [ 'shape' => 'DateType', ], ], ], 'UnauthorizedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'UnexpectedLambdaException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'UnsupportedIdentityProviderException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'UnsupportedOperationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'UnsupportedTokenTypeException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'UnsupportedUserStateException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceArn', 'TagKeys', ], 'members' => [ 'ResourceArn' => [ 'shape' => 'ArnType', ], 'TagKeys' => [ 'shape' => 'UserPoolTagsListType', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAuthEventFeedbackRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Username', 'EventId', 'FeedbackToken', 'FeedbackValue', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Username' => [ 'shape' => 'UsernameType', ], 'EventId' => [ 'shape' => 'EventIdType', ], 'FeedbackToken' => [ 'shape' => 'TokenModelType', ], 'FeedbackValue' => [ 'shape' => 'FeedbackValueType', ], ], ], 'UpdateAuthEventFeedbackResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateDeviceStatusRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', 'DeviceKey', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], 'DeviceKey' => [ 'shape' => 'DeviceKeyType', ], 'DeviceRememberedStatus' => [ 'shape' => 'DeviceRememberedStatusType', ], ], ], 'UpdateDeviceStatusResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateGroupRequest' => [ 'type' => 'structure', 'required' => [ 'GroupName', 'UserPoolId', ], 'members' => [ 'GroupName' => [ 'shape' => 'GroupNameType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Description' => [ 'shape' => 'DescriptionType', ], 'RoleArn' => [ 'shape' => 'ArnType', ], 'Precedence' => [ 'shape' => 'PrecedenceType', ], ], ], 'UpdateGroupResponse' => [ 'type' => 'structure', 'members' => [ 'Group' => [ 'shape' => 'GroupType', ], ], ], 'UpdateIdentityProviderRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'ProviderName', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ProviderName' => [ 'shape' => 'ProviderNameType', ], 'ProviderDetails' => [ 'shape' => 'ProviderDetailsType', ], 'AttributeMapping' => [ 'shape' => 'AttributeMappingType', ], 'IdpIdentifiers' => [ 'shape' => 'IdpIdentifiersListType', ], ], ], 'UpdateIdentityProviderResponse' => [ 'type' => 'structure', 'required' => [ 'IdentityProvider', ], 'members' => [ 'IdentityProvider' => [ 'shape' => 'IdentityProviderType', ], ], ], 'UpdateManagedLoginBrandingRequest' => [ 'type' => 'structure', 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ManagedLoginBrandingId' => [ 'shape' => 'ManagedLoginBrandingIdType', ], 'UseCognitoProvidedValues' => [ 'shape' => 'BooleanType', ], 'Settings' => [ 'shape' => 'Document', ], 'Assets' => [ 'shape' => 'AssetListType', ], ], ], 'UpdateManagedLoginBrandingResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedLoginBranding' => [ 'shape' => 'ManagedLoginBrandingType', ], ], ], 'UpdateResourceServerRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'Identifier', 'Name', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Identifier' => [ 'shape' => 'ResourceServerIdentifierType', ], 'Name' => [ 'shape' => 'ResourceServerNameType', ], 'Scopes' => [ 'shape' => 'ResourceServerScopeListType', ], ], ], 'UpdateResourceServerResponse' => [ 'type' => 'structure', 'required' => [ 'ResourceServer', ], 'members' => [ 'ResourceServer' => [ 'shape' => 'ResourceServerType', ], ], ], 'UpdateUserAttributesRequest' => [ 'type' => 'structure', 'required' => [ 'UserAttributes', 'AccessToken', ], 'members' => [ 'UserAttributes' => [ 'shape' => 'AttributeListType', ], 'AccessToken' => [ 'shape' => 'TokenModelType', ], 'ClientMetadata' => [ 'shape' => 'ClientMetadataType', ], ], ], 'UpdateUserAttributesResponse' => [ 'type' => 'structure', 'members' => [ 'CodeDeliveryDetailsList' => [ 'shape' => 'CodeDeliveryDetailsListType', ], ], ], 'UpdateUserPoolClientRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', 'ClientId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], 'ClientName' => [ 'shape' => 'ClientNameType', ], 'RefreshTokenValidity' => [ 'shape' => 'RefreshTokenValidityType', ], 'AccessTokenValidity' => [ 'shape' => 'AccessTokenValidityType', ], 'IdTokenValidity' => [ 'shape' => 'IdTokenValidityType', ], 'TokenValidityUnits' => [ 'shape' => 'TokenValidityUnitsType', ], 'ReadAttributes' => [ 'shape' => 'ClientPermissionListType', ], 'WriteAttributes' => [ 'shape' => 'ClientPermissionListType', ], 'ExplicitAuthFlows' => [ 'shape' => 'ExplicitAuthFlowsListType', ], 'SupportedIdentityProviders' => [ 'shape' => 'SupportedIdentityProvidersListType', ], 'CallbackURLs' => [ 'shape' => 'CallbackURLsListType', ], 'LogoutURLs' => [ 'shape' => 'LogoutURLsListType', ], 'DefaultRedirectURI' => [ 'shape' => 'RedirectUrlType', ], 'AllowedOAuthFlows' => [ 'shape' => 'OAuthFlowsType', ], 'AllowedOAuthScopes' => [ 'shape' => 'ScopeListType', ], 'AllowedOAuthFlowsUserPoolClient' => [ 'shape' => 'BooleanType', ], 'AnalyticsConfiguration' => [ 'shape' => 'AnalyticsConfigurationType', ], 'PreventUserExistenceErrors' => [ 'shape' => 'PreventUserExistenceErrorTypes', ], 'EnableTokenRevocation' => [ 'shape' => 'WrappedBooleanType', ], 'EnablePropagateAdditionalUserContextData' => [ 'shape' => 'WrappedBooleanType', ], 'AuthSessionValidity' => [ 'shape' => 'AuthSessionValidityType', ], 'RefreshTokenRotation' => [ 'shape' => 'RefreshTokenRotationType', ], ], ], 'UpdateUserPoolClientResponse' => [ 'type' => 'structure', 'members' => [ 'UserPoolClient' => [ 'shape' => 'UserPoolClientType', ], ], ], 'UpdateUserPoolDomainRequest' => [ 'type' => 'structure', 'required' => [ 'Domain', 'UserPoolId', ], 'members' => [ 'Domain' => [ 'shape' => 'DomainType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ManagedLoginVersion' => [ 'shape' => 'WrappedIntegerType', ], 'CustomDomainConfig' => [ 'shape' => 'CustomDomainConfigType', ], ], ], 'UpdateUserPoolDomainResponse' => [ 'type' => 'structure', 'members' => [ 'ManagedLoginVersion' => [ 'shape' => 'WrappedIntegerType', ], 'CloudFrontDomain' => [ 'shape' => 'DomainType', ], ], ], 'UpdateUserPoolRequest' => [ 'type' => 'structure', 'required' => [ 'UserPoolId', ], 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'Policies' => [ 'shape' => 'UserPoolPolicyType', ], 'DeletionProtection' => [ 'shape' => 'DeletionProtectionType', ], 'LambdaConfig' => [ 'shape' => 'LambdaConfigType', ], 'AutoVerifiedAttributes' => [ 'shape' => 'VerifiedAttributesListType', ], 'SmsVerificationMessage' => [ 'shape' => 'SmsVerificationMessageType', ], 'EmailVerificationMessage' => [ 'shape' => 'EmailVerificationMessageType', ], 'EmailVerificationSubject' => [ 'shape' => 'EmailVerificationSubjectType', ], 'VerificationMessageTemplate' => [ 'shape' => 'VerificationMessageTemplateType', ], 'SmsAuthenticationMessage' => [ 'shape' => 'SmsVerificationMessageType', ], 'UserAttributeUpdateSettings' => [ 'shape' => 'UserAttributeUpdateSettingsType', ], 'MfaConfiguration' => [ 'shape' => 'UserPoolMfaType', ], 'DeviceConfiguration' => [ 'shape' => 'DeviceConfigurationType', ], 'EmailConfiguration' => [ 'shape' => 'EmailConfigurationType', ], 'SmsConfiguration' => [ 'shape' => 'SmsConfigurationType', ], 'UserPoolTags' => [ 'shape' => 'UserPoolTagsType', ], 'AdminCreateUserConfig' => [ 'shape' => 'AdminCreateUserConfigType', ], 'UserPoolAddOns' => [ 'shape' => 'UserPoolAddOnsType', ], 'AccountRecoverySetting' => [ 'shape' => 'AccountRecoverySettingType', ], 'PoolName' => [ 'shape' => 'UserPoolNameType', ], 'UserPoolTier' => [ 'shape' => 'UserPoolTierType', ], ], ], 'UpdateUserPoolResponse' => [ 'type' => 'structure', 'members' => [], ], 'UserAttributeUpdateSettingsType' => [ 'type' => 'structure', 'members' => [ 'AttributesRequireVerificationBeforeUpdate' => [ 'shape' => 'AttributesRequireVerificationBeforeUpdateType', ], ], ], 'UserContextDataType' => [ 'type' => 'structure', 'members' => [ 'IpAddress' => [ 'shape' => 'StringType', ], 'EncodedData' => [ 'shape' => 'StringType', ], ], 'sensitive' => true, ], 'UserFilterType' => [ 'type' => 'string', 'max' => 256, ], 'UserImportInProgressException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'UserImportJobIdType' => [ 'type' => 'string', 'max' => 55, 'min' => 1, 'pattern' => 'import-[0-9a-zA-Z-]+', ], 'UserImportJobNameType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w\\s+=,.@-]+', ], 'UserImportJobStatusType' => [ 'type' => 'string', 'enum' => [ 'Created', 'Pending', 'InProgress', 'Stopping', 'Expired', 'Stopped', 'Failed', 'Succeeded', ], ], 'UserImportJobType' => [ 'type' => 'structure', 'members' => [ 'JobName' => [ 'shape' => 'UserImportJobNameType', ], 'JobId' => [ 'shape' => 'UserImportJobIdType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'PreSignedUrl' => [ 'shape' => 'PreSignedUrlType', ], 'CreationDate' => [ 'shape' => 'DateType', ], 'StartDate' => [ 'shape' => 'DateType', ], 'CompletionDate' => [ 'shape' => 'DateType', ], 'Status' => [ 'shape' => 'UserImportJobStatusType', ], 'CloudWatchLogsRoleArn' => [ 'shape' => 'ArnType', ], 'ImportedUsers' => [ 'shape' => 'LongType', ], 'SkippedUsers' => [ 'shape' => 'LongType', ], 'FailedUsers' => [ 'shape' => 'LongType', ], 'CompletionMessage' => [ 'shape' => 'CompletionMessageType', ], ], ], 'UserImportJobsListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserImportJobType', ], 'max' => 50, 'min' => 1, ], 'UserLambdaValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'UserMFASettingListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'StringType', ], ], 'UserNotConfirmedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'UserNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'UserPoolAddOnNotEnabledException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'UserPoolAddOnsType' => [ 'type' => 'structure', 'required' => [ 'AdvancedSecurityMode', ], 'members' => [ 'AdvancedSecurityMode' => [ 'shape' => 'AdvancedSecurityModeType', ], 'AdvancedSecurityAdditionalFlows' => [ 'shape' => 'AdvancedSecurityAdditionalFlowsType', ], ], ], 'UserPoolClientDescription' => [ 'type' => 'structure', 'members' => [ 'ClientId' => [ 'shape' => 'ClientIdType', ], 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientName' => [ 'shape' => 'ClientNameType', ], ], ], 'UserPoolClientListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserPoolClientDescription', ], ], 'UserPoolClientType' => [ 'type' => 'structure', 'members' => [ 'UserPoolId' => [ 'shape' => 'UserPoolIdType', ], 'ClientName' => [ 'shape' => 'ClientNameType', ], 'ClientId' => [ 'shape' => 'ClientIdType', ], 'ClientSecret' => [ 'shape' => 'ClientSecretType', ], 'LastModifiedDate' => [ 'shape' => 'DateType', ], 'CreationDate' => [ 'shape' => 'DateType', ], 'RefreshTokenValidity' => [ 'shape' => 'RefreshTokenValidityType', ], 'AccessTokenValidity' => [ 'shape' => 'AccessTokenValidityType', ], 'IdTokenValidity' => [ 'shape' => 'IdTokenValidityType', ], 'TokenValidityUnits' => [ 'shape' => 'TokenValidityUnitsType', ], 'ReadAttributes' => [ 'shape' => 'ClientPermissionListType', ], 'WriteAttributes' => [ 'shape' => 'ClientPermissionListType', ], 'ExplicitAuthFlows' => [ 'shape' => 'ExplicitAuthFlowsListType', ], 'SupportedIdentityProviders' => [ 'shape' => 'SupportedIdentityProvidersListType', ], 'CallbackURLs' => [ 'shape' => 'CallbackURLsListType', ], 'LogoutURLs' => [ 'shape' => 'LogoutURLsListType', ], 'DefaultRedirectURI' => [ 'shape' => 'RedirectUrlType', ], 'AllowedOAuthFlows' => [ 'shape' => 'OAuthFlowsType', ], 'AllowedOAuthScopes' => [ 'shape' => 'ScopeListType', ], 'AllowedOAuthFlowsUserPoolClient' => [ 'shape' => 'BooleanType', 'box' => true, ], 'AnalyticsConfiguration' => [ 'shape' => 'AnalyticsConfigurationType', ], 'PreventUserExistenceErrors' => [ 'shape' => 'PreventUserExistenceErrorTypes', ], 'EnableTokenRevocation' => [ 'shape' => 'WrappedBooleanType', ], 'EnablePropagateAdditionalUserContextData' => [ 'shape' => 'WrappedBooleanType', ], 'AuthSessionValidity' => [ 'shape' => 'AuthSessionValidityType', ], 'RefreshTokenRotation' => [ 'shape' => 'RefreshTokenRotationType', ], ], ], 'UserPoolDescriptionType' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'UserPoolIdType', ], 'Name' => [ 'shape' => 'UserPoolNameType', ], 'LambdaConfig' => [ 'shape' => 'LambdaConfigType', ], 'Status' => [ 'shape' => 'StatusType', 'deprecated' => true, 'deprecatedMessage' => 'This property is no longer available.', ], 'LastModifiedDate' => [ 'shape' => 'DateType', ], 'CreationDate' => [ 'shape' => 'DateType', ], ], ], 'UserPoolIdType' => [ 'type' => 'string', 'max' => 55, 'min' => 1, 'pattern' => '[\\w-]+_[0-9a-zA-Z]+', ], 'UserPoolListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserPoolDescriptionType', ], ], 'UserPoolMfaType' => [ 'type' => 'string', 'enum' => [ 'OFF', 'ON', 'OPTIONAL', ], ], 'UserPoolNameType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\w\\s+=,.@-]+', ], 'UserPoolPolicyType' => [ 'type' => 'structure', 'members' => [ 'PasswordPolicy' => [ 'shape' => 'PasswordPolicyType', ], 'SignInPolicy' => [ 'shape' => 'SignInPolicyType', ], ], ], 'UserPoolTaggingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'UserPoolTagsListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKeysType', ], ], 'UserPoolTagsType' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKeysType', ], 'value' => [ 'shape' => 'TagValueType', ], ], 'UserPoolTierType' => [ 'type' => 'string', 'enum' => [ 'LITE', 'ESSENTIALS', 'PLUS', ], ], 'UserPoolType' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'UserPoolIdType', ], 'Name' => [ 'shape' => 'UserPoolNameType', ], 'Policies' => [ 'shape' => 'UserPoolPolicyType', ], 'DeletionProtection' => [ 'shape' => 'DeletionProtectionType', ], 'LambdaConfig' => [ 'shape' => 'LambdaConfigType', ], 'Status' => [ 'shape' => 'StatusType', 'deprecated' => true, 'deprecatedMessage' => 'This property is no longer available.', ], 'LastModifiedDate' => [ 'shape' => 'DateType', ], 'CreationDate' => [ 'shape' => 'DateType', ], 'SchemaAttributes' => [ 'shape' => 'SchemaAttributesListType', ], 'AutoVerifiedAttributes' => [ 'shape' => 'VerifiedAttributesListType', ], 'AliasAttributes' => [ 'shape' => 'AliasAttributesListType', ], 'UsernameAttributes' => [ 'shape' => 'UsernameAttributesListType', ], 'SmsVerificationMessage' => [ 'shape' => 'SmsVerificationMessageType', ], 'EmailVerificationMessage' => [ 'shape' => 'EmailVerificationMessageType', ], 'EmailVerificationSubject' => [ 'shape' => 'EmailVerificationSubjectType', ], 'VerificationMessageTemplate' => [ 'shape' => 'VerificationMessageTemplateType', ], 'SmsAuthenticationMessage' => [ 'shape' => 'SmsVerificationMessageType', ], 'UserAttributeUpdateSettings' => [ 'shape' => 'UserAttributeUpdateSettingsType', ], 'MfaConfiguration' => [ 'shape' => 'UserPoolMfaType', ], 'DeviceConfiguration' => [ 'shape' => 'DeviceConfigurationType', ], 'EstimatedNumberOfUsers' => [ 'shape' => 'IntegerType', ], 'EmailConfiguration' => [ 'shape' => 'EmailConfigurationType', ], 'SmsConfiguration' => [ 'shape' => 'SmsConfigurationType', ], 'UserPoolTags' => [ 'shape' => 'UserPoolTagsType', ], 'SmsConfigurationFailure' => [ 'shape' => 'StringType', ], 'EmailConfigurationFailure' => [ 'shape' => 'StringType', ], 'Domain' => [ 'shape' => 'DomainType', ], 'CustomDomain' => [ 'shape' => 'DomainType', ], 'AdminCreateUserConfig' => [ 'shape' => 'AdminCreateUserConfigType', ], 'UserPoolAddOns' => [ 'shape' => 'UserPoolAddOnsType', ], 'UsernameConfiguration' => [ 'shape' => 'UsernameConfigurationType', ], 'Arn' => [ 'shape' => 'ArnType', ], 'AccountRecoverySetting' => [ 'shape' => 'AccountRecoverySettingType', ], 'UserPoolTier' => [ 'shape' => 'UserPoolTierType', ], ], ], 'UserStatusType' => [ 'type' => 'string', 'enum' => [ 'UNCONFIRMED', 'CONFIRMED', 'ARCHIVED', 'COMPROMISED', 'UNKNOWN', 'RESET_REQUIRED', 'FORCE_CHANGE_PASSWORD', 'EXTERNAL_PROVIDER', ], ], 'UserType' => [ 'type' => 'structure', 'members' => [ 'Username' => [ 'shape' => 'UsernameType', ], 'Attributes' => [ 'shape' => 'AttributeListType', ], 'UserCreateDate' => [ 'shape' => 'DateType', ], 'UserLastModifiedDate' => [ 'shape' => 'DateType', ], 'Enabled' => [ 'shape' => 'BooleanType', ], 'UserStatus' => [ 'shape' => 'UserStatusType', ], 'MFAOptions' => [ 'shape' => 'MFAOptionListType', ], ], ], 'UserVerificationType' => [ 'type' => 'string', 'enum' => [ 'required', 'preferred', ], ], 'UsernameAttributeType' => [ 'type' => 'string', 'enum' => [ 'phone_number', 'email', ], ], 'UsernameAttributesListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'UsernameAttributeType', ], ], 'UsernameConfigurationType' => [ 'type' => 'structure', 'required' => [ 'CaseSensitive', ], 'members' => [ 'CaseSensitive' => [ 'shape' => 'WrappedBooleanType', ], ], ], 'UsernameExistsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'UsernameType' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}]+', 'sensitive' => true, ], 'UsersListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'UserType', ], ], 'VerificationMessageTemplateType' => [ 'type' => 'structure', 'members' => [ 'SmsMessage' => [ 'shape' => 'SmsVerificationMessageType', ], 'EmailMessage' => [ 'shape' => 'EmailVerificationMessageType', ], 'EmailSubject' => [ 'shape' => 'EmailVerificationSubjectType', ], 'EmailMessageByLink' => [ 'shape' => 'EmailVerificationMessageByLinkType', ], 'EmailSubjectByLink' => [ 'shape' => 'EmailVerificationSubjectByLinkType', ], 'DefaultEmailOption' => [ 'shape' => 'DefaultEmailOptionType', ], ], ], 'VerifiedAttributeType' => [ 'type' => 'string', 'enum' => [ 'phone_number', 'email', ], ], 'VerifiedAttributesListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'VerifiedAttributeType', ], ], 'VerifySoftwareTokenRequest' => [ 'type' => 'structure', 'required' => [ 'UserCode', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], 'Session' => [ 'shape' => 'SessionType', ], 'UserCode' => [ 'shape' => 'SoftwareTokenMFAUserCodeType', ], 'FriendlyDeviceName' => [ 'shape' => 'StringType', ], ], ], 'VerifySoftwareTokenResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'VerifySoftwareTokenResponseType', ], 'Session' => [ 'shape' => 'SessionType', ], ], ], 'VerifySoftwareTokenResponseType' => [ 'type' => 'string', 'enum' => [ 'SUCCESS', 'ERROR', ], ], 'VerifyUserAttributeRequest' => [ 'type' => 'structure', 'required' => [ 'AccessToken', 'AttributeName', 'Code', ], 'members' => [ 'AccessToken' => [ 'shape' => 'TokenModelType', ], 'AttributeName' => [ 'shape' => 'AttributeNameType', ], 'Code' => [ 'shape' => 'ConfirmationCodeType', ], ], ], 'VerifyUserAttributeResponse' => [ 'type' => 'structure', 'members' => [], ], 'WebAuthnAuthenticatorAttachmentType' => [ 'type' => 'string', ], 'WebAuthnAuthenticatorTransportType' => [ 'type' => 'string', ], 'WebAuthnAuthenticatorTransportsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'WebAuthnAuthenticatorTransportType', ], ], 'WebAuthnChallengeNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'WebAuthnClientMismatchException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'WebAuthnConfigurationMissingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'WebAuthnConfigurationType' => [ 'type' => 'structure', 'members' => [ 'RelyingPartyId' => [ 'shape' => 'RelyingPartyIdType', ], 'UserVerification' => [ 'shape' => 'UserVerificationType', ], ], ], 'WebAuthnCredentialDescription' => [ 'type' => 'structure', 'required' => [ 'CredentialId', 'FriendlyCredentialName', 'RelyingPartyId', 'AuthenticatorTransports', 'CreatedAt', ], 'members' => [ 'CredentialId' => [ 'shape' => 'StringType', ], 'FriendlyCredentialName' => [ 'shape' => 'StringType', ], 'RelyingPartyId' => [ 'shape' => 'StringType', ], 'AuthenticatorAttachment' => [ 'shape' => 'WebAuthnAuthenticatorAttachmentType', ], 'AuthenticatorTransports' => [ 'shape' => 'WebAuthnAuthenticatorTransportsList', ], 'CreatedAt' => [ 'shape' => 'DateType', ], ], ], 'WebAuthnCredentialDescriptionListType' => [ 'type' => 'list', 'member' => [ 'shape' => 'WebAuthnCredentialDescription', ], ], 'WebAuthnCredentialNotSupportedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'WebAuthnCredentialsQueryLimitType' => [ 'type' => 'integer', 'max' => 20, 'min' => 0, ], 'WebAuthnNotEnabledException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'WebAuthnOriginNotAllowedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'WebAuthnRelyingPartyMismatchException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'MessageType', ], ], 'exception' => true, ], 'WrappedBooleanType' => [ 'type' => 'boolean', ], 'WrappedIntegerType' => [ 'type' => 'integer', ], ],];
