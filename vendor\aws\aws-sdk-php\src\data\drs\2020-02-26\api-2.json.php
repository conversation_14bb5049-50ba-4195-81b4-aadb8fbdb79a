<?php
// This file was auto-generated from sdk-root/src/data/drs/2020-02-26/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-02-26', 'endpointPrefix' => 'drs', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceAbbreviation' => 'drs', 'serviceFullName' => 'Elastic Disaster Recovery Service', 'serviceId' => 'drs', 'signatureVersion' => 'v4', 'signingName' => 'drs', 'uid' => 'drs-2020-02-26', ], 'operations' => [ 'AssociateSourceNetworkStack' => [ 'name' => 'AssociateSourceNetworkStack', 'http' => [ 'method' => 'POST', 'requestUri' => '/AssociateSourceNetworkStack', 'responseCode' => 202, ], 'input' => [ 'shape' => 'AssociateSourceNetworkStackRequest', ], 'output' => [ 'shape' => 'AssociateSourceNetworkStackResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'CreateExtendedSourceServer' => [ 'name' => 'CreateExtendedSourceServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateExtendedSourceServer', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateExtendedSourceServerRequest', ], 'output' => [ 'shape' => 'CreateExtendedSourceServerResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'CreateLaunchConfigurationTemplate' => [ 'name' => 'CreateLaunchConfigurationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateLaunchConfigurationTemplate', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateLaunchConfigurationTemplateRequest', ], 'output' => [ 'shape' => 'CreateLaunchConfigurationTemplateResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'CreateReplicationConfigurationTemplate' => [ 'name' => 'CreateReplicationConfigurationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateReplicationConfigurationTemplate', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateReplicationConfigurationTemplateRequest', ], 'output' => [ 'shape' => 'ReplicationConfigurationTemplate', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'CreateSourceNetwork' => [ 'name' => 'CreateSourceNetwork', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateSourceNetwork', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSourceNetworkRequest', ], 'output' => [ 'shape' => 'CreateSourceNetworkResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DeleteJob' => [ 'name' => 'DeleteJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteJob', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteJobRequest', ], 'output' => [ 'shape' => 'DeleteJobResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], 'idempotent' => true, ], 'DeleteLaunchAction' => [ 'name' => 'DeleteLaunchAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteLaunchAction', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteLaunchActionRequest', ], 'output' => [ 'shape' => 'DeleteLaunchActionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DeleteLaunchConfigurationTemplate' => [ 'name' => 'DeleteLaunchConfigurationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteLaunchConfigurationTemplate', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteLaunchConfigurationTemplateRequest', ], 'output' => [ 'shape' => 'DeleteLaunchConfigurationTemplateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], 'idempotent' => true, ], 'DeleteRecoveryInstance' => [ 'name' => 'DeleteRecoveryInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteRecoveryInstance', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteRecoveryInstanceRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DeleteReplicationConfigurationTemplate' => [ 'name' => 'DeleteReplicationConfigurationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteReplicationConfigurationTemplate', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteReplicationConfigurationTemplateRequest', ], 'output' => [ 'shape' => 'DeleteReplicationConfigurationTemplateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], 'idempotent' => true, ], 'DeleteSourceNetwork' => [ 'name' => 'DeleteSourceNetwork', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteSourceNetwork', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSourceNetworkRequest', ], 'output' => [ 'shape' => 'DeleteSourceNetworkResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], 'idempotent' => true, ], 'DeleteSourceServer' => [ 'name' => 'DeleteSourceServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteSourceServer', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSourceServerRequest', ], 'output' => [ 'shape' => 'DeleteSourceServerResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], 'idempotent' => true, ], 'DescribeJobLogItems' => [ 'name' => 'DescribeJobLogItems', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeJobLogItems', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeJobLogItemsRequest', ], 'output' => [ 'shape' => 'DescribeJobLogItemsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DescribeJobs' => [ 'name' => 'DescribeJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeJobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeJobsRequest', ], 'output' => [ 'shape' => 'DescribeJobsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DescribeLaunchConfigurationTemplates' => [ 'name' => 'DescribeLaunchConfigurationTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeLaunchConfigurationTemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeLaunchConfigurationTemplatesRequest', ], 'output' => [ 'shape' => 'DescribeLaunchConfigurationTemplatesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DescribeRecoveryInstances' => [ 'name' => 'DescribeRecoveryInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeRecoveryInstances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeRecoveryInstancesRequest', ], 'output' => [ 'shape' => 'DescribeRecoveryInstancesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DescribeRecoverySnapshots' => [ 'name' => 'DescribeRecoverySnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeRecoverySnapshots', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeRecoverySnapshotsRequest', ], 'output' => [ 'shape' => 'DescribeRecoverySnapshotsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DescribeReplicationConfigurationTemplates' => [ 'name' => 'DescribeReplicationConfigurationTemplates', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeReplicationConfigurationTemplates', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeReplicationConfigurationTemplatesRequest', ], 'output' => [ 'shape' => 'DescribeReplicationConfigurationTemplatesResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DescribeSourceNetworks' => [ 'name' => 'DescribeSourceNetworks', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeSourceNetworks', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSourceNetworksRequest', ], 'output' => [ 'shape' => 'DescribeSourceNetworksResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DescribeSourceServers' => [ 'name' => 'DescribeSourceServers', 'http' => [ 'method' => 'POST', 'requestUri' => '/DescribeSourceServers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSourceServersRequest', ], 'output' => [ 'shape' => 'DescribeSourceServersResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DisconnectRecoveryInstance' => [ 'name' => 'DisconnectRecoveryInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/DisconnectRecoveryInstance', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisconnectRecoveryInstanceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'DisconnectSourceServer' => [ 'name' => 'DisconnectSourceServer', 'http' => [ 'method' => 'POST', 'requestUri' => '/DisconnectSourceServer', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisconnectSourceServerRequest', ], 'output' => [ 'shape' => 'SourceServer', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'ExportSourceNetworkCfnTemplate' => [ 'name' => 'ExportSourceNetworkCfnTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/ExportSourceNetworkCfnTemplate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExportSourceNetworkCfnTemplateRequest', ], 'output' => [ 'shape' => 'ExportSourceNetworkCfnTemplateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'GetFailbackReplicationConfiguration' => [ 'name' => 'GetFailbackReplicationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetFailbackReplicationConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFailbackReplicationConfigurationRequest', ], 'output' => [ 'shape' => 'GetFailbackReplicationConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'GetLaunchConfiguration' => [ 'name' => 'GetLaunchConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetLaunchConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetLaunchConfigurationRequest', ], 'output' => [ 'shape' => 'LaunchConfiguration', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'GetReplicationConfiguration' => [ 'name' => 'GetReplicationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetReplicationConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetReplicationConfigurationRequest', ], 'output' => [ 'shape' => 'ReplicationConfiguration', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'InitializeService' => [ 'name' => 'InitializeService', 'http' => [ 'method' => 'POST', 'requestUri' => '/InitializeService', 'responseCode' => 204, ], 'input' => [ 'shape' => 'InitializeServiceRequest', ], 'output' => [ 'shape' => 'InitializeServiceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListExtensibleSourceServers' => [ 'name' => 'ListExtensibleSourceServers', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListExtensibleSourceServers', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListExtensibleSourceServersRequest', ], 'output' => [ 'shape' => 'ListExtensibleSourceServersResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'ListLaunchActions' => [ 'name' => 'ListLaunchActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListLaunchActions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListLaunchActionsRequest', ], 'output' => [ 'shape' => 'ListLaunchActionsResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'ListStagingAccounts' => [ 'name' => 'ListStagingAccounts', 'http' => [ 'method' => 'GET', 'requestUri' => '/ListStagingAccounts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStagingAccountsRequest', ], 'output' => [ 'shape' => 'ListStagingAccountsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutLaunchAction' => [ 'name' => 'PutLaunchAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/PutLaunchAction', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutLaunchActionRequest', ], 'output' => [ 'shape' => 'PutLaunchActionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'RetryDataReplication' => [ 'name' => 'RetryDataReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/RetryDataReplication', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RetryDataReplicationRequest', ], 'output' => [ 'shape' => 'SourceServer', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], 'deprecated' => true, 'deprecatedMessage' => 'WARNING: RetryDataReplication is deprecated', ], 'ReverseReplication' => [ 'name' => 'ReverseReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/ReverseReplication', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ReverseReplicationRequest', ], 'output' => [ 'shape' => 'ReverseReplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'StartFailbackLaunch' => [ 'name' => 'StartFailbackLaunch', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartFailbackLaunch', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartFailbackLaunchRequest', ], 'output' => [ 'shape' => 'StartFailbackLaunchResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'StartRecovery' => [ 'name' => 'StartRecovery', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartRecovery', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartRecoveryRequest', ], 'output' => [ 'shape' => 'StartRecoveryResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'StartReplication' => [ 'name' => 'StartReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartReplication', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartReplicationRequest', ], 'output' => [ 'shape' => 'StartReplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'StartSourceNetworkRecovery' => [ 'name' => 'StartSourceNetworkRecovery', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartSourceNetworkRecovery', 'responseCode' => 202, ], 'input' => [ 'shape' => 'StartSourceNetworkRecoveryRequest', ], 'output' => [ 'shape' => 'StartSourceNetworkRecoveryResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'StartSourceNetworkReplication' => [ 'name' => 'StartSourceNetworkReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartSourceNetworkReplication', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartSourceNetworkReplicationRequest', ], 'output' => [ 'shape' => 'StartSourceNetworkReplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'StopFailback' => [ 'name' => 'StopFailback', 'http' => [ 'method' => 'POST', 'requestUri' => '/StopFailback', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopFailbackRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'StopReplication' => [ 'name' => 'StopReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/StopReplication', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopReplicationRequest', ], 'output' => [ 'shape' => 'StopReplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'StopSourceNetworkReplication' => [ 'name' => 'StopSourceNetworkReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/StopSourceNetworkReplication', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopSourceNetworkReplicationRequest', ], 'output' => [ 'shape' => 'StopSourceNetworkReplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'TerminateRecoveryInstances' => [ 'name' => 'TerminateRecoveryInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/TerminateRecoveryInstances', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TerminateRecoveryInstancesRequest', ], 'output' => [ 'shape' => 'TerminateRecoveryInstancesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateFailbackReplicationConfiguration' => [ 'name' => 'UpdateFailbackReplicationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateFailbackReplicationConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFailbackReplicationConfigurationRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'UpdateLaunchConfiguration' => [ 'name' => 'UpdateLaunchConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateLaunchConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLaunchConfigurationRequest', ], 'output' => [ 'shape' => 'LaunchConfiguration', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], 'idempotent' => true, ], 'UpdateLaunchConfigurationTemplate' => [ 'name' => 'UpdateLaunchConfigurationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateLaunchConfigurationTemplate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateLaunchConfigurationTemplateRequest', ], 'output' => [ 'shape' => 'UpdateLaunchConfigurationTemplateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], 'UpdateReplicationConfiguration' => [ 'name' => 'UpdateReplicationConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateReplicationConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateReplicationConfigurationRequest', ], 'output' => [ 'shape' => 'ReplicationConfiguration', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], 'idempotent' => true, ], 'UpdateReplicationConfigurationTemplate' => [ 'name' => 'UpdateReplicationConfigurationTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateReplicationConfigurationTemplate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateReplicationConfigurationTemplateRequest', ], 'output' => [ 'shape' => 'ReplicationConfigurationTemplate', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'UninitializedAccountException', ], ], ], ], 'shapes' => [ 'ARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:.{16,2044}$', ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Account' => [ 'type' => 'structure', 'members' => [ 'accountID' => [ 'shape' => 'AccountID', ], ], ], 'AccountID' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '[0-9]{12,}', ], 'AccountIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountID', ], 'max' => 200, 'min' => 0, ], 'Accounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'Account', ], 'max' => 50, 'min' => 0, ], 'AgentVersion' => [ 'type' => 'string', 'pattern' => '^[0-9]{1,5}.[0-9]{1,5}.[0-9]{1,5}(.[0-9]{4}.[0-9]{3}.[0-9]{4})?$', ], 'AssociateSourceNetworkStackRequest' => [ 'type' => 'structure', 'required' => [ 'cfnStackName', 'sourceNetworkID', ], 'members' => [ 'cfnStackName' => [ 'shape' => 'CfnStackName', ], 'sourceNetworkID' => [ 'shape' => 'SourceNetworkID', ], ], ], 'AssociateSourceNetworkStackResponse' => [ 'type' => 'structure', 'members' => [ 'job' => [ 'shape' => 'Job', ], ], ], 'AwsAvailabilityZone' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^(us(-gov)?|ap|ca|cn|eu|sa|af|me|il)-(central|north|(north(?:east|west))|south|south(?:east|west)|east|west)-[0-9][a-z]$', ], 'AwsRegion' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^(us(-gov)?|ap|ca|cn|eu|sa|af|me|il)-(central|north|(north(?:east|west))|south|south(?:east|west)|east|west)-[0-9]$', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoundedString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'CPU' => [ 'type' => 'structure', 'members' => [ 'cores' => [ 'shape' => 'PositiveInteger', ], 'modelName' => [ 'shape' => 'BoundedString', ], ], ], 'CfnStackName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[a-zA-Z][-a-zA-Z0-9]*$', 'sensitive' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], 'resourceId' => [ 'shape' => 'LargeBoundedString', ], 'resourceType' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConversionMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'EbsSnapshot', ], 'value' => [ 'shape' => 'EbsSnapshot', ], ], 'ConversionProperties' => [ 'type' => 'structure', 'members' => [ 'dataTimestamp' => [ 'shape' => 'LargeBoundedString', ], 'forceUefi' => [ 'shape' => 'Boolean', ], 'rootVolumeName' => [ 'shape' => 'LargeBoundedString', ], 'volumeToConversionMap' => [ 'shape' => 'VolumeToConversionMap', ], 'volumeToProductCodes' => [ 'shape' => 'VolumeToProductCodes', ], 'volumeToVolumeSize' => [ 'shape' => 'VolumeToSizeMap', ], ], ], 'Cpus' => [ 'type' => 'list', 'member' => [ 'shape' => 'CPU', ], 'max' => 256, 'min' => 0, ], 'CreateExtendedSourceServerRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerArn', ], 'members' => [ 'sourceServerArn' => [ 'shape' => 'SourceServerARN', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'CreateExtendedSourceServerResponse' => [ 'type' => 'structure', 'members' => [ 'sourceServer' => [ 'shape' => 'SourceServer', ], ], ], 'CreateLaunchConfigurationTemplateRequest' => [ 'type' => 'structure', 'members' => [ 'copyPrivateIp' => [ 'shape' => 'Boolean', ], 'copyTags' => [ 'shape' => 'Boolean', ], 'exportBucketArn' => [ 'shape' => 'ARN', ], 'launchDisposition' => [ 'shape' => 'LaunchDisposition', ], 'launchIntoSourceInstance' => [ 'shape' => 'Boolean', ], 'licensing' => [ 'shape' => 'Licensing', ], 'postLaunchEnabled' => [ 'shape' => 'Boolean', ], 'tags' => [ 'shape' => 'TagsMap', ], 'targetInstanceTypeRightSizingMethod' => [ 'shape' => 'TargetInstanceTypeRightSizingMethod', ], ], ], 'CreateLaunchConfigurationTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'launchConfigurationTemplate' => [ 'shape' => 'LaunchConfigurationTemplate', ], ], ], 'CreateReplicationConfigurationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'associateDefaultSecurityGroup', 'bandwidthThrottling', 'createPublicIP', 'dataPlaneRouting', 'defaultLargeStagingDiskType', 'ebsEncryption', 'pitPolicy', 'replicationServerInstanceType', 'replicationServersSecurityGroupsIDs', 'stagingAreaSubnetId', 'stagingAreaTags', 'useDedicatedReplicationServer', ], 'members' => [ 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'autoReplicateNewDisks' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'pitPolicy' => [ 'shape' => 'PITPolicy', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'tags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'CreateSourceNetworkRequest' => [ 'type' => 'structure', 'required' => [ 'originAccountID', 'originRegion', 'vpcID', ], 'members' => [ 'originAccountID' => [ 'shape' => 'AccountID', ], 'originRegion' => [ 'shape' => 'AwsRegion', ], 'tags' => [ 'shape' => 'TagsMap', ], 'vpcID' => [ 'shape' => 'VpcID', ], ], ], 'CreateSourceNetworkResponse' => [ 'type' => 'structure', 'members' => [ 'sourceNetworkID' => [ 'shape' => 'SourceNetworkID', ], ], ], 'DataReplicationError' => [ 'type' => 'structure', 'members' => [ 'error' => [ 'shape' => 'DataReplicationErrorString', ], 'rawError' => [ 'shape' => 'LargeBoundedString', ], ], ], 'DataReplicationErrorString' => [ 'type' => 'string', 'enum' => [ 'AGENT_NOT_SEEN', 'SNAPSHOTS_FAILURE', 'NOT_CONVERGING', 'UNSTABLE_NETWORK', 'FAILED_TO_CREATE_SECURITY_GROUP', 'FAILED_TO_LAUNCH_REPLICATION_SERVER', 'FAILED_TO_BOOT_REPLICATION_SERVER', 'FAILED_TO_AUTHENTICATE_WITH_SERVICE', 'FAILED_TO_DOWNLOAD_REPLICATION_SOFTWARE', 'FAILED_TO_CREATE_STAGING_DISKS', 'FAILED_TO_ATTACH_STAGING_DISKS', 'FAILED_TO_PAIR_REPLICATION_SERVER_WITH_AGENT', 'FAILED_TO_CONNECT_AGENT_TO_REPLICATION_SERVER', 'FAILED_TO_START_DATA_TRANSFER', ], ], 'DataReplicationInfo' => [ 'type' => 'structure', 'members' => [ 'dataReplicationError' => [ 'shape' => 'DataReplicationError', ], 'dataReplicationInitiation' => [ 'shape' => 'DataReplicationInitiation', ], 'dataReplicationState' => [ 'shape' => 'DataReplicationState', ], 'etaDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'lagDuration' => [ 'shape' => 'ISO8601DurationString', ], 'replicatedDisks' => [ 'shape' => 'DataReplicationInfoReplicatedDisks', ], 'stagingAvailabilityZone' => [ 'shape' => 'AwsAvailabilityZone', ], 'stagingOutpostArn' => [ 'shape' => 'OutpostARN', ], ], ], 'DataReplicationInfoReplicatedDisk' => [ 'type' => 'structure', 'members' => [ 'backloggedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'deviceName' => [ 'shape' => 'BoundedString', ], 'replicatedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'rescannedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'totalStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'volumeStatus' => [ 'shape' => 'VolumeStatus', ], ], ], 'DataReplicationInfoReplicatedDisks' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataReplicationInfoReplicatedDisk', ], 'max' => 60, 'min' => 0, ], 'DataReplicationInitiation' => [ 'type' => 'structure', 'members' => [ 'nextAttemptDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'startDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'steps' => [ 'shape' => 'DataReplicationInitiationSteps', ], ], ], 'DataReplicationInitiationStep' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'DataReplicationInitiationStepName', ], 'status' => [ 'shape' => 'DataReplicationInitiationStepStatus', ], ], ], 'DataReplicationInitiationStepName' => [ 'type' => 'string', 'enum' => [ 'WAIT', 'CREATE_SECURITY_GROUP', 'LAUNCH_REPLICATION_SERVER', 'BOOT_REPLICATION_SERVER', 'AUTHENTICATE_WITH_SERVICE', 'DOWNLOAD_REPLICATION_SOFTWARE', 'CREATE_STAGING_DISKS', 'ATTACH_STAGING_DISKS', 'PAIR_REPLICATION_SERVER_WITH_AGENT', 'CONNECT_AGENT_TO_REPLICATION_SERVER', 'START_DATA_TRANSFER', ], ], 'DataReplicationInitiationStepStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', 'SKIPPED', ], ], 'DataReplicationInitiationSteps' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataReplicationInitiationStep', ], ], 'DataReplicationState' => [ 'type' => 'string', 'enum' => [ 'STOPPED', 'INITIATING', 'INITIAL_SYNC', 'BACKLOG', 'CREATING_SNAPSHOT', 'CONTINUOUS', 'PAUSED', 'RESCAN', 'STALLED', 'DISCONNECTED', ], ], 'DeleteJobRequest' => [ 'type' => 'structure', 'required' => [ 'jobID', ], 'members' => [ 'jobID' => [ 'shape' => 'JobID', ], ], ], 'DeleteJobResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLaunchActionRequest' => [ 'type' => 'structure', 'required' => [ 'actionId', 'resourceId', ], 'members' => [ 'actionId' => [ 'shape' => 'LaunchActionId', ], 'resourceId' => [ 'shape' => 'LaunchActionResourceId', ], ], ], 'DeleteLaunchActionResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteLaunchConfigurationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'launchConfigurationTemplateID', ], 'members' => [ 'launchConfigurationTemplateID' => [ 'shape' => 'LaunchConfigurationTemplateID', ], ], ], 'DeleteLaunchConfigurationTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteRecoveryInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceID', ], 'members' => [ 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], ], ], 'DeleteReplicationConfigurationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'replicationConfigurationTemplateID', ], 'members' => [ 'replicationConfigurationTemplateID' => [ 'shape' => 'ReplicationConfigurationTemplateID', ], ], ], 'DeleteReplicationConfigurationTemplateResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSourceNetworkRequest' => [ 'type' => 'structure', 'required' => [ 'sourceNetworkID', ], 'members' => [ 'sourceNetworkID' => [ 'shape' => 'SourceNetworkID', ], ], ], 'DeleteSourceNetworkResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSourceServerRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'DeleteSourceServerResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeJobLogItemsRequest' => [ 'type' => 'structure', 'required' => [ 'jobID', ], 'members' => [ 'jobID' => [ 'shape' => 'JobID', ], 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeJobLogItemsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'JobLogs', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeJobsRequest' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'DescribeJobsRequestFilters', ], 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeJobsRequestFilters' => [ 'type' => 'structure', 'members' => [ 'fromDate' => [ 'shape' => 'ISO8601DatetimeString', ], 'jobIDs' => [ 'shape' => 'DescribeJobsRequestFiltersJobIDs', ], 'toDate' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'DescribeJobsRequestFiltersJobIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobID', ], 'max' => 1000, 'min' => 0, ], 'DescribeJobsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'JobsList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeLaunchConfigurationTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'launchConfigurationTemplateIDs' => [ 'shape' => 'LaunchConfigurationTemplateIDs', ], 'maxResults' => [ 'shape' => 'MaxResultsType', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeLaunchConfigurationTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'LaunchConfigurationTemplates', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeRecoveryInstancesItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryInstance', ], ], 'DescribeRecoveryInstancesRequest' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'DescribeRecoveryInstancesRequestFilters', ], 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeRecoveryInstancesRequestFilters' => [ 'type' => 'structure', 'members' => [ 'recoveryInstanceIDs' => [ 'shape' => 'RecoveryInstanceIDs', ], 'sourceServerIDs' => [ 'shape' => 'SourceServerIDs', ], ], ], 'DescribeRecoveryInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'DescribeRecoveryInstancesItems', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeRecoverySnapshotsRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'filters' => [ 'shape' => 'DescribeRecoverySnapshotsRequestFilters', ], 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'order' => [ 'shape' => 'RecoverySnapshotsOrder', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'DescribeRecoverySnapshotsRequestFilters' => [ 'type' => 'structure', 'members' => [ 'fromDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'toDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'DescribeRecoverySnapshotsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'RecoverySnapshotsList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeReplicationConfigurationTemplatesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'replicationConfigurationTemplateIDs' => [ 'shape' => 'ReplicationConfigurationTemplateIDs', ], ], ], 'DescribeReplicationConfigurationTemplatesResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'ReplicationConfigurationTemplates', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeSourceNetworksRequest' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'DescribeSourceNetworksRequestFilters', ], 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeSourceNetworksRequestFilters' => [ 'type' => 'structure', 'members' => [ 'originAccountID' => [ 'shape' => 'AccountID', ], 'originRegion' => [ 'shape' => 'AwsRegion', ], 'sourceNetworkIDs' => [ 'shape' => 'DescribeSourceNetworksRequestFiltersIDs', ], ], ], 'DescribeSourceNetworksRequestFiltersIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceNetworkID', ], 'max' => 100, 'min' => 0, ], 'DescribeSourceNetworksResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'SourceNetworksList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeSourceServersRequest' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'DescribeSourceServersRequestFilters', ], 'maxResults' => [ 'shape' => 'StrictlyPositiveInteger', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeSourceServersRequestFilters' => [ 'type' => 'structure', 'members' => [ 'hardwareId' => [ 'shape' => 'BoundedString', ], 'sourceServerIDs' => [ 'shape' => 'DescribeSourceServersRequestFiltersIDs', ], 'stagingAccountIDs' => [ 'shape' => 'AccountIDs', ], ], ], 'DescribeSourceServersRequestFiltersIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceServerID', ], 'max' => 200, 'min' => 0, ], 'DescribeSourceServersResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'SourceServersList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DisconnectRecoveryInstanceRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceID', ], 'members' => [ 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], ], ], 'DisconnectSourceServerRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'Disk' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'PositiveInteger', ], 'deviceName' => [ 'shape' => 'BoundedString', ], ], ], 'Disks' => [ 'type' => 'list', 'member' => [ 'shape' => 'Disk', ], 'max' => 1000, 'min' => 0, ], 'EC2InstanceID' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^i-[0-9a-fA-F]{8,}$', ], 'EC2InstanceState' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'RUNNING', 'STOPPING', 'STOPPED', 'SHUTTING-DOWN', 'TERMINATED', 'NOT_FOUND', ], ], 'EC2InstanceType' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'EbsSnapshot' => [ 'type' => 'string', 'pattern' => '^snap-[0-9a-zA-Z]{17}$', ], 'EbsSnapshotsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EbsSnapshot', ], ], 'EbsVolumeID' => [ 'type' => 'string', 'max' => 19, 'min' => 10, 'pattern' => '^vol-([0-9a-fA-F]{8}|[0-9a-fA-F]{17})$', ], 'EventResourceData' => [ 'type' => 'structure', 'members' => [ 'sourceNetworkData' => [ 'shape' => 'SourceNetworkData', ], ], 'union' => true, ], 'ExportSourceNetworkCfnTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'sourceNetworkID', ], 'members' => [ 'sourceNetworkID' => [ 'shape' => 'SourceNetworkID', ], ], ], 'ExportSourceNetworkCfnTemplateResponse' => [ 'type' => 'structure', 'members' => [ 's3DestinationUrl' => [ 'shape' => 'LargeBoundedString', ], ], ], 'ExtensionStatus' => [ 'type' => 'string', 'enum' => [ 'EXTENDED', 'EXTENSION_ERROR', 'NOT_EXTENDED', ], ], 'FailbackLaunchType' => [ 'type' => 'string', 'enum' => [ 'RECOVERY', 'DRILL', ], ], 'FailbackReplicationError' => [ 'type' => 'string', 'enum' => [ 'AGENT_NOT_SEEN', 'FAILBACK_CLIENT_NOT_SEEN', 'NOT_CONVERGING', 'UNSTABLE_NETWORK', 'FAILED_TO_ESTABLISH_RECOVERY_INSTANCE_COMMUNICATION', 'FAILED_TO_DOWNLOAD_REPLICATION_SOFTWARE_TO_FAILBACK_CLIENT', 'FAILED_TO_CONFIGURE_REPLICATION_SOFTWARE', 'FAILED_TO_PAIR_AGENT_WITH_REPLICATION_SOFTWARE', 'FAILED_TO_ESTABLISH_AGENT_REPLICATOR_SOFTWARE_COMMUNICATION', 'FAILED_GETTING_REPLICATION_STATE', 'SNAPSHOTS_FAILURE', 'FAILED_TO_CREATE_SECURITY_GROUP', 'FAILED_TO_LAUNCH_REPLICATION_SERVER', 'FAILED_TO_BOOT_REPLICATION_SERVER', 'FAILED_TO_AUTHENTICATE_WITH_SERVICE', 'FAILED_TO_DOWNLOAD_REPLICATION_SOFTWARE', 'FAILED_TO_CREATE_STAGING_DISKS', 'FAILED_TO_ATTACH_STAGING_DISKS', 'FAILED_TO_PAIR_REPLICATION_SERVER_WITH_AGENT', 'FAILED_TO_CONNECT_AGENT_TO_REPLICATION_SERVER', 'FAILED_TO_START_DATA_TRANSFER', ], ], 'FailbackState' => [ 'type' => 'string', 'enum' => [ 'FAILBACK_NOT_STARTED', 'FAILBACK_IN_PROGRESS', 'FAILBACK_READY_FOR_LAUNCH', 'FAILBACK_COMPLETED', 'FAILBACK_ERROR', 'FAILBACK_NOT_READY_FOR_LAUNCH', 'FAILBACK_LAUNCH_STATE_NOT_AVAILABLE', ], ], 'FailureReason' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '^[0-9a-zA-Z ():/.,\'-_#*;]*$', ], 'GetFailbackReplicationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceID', ], 'members' => [ 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], ], ], 'GetFailbackReplicationConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceID', ], 'members' => [ 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'name' => [ 'shape' => 'BoundedString', ], 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], 'usePrivateIP' => [ 'shape' => 'Boolean', ], ], ], 'GetLaunchConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'GetReplicationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'IPsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BoundedString', ], ], 'ISO8601DatetimeString' => [ 'type' => 'string', 'max' => 32, 'min' => 19, 'pattern' => '^[1-9][0-9]*-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9](\\.[0-9]+)?Z$', ], 'ISO8601DurationString' => [ 'type' => 'string', 'max' => 64, 'min' => 1, ], 'IdentificationHints' => [ 'type' => 'structure', 'members' => [ 'awsInstanceID' => [ 'shape' => 'EC2InstanceID', ], 'fqdn' => [ 'shape' => 'BoundedString', ], 'hostname' => [ 'shape' => 'BoundedString', ], 'vmWareUuid' => [ 'shape' => 'BoundedString', ], ], ], 'InitializeServiceRequest' => [ 'type' => 'structure', 'members' => [], ], 'InitializeServiceResponse' => [ 'type' => 'structure', 'members' => [], ], 'InitiatedBy' => [ 'type' => 'string', 'enum' => [ 'START_RECOVERY', 'START_DRILL', 'FAILBACK', 'DIAGNOSTIC', 'TERMINATE_RECOVERY_INSTANCES', 'TARGET_ACCOUNT', 'CREATE_NETWORK_RECOVERY', 'UPDATE_NETWORK_RECOVERY', 'ASSOCIATE_NETWORK_RECOVERY', ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'LargeBoundedString', ], 'retryAfterSeconds' => [ 'shape' => 'PositiveInteger', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'Job' => [ 'type' => 'structure', 'required' => [ 'jobID', ], 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'creationDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'endDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'initiatedBy' => [ 'shape' => 'InitiatedBy', ], 'jobID' => [ 'shape' => 'JobID', ], 'participatingResources' => [ 'shape' => 'ParticipatingResources', ], 'participatingServers' => [ 'shape' => 'ParticipatingServers', ], 'status' => [ 'shape' => 'JobStatus', ], 'tags' => [ 'shape' => 'TagsMap', ], 'type' => [ 'shape' => 'JobType', ], ], ], 'JobID' => [ 'type' => 'string', 'max' => 24, 'min' => 24, 'pattern' => '^drsjob-[0-9a-zA-Z]{17}$', ], 'JobLog' => [ 'type' => 'structure', 'members' => [ 'event' => [ 'shape' => 'JobLogEvent', ], 'eventData' => [ 'shape' => 'JobLogEventData', ], 'logDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'JobLogEvent' => [ 'type' => 'string', 'enum' => [ 'JOB_START', 'SERVER_SKIPPED', 'CLEANUP_START', 'CLEANUP_END', 'CLEANUP_FAIL', 'SNAPSHOT_START', 'SNAPSHOT_END', 'SNAPSHOT_FAIL', 'USING_PREVIOUS_SNAPSHOT', 'USING_PREVIOUS_SNAPSHOT_FAILED', 'CONVERSION_START', 'CONVERSION_END', 'CONVERSION_FAIL', 'LAUNCH_START', 'LAUNCH_FAILED', 'JOB_CANCEL', 'JOB_END', 'DEPLOY_NETWORK_CONFIGURATION_START', 'DEPLOY_NETWORK_CONFIGURATION_END', 'DEPLOY_NETWORK_CONFIGURATION_FAILED', 'UPDATE_NETWORK_CONFIGURATION_START', 'UPDATE_NETWORK_CONFIGURATION_END', 'UPDATE_NETWORK_CONFIGURATION_FAILED', 'UPDATE_LAUNCH_TEMPLATE_START', 'UPDATE_LAUNCH_TEMPLATE_END', 'UPDATE_LAUNCH_TEMPLATE_FAILED', 'NETWORK_RECOVERY_FAIL', ], ], 'JobLogEventData' => [ 'type' => 'structure', 'members' => [ 'conversionProperties' => [ 'shape' => 'ConversionProperties', ], 'conversionServerID' => [ 'shape' => 'EC2InstanceID', ], 'eventResourceData' => [ 'shape' => 'EventResourceData', ], 'rawError' => [ 'shape' => 'LargeBoundedString', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'targetInstanceID' => [ 'shape' => 'EC2InstanceID', ], ], ], 'JobLogs' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobLog', ], ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'STARTED', 'COMPLETED', ], ], 'JobType' => [ 'type' => 'string', 'enum' => [ 'LAUNCH', 'TERMINATE', 'CREATE_CONVERTED_SNAPSHOT', ], ], 'JobsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Job', ], ], 'LargeBoundedString' => [ 'type' => 'string', 'max' => 65536, 'min' => 0, ], 'LastLaunchResult' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'PENDING', 'SUCCEEDED', 'FAILED', ], ], 'LastLaunchType' => [ 'type' => 'string', 'enum' => [ 'RECOVERY', 'DRILL', ], ], 'LaunchAction' => [ 'type' => 'structure', 'members' => [ 'actionCode' => [ 'shape' => 'SsmDocumentName', ], 'actionId' => [ 'shape' => 'LaunchActionId', ], 'actionVersion' => [ 'shape' => 'LaunchActionVersion', ], 'active' => [ 'shape' => 'Boolean', ], 'category' => [ 'shape' => 'LaunchActionCategory', ], 'description' => [ 'shape' => 'LaunchActionDescription', ], 'name' => [ 'shape' => 'LaunchActionName', ], 'optional' => [ 'shape' => 'Boolean', ], 'order' => [ 'shape' => 'LaunchActionOrder', ], 'parameters' => [ 'shape' => 'LaunchActionParameters', ], 'type' => [ 'shape' => 'LaunchActionType', ], ], ], 'LaunchActionCategory' => [ 'type' => 'string', 'enum' => [ 'MONITORING', 'VALIDATION', 'CONFIGURATION', 'SECURITY', 'OTHER', ], ], 'LaunchActionDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '^[0-9a-zA-Z ():/.,\'-_#*; ]*$', ], 'LaunchActionId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$', ], 'LaunchActionIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchActionId', ], 'max' => 100, 'min' => 0, ], 'LaunchActionName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[A-Za-z0-9][A-Za-z0-9 /_-]*$', ], 'LaunchActionOrder' => [ 'type' => 'integer', 'box' => true, 'max' => 10000, 'min' => 2, ], 'LaunchActionParameter' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'LaunchActionParameterType', ], 'value' => [ 'shape' => 'LaunchActionParameterValue', ], ], ], 'LaunchActionParameterName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => '^([A-Za-z0-9])+$', ], 'LaunchActionParameterType' => [ 'type' => 'string', 'enum' => [ 'SSM_STORE', 'DYNAMIC', ], ], 'LaunchActionParameterValue' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => '^[A-Za-z0-9.-]+$', ], 'LaunchActionParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'LaunchActionParameterName', ], 'value' => [ 'shape' => 'LaunchActionParameter', ], 'max' => 20, 'min' => 0, ], 'LaunchActionResourceId' => [ 'type' => 'string', 'pattern' => '^(s-[0-9a-zA-Z]{17}$|lct-[0-9a-zA-Z]{17})$', ], 'LaunchActionRun' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'LaunchAction', ], 'failureReason' => [ 'shape' => 'FailureReason', ], 'runId' => [ 'shape' => 'LaunchActionRunId', ], 'status' => [ 'shape' => 'LaunchActionRunStatus', ], ], ], 'LaunchActionRunId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$', ], 'LaunchActionRunStatus' => [ 'type' => 'string', 'enum' => [ 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', ], ], 'LaunchActionRuns' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchActionRun', ], ], 'LaunchActionType' => [ 'type' => 'string', 'enum' => [ 'SSM_AUTOMATION', 'SSM_COMMAND', ], ], 'LaunchActionVersion' => [ 'type' => 'string', 'max' => 10, 'min' => 1, 'pattern' => '^(\\$DEFAULT|\\$LATEST|[0-9]+)$', ], 'LaunchActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchAction', ], 'max' => 200, 'min' => 0, ], 'LaunchActionsRequestFilters' => [ 'type' => 'structure', 'members' => [ 'actionIds' => [ 'shape' => 'LaunchActionIds', ], ], ], 'LaunchActionsStatus' => [ 'type' => 'structure', 'members' => [ 'runs' => [ 'shape' => 'LaunchActionRuns', ], 'ssmAgentDiscoveryDatetime' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'LaunchConfiguration' => [ 'type' => 'structure', 'members' => [ 'copyPrivateIp' => [ 'shape' => 'Boolean', ], 'copyTags' => [ 'shape' => 'Boolean', ], 'ec2LaunchTemplateID' => [ 'shape' => 'BoundedString', ], 'launchDisposition' => [ 'shape' => 'LaunchDisposition', ], 'launchIntoInstanceProperties' => [ 'shape' => 'LaunchIntoInstanceProperties', ], 'licensing' => [ 'shape' => 'Licensing', ], 'name' => [ 'shape' => 'SmallBoundedString', ], 'postLaunchEnabled' => [ 'shape' => 'Boolean', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'targetInstanceTypeRightSizingMethod' => [ 'shape' => 'TargetInstanceTypeRightSizingMethod', ], ], ], 'LaunchConfigurationTemplate' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'copyPrivateIp' => [ 'shape' => 'Boolean', ], 'copyTags' => [ 'shape' => 'Boolean', ], 'exportBucketArn' => [ 'shape' => 'ARN', ], 'launchConfigurationTemplateID' => [ 'shape' => 'LaunchConfigurationTemplateID', ], 'launchDisposition' => [ 'shape' => 'LaunchDisposition', ], 'launchIntoSourceInstance' => [ 'shape' => 'Boolean', ], 'licensing' => [ 'shape' => 'Licensing', ], 'postLaunchEnabled' => [ 'shape' => 'Boolean', ], 'tags' => [ 'shape' => 'TagsMap', ], 'targetInstanceTypeRightSizingMethod' => [ 'shape' => 'TargetInstanceTypeRightSizingMethod', ], ], ], 'LaunchConfigurationTemplateID' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^lct-[0-9a-zA-Z]{17}$', ], 'LaunchConfigurationTemplateIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchConfigurationTemplateID', ], 'max' => 1, 'min' => 0, ], 'LaunchConfigurationTemplates' => [ 'type' => 'list', 'member' => [ 'shape' => 'LaunchConfigurationTemplate', ], 'max' => 200, 'min' => 0, ], 'LaunchDisposition' => [ 'type' => 'string', 'enum' => [ 'STOPPED', 'STARTED', ], ], 'LaunchIntoInstanceProperties' => [ 'type' => 'structure', 'members' => [ 'launchIntoEC2InstanceID' => [ 'shape' => 'EC2InstanceID', ], ], ], 'LaunchStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'LAUNCHED', 'FAILED', 'TERMINATED', ], ], 'Licensing' => [ 'type' => 'structure', 'members' => [ 'osByol' => [ 'shape' => 'Boolean', ], ], ], 'LifeCycle' => [ 'type' => 'structure', 'members' => [ 'addedToServiceDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'elapsedReplicationDuration' => [ 'shape' => 'ISO8601DurationString', ], 'firstByteDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'lastLaunch' => [ 'shape' => 'LifeCycleLastLaunch', ], 'lastSeenByServiceDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'LifeCycleLastLaunch' => [ 'type' => 'structure', 'members' => [ 'initiated' => [ 'shape' => 'LifeCycleLastLaunchInitiated', ], 'status' => [ 'shape' => 'LaunchStatus', ], ], ], 'LifeCycleLastLaunchInitiated' => [ 'type' => 'structure', 'members' => [ 'apiCallDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'jobID' => [ 'shape' => 'JobID', ], 'type' => [ 'shape' => 'LastLaunchType', ], ], ], 'ListExtensibleSourceServersRequest' => [ 'type' => 'structure', 'required' => [ 'stagingAccountID', ], 'members' => [ 'maxResults' => [ 'shape' => 'MaxResultsReplicatingSourceServers', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'stagingAccountID' => [ 'shape' => 'AccountID', ], ], ], 'ListExtensibleSourceServersResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'StagingSourceServersList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListLaunchActionsRequest' => [ 'type' => 'structure', 'required' => [ 'resourceId', ], 'members' => [ 'filters' => [ 'shape' => 'LaunchActionsRequestFilters', ], 'maxResults' => [ 'shape' => 'MaxResultsType', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'resourceId' => [ 'shape' => 'LaunchActionResourceId', ], ], ], 'ListLaunchActionsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'LaunchActions', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListStagingAccountsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListStagingAccountsRequestMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'PaginationToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListStagingAccountsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'ListStagingAccountsResponse' => [ 'type' => 'structure', 'members' => [ 'accounts' => [ 'shape' => 'Accounts', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'MaxResultsReplicatingSourceServers' => [ 'type' => 'integer', 'box' => true, 'max' => 300, 'min' => 1, ], 'MaxResultsType' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 1, ], 'NetworkInterface' => [ 'type' => 'structure', 'members' => [ 'ips' => [ 'shape' => 'IPsList', ], 'isPrimary' => [ 'shape' => 'Boolean', ], 'macAddress' => [ 'shape' => 'BoundedString', ], ], ], 'NetworkInterfaces' => [ 'type' => 'list', 'member' => [ 'shape' => 'NetworkInterface', ], 'max' => 32, 'min' => 0, ], 'OS' => [ 'type' => 'structure', 'members' => [ 'fullString' => [ 'shape' => 'BoundedString', ], ], ], 'OriginEnvironment' => [ 'type' => 'string', 'enum' => [ 'ON_PREMISES', 'AWS', ], ], 'OutpostARN' => [ 'type' => 'string', 'max' => 255, 'min' => 20, 'pattern' => '^arn:aws([a-z-]+)?:outposts:[a-z\\d-]+:\\d{12}:outpost/op-[a-f0-9]{17}$', ], 'PITPolicy' => [ 'type' => 'list', 'member' => [ 'shape' => 'PITPolicyRule', ], 'max' => 10, 'min' => 1, ], 'PITPolicyRule' => [ 'type' => 'structure', 'required' => [ 'interval', 'retentionDuration', 'units', ], 'members' => [ 'enabled' => [ 'shape' => 'Boolean', ], 'interval' => [ 'shape' => 'StrictlyPositiveInteger', ], 'retentionDuration' => [ 'shape' => 'StrictlyPositiveInteger', ], 'ruleID' => [ 'shape' => 'PositiveInteger', ], 'units' => [ 'shape' => 'PITPolicyRuleUnits', ], ], ], 'PITPolicyRuleUnits' => [ 'type' => 'string', 'enum' => [ 'MINUTE', 'HOUR', 'DAY', ], ], 'PaginationToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'ParticipatingResource' => [ 'type' => 'structure', 'members' => [ 'launchStatus' => [ 'shape' => 'LaunchStatus', ], 'participatingResourceID' => [ 'shape' => 'ParticipatingResourceID', ], ], ], 'ParticipatingResourceID' => [ 'type' => 'structure', 'members' => [ 'sourceNetworkID' => [ 'shape' => 'SourceNetworkID', ], ], 'union' => true, ], 'ParticipatingResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipatingResource', ], ], 'ParticipatingServer' => [ 'type' => 'structure', 'members' => [ 'launchActionsStatus' => [ 'shape' => 'LaunchActionsStatus', ], 'launchStatus' => [ 'shape' => 'LaunchStatus', ], 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'ParticipatingServers' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipatingServer', ], ], 'PositiveInteger' => [ 'type' => 'long', 'min' => 0, ], 'ProductCode' => [ 'type' => 'structure', 'members' => [ 'productCodeId' => [ 'shape' => 'ProductCodeId', ], 'productCodeMode' => [ 'shape' => 'ProductCodeMode', ], ], ], 'ProductCodeId' => [ 'type' => 'string', 'max' => 25, 'min' => 25, 'pattern' => '^([A-Za-z0-9])+$', ], 'ProductCodeMode' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'ProductCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProductCode', ], ], 'PutLaunchActionRequest' => [ 'type' => 'structure', 'required' => [ 'actionCode', 'actionId', 'actionVersion', 'active', 'category', 'description', 'name', 'optional', 'order', 'resourceId', ], 'members' => [ 'actionCode' => [ 'shape' => 'SsmDocumentName', ], 'actionId' => [ 'shape' => 'LaunchActionId', ], 'actionVersion' => [ 'shape' => 'LaunchActionVersion', ], 'active' => [ 'shape' => 'Boolean', ], 'category' => [ 'shape' => 'LaunchActionCategory', ], 'description' => [ 'shape' => 'LaunchActionDescription', ], 'name' => [ 'shape' => 'LaunchActionName', ], 'optional' => [ 'shape' => 'Boolean', ], 'order' => [ 'shape' => 'LaunchActionOrder', ], 'parameters' => [ 'shape' => 'LaunchActionParameters', ], 'resourceId' => [ 'shape' => 'LaunchActionResourceId', ], ], ], 'PutLaunchActionResponse' => [ 'type' => 'structure', 'members' => [ 'actionCode' => [ 'shape' => 'SsmDocumentName', ], 'actionId' => [ 'shape' => 'LaunchActionId', ], 'actionVersion' => [ 'shape' => 'LaunchActionVersion', ], 'active' => [ 'shape' => 'Boolean', ], 'category' => [ 'shape' => 'LaunchActionCategory', ], 'description' => [ 'shape' => 'LaunchActionDescription', ], 'name' => [ 'shape' => 'LaunchActionName', ], 'optional' => [ 'shape' => 'Boolean', ], 'order' => [ 'shape' => 'LaunchActionOrder', ], 'parameters' => [ 'shape' => 'LaunchActionParameters', ], 'resourceId' => [ 'shape' => 'LaunchActionResourceId', ], 'type' => [ 'shape' => 'LaunchActionType', ], ], ], 'RecoveryInstance' => [ 'type' => 'structure', 'members' => [ 'agentVersion' => [ 'shape' => 'AgentVersion', ], 'arn' => [ 'shape' => 'ARN', ], 'dataReplicationInfo' => [ 'shape' => 'RecoveryInstanceDataReplicationInfo', ], 'ec2InstanceID' => [ 'shape' => 'EC2InstanceID', ], 'ec2InstanceState' => [ 'shape' => 'EC2InstanceState', ], 'failback' => [ 'shape' => 'RecoveryInstanceFailback', ], 'isDrill' => [ 'shape' => 'Boolean', ], 'jobID' => [ 'shape' => 'JobID', ], 'originAvailabilityZone' => [ 'shape' => 'AwsAvailabilityZone', ], 'originEnvironment' => [ 'shape' => 'OriginEnvironment', ], 'pointInTimeSnapshotDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], 'recoveryInstanceProperties' => [ 'shape' => 'RecoveryInstanceProperties', ], 'sourceOutpostArn' => [ 'shape' => 'OutpostARN', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'RecoveryInstanceDataReplicationError' => [ 'type' => 'structure', 'members' => [ 'error' => [ 'shape' => 'FailbackReplicationError', ], 'rawError' => [ 'shape' => 'LargeBoundedString', ], ], ], 'RecoveryInstanceDataReplicationInfo' => [ 'type' => 'structure', 'members' => [ 'dataReplicationError' => [ 'shape' => 'RecoveryInstanceDataReplicationError', ], 'dataReplicationInitiation' => [ 'shape' => 'RecoveryInstanceDataReplicationInitiation', ], 'dataReplicationState' => [ 'shape' => 'RecoveryInstanceDataReplicationState', ], 'etaDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'lagDuration' => [ 'shape' => 'ISO8601DatetimeString', ], 'replicatedDisks' => [ 'shape' => 'RecoveryInstanceDataReplicationInfoReplicatedDisks', ], 'stagingAvailabilityZone' => [ 'shape' => 'AwsAvailabilityZone', ], 'stagingOutpostArn' => [ 'shape' => 'OutpostARN', ], ], ], 'RecoveryInstanceDataReplicationInfoReplicatedDisk' => [ 'type' => 'structure', 'members' => [ 'backloggedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'deviceName' => [ 'shape' => 'BoundedString', ], 'replicatedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'rescannedStorageBytes' => [ 'shape' => 'PositiveInteger', ], 'totalStorageBytes' => [ 'shape' => 'PositiveInteger', ], ], ], 'RecoveryInstanceDataReplicationInfoReplicatedDisks' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryInstanceDataReplicationInfoReplicatedDisk', ], 'max' => 60, 'min' => 0, ], 'RecoveryInstanceDataReplicationInitiation' => [ 'type' => 'structure', 'members' => [ 'startDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'steps' => [ 'shape' => 'RecoveryInstanceDataReplicationInitiationSteps', ], ], ], 'RecoveryInstanceDataReplicationInitiationStep' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'RecoveryInstanceDataReplicationInitiationStepName', ], 'status' => [ 'shape' => 'RecoveryInstanceDataReplicationInitiationStepStatus', ], ], ], 'RecoveryInstanceDataReplicationInitiationStepName' => [ 'type' => 'string', 'enum' => [ 'LINK_FAILBACK_CLIENT_WITH_RECOVERY_INSTANCE', 'COMPLETE_VOLUME_MAPPING', 'ESTABLISH_RECOVERY_INSTANCE_COMMUNICATION', 'DOWNLOAD_REPLICATION_SOFTWARE_TO_FAILBACK_CLIENT', 'CONFIGURE_REPLICATION_SOFTWARE', 'PAIR_AGENT_WITH_REPLICATION_SOFTWARE', 'ESTABLISH_AGENT_REPLICATOR_SOFTWARE_COMMUNICATION', 'WAIT', 'CREATE_SECURITY_GROUP', 'LAUNCH_REPLICATION_SERVER', 'BOOT_REPLICATION_SERVER', 'AUTHENTICATE_WITH_SERVICE', 'DOWNLOAD_REPLICATION_SOFTWARE', 'CREATE_STAGING_DISKS', 'ATTACH_STAGING_DISKS', 'PAIR_REPLICATION_SERVER_WITH_AGENT', 'CONNECT_AGENT_TO_REPLICATION_SERVER', 'START_DATA_TRANSFER', ], ], 'RecoveryInstanceDataReplicationInitiationStepStatus' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'IN_PROGRESS', 'SUCCEEDED', 'FAILED', 'SKIPPED', ], ], 'RecoveryInstanceDataReplicationInitiationSteps' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryInstanceDataReplicationInitiationStep', ], ], 'RecoveryInstanceDataReplicationState' => [ 'type' => 'string', 'enum' => [ 'STOPPED', 'INITIATING', 'INITIAL_SYNC', 'BACKLOG', 'CREATING_SNAPSHOT', 'CONTINUOUS', 'PAUSED', 'RESCAN', 'STALLED', 'DISCONNECTED', 'REPLICATION_STATE_NOT_AVAILABLE', 'NOT_STARTED', ], ], 'RecoveryInstanceDisk' => [ 'type' => 'structure', 'members' => [ 'bytes' => [ 'shape' => 'PositiveInteger', ], 'ebsVolumeID' => [ 'shape' => 'EbsVolumeID', ], 'internalDeviceName' => [ 'shape' => 'BoundedString', ], ], ], 'RecoveryInstanceDisks' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryInstanceDisk', ], 'max' => 1000, 'min' => 0, ], 'RecoveryInstanceFailback' => [ 'type' => 'structure', 'members' => [ 'agentLastSeenByServiceDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'elapsedReplicationDuration' => [ 'shape' => 'ISO8601DatetimeString', ], 'failbackClientID' => [ 'shape' => 'BoundedString', ], 'failbackClientLastSeenByServiceDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'failbackInitiationTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'failbackJobID' => [ 'shape' => 'JobID', ], 'failbackLaunchType' => [ 'shape' => 'FailbackLaunchType', ], 'failbackToOriginalServer' => [ 'shape' => 'Boolean', ], 'firstByteDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'state' => [ 'shape' => 'FailbackState', ], ], ], 'RecoveryInstanceID' => [ 'type' => 'string', 'max' => 19, 'min' => 10, 'pattern' => '^i-[0-9a-fA-F]{8,}$', ], 'RecoveryInstanceIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryInstanceID', ], 'max' => 200, 'min' => 0, ], 'RecoveryInstanceProperties' => [ 'type' => 'structure', 'members' => [ 'cpus' => [ 'shape' => 'Cpus', ], 'disks' => [ 'shape' => 'RecoveryInstanceDisks', ], 'identificationHints' => [ 'shape' => 'IdentificationHints', ], 'lastUpdatedDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'networkInterfaces' => [ 'shape' => 'NetworkInterfaces', ], 'os' => [ 'shape' => 'OS', ], 'ramBytes' => [ 'shape' => 'PositiveInteger', ], ], ], 'RecoveryInstancesForTerminationRequest' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryInstanceID', ], 'max' => 200, 'min' => 1, ], 'RecoveryLifeCycle' => [ 'type' => 'structure', 'members' => [ 'apiCallDateTime' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'jobID' => [ 'shape' => 'JobID', ], 'lastRecoveryResult' => [ 'shape' => 'RecoveryResult', ], ], ], 'RecoveryResult' => [ 'type' => 'string', 'enum' => [ 'NOT_STARTED', 'IN_PROGRESS', 'SUCCESS', 'FAIL', 'PARTIAL_SUCCESS', 'ASSOCIATE_SUCCESS', 'ASSOCIATE_FAIL', ], ], 'RecoverySnapshot' => [ 'type' => 'structure', 'required' => [ 'expectedTimestamp', 'snapshotID', 'sourceServerID', ], 'members' => [ 'ebsSnapshots' => [ 'shape' => 'EbsSnapshotsList', ], 'expectedTimestamp' => [ 'shape' => 'ISO8601DatetimeString', ], 'snapshotID' => [ 'shape' => 'RecoverySnapshotID', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'timestamp' => [ 'shape' => 'ISO8601DatetimeString', ], ], ], 'RecoverySnapshotID' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^pit-[0-9a-zA-Z]{17}$', ], 'RecoverySnapshotsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoverySnapshot', ], ], 'RecoverySnapshotsOrder' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'ReplicationConfiguration' => [ 'type' => 'structure', 'members' => [ 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'autoReplicateNewDisks' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'name' => [ 'shape' => 'SmallBoundedString', ], 'pitPolicy' => [ 'shape' => 'PITPolicy', ], 'replicatedDisks' => [ 'shape' => 'ReplicationConfigurationReplicatedDisks', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'ReplicationConfigurationDataPlaneRouting' => [ 'type' => 'string', 'enum' => [ 'PRIVATE_IP', 'PUBLIC_IP', ], ], 'ReplicationConfigurationDefaultLargeStagingDiskType' => [ 'type' => 'string', 'enum' => [ 'GP2', 'GP3', 'ST1', 'AUTO', ], ], 'ReplicationConfigurationEbsEncryption' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'CUSTOM', 'NONE', ], ], 'ReplicationConfigurationReplicatedDisk' => [ 'type' => 'structure', 'members' => [ 'deviceName' => [ 'shape' => 'BoundedString', ], 'iops' => [ 'shape' => 'PositiveInteger', ], 'isBootDisk' => [ 'shape' => 'Boolean', ], 'optimizedStagingDiskType' => [ 'shape' => 'ReplicationConfigurationReplicatedDiskStagingDiskType', ], 'stagingDiskType' => [ 'shape' => 'ReplicationConfigurationReplicatedDiskStagingDiskType', ], 'throughput' => [ 'shape' => 'PositiveInteger', ], ], ], 'ReplicationConfigurationReplicatedDiskStagingDiskType' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'GP2', 'GP3', 'IO1', 'SC1', 'ST1', 'STANDARD', ], ], 'ReplicationConfigurationReplicatedDisks' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationConfigurationReplicatedDisk', ], 'max' => 60, 'min' => 0, ], 'ReplicationConfigurationTemplate' => [ 'type' => 'structure', 'required' => [ 'replicationConfigurationTemplateID', ], 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'autoReplicateNewDisks' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'pitPolicy' => [ 'shape' => 'PITPolicy', ], 'replicationConfigurationTemplateID' => [ 'shape' => 'ReplicationConfigurationTemplateID', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'tags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'ReplicationConfigurationTemplateID' => [ 'type' => 'string', 'max' => 21, 'min' => 21, 'pattern' => '^rct-[0-9a-zA-Z]{17}$', ], 'ReplicationConfigurationTemplateIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationConfigurationTemplateID', ], 'max' => 200, 'min' => 0, ], 'ReplicationConfigurationTemplates' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReplicationConfigurationTemplate', ], ], 'ReplicationDirection' => [ 'type' => 'string', 'enum' => [ 'FAILOVER', 'FAILBACK', ], ], 'ReplicationServersSecurityGroupsIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SecurityGroupID', ], 'max' => 32, 'min' => 0, ], 'ReplicationStatus' => [ 'type' => 'string', 'enum' => [ 'STOPPED', 'IN_PROGRESS', 'PROTECTED', 'ERROR', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], 'resourceId' => [ 'shape' => 'LargeBoundedString', ], 'resourceType' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RetryDataReplicationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], 'deprecated' => true, 'deprecatedMessage' => 'WARNING: RetryDataReplication is deprecated', ], 'ReverseReplicationRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceID', ], 'members' => [ 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], ], ], 'ReverseReplicationResponse' => [ 'type' => 'structure', 'members' => [ 'reversedDirectionSourceServerArn' => [ 'shape' => 'SourceServerARN', ], ], ], 'SecurityGroupID' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^sg-[0-9a-fA-F]{8,}$', ], 'SensitiveBoundedString' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'sensitive' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], 'quotaCode' => [ 'shape' => 'LargeBoundedString', ], 'resourceId' => [ 'shape' => 'LargeBoundedString', ], 'resourceType' => [ 'shape' => 'LargeBoundedString', ], 'serviceCode' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SmallBoundedString' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'SourceCloudProperties' => [ 'type' => 'structure', 'members' => [ 'originAccountID' => [ 'shape' => 'AccountID', ], 'originAvailabilityZone' => [ 'shape' => 'AwsAvailabilityZone', ], 'originRegion' => [ 'shape' => 'AwsRegion', ], 'sourceOutpostArn' => [ 'shape' => 'OutpostARN', ], ], ], 'SourceNetwork' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'cfnStackName' => [ 'shape' => 'CfnStackName', ], 'lastRecovery' => [ 'shape' => 'RecoveryLifeCycle', ], 'launchedVpcID' => [ 'shape' => 'VpcID', ], 'replicationStatus' => [ 'shape' => 'ReplicationStatus', ], 'replicationStatusDetails' => [ 'shape' => 'SensitiveBoundedString', ], 'sourceAccountID' => [ 'shape' => 'AccountID', ], 'sourceNetworkID' => [ 'shape' => 'SourceNetworkID', ], 'sourceRegion' => [ 'shape' => 'AwsRegion', ], 'sourceVpcID' => [ 'shape' => 'VpcID', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'SourceNetworkData' => [ 'type' => 'structure', 'members' => [ 'sourceNetworkID' => [ 'shape' => 'SourceNetworkID', ], 'sourceVpc' => [ 'shape' => 'VpcID', ], 'stackName' => [ 'shape' => 'LargeBoundedString', ], 'targetVpc' => [ 'shape' => 'VpcID', ], ], ], 'SourceNetworkID' => [ 'type' => 'string', 'max' => 20, 'min' => 20, 'pattern' => '^sn-[0-9a-zA-Z]{17}$', ], 'SourceNetworksList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceNetwork', ], ], 'SourceProperties' => [ 'type' => 'structure', 'members' => [ 'cpus' => [ 'shape' => 'Cpus', ], 'disks' => [ 'shape' => 'Disks', ], 'identificationHints' => [ 'shape' => 'IdentificationHints', ], 'lastUpdatedDateTime' => [ 'shape' => 'ISO8601DatetimeString', ], 'networkInterfaces' => [ 'shape' => 'NetworkInterfaces', ], 'os' => [ 'shape' => 'OS', ], 'ramBytes' => [ 'shape' => 'PositiveInteger', ], 'recommendedInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'supportsNitroInstances' => [ 'shape' => 'Boolean', ], ], ], 'SourceServer' => [ 'type' => 'structure', 'members' => [ 'agentVersion' => [ 'shape' => 'AgentVersion', ], 'arn' => [ 'shape' => 'ARN', ], 'dataReplicationInfo' => [ 'shape' => 'DataReplicationInfo', ], 'lastLaunchResult' => [ 'shape' => 'LastLaunchResult', ], 'lifeCycle' => [ 'shape' => 'LifeCycle', ], 'recoveryInstanceId' => [ 'shape' => 'RecoveryInstanceID', ], 'replicationDirection' => [ 'shape' => 'ReplicationDirection', ], 'reversedDirectionSourceServerArn' => [ 'shape' => 'SourceServerARN', ], 'sourceCloudProperties' => [ 'shape' => 'SourceCloudProperties', ], 'sourceNetworkID' => [ 'shape' => 'SourceNetworkID', ], 'sourceProperties' => [ 'shape' => 'SourceProperties', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'stagingArea' => [ 'shape' => 'StagingArea', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'SourceServerARN' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => '^arn:(?:[0-9a-zA-Z_-]+:){3}([0-9]{12,}):source-server/(s-[0-9a-zA-Z]{17})$', ], 'SourceServerID' => [ 'type' => 'string', 'max' => 19, 'min' => 19, 'pattern' => '^s-[0-9a-zA-Z]{17}$', ], 'SourceServerIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceServerID', ], ], 'SourceServersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceServer', ], ], 'SsmDocumentName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => '^([A-Za-z0-9-/:])+$', ], 'StagingArea' => [ 'type' => 'structure', 'members' => [ 'errorMessage' => [ 'shape' => 'LargeBoundedString', ], 'stagingAccountID' => [ 'shape' => 'AccountID', ], 'stagingSourceServerArn' => [ 'shape' => 'ARN', ], 'status' => [ 'shape' => 'ExtensionStatus', ], ], ], 'StagingSourceServer' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'SourceServerARN', ], 'hostname' => [ 'shape' => 'BoundedString', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'StagingSourceServersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StagingSourceServer', ], ], 'StartFailbackLaunchRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceIDs', ], 'members' => [ 'recoveryInstanceIDs' => [ 'shape' => 'StartFailbackRequestRecoveryInstanceIDs', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'StartFailbackLaunchResponse' => [ 'type' => 'structure', 'members' => [ 'job' => [ 'shape' => 'Job', ], ], ], 'StartFailbackRequestRecoveryInstanceIDs' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecoveryInstanceID', ], 'max' => 200, 'min' => 1, ], 'StartRecoveryRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServers', ], 'members' => [ 'isDrill' => [ 'shape' => 'Boolean', ], 'sourceServers' => [ 'shape' => 'StartRecoveryRequestSourceServers', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'StartRecoveryRequestSourceServer' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'recoverySnapshotID' => [ 'shape' => 'RecoverySnapshotID', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'StartRecoveryRequestSourceServers' => [ 'type' => 'list', 'member' => [ 'shape' => 'StartRecoveryRequestSourceServer', ], 'max' => 200, 'min' => 1, ], 'StartRecoveryResponse' => [ 'type' => 'structure', 'members' => [ 'job' => [ 'shape' => 'Job', ], ], ], 'StartReplicationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'StartReplicationResponse' => [ 'type' => 'structure', 'members' => [ 'sourceServer' => [ 'shape' => 'SourceServer', ], ], ], 'StartSourceNetworkRecoveryRequest' => [ 'type' => 'structure', 'required' => [ 'sourceNetworks', ], 'members' => [ 'deployAsNew' => [ 'shape' => 'Boolean', ], 'sourceNetworks' => [ 'shape' => 'StartSourceNetworkRecoveryRequestNetworkEntries', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'StartSourceNetworkRecoveryRequestNetworkEntries' => [ 'type' => 'list', 'member' => [ 'shape' => 'StartSourceNetworkRecoveryRequestNetworkEntry', ], 'max' => 100, 'min' => 1, ], 'StartSourceNetworkRecoveryRequestNetworkEntry' => [ 'type' => 'structure', 'required' => [ 'sourceNetworkID', ], 'members' => [ 'cfnStackName' => [ 'shape' => 'CfnStackName', ], 'sourceNetworkID' => [ 'shape' => 'SourceNetworkID', ], ], ], 'StartSourceNetworkRecoveryResponse' => [ 'type' => 'structure', 'members' => [ 'job' => [ 'shape' => 'Job', ], ], ], 'StartSourceNetworkReplicationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceNetworkID', ], 'members' => [ 'sourceNetworkID' => [ 'shape' => 'SourceNetworkID', ], ], ], 'StartSourceNetworkReplicationResponse' => [ 'type' => 'structure', 'members' => [ 'sourceNetwork' => [ 'shape' => 'SourceNetwork', ], ], ], 'StopFailbackRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceID', ], 'members' => [ 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], ], ], 'StopReplicationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'sourceServerID' => [ 'shape' => 'SourceServerID', ], ], ], 'StopReplicationResponse' => [ 'type' => 'structure', 'members' => [ 'sourceServer' => [ 'shape' => 'SourceServer', ], ], ], 'StopSourceNetworkReplicationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceNetworkID', ], 'members' => [ 'sourceNetworkID' => [ 'shape' => 'SourceNetworkID', ], ], ], 'StopSourceNetworkReplicationResponse' => [ 'type' => 'structure', 'members' => [ 'sourceNetwork' => [ 'shape' => 'SourceNetwork', ], ], ], 'StrictlyPositiveInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'SubnetID' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^subnet-[0-9a-fA-F]{8,}$', ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagKey' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'sensitive' => true, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagsMap', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TagsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'sensitive' => true, ], 'TargetInstanceTypeRightSizingMethod' => [ 'type' => 'string', 'enum' => [ 'NONE', 'BASIC', 'IN_AWS', ], ], 'TerminateRecoveryInstancesRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceIDs', ], 'members' => [ 'recoveryInstanceIDs' => [ 'shape' => 'RecoveryInstancesForTerminationRequest', ], ], ], 'TerminateRecoveryInstancesResponse' => [ 'type' => 'structure', 'members' => [ 'job' => [ 'shape' => 'Job', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'LargeBoundedString', ], 'quotaCode' => [ 'shape' => 'LargeBoundedString', ], 'retryAfterSeconds' => [ 'shape' => 'LargeBoundedString', 'location' => 'header', 'locationName' => 'Retry-After', ], 'serviceCode' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'UninitializedAccountException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'message' => [ 'shape' => 'LargeBoundedString', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ARN', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UpdateFailbackReplicationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'recoveryInstanceID', ], 'members' => [ 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'name' => [ 'shape' => 'BoundedString', ], 'recoveryInstanceID' => [ 'shape' => 'RecoveryInstanceID', ], 'usePrivateIP' => [ 'shape' => 'Boolean', ], ], ], 'UpdateLaunchConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'copyPrivateIp' => [ 'shape' => 'Boolean', ], 'copyTags' => [ 'shape' => 'Boolean', ], 'launchDisposition' => [ 'shape' => 'LaunchDisposition', ], 'launchIntoInstanceProperties' => [ 'shape' => 'LaunchIntoInstanceProperties', ], 'licensing' => [ 'shape' => 'Licensing', ], 'name' => [ 'shape' => 'SmallBoundedString', ], 'postLaunchEnabled' => [ 'shape' => 'Boolean', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'targetInstanceTypeRightSizingMethod' => [ 'shape' => 'TargetInstanceTypeRightSizingMethod', ], ], ], 'UpdateLaunchConfigurationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'launchConfigurationTemplateID', ], 'members' => [ 'copyPrivateIp' => [ 'shape' => 'Boolean', ], 'copyTags' => [ 'shape' => 'Boolean', ], 'exportBucketArn' => [ 'shape' => 'ARN', ], 'launchConfigurationTemplateID' => [ 'shape' => 'LaunchConfigurationTemplateID', ], 'launchDisposition' => [ 'shape' => 'LaunchDisposition', ], 'launchIntoSourceInstance' => [ 'shape' => 'Boolean', ], 'licensing' => [ 'shape' => 'Licensing', ], 'postLaunchEnabled' => [ 'shape' => 'Boolean', ], 'targetInstanceTypeRightSizingMethod' => [ 'shape' => 'TargetInstanceTypeRightSizingMethod', ], ], ], 'UpdateLaunchConfigurationTemplateResponse' => [ 'type' => 'structure', 'members' => [ 'launchConfigurationTemplate' => [ 'shape' => 'LaunchConfigurationTemplate', ], ], ], 'UpdateReplicationConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceServerID', ], 'members' => [ 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'autoReplicateNewDisks' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'name' => [ 'shape' => 'SmallBoundedString', ], 'pitPolicy' => [ 'shape' => 'PITPolicy', ], 'replicatedDisks' => [ 'shape' => 'ReplicationConfigurationReplicatedDisks', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'sourceServerID' => [ 'shape' => 'SourceServerID', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'UpdateReplicationConfigurationTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'replicationConfigurationTemplateID', ], 'members' => [ 'arn' => [ 'shape' => 'ARN', ], 'associateDefaultSecurityGroup' => [ 'shape' => 'Boolean', ], 'autoReplicateNewDisks' => [ 'shape' => 'Boolean', ], 'bandwidthThrottling' => [ 'shape' => 'PositiveInteger', ], 'createPublicIP' => [ 'shape' => 'Boolean', ], 'dataPlaneRouting' => [ 'shape' => 'ReplicationConfigurationDataPlaneRouting', ], 'defaultLargeStagingDiskType' => [ 'shape' => 'ReplicationConfigurationDefaultLargeStagingDiskType', ], 'ebsEncryption' => [ 'shape' => 'ReplicationConfigurationEbsEncryption', ], 'ebsEncryptionKeyArn' => [ 'shape' => 'ARN', ], 'pitPolicy' => [ 'shape' => 'PITPolicy', ], 'replicationConfigurationTemplateID' => [ 'shape' => 'ReplicationConfigurationTemplateID', ], 'replicationServerInstanceType' => [ 'shape' => 'EC2InstanceType', ], 'replicationServersSecurityGroupsIDs' => [ 'shape' => 'ReplicationServersSecurityGroupsIDs', ], 'stagingAreaSubnetId' => [ 'shape' => 'SubnetID', ], 'stagingAreaTags' => [ 'shape' => 'TagsMap', ], 'useDedicatedReplicationServer' => [ 'shape' => 'Boolean', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'LargeBoundedString', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'LargeBoundedString', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'LargeBoundedString', ], 'name' => [ 'shape' => 'LargeBoundedString', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', ], ], 'VolumeStatus' => [ 'type' => 'string', 'enum' => [ 'REGULAR', 'CONTAINS_MARKETPLACE_PRODUCT_CODES', 'MISSING_VOLUME_ATTRIBUTES', 'MISSING_VOLUME_ATTRIBUTES_AND_PRECHECK_UNAVAILABLE', 'PENDING', ], ], 'VolumeToConversionMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'LargeBoundedString', ], 'value' => [ 'shape' => 'ConversionMap', ], ], 'VolumeToProductCodes' => [ 'type' => 'map', 'key' => [ 'shape' => 'LargeBoundedString', ], 'value' => [ 'shape' => 'ProductCodes', ], ], 'VolumeToSizeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'LargeBoundedString', ], 'value' => [ 'shape' => 'PositiveInteger', ], ], 'VpcID' => [ 'type' => 'string', 'max' => 21, 'min' => 12, 'pattern' => '^vpc-[0-9a-fA-F]{8,}$', ], ],];
