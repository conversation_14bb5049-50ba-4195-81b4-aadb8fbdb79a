<?php
// This file was auto-generated from sdk-root/src/data/chime-sdk-media-pipelines/2021-07-15/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-07-15', 'endpointPrefix' => 'media-pipelines-chime', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Amazon Chime SDK Media Pipelines', 'serviceId' => 'Chime SDK Media Pipelines', 'signatureVersion' => 'v4', 'signingName' => 'chime', 'uid' => 'chime-sdk-media-pipelines-2021-07-15', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'CreateMediaCapturePipeline' => [ 'name' => 'CreateMediaCapturePipeline', 'http' => [ 'method' => 'POST', 'requestUri' => '/sdk-media-capture-pipelines', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMediaCapturePipelineRequest', ], 'output' => [ 'shape' => 'CreateMediaCapturePipelineResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateMediaConcatenationPipeline' => [ 'name' => 'CreateMediaConcatenationPipeline', 'http' => [ 'method' => 'POST', 'requestUri' => '/sdk-media-concatenation-pipelines', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMediaConcatenationPipelineRequest', ], 'output' => [ 'shape' => 'CreateMediaConcatenationPipelineResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateMediaInsightsPipeline' => [ 'name' => 'CreateMediaInsightsPipeline', 'http' => [ 'method' => 'POST', 'requestUri' => '/media-insights-pipelines', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMediaInsightsPipelineRequest', ], 'output' => [ 'shape' => 'CreateMediaInsightsPipelineResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateMediaInsightsPipelineConfiguration' => [ 'name' => 'CreateMediaInsightsPipelineConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/media-insights-pipeline-configurations', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMediaInsightsPipelineConfigurationRequest', ], 'output' => [ 'shape' => 'CreateMediaInsightsPipelineConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateMediaLiveConnectorPipeline' => [ 'name' => 'CreateMediaLiveConnectorPipeline', 'http' => [ 'method' => 'POST', 'requestUri' => '/sdk-media-live-connector-pipelines', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMediaLiveConnectorPipelineRequest', ], 'output' => [ 'shape' => 'CreateMediaLiveConnectorPipelineResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateMediaPipelineKinesisVideoStreamPool' => [ 'name' => 'CreateMediaPipelineKinesisVideoStreamPool', 'http' => [ 'method' => 'POST', 'requestUri' => '/media-pipeline-kinesis-video-stream-pools', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMediaPipelineKinesisVideoStreamPoolRequest', ], 'output' => [ 'shape' => 'CreateMediaPipelineKinesisVideoStreamPoolResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'CreateMediaStreamPipeline' => [ 'name' => 'CreateMediaStreamPipeline', 'http' => [ 'method' => 'POST', 'requestUri' => '/sdk-media-stream-pipelines', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMediaStreamPipelineRequest', ], 'output' => [ 'shape' => 'CreateMediaStreamPipelineResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteMediaCapturePipeline' => [ 'name' => 'DeleteMediaCapturePipeline', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sdk-media-capture-pipelines/{mediaPipelineId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteMediaCapturePipelineRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteMediaInsightsPipelineConfiguration' => [ 'name' => 'DeleteMediaInsightsPipelineConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/media-insights-pipeline-configurations/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteMediaInsightsPipelineConfigurationRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteMediaPipeline' => [ 'name' => 'DeleteMediaPipeline', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/sdk-media-pipelines/{mediaPipelineId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteMediaPipelineRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'DeleteMediaPipelineKinesisVideoStreamPool' => [ 'name' => 'DeleteMediaPipelineKinesisVideoStreamPool', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/media-pipeline-kinesis-video-stream-pools/{identifier}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteMediaPipelineKinesisVideoStreamPoolRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetMediaCapturePipeline' => [ 'name' => 'GetMediaCapturePipeline', 'http' => [ 'method' => 'GET', 'requestUri' => '/sdk-media-capture-pipelines/{mediaPipelineId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMediaCapturePipelineRequest', ], 'output' => [ 'shape' => 'GetMediaCapturePipelineResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetMediaInsightsPipelineConfiguration' => [ 'name' => 'GetMediaInsightsPipelineConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/media-insights-pipeline-configurations/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMediaInsightsPipelineConfigurationRequest', ], 'output' => [ 'shape' => 'GetMediaInsightsPipelineConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetMediaPipeline' => [ 'name' => 'GetMediaPipeline', 'http' => [ 'method' => 'GET', 'requestUri' => '/sdk-media-pipelines/{mediaPipelineId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMediaPipelineRequest', ], 'output' => [ 'shape' => 'GetMediaPipelineResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetMediaPipelineKinesisVideoStreamPool' => [ 'name' => 'GetMediaPipelineKinesisVideoStreamPool', 'http' => [ 'method' => 'GET', 'requestUri' => '/media-pipeline-kinesis-video-stream-pools/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMediaPipelineKinesisVideoStreamPoolRequest', ], 'output' => [ 'shape' => 'GetMediaPipelineKinesisVideoStreamPoolResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetSpeakerSearchTask' => [ 'name' => 'GetSpeakerSearchTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/media-insights-pipelines/{identifier}/speaker-search-tasks/{speakerSearchTaskId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSpeakerSearchTaskRequest', ], 'output' => [ 'shape' => 'GetSpeakerSearchTaskResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'GetVoiceToneAnalysisTask' => [ 'name' => 'GetVoiceToneAnalysisTask', 'http' => [ 'method' => 'GET', 'requestUri' => '/media-insights-pipelines/{identifier}/voice-tone-analysis-tasks/{voiceToneAnalysisTaskId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVoiceToneAnalysisTaskRequest', ], 'output' => [ 'shape' => 'GetVoiceToneAnalysisTaskResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListMediaCapturePipelines' => [ 'name' => 'ListMediaCapturePipelines', 'http' => [ 'method' => 'GET', 'requestUri' => '/sdk-media-capture-pipelines', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMediaCapturePipelinesRequest', ], 'output' => [ 'shape' => 'ListMediaCapturePipelinesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListMediaInsightsPipelineConfigurations' => [ 'name' => 'ListMediaInsightsPipelineConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/media-insights-pipeline-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMediaInsightsPipelineConfigurationsRequest', ], 'output' => [ 'shape' => 'ListMediaInsightsPipelineConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListMediaPipelineKinesisVideoStreamPools' => [ 'name' => 'ListMediaPipelineKinesisVideoStreamPools', 'http' => [ 'method' => 'GET', 'requestUri' => '/media-pipeline-kinesis-video-stream-pools', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMediaPipelineKinesisVideoStreamPoolsRequest', ], 'output' => [ 'shape' => 'ListMediaPipelineKinesisVideoStreamPoolsResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListMediaPipelines' => [ 'name' => 'ListMediaPipelines', 'http' => [ 'method' => 'GET', 'requestUri' => '/sdk-media-pipelines', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMediaPipelinesRequest', ], 'output' => [ 'shape' => 'ListMediaPipelinesResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'StartSpeakerSearchTask' => [ 'name' => 'StartSpeakerSearchTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/media-insights-pipelines/{identifier}/speaker-search-tasks?operation=start', 'responseCode' => 201, ], 'input' => [ 'shape' => 'StartSpeakerSearchTaskRequest', ], 'output' => [ 'shape' => 'StartSpeakerSearchTaskResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'StartVoiceToneAnalysisTask' => [ 'name' => 'StartVoiceToneAnalysisTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/media-insights-pipelines/{identifier}/voice-tone-analysis-tasks?operation=start', 'responseCode' => 201, ], 'input' => [ 'shape' => 'StartVoiceToneAnalysisTaskRequest', ], 'output' => [ 'shape' => 'StartVoiceToneAnalysisTaskResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'StopSpeakerSearchTask' => [ 'name' => 'StopSpeakerSearchTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/media-insights-pipelines/{identifier}/speaker-search-tasks/{speakerSearchTaskId}?operation=stop', 'responseCode' => 204, ], 'input' => [ 'shape' => 'StopSpeakerSearchTaskRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'StopVoiceToneAnalysisTask' => [ 'name' => 'StopVoiceToneAnalysisTask', 'http' => [ 'method' => 'POST', 'requestUri' => '/media-insights-pipelines/{identifier}/voice-tone-analysis-tasks/{voiceToneAnalysisTaskId}?operation=stop', 'responseCode' => 204, ], 'input' => [ 'shape' => 'StopVoiceToneAnalysisTaskRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags?operation=tag-resource', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags?operation=untag-resource', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateMediaInsightsPipelineConfiguration' => [ 'name' => 'UpdateMediaInsightsPipelineConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/media-insights-pipeline-configurations/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMediaInsightsPipelineConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateMediaInsightsPipelineConfigurationResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateMediaInsightsPipelineStatus' => [ 'name' => 'UpdateMediaInsightsPipelineStatus', 'http' => [ 'method' => 'PUT', 'requestUri' => '/media-insights-pipeline-status/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMediaInsightsPipelineStatusRequest', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], 'UpdateMediaPipelineKinesisVideoStreamPool' => [ 'name' => 'UpdateMediaPipelineKinesisVideoStreamPool', 'http' => [ 'method' => 'PUT', 'requestUri' => '/media-pipeline-kinesis-video-stream-pools/{identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMediaPipelineKinesisVideoStreamPoolRequest', ], 'output' => [ 'shape' => 'UpdateMediaPipelineKinesisVideoStreamPoolResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'ForbiddenException', ], [ 'shape' => 'NotFoundException', ], [ 'shape' => 'UnauthorizedClientException', ], [ 'shape' => 'ThrottledClientException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ServiceFailureException', ], ], ], ], 'shapes' => [ 'ActiveSpeakerOnlyConfiguration' => [ 'type' => 'structure', 'members' => [ 'ActiveSpeakerPosition' => [ 'shape' => 'ActiveSpeakerPosition', ], ], ], 'ActiveSpeakerPosition' => [ 'type' => 'string', 'enum' => [ 'TopLeft', 'TopRight', 'BottomLeft', 'BottomRight', ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => '^arn[\\/\\:\\-\\_\\.a-zA-Z0-9]+$', ], 'AmazonTranscribeCallAnalyticsProcessorConfiguration' => [ 'type' => 'structure', 'required' => [ 'LanguageCode', ], 'members' => [ 'LanguageCode' => [ 'shape' => 'CallAnalyticsLanguageCode', ], 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'VocabularyFilterName' => [ 'shape' => 'VocabularyFilterName', ], 'VocabularyFilterMethod' => [ 'shape' => 'VocabularyFilterMethod', ], 'LanguageModelName' => [ 'shape' => 'ModelName', ], 'EnablePartialResultsStabilization' => [ 'shape' => 'Boolean', ], 'PartialResultsStability' => [ 'shape' => 'PartialResultsStability', ], 'ContentIdentificationType' => [ 'shape' => 'ContentType', ], 'ContentRedactionType' => [ 'shape' => 'ContentType', ], 'PiiEntityTypes' => [ 'shape' => 'PiiEntityTypes', ], 'FilterPartialResults' => [ 'shape' => 'Boolean', ], 'PostCallAnalyticsSettings' => [ 'shape' => 'PostCallAnalyticsSettings', ], 'CallAnalyticsStreamCategories' => [ 'shape' => 'CategoryNameList', ], ], ], 'AmazonTranscribeProcessorConfiguration' => [ 'type' => 'structure', 'members' => [ 'LanguageCode' => [ 'shape' => 'CallAnalyticsLanguageCode', ], 'VocabularyName' => [ 'shape' => 'VocabularyName', ], 'VocabularyFilterName' => [ 'shape' => 'VocabularyFilterName', ], 'VocabularyFilterMethod' => [ 'shape' => 'VocabularyFilterMethod', ], 'ShowSpeakerLabel' => [ 'shape' => 'Boolean', ], 'EnablePartialResultsStabilization' => [ 'shape' => 'Boolean', ], 'PartialResultsStability' => [ 'shape' => 'PartialResultsStability', ], 'ContentIdentificationType' => [ 'shape' => 'ContentType', ], 'ContentRedactionType' => [ 'shape' => 'ContentType', ], 'PiiEntityTypes' => [ 'shape' => 'PiiEntityTypes', ], 'LanguageModelName' => [ 'shape' => 'ModelName', ], 'FilterPartialResults' => [ 'shape' => 'Boolean', ], 'IdentifyLanguage' => [ 'shape' => 'Boolean', ], 'IdentifyMultipleLanguages' => [ 'shape' => 'Boolean', ], 'LanguageOptions' => [ 'shape' => 'LanguageOptions', ], 'PreferredLanguage' => [ 'shape' => 'CallAnalyticsLanguageCode', ], 'VocabularyNames' => [ 'shape' => 'VocabularyNames', ], 'VocabularyFilterNames' => [ 'shape' => 'VocabularyFilterNames', ], ], ], 'Arn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '^arn[\\/\\:\\-\\_\\.a-zA-Z0-9]+$', 'sensitive' => true, ], 'ArtifactsConcatenationConfiguration' => [ 'type' => 'structure', 'required' => [ 'Audio', 'Video', 'Content', 'DataChannel', 'TranscriptionMessages', 'MeetingEvents', 'CompositedVideo', ], 'members' => [ 'Audio' => [ 'shape' => 'AudioConcatenationConfiguration', ], 'Video' => [ 'shape' => 'VideoConcatenationConfiguration', ], 'Content' => [ 'shape' => 'ContentConcatenationConfiguration', ], 'DataChannel' => [ 'shape' => 'DataChannelConcatenationConfiguration', ], 'TranscriptionMessages' => [ 'shape' => 'TranscriptionMessagesConcatenationConfiguration', ], 'MeetingEvents' => [ 'shape' => 'MeetingEventsConcatenationConfiguration', ], 'CompositedVideo' => [ 'shape' => 'CompositedVideoConcatenationConfiguration', ], ], ], 'ArtifactsConcatenationState' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'ArtifactsConfiguration' => [ 'type' => 'structure', 'required' => [ 'Audio', 'Video', 'Content', ], 'members' => [ 'Audio' => [ 'shape' => 'AudioArtifactsConfiguration', ], 'Video' => [ 'shape' => 'VideoArtifactsConfiguration', ], 'Content' => [ 'shape' => 'ContentArtifactsConfiguration', ], 'CompositedVideo' => [ 'shape' => 'CompositedVideoArtifactsConfiguration', ], ], ], 'ArtifactsState' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'AttendeeIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuidString', ], 'min' => 1, ], 'AudioArtifactsConcatenationState' => [ 'type' => 'string', 'enum' => [ 'Enabled', ], ], 'AudioArtifactsConfiguration' => [ 'type' => 'structure', 'required' => [ 'MuxType', ], 'members' => [ 'MuxType' => [ 'shape' => 'AudioMuxType', ], ], ], 'AudioChannelsOption' => [ 'type' => 'string', 'enum' => [ 'Stereo', 'Mono', ], ], 'AudioConcatenationConfiguration' => [ 'type' => 'structure', 'required' => [ 'State', ], 'members' => [ 'State' => [ 'shape' => 'AudioArtifactsConcatenationState', ], ], ], 'AudioMuxType' => [ 'type' => 'string', 'enum' => [ 'AudioOnly', 'AudioWithActiveSpeakerVideo', 'AudioWithCompositedVideo', ], ], 'AudioSampleRateOption' => [ 'type' => 'string', 'pattern' => '44100|48000', ], 'AwsRegion' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '^([a-z]+-){2,}\\d+$', ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'Boolean' => [ 'type' => 'boolean', ], 'BorderColor' => [ 'type' => 'string', 'enum' => [ 'Black', 'Blue', 'Red', 'Green', 'White', 'Yellow', ], ], 'BorderThickness' => [ 'type' => 'integer', 'max' => 20, 'min' => 1, ], 'CallAnalyticsLanguageCode' => [ 'type' => 'string', 'enum' => [ 'en-US', 'en-GB', 'es-US', 'fr-CA', 'fr-FR', 'en-AU', 'it-IT', 'de-DE', 'pt-BR', ], ], 'CanvasOrientation' => [ 'type' => 'string', 'enum' => [ 'Landscape', 'Portrait', ], ], 'CategoryName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'CategoryNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CategoryName', ], 'max' => 20, 'min' => 1, ], 'ChannelDefinition' => [ 'type' => 'structure', 'required' => [ 'ChannelId', ], 'members' => [ 'ChannelId' => [ 'shape' => 'ChannelId', ], 'ParticipantRole' => [ 'shape' => 'ParticipantRole', ], ], ], 'ChannelDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChannelDefinition', ], 'max' => 2, 'min' => 1, ], 'ChannelId' => [ 'type' => 'integer', 'max' => 1, 'min' => 0, ], 'ChimeSdkMeetingConcatenationConfiguration' => [ 'type' => 'structure', 'required' => [ 'ArtifactsConfiguration', ], 'members' => [ 'ArtifactsConfiguration' => [ 'shape' => 'ArtifactsConcatenationConfiguration', ], ], ], 'ChimeSdkMeetingConfiguration' => [ 'type' => 'structure', 'members' => [ 'SourceConfiguration' => [ 'shape' => 'SourceConfiguration', ], 'ArtifactsConfiguration' => [ 'shape' => 'ArtifactsConfiguration', ], ], ], 'ChimeSdkMeetingLiveConnectorConfiguration' => [ 'type' => 'structure', 'required' => [ 'Arn', 'MuxType', ], 'members' => [ 'Arn' => [ 'shape' => 'Arn', ], 'MuxType' => [ 'shape' => 'LiveConnectorMuxType', ], 'CompositedVideo' => [ 'shape' => 'CompositedVideoArtifactsConfiguration', ], 'SourceConfiguration' => [ 'shape' => 'SourceConfiguration', ], ], ], 'ClientRequestToken' => [ 'type' => 'string', 'max' => 64, 'min' => 2, 'pattern' => '[-_a-zA-Z0-9]*', 'sensitive' => true, ], 'CompositedVideoArtifactsConfiguration' => [ 'type' => 'structure', 'required' => [ 'GridViewConfiguration', ], 'members' => [ 'Layout' => [ 'shape' => 'LayoutOption', ], 'Resolution' => [ 'shape' => 'ResolutionOption', ], 'GridViewConfiguration' => [ 'shape' => 'GridViewConfiguration', ], ], ], 'CompositedVideoConcatenationConfiguration' => [ 'type' => 'structure', 'required' => [ 'State', ], 'members' => [ 'State' => [ 'shape' => 'ArtifactsConcatenationState', ], ], ], 'ConcatenationSink' => [ 'type' => 'structure', 'required' => [ 'Type', 'S3BucketSinkConfiguration', ], 'members' => [ 'Type' => [ 'shape' => 'ConcatenationSinkType', ], 'S3BucketSinkConfiguration' => [ 'shape' => 'S3BucketSinkConfiguration', ], ], ], 'ConcatenationSinkList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConcatenationSink', ], 'max' => 1, 'min' => 1, ], 'ConcatenationSinkType' => [ 'type' => 'string', 'enum' => [ 'S3Bucket', ], ], 'ConcatenationSource' => [ 'type' => 'structure', 'required' => [ 'Type', 'MediaCapturePipelineSourceConfiguration', ], 'members' => [ 'Type' => [ 'shape' => 'ConcatenationSourceType', ], 'MediaCapturePipelineSourceConfiguration' => [ 'shape' => 'MediaCapturePipelineSourceConfiguration', ], ], ], 'ConcatenationSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConcatenationSource', ], 'max' => 1, 'min' => 1, ], 'ConcatenationSourceType' => [ 'type' => 'string', 'enum' => [ 'MediaCapturePipeline', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ContentArtifactsConfiguration' => [ 'type' => 'structure', 'required' => [ 'State', ], 'members' => [ 'State' => [ 'shape' => 'ArtifactsState', ], 'MuxType' => [ 'shape' => 'ContentMuxType', ], ], ], 'ContentConcatenationConfiguration' => [ 'type' => 'structure', 'required' => [ 'State', ], 'members' => [ 'State' => [ 'shape' => 'ArtifactsConcatenationState', ], ], ], 'ContentMuxType' => [ 'type' => 'string', 'enum' => [ 'ContentOnly', ], ], 'ContentRedactionOutput' => [ 'type' => 'string', 'enum' => [ 'redacted', 'redacted_and_unredacted', ], ], 'ContentShareLayoutOption' => [ 'type' => 'string', 'enum' => [ 'PresenterOnly', 'Horizontal', 'Vertical', 'ActiveSpeakerOnly', ], ], 'ContentType' => [ 'type' => 'string', 'enum' => [ 'PII', ], ], 'CornerRadius' => [ 'type' => 'integer', 'max' => 20, 'min' => 1, ], 'CreateMediaCapturePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'SourceType', 'SourceArn', 'SinkType', 'SinkArn', ], 'members' => [ 'SourceType' => [ 'shape' => 'MediaPipelineSourceType', ], 'SourceArn' => [ 'shape' => 'Arn', ], 'SinkType' => [ 'shape' => 'MediaPipelineSinkType', ], 'SinkArn' => [ 'shape' => 'Arn', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'ChimeSdkMeetingConfiguration' => [ 'shape' => 'ChimeSdkMeetingConfiguration', ], 'SseAwsKeyManagementParams' => [ 'shape' => 'SseAwsKeyManagementParams', ], 'SinkIamRoleArn' => [ 'shape' => 'Arn', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateMediaCapturePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'MediaCapturePipeline' => [ 'shape' => 'MediaCapturePipeline', ], ], ], 'CreateMediaConcatenationPipelineRequest' => [ 'type' => 'structure', 'required' => [ 'Sources', 'Sinks', ], 'members' => [ 'Sources' => [ 'shape' => 'ConcatenationSourceList', ], 'Sinks' => [ 'shape' => 'ConcatenationSinkList', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateMediaConcatenationPipelineResponse' => [ 'type' => 'structure', 'members' => [ 'MediaConcatenationPipeline' => [ 'shape' => 'MediaConcatenationPipeline', ], ], ], 'CreateMediaInsightsPipelineConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'MediaInsightsPipelineConfigurationName', 'ResourceAccessRoleArn', 'Elements', ], 'members' => [ 'MediaInsightsPipelineConfigurationName' => [ 'shape' => 'MediaInsightsPipelineConfigurationNameString', ], 'ResourceAccessRoleArn' => [ 'shape' => 'Arn', ], 'RealTimeAlertConfiguration' => [ 'shape' => 'RealTimeAlertConfiguration', ], 'Elements' => [ 'shape' => 'MediaInsightsPipelineConfigurationElements', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateMediaInsightsPipelineConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'MediaInsightsPipelineConfiguration' => [ 'shape' => 'MediaInsightsPipelineConfiguration', ], ], ], 'CreateMediaInsightsPipelineRequest' => [ 'type' => 'structure', 'required' => [ 'MediaInsightsPipelineConfigurationArn', ], 'members' => [ 'MediaInsightsPipelineConfigurationArn' => [ 'shape' => 'Arn', ], 'KinesisVideoStreamSourceRuntimeConfiguration' => [ 'shape' => 'KinesisVideoStreamSourceRuntimeConfiguration', ], 'MediaInsightsRuntimeMetadata' => [ 'shape' => 'MediaInsightsRuntimeMetadata', ], 'KinesisVideoStreamRecordingSourceRuntimeConfiguration' => [ 'shape' => 'KinesisVideoStreamRecordingSourceRuntimeConfiguration', ], 'S3RecordingSinkRuntimeConfiguration' => [ 'shape' => 'S3RecordingSinkRuntimeConfiguration', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'CreateMediaInsightsPipelineResponse' => [ 'type' => 'structure', 'required' => [ 'MediaInsightsPipeline', ], 'members' => [ 'MediaInsightsPipeline' => [ 'shape' => 'MediaInsightsPipeline', ], ], ], 'CreateMediaLiveConnectorPipelineRequest' => [ 'type' => 'structure', 'required' => [ 'Sources', 'Sinks', ], 'members' => [ 'Sources' => [ 'shape' => 'LiveConnectorSourceList', ], 'Sinks' => [ 'shape' => 'LiveConnectorSinkList', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateMediaLiveConnectorPipelineResponse' => [ 'type' => 'structure', 'members' => [ 'MediaLiveConnectorPipeline' => [ 'shape' => 'MediaLiveConnectorPipeline', ], ], ], 'CreateMediaPipelineKinesisVideoStreamPoolRequest' => [ 'type' => 'structure', 'required' => [ 'StreamConfiguration', 'PoolName', ], 'members' => [ 'StreamConfiguration' => [ 'shape' => 'KinesisVideoStreamConfiguration', ], 'PoolName' => [ 'shape' => 'KinesisVideoStreamPoolName', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateMediaPipelineKinesisVideoStreamPoolResponse' => [ 'type' => 'structure', 'members' => [ 'KinesisVideoStreamPoolConfiguration' => [ 'shape' => 'KinesisVideoStreamPoolConfiguration', ], ], ], 'CreateMediaStreamPipelineRequest' => [ 'type' => 'structure', 'required' => [ 'Sources', 'Sinks', ], 'members' => [ 'Sources' => [ 'shape' => 'MediaStreamSourceList', ], 'Sinks' => [ 'shape' => 'MediaStreamSinkList', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateMediaStreamPipelineResponse' => [ 'type' => 'structure', 'members' => [ 'MediaStreamPipeline' => [ 'shape' => 'MediaStreamPipeline', ], ], ], 'DataChannelConcatenationConfiguration' => [ 'type' => 'structure', 'required' => [ 'State', ], 'members' => [ 'State' => [ 'shape' => 'ArtifactsConcatenationState', ], ], ], 'DataRetentionChangeInHours' => [ 'type' => 'integer', 'min' => 1, ], 'DataRetentionInHours' => [ 'type' => 'integer', 'min' => 0, ], 'DeleteMediaCapturePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'MediaPipelineId', ], 'members' => [ 'MediaPipelineId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'mediaPipelineId', ], ], ], 'DeleteMediaInsightsPipelineConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteMediaPipelineKinesisVideoStreamPoolRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'DeleteMediaPipelineRequest' => [ 'type' => 'structure', 'required' => [ 'MediaPipelineId', ], 'members' => [ 'MediaPipelineId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'mediaPipelineId', ], ], ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'BadRequest', 'Forbidden', 'NotFound', 'ResourceLimitExceeded', 'ServiceFailure', 'ServiceUnavailable', 'Throttling', ], ], 'ExternalUserIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExternalUserIdType', ], 'min' => 1, ], 'ExternalUserIdType' => [ 'type' => 'string', 'max' => 64, 'min' => 2, 'sensitive' => true, ], 'ForbiddenException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'FragmentNumberString' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[0-9]+$', ], 'FragmentSelector' => [ 'type' => 'structure', 'required' => [ 'FragmentSelectorType', 'TimestampRange', ], 'members' => [ 'FragmentSelectorType' => [ 'shape' => 'FragmentSelectorType', ], 'TimestampRange' => [ 'shape' => 'TimestampRange', ], ], ], 'FragmentSelectorType' => [ 'type' => 'string', 'enum' => [ 'ProducerTimestamp', 'ServerTimestamp', ], ], 'GetMediaCapturePipelineRequest' => [ 'type' => 'structure', 'required' => [ 'MediaPipelineId', ], 'members' => [ 'MediaPipelineId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'mediaPipelineId', ], ], ], 'GetMediaCapturePipelineResponse' => [ 'type' => 'structure', 'members' => [ 'MediaCapturePipeline' => [ 'shape' => 'MediaCapturePipeline', ], ], ], 'GetMediaInsightsPipelineConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetMediaInsightsPipelineConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'MediaInsightsPipelineConfiguration' => [ 'shape' => 'MediaInsightsPipelineConfiguration', ], ], ], 'GetMediaPipelineKinesisVideoStreamPoolRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'identifier', ], ], ], 'GetMediaPipelineKinesisVideoStreamPoolResponse' => [ 'type' => 'structure', 'members' => [ 'KinesisVideoStreamPoolConfiguration' => [ 'shape' => 'KinesisVideoStreamPoolConfiguration', ], ], ], 'GetMediaPipelineRequest' => [ 'type' => 'structure', 'required' => [ 'MediaPipelineId', ], 'members' => [ 'MediaPipelineId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'mediaPipelineId', ], ], ], 'GetMediaPipelineResponse' => [ 'type' => 'structure', 'members' => [ 'MediaPipeline' => [ 'shape' => 'MediaPipeline', ], ], ], 'GetSpeakerSearchTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'SpeakerSearchTaskId', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'identifier', ], 'SpeakerSearchTaskId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'speakerSearchTaskId', ], ], ], 'GetSpeakerSearchTaskResponse' => [ 'type' => 'structure', 'members' => [ 'SpeakerSearchTask' => [ 'shape' => 'SpeakerSearchTask', ], ], ], 'GetVoiceToneAnalysisTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'VoiceToneAnalysisTaskId', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'identifier', ], 'VoiceToneAnalysisTaskId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'voiceToneAnalysisTaskId', ], ], ], 'GetVoiceToneAnalysisTaskResponse' => [ 'type' => 'structure', 'members' => [ 'VoiceToneAnalysisTask' => [ 'shape' => 'VoiceToneAnalysisTask', ], ], ], 'GridViewConfiguration' => [ 'type' => 'structure', 'required' => [ 'ContentShareLayout', ], 'members' => [ 'ContentShareLayout' => [ 'shape' => 'ContentShareLayoutOption', ], 'PresenterOnlyConfiguration' => [ 'shape' => 'PresenterOnlyConfiguration', ], 'ActiveSpeakerOnlyConfiguration' => [ 'shape' => 'ActiveSpeakerOnlyConfiguration', ], 'HorizontalLayoutConfiguration' => [ 'shape' => 'HorizontalLayoutConfiguration', ], 'VerticalLayoutConfiguration' => [ 'shape' => 'VerticalLayoutConfiguration', ], 'VideoAttribute' => [ 'shape' => 'VideoAttribute', ], 'CanvasOrientation' => [ 'shape' => 'CanvasOrientation', ], ], ], 'GuidString' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-fA-F0-9]{8}(?:-[a-fA-F0-9]{4}){3}-[a-fA-F0-9]{12}', ], 'HighlightColor' => [ 'type' => 'string', 'enum' => [ 'Black', 'Blue', 'Red', 'Green', 'White', 'Yellow', ], ], 'HorizontalLayoutConfiguration' => [ 'type' => 'structure', 'members' => [ 'TileOrder' => [ 'shape' => 'TileOrder', ], 'TilePosition' => [ 'shape' => 'HorizontalTilePosition', ], 'TileCount' => [ 'shape' => 'TileCount', ], 'TileAspectRatio' => [ 'shape' => 'TileAspectRatio', ], ], ], 'HorizontalTilePosition' => [ 'type' => 'string', 'enum' => [ 'Top', 'Bottom', ], ], 'Iso8601Timestamp' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'IssueDetectionConfiguration' => [ 'type' => 'structure', 'required' => [ 'RuleName', ], 'members' => [ 'RuleName' => [ 'shape' => 'RuleName', ], ], ], 'Keyword' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[\\s0-9a-zA-Z\'-]+', ], 'KeywordMatchConfiguration' => [ 'type' => 'structure', 'required' => [ 'RuleName', 'Keywords', ], 'members' => [ 'RuleName' => [ 'shape' => 'RuleName', ], 'Keywords' => [ 'shape' => 'KeywordMatchWordList', ], 'Negate' => [ 'shape' => 'Boolean', ], ], ], 'KeywordMatchWordList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Keyword', ], 'max' => 10, 'min' => 1, ], 'KinesisDataStreamSinkConfiguration' => [ 'type' => 'structure', 'members' => [ 'InsightsTarget' => [ 'shape' => 'Arn', ], ], ], 'KinesisVideoStreamArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 'arn:[a-z\\d-]+:kinesisvideo:[a-z0-9-]+:[0-9]+:[a-z]+/[a-zA-Z0-9_.-]+/[0-9]+', ], 'KinesisVideoStreamConfiguration' => [ 'type' => 'structure', 'required' => [ 'Region', ], 'members' => [ 'Region' => [ 'shape' => 'AwsRegion', ], 'DataRetentionInHours' => [ 'shape' => 'DataRetentionInHours', ], ], ], 'KinesisVideoStreamConfigurationUpdate' => [ 'type' => 'structure', 'members' => [ 'DataRetentionInHours' => [ 'shape' => 'DataRetentionChangeInHours', ], ], ], 'KinesisVideoStreamPoolConfiguration' => [ 'type' => 'structure', 'members' => [ 'PoolArn' => [ 'shape' => 'Arn', ], 'PoolName' => [ 'shape' => 'KinesisVideoStreamPoolName', ], 'PoolId' => [ 'shape' => 'KinesisVideoStreamPoolId', ], 'PoolStatus' => [ 'shape' => 'KinesisVideoStreamPoolStatus', ], 'PoolSize' => [ 'shape' => 'KinesisVideoStreamPoolSize', ], 'StreamConfiguration' => [ 'shape' => 'KinesisVideoStreamConfiguration', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'KinesisVideoStreamPoolId' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'KinesisVideoStreamPoolName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'KinesisVideoStreamPoolSize' => [ 'type' => 'integer', 'min' => 0, ], 'KinesisVideoStreamPoolStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'DELETING', 'FAILED', ], ], 'KinesisVideoStreamPoolSummary' => [ 'type' => 'structure', 'members' => [ 'PoolName' => [ 'shape' => 'KinesisVideoStreamPoolName', ], 'PoolId' => [ 'shape' => 'KinesisVideoStreamPoolId', ], 'PoolArn' => [ 'shape' => 'Arn', ], ], ], 'KinesisVideoStreamPoolSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KinesisVideoStreamPoolSummary', ], ], 'KinesisVideoStreamRecordingSourceRuntimeConfiguration' => [ 'type' => 'structure', 'required' => [ 'Streams', 'FragmentSelector', ], 'members' => [ 'Streams' => [ 'shape' => 'RecordingStreamList', ], 'FragmentSelector' => [ 'shape' => 'FragmentSelector', ], ], ], 'KinesisVideoStreamSourceRuntimeConfiguration' => [ 'type' => 'structure', 'required' => [ 'Streams', 'MediaEncoding', 'MediaSampleRate', ], 'members' => [ 'Streams' => [ 'shape' => 'Streams', ], 'MediaEncoding' => [ 'shape' => 'MediaEncoding', ], 'MediaSampleRate' => [ 'shape' => 'MediaSampleRateHertz', ], ], ], 'KinesisVideoStreamSourceTaskConfiguration' => [ 'type' => 'structure', 'required' => [ 'StreamArn', 'ChannelId', ], 'members' => [ 'StreamArn' => [ 'shape' => 'KinesisVideoStreamArn', ], 'ChannelId' => [ 'shape' => 'ChannelId', ], 'FragmentNumber' => [ 'shape' => 'FragmentNumberString', ], ], ], 'LambdaFunctionSinkConfiguration' => [ 'type' => 'structure', 'members' => [ 'InsightsTarget' => [ 'shape' => 'Arn', ], ], ], 'LanguageOptions' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[a-zA-Z-,]+', ], 'LayoutOption' => [ 'type' => 'string', 'enum' => [ 'GridView', ], ], 'ListMediaCapturePipelinesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListMediaCapturePipelinesResponse' => [ 'type' => 'structure', 'members' => [ 'MediaCapturePipelines' => [ 'shape' => 'MediaCapturePipelineSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListMediaInsightsPipelineConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListMediaInsightsPipelineConfigurationsResponse' => [ 'type' => 'structure', 'members' => [ 'MediaInsightsPipelineConfigurations' => [ 'shape' => 'MediaInsightsPipelineConfigurationSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListMediaPipelineKinesisVideoStreamPoolsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListMediaPipelineKinesisVideoStreamPoolsResponse' => [ 'type' => 'structure', 'members' => [ 'KinesisVideoStreamPools' => [ 'shape' => 'KinesisVideoStreamPoolSummaryList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListMediaPipelinesRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'next-token', ], 'MaxResults' => [ 'shape' => 'ResultMax', 'location' => 'querystring', 'locationName' => 'max-results', ], ], ], 'ListMediaPipelinesResponse' => [ 'type' => 'structure', 'members' => [ 'MediaPipelines' => [ 'shape' => 'MediaPipelineList', ], 'NextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', 'location' => 'querystring', 'locationName' => 'arn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'LiveConnectorMuxType' => [ 'type' => 'string', 'enum' => [ 'AudioWithCompositedVideo', 'AudioWithActiveSpeakerVideo', ], ], 'LiveConnectorRTMPConfiguration' => [ 'type' => 'structure', 'required' => [ 'Url', ], 'members' => [ 'Url' => [ 'shape' => 'SensitiveString', ], 'AudioChannels' => [ 'shape' => 'AudioChannelsOption', ], 'AudioSampleRate' => [ 'shape' => 'AudioSampleRateOption', ], ], ], 'LiveConnectorSinkConfiguration' => [ 'type' => 'structure', 'required' => [ 'SinkType', 'RTMPConfiguration', ], 'members' => [ 'SinkType' => [ 'shape' => 'LiveConnectorSinkType', ], 'RTMPConfiguration' => [ 'shape' => 'LiveConnectorRTMPConfiguration', ], ], ], 'LiveConnectorSinkList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LiveConnectorSinkConfiguration', ], 'max' => 1, 'min' => 1, ], 'LiveConnectorSinkType' => [ 'type' => 'string', 'enum' => [ 'RTMP', ], ], 'LiveConnectorSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'SourceType', 'ChimeSdkMeetingLiveConnectorConfiguration', ], 'members' => [ 'SourceType' => [ 'shape' => 'LiveConnectorSourceType', ], 'ChimeSdkMeetingLiveConnectorConfiguration' => [ 'shape' => 'ChimeSdkMeetingLiveConnectorConfiguration', ], ], ], 'LiveConnectorSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LiveConnectorSourceConfiguration', ], 'max' => 1, 'min' => 1, ], 'LiveConnectorSourceType' => [ 'type' => 'string', 'enum' => [ 'ChimeSdkMeeting', ], ], 'MediaCapturePipeline' => [ 'type' => 'structure', 'members' => [ 'MediaPipelineId' => [ 'shape' => 'GuidString', ], 'MediaPipelineArn' => [ 'shape' => 'AmazonResourceName', ], 'SourceType' => [ 'shape' => 'MediaPipelineSourceType', ], 'SourceArn' => [ 'shape' => 'Arn', ], 'Status' => [ 'shape' => 'MediaPipelineStatus', ], 'SinkType' => [ 'shape' => 'MediaPipelineSinkType', ], 'SinkArn' => [ 'shape' => 'Arn', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'ChimeSdkMeetingConfiguration' => [ 'shape' => 'ChimeSdkMeetingConfiguration', ], 'SseAwsKeyManagementParams' => [ 'shape' => 'SseAwsKeyManagementParams', ], 'SinkIamRoleArn' => [ 'shape' => 'Arn', ], ], ], 'MediaCapturePipelineSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'MediaPipelineArn', 'ChimeSdkMeetingConfiguration', ], 'members' => [ 'MediaPipelineArn' => [ 'shape' => 'Arn', ], 'ChimeSdkMeetingConfiguration' => [ 'shape' => 'ChimeSdkMeetingConcatenationConfiguration', ], ], ], 'MediaCapturePipelineSummary' => [ 'type' => 'structure', 'members' => [ 'MediaPipelineId' => [ 'shape' => 'GuidString', ], 'MediaPipelineArn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'MediaCapturePipelineSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaCapturePipelineSummary', ], ], 'MediaConcatenationPipeline' => [ 'type' => 'structure', 'members' => [ 'MediaPipelineId' => [ 'shape' => 'GuidString', ], 'MediaPipelineArn' => [ 'shape' => 'AmazonResourceName', ], 'Sources' => [ 'shape' => 'ConcatenationSourceList', ], 'Sinks' => [ 'shape' => 'ConcatenationSinkList', ], 'Status' => [ 'shape' => 'MediaPipelineStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'MediaEncoding' => [ 'type' => 'string', 'enum' => [ 'pcm', ], ], 'MediaInsightsPipeline' => [ 'type' => 'structure', 'members' => [ 'MediaPipelineId' => [ 'shape' => 'GuidString', ], 'MediaPipelineArn' => [ 'shape' => 'Arn', ], 'MediaInsightsPipelineConfigurationArn' => [ 'shape' => 'Arn', ], 'Status' => [ 'shape' => 'MediaPipelineStatus', ], 'KinesisVideoStreamSourceRuntimeConfiguration' => [ 'shape' => 'KinesisVideoStreamSourceRuntimeConfiguration', ], 'MediaInsightsRuntimeMetadata' => [ 'shape' => 'MediaInsightsRuntimeMetadata', ], 'KinesisVideoStreamRecordingSourceRuntimeConfiguration' => [ 'shape' => 'KinesisVideoStreamRecordingSourceRuntimeConfiguration', ], 'S3RecordingSinkRuntimeConfiguration' => [ 'shape' => 'S3RecordingSinkRuntimeConfiguration', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'ElementStatuses' => [ 'shape' => 'MediaInsightsPipelineElementStatuses', ], ], ], 'MediaInsightsPipelineConfiguration' => [ 'type' => 'structure', 'members' => [ 'MediaInsightsPipelineConfigurationName' => [ 'shape' => 'MediaInsightsPipelineConfigurationNameString', ], 'MediaInsightsPipelineConfigurationArn' => [ 'shape' => 'Arn', ], 'ResourceAccessRoleArn' => [ 'shape' => 'Arn', ], 'RealTimeAlertConfiguration' => [ 'shape' => 'RealTimeAlertConfiguration', ], 'Elements' => [ 'shape' => 'MediaInsightsPipelineConfigurationElements', ], 'MediaInsightsPipelineConfigurationId' => [ 'shape' => 'GuidString', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'MediaInsightsPipelineConfigurationElement' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'MediaInsightsPipelineConfigurationElementType', ], 'AmazonTranscribeCallAnalyticsProcessorConfiguration' => [ 'shape' => 'AmazonTranscribeCallAnalyticsProcessorConfiguration', ], 'AmazonTranscribeProcessorConfiguration' => [ 'shape' => 'AmazonTranscribeProcessorConfiguration', ], 'KinesisDataStreamSinkConfiguration' => [ 'shape' => 'KinesisDataStreamSinkConfiguration', ], 'S3RecordingSinkConfiguration' => [ 'shape' => 'S3RecordingSinkConfiguration', ], 'VoiceAnalyticsProcessorConfiguration' => [ 'shape' => 'VoiceAnalyticsProcessorConfiguration', ], 'LambdaFunctionSinkConfiguration' => [ 'shape' => 'LambdaFunctionSinkConfiguration', ], 'SqsQueueSinkConfiguration' => [ 'shape' => 'SqsQueueSinkConfiguration', ], 'SnsTopicSinkConfiguration' => [ 'shape' => 'SnsTopicSinkConfiguration', ], 'VoiceEnhancementSinkConfiguration' => [ 'shape' => 'VoiceEnhancementSinkConfiguration', ], ], ], 'MediaInsightsPipelineConfigurationElementType' => [ 'type' => 'string', 'enum' => [ 'AmazonTranscribeCallAnalyticsProcessor', 'VoiceAnalyticsProcessor', 'AmazonTranscribeProcessor', 'KinesisDataStreamSink', 'LambdaFunctionSink', 'SqsQueueSink', 'SnsTopicSink', 'S3RecordingSink', 'VoiceEnhancementSink', ], ], 'MediaInsightsPipelineConfigurationElements' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaInsightsPipelineConfigurationElement', ], ], 'MediaInsightsPipelineConfigurationNameString' => [ 'type' => 'string', 'max' => 64, 'min' => 2, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'MediaInsightsPipelineConfigurationSummary' => [ 'type' => 'structure', 'members' => [ 'MediaInsightsPipelineConfigurationName' => [ 'shape' => 'MediaInsightsPipelineConfigurationNameString', ], 'MediaInsightsPipelineConfigurationId' => [ 'shape' => 'GuidString', ], 'MediaInsightsPipelineConfigurationArn' => [ 'shape' => 'Arn', ], ], ], 'MediaInsightsPipelineConfigurationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaInsightsPipelineConfigurationSummary', ], ], 'MediaInsightsPipelineElementStatus' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'MediaInsightsPipelineConfigurationElementType', ], 'Status' => [ 'shape' => 'MediaPipelineElementStatus', ], ], ], 'MediaInsightsPipelineElementStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaInsightsPipelineElementStatus', ], ], 'MediaInsightsRuntimeMetadata' => [ 'type' => 'map', 'key' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'String', ], 'sensitive' => true, ], 'MediaLiveConnectorPipeline' => [ 'type' => 'structure', 'members' => [ 'Sources' => [ 'shape' => 'LiveConnectorSourceList', ], 'Sinks' => [ 'shape' => 'LiveConnectorSinkList', ], 'MediaPipelineId' => [ 'shape' => 'GuidString', ], 'MediaPipelineArn' => [ 'shape' => 'AmazonResourceName', ], 'Status' => [ 'shape' => 'MediaPipelineStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'MediaPipeline' => [ 'type' => 'structure', 'members' => [ 'MediaCapturePipeline' => [ 'shape' => 'MediaCapturePipeline', ], 'MediaLiveConnectorPipeline' => [ 'shape' => 'MediaLiveConnectorPipeline', ], 'MediaConcatenationPipeline' => [ 'shape' => 'MediaConcatenationPipeline', ], 'MediaInsightsPipeline' => [ 'shape' => 'MediaInsightsPipeline', ], 'MediaStreamPipeline' => [ 'shape' => 'MediaStreamPipeline', ], ], ], 'MediaPipelineElementStatus' => [ 'type' => 'string', 'enum' => [ 'NotStarted', 'NotSupported', 'Initializing', 'InProgress', 'Failed', 'Stopping', 'Stopped', 'Paused', ], ], 'MediaPipelineList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaPipelineSummary', ], ], 'MediaPipelineSinkType' => [ 'type' => 'string', 'enum' => [ 'S3Bucket', ], ], 'MediaPipelineSourceType' => [ 'type' => 'string', 'enum' => [ 'ChimeSdkMeeting', ], ], 'MediaPipelineStatus' => [ 'type' => 'string', 'enum' => [ 'Initializing', 'InProgress', 'Failed', 'Stopping', 'Stopped', 'Paused', 'NotStarted', ], ], 'MediaPipelineStatusUpdate' => [ 'type' => 'string', 'enum' => [ 'Pause', 'Resume', ], ], 'MediaPipelineSummary' => [ 'type' => 'structure', 'members' => [ 'MediaPipelineId' => [ 'shape' => 'GuidString', ], 'MediaPipelineArn' => [ 'shape' => 'AmazonResourceName', ], ], ], 'MediaPipelineTaskStatus' => [ 'type' => 'string', 'enum' => [ 'NotStarted', 'Initializing', 'InProgress', 'Failed', 'Stopping', 'Stopped', ], ], 'MediaSampleRateHertz' => [ 'type' => 'integer', 'max' => 48000, 'min' => 8000, ], 'MediaStreamPipeline' => [ 'type' => 'structure', 'members' => [ 'MediaPipelineId' => [ 'shape' => 'GuidString', ], 'MediaPipelineArn' => [ 'shape' => 'AmazonResourceName', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'Status' => [ 'shape' => 'MediaPipelineStatus', ], 'Sources' => [ 'shape' => 'MediaStreamSourceList', ], 'Sinks' => [ 'shape' => 'MediaStreamSinkList', ], ], ], 'MediaStreamPipelineSinkType' => [ 'type' => 'string', 'enum' => [ 'KinesisVideoStreamPool', ], ], 'MediaStreamSink' => [ 'type' => 'structure', 'required' => [ 'SinkArn', 'SinkType', 'ReservedStreamCapacity', 'MediaStreamType', ], 'members' => [ 'SinkArn' => [ 'shape' => 'Arn', ], 'SinkType' => [ 'shape' => 'MediaStreamPipelineSinkType', ], 'ReservedStreamCapacity' => [ 'shape' => 'ReservedStreamCapacity', ], 'MediaStreamType' => [ 'shape' => 'MediaStreamType', ], ], ], 'MediaStreamSinkList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaStreamSink', ], 'max' => 2, 'min' => 1, ], 'MediaStreamSource' => [ 'type' => 'structure', 'required' => [ 'SourceType', 'SourceArn', ], 'members' => [ 'SourceType' => [ 'shape' => 'MediaPipelineSourceType', ], 'SourceArn' => [ 'shape' => 'Arn', ], ], ], 'MediaStreamSourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MediaStreamSource', ], 'min' => 1, ], 'MediaStreamType' => [ 'type' => 'string', 'enum' => [ 'MixedAudio', 'IndividualAudio', ], ], 'MeetingEventsConcatenationConfiguration' => [ 'type' => 'structure', 'required' => [ 'State', ], 'members' => [ 'State' => [ 'shape' => 'ArtifactsConcatenationState', ], ], ], 'ModelName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'NonEmptyString' => [ 'type' => 'string', 'max' => 1024, 'pattern' => '.*\\S.*', ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'NumberOfChannels' => [ 'type' => 'integer', 'max' => 2, 'min' => 1, ], 'PartialResultsStability' => [ 'type' => 'string', 'enum' => [ 'high', 'medium', 'low', ], ], 'ParticipantRole' => [ 'type' => 'string', 'enum' => [ 'AGENT', 'CUSTOMER', ], ], 'PiiEntityTypes' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'pattern' => '^[A-Z_, ]+', ], 'PostCallAnalyticsSettings' => [ 'type' => 'structure', 'required' => [ 'OutputLocation', 'DataAccessRoleArn', ], 'members' => [ 'OutputLocation' => [ 'shape' => 'String', ], 'DataAccessRoleArn' => [ 'shape' => 'String', ], 'ContentRedactionOutput' => [ 'shape' => 'ContentRedactionOutput', ], 'OutputEncryptionKMSKeyId' => [ 'shape' => 'String', ], ], ], 'PresenterOnlyConfiguration' => [ 'type' => 'structure', 'members' => [ 'PresenterPosition' => [ 'shape' => 'PresenterPosition', ], ], ], 'PresenterPosition' => [ 'type' => 'string', 'enum' => [ 'TopLeft', 'TopRight', 'BottomLeft', 'BottomRight', ], ], 'RealTimeAlertConfiguration' => [ 'type' => 'structure', 'members' => [ 'Disabled' => [ 'shape' => 'Boolean', ], 'Rules' => [ 'shape' => 'RealTimeAlertRuleList', ], ], ], 'RealTimeAlertRule' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'Type' => [ 'shape' => 'RealTimeAlertRuleType', ], 'KeywordMatchConfiguration' => [ 'shape' => 'KeywordMatchConfiguration', ], 'SentimentConfiguration' => [ 'shape' => 'SentimentConfiguration', ], 'IssueDetectionConfiguration' => [ 'shape' => 'IssueDetectionConfiguration', ], ], ], 'RealTimeAlertRuleList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RealTimeAlertRule', ], 'max' => 3, 'min' => 1, ], 'RealTimeAlertRuleType' => [ 'type' => 'string', 'enum' => [ 'KeywordMatch', 'Sentiment', 'IssueDetection', ], ], 'RecordingFileFormat' => [ 'type' => 'string', 'enum' => [ 'Wav', 'Opus', ], ], 'RecordingStreamConfiguration' => [ 'type' => 'structure', 'members' => [ 'StreamArn' => [ 'shape' => 'KinesisVideoStreamArn', ], ], ], 'RecordingStreamList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecordingStreamConfiguration', ], 'max' => 2, 'min' => 1, ], 'ReservedStreamCapacity' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'ResolutionOption' => [ 'type' => 'string', 'enum' => [ 'HD', 'FHD', ], ], 'ResourceLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResultMax' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'RuleName' => [ 'type' => 'string', 'max' => 64, 'min' => 2, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'S3BucketSinkConfiguration' => [ 'type' => 'structure', 'required' => [ 'Destination', ], 'members' => [ 'Destination' => [ 'shape' => 'Arn', ], ], ], 'S3RecordingSinkConfiguration' => [ 'type' => 'structure', 'members' => [ 'Destination' => [ 'shape' => 'Arn', ], 'RecordingFileFormat' => [ 'shape' => 'RecordingFileFormat', ], ], ], 'S3RecordingSinkRuntimeConfiguration' => [ 'type' => 'structure', 'required' => [ 'Destination', 'RecordingFileFormat', ], 'members' => [ 'Destination' => [ 'shape' => 'Arn', ], 'RecordingFileFormat' => [ 'shape' => 'RecordingFileFormat', ], ], ], 'SelectedVideoStreams' => [ 'type' => 'structure', 'members' => [ 'AttendeeIds' => [ 'shape' => 'AttendeeIdList', ], 'ExternalUserIds' => [ 'shape' => 'ExternalUserIdList', ], ], ], 'SensitiveString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'sensitive' => true, ], 'SentimentConfiguration' => [ 'type' => 'structure', 'required' => [ 'RuleName', 'SentimentType', 'TimePeriod', ], 'members' => [ 'RuleName' => [ 'shape' => 'RuleName', ], 'SentimentType' => [ 'shape' => 'SentimentType', ], 'TimePeriod' => [ 'shape' => 'SentimentTimePeriodInSeconds', ], ], ], 'SentimentTimePeriodInSeconds' => [ 'type' => 'integer', 'max' => 1800, 'min' => 60, ], 'SentimentType' => [ 'type' => 'string', 'enum' => [ 'NEGATIVE', ], ], 'ServiceFailureException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'SnsTopicSinkConfiguration' => [ 'type' => 'structure', 'members' => [ 'InsightsTarget' => [ 'shape' => 'Arn', ], ], ], 'SourceConfiguration' => [ 'type' => 'structure', 'members' => [ 'SelectedVideoStreams' => [ 'shape' => 'SelectedVideoStreams', ], ], ], 'SpeakerSearchTask' => [ 'type' => 'structure', 'members' => [ 'SpeakerSearchTaskId' => [ 'shape' => 'GuidString', ], 'SpeakerSearchTaskStatus' => [ 'shape' => 'MediaPipelineTaskStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], 'SqsQueueSinkConfiguration' => [ 'type' => 'structure', 'members' => [ 'InsightsTarget' => [ 'shape' => 'Arn', ], ], ], 'SseAwsKeyManagementParams' => [ 'type' => 'structure', 'required' => [ 'AwsKmsKeyId', ], 'members' => [ 'AwsKmsKeyId' => [ 'shape' => 'String', ], 'AwsKmsEncryptionContext' => [ 'shape' => 'String', ], ], ], 'StartSpeakerSearchTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'VoiceProfileDomainArn', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'identifier', ], 'VoiceProfileDomainArn' => [ 'shape' => 'Arn', ], 'KinesisVideoStreamSourceTaskConfiguration' => [ 'shape' => 'KinesisVideoStreamSourceTaskConfiguration', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'StartSpeakerSearchTaskResponse' => [ 'type' => 'structure', 'members' => [ 'SpeakerSearchTask' => [ 'shape' => 'SpeakerSearchTask', ], ], ], 'StartVoiceToneAnalysisTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'LanguageCode', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'identifier', ], 'LanguageCode' => [ 'shape' => 'VoiceAnalyticsLanguageCode', ], 'KinesisVideoStreamSourceTaskConfiguration' => [ 'shape' => 'KinesisVideoStreamSourceTaskConfiguration', ], 'ClientRequestToken' => [ 'shape' => 'ClientRequestToken', 'idempotencyToken' => true, ], ], ], 'StartVoiceToneAnalysisTaskResponse' => [ 'type' => 'structure', 'members' => [ 'VoiceToneAnalysisTask' => [ 'shape' => 'VoiceToneAnalysisTask', ], ], ], 'StopSpeakerSearchTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'SpeakerSearchTaskId', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'identifier', ], 'SpeakerSearchTaskId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'speakerSearchTaskId', ], ], ], 'StopVoiceToneAnalysisTaskRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'VoiceToneAnalysisTaskId', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'identifier', ], 'VoiceToneAnalysisTaskId' => [ 'shape' => 'GuidString', 'location' => 'uri', 'locationName' => 'voiceToneAnalysisTaskId', ], ], ], 'StreamChannelDefinition' => [ 'type' => 'structure', 'required' => [ 'NumberOfChannels', ], 'members' => [ 'NumberOfChannels' => [ 'shape' => 'NumberOfChannels', ], 'ChannelDefinitions' => [ 'shape' => 'ChannelDefinitions', ], ], ], 'StreamConfiguration' => [ 'type' => 'structure', 'required' => [ 'StreamArn', 'StreamChannelDefinition', ], 'members' => [ 'StreamArn' => [ 'shape' => 'KinesisVideoStreamArn', ], 'FragmentNumber' => [ 'shape' => 'FragmentNumberString', ], 'StreamChannelDefinition' => [ 'shape' => 'StreamChannelDefinition', ], ], ], 'Streams' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamConfiguration', ], 'max' => 2, 'min' => 1, ], 'String' => [ 'type' => 'string', 'max' => 4096, 'pattern' => '.*', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 1, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottledClientException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'TileAspectRatio' => [ 'type' => 'string', 'pattern' => '^\\d{1,2}\\/\\d{1,2}$', ], 'TileCount' => [ 'type' => 'integer', 'max' => 10, 'min' => 1, ], 'TileOrder' => [ 'type' => 'string', 'enum' => [ 'JoinSequence', 'SpeakerSequence', ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TimestampRange' => [ 'type' => 'structure', 'required' => [ 'StartTimestamp', 'EndTimestamp', ], 'members' => [ 'StartTimestamp' => [ 'shape' => 'Timestamp', ], 'EndTimestamp' => [ 'shape' => 'Timestamp', ], ], ], 'TranscriptionMessagesConcatenationConfiguration' => [ 'type' => 'structure', 'required' => [ 'State', ], 'members' => [ 'State' => [ 'shape' => 'ArtifactsConcatenationState', ], ], ], 'UnauthorizedClientException' => [ 'type' => 'structure', 'members' => [ 'Code' => [ 'shape' => 'ErrorCode', ], 'Message' => [ 'shape' => 'String', ], 'RequestId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 401, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateMediaInsightsPipelineConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'ResourceAccessRoleArn', 'Elements', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'identifier', ], 'ResourceAccessRoleArn' => [ 'shape' => 'Arn', ], 'RealTimeAlertConfiguration' => [ 'shape' => 'RealTimeAlertConfiguration', ], 'Elements' => [ 'shape' => 'MediaInsightsPipelineConfigurationElements', ], ], ], 'UpdateMediaInsightsPipelineConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'MediaInsightsPipelineConfiguration' => [ 'shape' => 'MediaInsightsPipelineConfiguration', ], ], ], 'UpdateMediaInsightsPipelineStatusRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', 'UpdateStatus', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'identifier', ], 'UpdateStatus' => [ 'shape' => 'MediaPipelineStatusUpdate', ], ], ], 'UpdateMediaPipelineKinesisVideoStreamPoolRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'NonEmptyString', 'location' => 'uri', 'locationName' => 'identifier', ], 'StreamConfiguration' => [ 'shape' => 'KinesisVideoStreamConfigurationUpdate', ], ], ], 'UpdateMediaPipelineKinesisVideoStreamPoolResponse' => [ 'type' => 'structure', 'members' => [ 'KinesisVideoStreamPoolConfiguration' => [ 'shape' => 'KinesisVideoStreamPoolConfiguration', ], ], ], 'VerticalLayoutConfiguration' => [ 'type' => 'structure', 'members' => [ 'TileOrder' => [ 'shape' => 'TileOrder', ], 'TilePosition' => [ 'shape' => 'VerticalTilePosition', ], 'TileCount' => [ 'shape' => 'TileCount', ], 'TileAspectRatio' => [ 'shape' => 'TileAspectRatio', ], ], ], 'VerticalTilePosition' => [ 'type' => 'string', 'enum' => [ 'Left', 'Right', ], ], 'VideoArtifactsConfiguration' => [ 'type' => 'structure', 'required' => [ 'State', ], 'members' => [ 'State' => [ 'shape' => 'ArtifactsState', ], 'MuxType' => [ 'shape' => 'VideoMuxType', ], ], ], 'VideoAttribute' => [ 'type' => 'structure', 'members' => [ 'CornerRadius' => [ 'shape' => 'CornerRadius', ], 'BorderColor' => [ 'shape' => 'BorderColor', ], 'HighlightColor' => [ 'shape' => 'HighlightColor', ], 'BorderThickness' => [ 'shape' => 'BorderThickness', ], ], ], 'VideoConcatenationConfiguration' => [ 'type' => 'structure', 'required' => [ 'State', ], 'members' => [ 'State' => [ 'shape' => 'ArtifactsConcatenationState', ], ], ], 'VideoMuxType' => [ 'type' => 'string', 'enum' => [ 'VideoOnly', ], ], 'VocabularyFilterMethod' => [ 'type' => 'string', 'enum' => [ 'remove', 'mask', 'tag', ], ], 'VocabularyFilterName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'VocabularyFilterNames' => [ 'type' => 'string', 'max' => 3000, 'min' => 1, 'pattern' => '^[a-zA-Z0-9,-._]+', ], 'VocabularyName' => [ 'type' => 'string', 'max' => 200, 'min' => 1, 'pattern' => '^[0-9a-zA-Z._-]+', ], 'VocabularyNames' => [ 'type' => 'string', 'max' => 3000, 'min' => 1, 'pattern' => '^[a-zA-Z0-9,-._]+', ], 'VoiceAnalyticsConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'VoiceAnalyticsLanguageCode' => [ 'type' => 'string', 'enum' => [ 'en-US', ], ], 'VoiceAnalyticsProcessorConfiguration' => [ 'type' => 'structure', 'members' => [ 'SpeakerSearchStatus' => [ 'shape' => 'VoiceAnalyticsConfigurationStatus', ], 'VoiceToneAnalysisStatus' => [ 'shape' => 'VoiceAnalyticsConfigurationStatus', ], ], ], 'VoiceEnhancementSinkConfiguration' => [ 'type' => 'structure', 'members' => [ 'Disabled' => [ 'shape' => 'Boolean', ], ], ], 'VoiceToneAnalysisTask' => [ 'type' => 'structure', 'members' => [ 'VoiceToneAnalysisTaskId' => [ 'shape' => 'GuidString', ], 'VoiceToneAnalysisTaskStatus' => [ 'shape' => 'MediaPipelineTaskStatus', ], 'CreatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], 'UpdatedTimestamp' => [ 'shape' => 'Iso8601Timestamp', ], ], ], ],];
