<?php
// Allow CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/env.php';

use Aws\S3\S3Client;
use Aws\Exception\AwsException;

// Konfigurasi R2
$r2Config = [
    'version' => 'latest',
    'region'  => 'auto',
    'endpoint' => 'https://' . env('R2_ACCOUNT_ID') . '.r2.cloudflarestorage.com',
    'credentials' => [
        'key'    => env('R2_ACCESS_KEY_ID'),
        'secret' => env('R2_SECRET_ACCESS_KEY'),
    ],
    'use_aws_shared_config_files' => false,
];

// Inisialisasi S3 Client untuk R2
$s3Client = new S3Client($r2Config);

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'uploadFile') {
    header('Content-Type: application/json');
    
    try {
        if (!isset($_FILES['file'])) {
            throw new Exception('No file uploaded');
        }

        $file = $_FILES['file'];
        $filename = uniqid() . '_' . time() . '_' . basename($file['name']);
        
        // Upload file ke R2
        $result = $s3Client->putObject([
            'Bucket' => env('R2_BUCKET_NAME'),
            'Key'    => $filename,
            'Body'   => fopen($file['tmp_name'], 'rb'),
            'ContentType' => $file['type'],
            'ACL'    => 'public-read'
        ]);

        // Generate public URL
        $publicUrl = env('R2_PUBLIC_URL') . '/' . $filename;
        
        echo json_encode([
            'success' => true,
            'publicUrl' => $publicUrl
        ]);
        
    } catch (Exception $e) {
        error_log("Error uploading file: " . $e->getMessage());
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'error' => 'Failed to upload file: ' . $e->getMessage()
        ]);
    }
    exit;
}
?> 