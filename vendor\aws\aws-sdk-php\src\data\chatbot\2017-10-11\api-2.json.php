<?php
// This file was auto-generated from sdk-root/src/data/chatbot/2017-10-11/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-10-11', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'chatbot', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Chatbot', 'serviceId' => 'chatbot', 'signatureVersion' => 'v4', 'signingName' => 'chatbot', 'uid' => 'chatbot-2017-10-11', ], 'operations' => [ 'AssociateToConfiguration' => [ 'name' => 'AssociateToConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/associate-to-configuration', 'responseCode' => 201, ], 'input' => [ 'shape' => 'AssociateToConfigurationRequest', ], 'output' => [ 'shape' => 'AssociateToConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'CreateChimeWebhookConfiguration' => [ 'name' => 'CreateChimeWebhookConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-chime-webhook-configuration', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateChimeWebhookConfigurationRequest', ], 'output' => [ 'shape' => 'CreateChimeWebhookConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'CreateChimeWebhookConfigurationException', ], ], ], 'CreateCustomAction' => [ 'name' => 'CreateCustomAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-custom-action', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateCustomActionRequest', ], 'output' => [ 'shape' => 'CreateCustomActionResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'CreateMicrosoftTeamsChannelConfiguration' => [ 'name' => 'CreateMicrosoftTeamsChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-ms-teams-channel-configuration', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateTeamsChannelConfigurationRequest', ], 'output' => [ 'shape' => 'CreateTeamsChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'CreateTeamsChannelConfigurationException', ], ], ], 'CreateSlackChannelConfiguration' => [ 'name' => 'CreateSlackChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/create-slack-channel-configuration', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateSlackChannelConfigurationRequest', ], 'output' => [ 'shape' => 'CreateSlackChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'LimitExceededException', ], [ 'shape' => 'CreateSlackChannelConfigurationException', ], ], ], 'DeleteChimeWebhookConfiguration' => [ 'name' => 'DeleteChimeWebhookConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-chime-webhook-configuration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteChimeWebhookConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteChimeWebhookConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DeleteChimeWebhookConfigurationException', ], ], ], 'DeleteCustomAction' => [ 'name' => 'DeleteCustomAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-custom-action', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteCustomActionRequest', ], 'output' => [ 'shape' => 'DeleteCustomActionResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'DeleteMicrosoftTeamsChannelConfiguration' => [ 'name' => 'DeleteMicrosoftTeamsChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-ms-teams-channel-configuration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTeamsChannelConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteTeamsChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DeleteTeamsChannelConfigurationException', ], ], ], 'DeleteMicrosoftTeamsConfiguredTeam' => [ 'name' => 'DeleteMicrosoftTeamsConfiguredTeam', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-ms-teams-configured-teams', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTeamsConfiguredTeamRequest', ], 'output' => [ 'shape' => 'DeleteTeamsConfiguredTeamResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DeleteTeamsConfiguredTeamException', ], ], ], 'DeleteMicrosoftTeamsUserIdentity' => [ 'name' => 'DeleteMicrosoftTeamsUserIdentity', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-ms-teams-user-identity', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteMicrosoftTeamsUserIdentityRequest', ], 'output' => [ 'shape' => 'DeleteMicrosoftTeamsUserIdentityResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'DeleteMicrosoftTeamsUserIdentityException', ], ], ], 'DeleteSlackChannelConfiguration' => [ 'name' => 'DeleteSlackChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-slack-channel-configuration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSlackChannelConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteSlackChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'DeleteSlackChannelConfigurationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteSlackUserIdentity' => [ 'name' => 'DeleteSlackUserIdentity', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-slack-user-identity', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSlackUserIdentityRequest', ], 'output' => [ 'shape' => 'DeleteSlackUserIdentityResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DeleteSlackUserIdentityException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteSlackWorkspaceAuthorization' => [ 'name' => 'DeleteSlackWorkspaceAuthorization', 'http' => [ 'method' => 'POST', 'requestUri' => '/delete-slack-workspace-authorization', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteSlackWorkspaceAuthorizationRequest', ], 'output' => [ 'shape' => 'DeleteSlackWorkspaceAuthorizationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DeleteSlackWorkspaceAuthorizationFault', ], ], ], 'DescribeChimeWebhookConfigurations' => [ 'name' => 'DescribeChimeWebhookConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-chime-webhook-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeChimeWebhookConfigurationsRequest', ], 'output' => [ 'shape' => 'DescribeChimeWebhookConfigurationsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'DescribeChimeWebhookConfigurationsException', ], ], ], 'DescribeSlackChannelConfigurations' => [ 'name' => 'DescribeSlackChannelConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-slack-channel-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSlackChannelConfigurationsRequest', ], 'output' => [ 'shape' => 'DescribeSlackChannelConfigurationsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'DescribeSlackChannelConfigurationsException', ], ], ], 'DescribeSlackUserIdentities' => [ 'name' => 'DescribeSlackUserIdentities', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-slack-user-identities', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSlackUserIdentitiesRequest', ], 'output' => [ 'shape' => 'DescribeSlackUserIdentitiesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'DescribeSlackUserIdentitiesException', ], ], ], 'DescribeSlackWorkspaces' => [ 'name' => 'DescribeSlackWorkspaces', 'http' => [ 'method' => 'POST', 'requestUri' => '/describe-slack-workspaces', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeSlackWorkspacesRequest', ], 'output' => [ 'shape' => 'DescribeSlackWorkspacesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'DescribeSlackWorkspacesException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DisassociateFromConfiguration' => [ 'name' => 'DisassociateFromConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/disassociate-from-configuration', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DisassociateFromConfigurationRequest', ], 'output' => [ 'shape' => 'DisassociateFromConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'UnauthorizedException', ], ], 'idempotent' => true, ], 'GetAccountPreferences' => [ 'name' => 'GetAccountPreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/get-account-preferences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAccountPreferencesRequest', ], 'output' => [ 'shape' => 'GetAccountPreferencesResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'GetAccountPreferencesException', ], ], ], 'GetCustomAction' => [ 'name' => 'GetCustomAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/get-custom-action', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCustomActionRequest', ], 'output' => [ 'shape' => 'GetCustomActionResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'GetMicrosoftTeamsChannelConfiguration' => [ 'name' => 'GetMicrosoftTeamsChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/get-ms-teams-channel-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTeamsChannelConfigurationRequest', ], 'output' => [ 'shape' => 'GetTeamsChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'GetTeamsChannelConfigurationException', ], ], ], 'ListAssociations' => [ 'name' => 'ListAssociations', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-associations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAssociationsRequest', ], 'output' => [ 'shape' => 'ListAssociationsResult', ], ], 'ListCustomActions' => [ 'name' => 'ListCustomActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-custom-actions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCustomActionsRequest', ], 'output' => [ 'shape' => 'ListCustomActionsResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'ListMicrosoftTeamsChannelConfigurations' => [ 'name' => 'ListMicrosoftTeamsChannelConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-ms-teams-channel-configurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTeamsChannelConfigurationsRequest', ], 'output' => [ 'shape' => 'ListTeamsChannelConfigurationsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ListTeamsChannelConfigurationsException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListMicrosoftTeamsConfiguredTeams' => [ 'name' => 'ListMicrosoftTeamsConfiguredTeams', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-ms-teams-configured-teams', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMicrosoftTeamsConfiguredTeamsRequest', ], 'output' => [ 'shape' => 'ListMicrosoftTeamsConfiguredTeamsResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ListMicrosoftTeamsConfiguredTeamsException', ], ], ], 'ListMicrosoftTeamsUserIdentities' => [ 'name' => 'ListMicrosoftTeamsUserIdentities', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-ms-teams-user-identities', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMicrosoftTeamsUserIdentitiesRequest', ], 'output' => [ 'shape' => 'ListMicrosoftTeamsUserIdentitiesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ListMicrosoftTeamsUserIdentitiesException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/list-tags-for-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceError', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tag-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'TooManyTagsException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/untag-resource', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceError', ], ], ], 'UpdateAccountPreferences' => [ 'name' => 'UpdateAccountPreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-account-preferences', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAccountPreferencesRequest', ], 'output' => [ 'shape' => 'UpdateAccountPreferencesResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UpdateAccountPreferencesException', ], ], ], 'UpdateChimeWebhookConfiguration' => [ 'name' => 'UpdateChimeWebhookConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-chime-webhook-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateChimeWebhookConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateChimeWebhookConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'UpdateChimeWebhookConfigurationException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateCustomAction' => [ 'name' => 'UpdateCustomAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-custom-action', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCustomActionRequest', ], 'output' => [ 'shape' => 'UpdateCustomActionResult', ], 'errors' => [ [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServiceError', ], [ 'shape' => 'UnauthorizedException', ], ], ], 'UpdateMicrosoftTeamsChannelConfiguration' => [ 'name' => 'UpdateMicrosoftTeamsChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-ms-teams-channel-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTeamsChannelConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateTeamsChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UpdateTeamsChannelConfigurationException', ], ], ], 'UpdateSlackChannelConfiguration' => [ 'name' => 'UpdateSlackChannelConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/update-slack-channel-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSlackChannelConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateSlackChannelConfigurationResult', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'UpdateSlackChannelConfigurationException', ], ], ], ], 'shapes' => [ 'AccountPreferences' => [ 'type' => 'structure', 'members' => [ 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], 'TrainingDataCollectionEnabled' => [ 'shape' => 'BooleanAccountPreference', ], ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:aws:(wheatley|chatbot):[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}', ], 'Arn' => [ 'type' => 'string', 'max' => 1224, 'min' => 12, 'pattern' => 'arn:aws:[A-Za-z0-9][A-Za-z0-9_/.-]{0,62}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}', ], 'AssociateToConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', 'ChatConfiguration', ], 'members' => [ 'Resource' => [ 'shape' => 'ResourceIdentifier', ], 'ChatConfiguration' => [ 'shape' => 'ChatConfigurationArn', ], ], ], 'AssociateToConfigurationResult' => [ 'type' => 'structure', 'members' => [], ], 'AssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociationListing', ], ], 'AssociationListing' => [ 'type' => 'structure', 'required' => [ 'Resource', ], 'members' => [ 'Resource' => [ 'shape' => 'Arn', ], ], ], 'AwsUserIdentity' => [ 'type' => 'string', 'max' => 1101, 'min' => 15, 'pattern' => 'arn:aws:(iam|sts)::[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}', ], 'BooleanAccountPreference' => [ 'type' => 'boolean', 'box' => true, ], 'ChatConfigurationArn' => [ 'type' => 'string', 'max' => 1169, 'min' => 19, 'pattern' => 'arn:aws:(wheatley|chatbot):[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]{0,1023}', ], 'ChimeWebhookConfiguration' => [ 'type' => 'structure', 'required' => [ 'WebhookDescription', 'ChatConfigurationArn', 'IamRoleArn', 'SnsTopicArns', ], 'members' => [ 'WebhookDescription' => [ 'shape' => 'ChimeWebhookDescription', ], 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'ConfigurationName' => [ 'shape' => 'ConfigurationName', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'Tags' => [ 'shape' => 'Tags', ], 'State' => [ 'shape' => 'ResourceState', ], 'StateReason' => [ 'shape' => 'String', ], ], ], 'ChimeWebhookConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ChimeWebhookConfiguration', ], ], 'ChimeWebhookDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'sensitive' => true, ], 'ChimeWebhookUrl' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => 'https://hooks\\.chime\\.aws/incomingwebhooks/[A-Za-z0-9\\-]+?\\?token=[A-Za-z0-9\\-]+', 'sensitive' => true, ], 'ClientToken' => [ 'type' => 'string', 'max' => 126, 'min' => 33, 'pattern' => '[\\x21-\\x7F]+', ], 'ConfigurationName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Za-z0-9-_]+', ], 'ConfiguredTeam' => [ 'type' => 'structure', 'required' => [ 'TenantId', 'TeamId', ], 'members' => [ 'TenantId' => [ 'shape' => 'UUID', ], 'TeamId' => [ 'shape' => 'UUID', ], 'TeamName' => [ 'shape' => 'UUID', ], 'State' => [ 'shape' => 'ResourceState', ], 'StateReason' => [ 'shape' => 'String', ], ], ], 'ConfiguredTeamsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConfiguredTeam', ], ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateChimeWebhookConfigurationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'CreateChimeWebhookConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'WebhookDescription', 'WebhookUrl', 'SnsTopicArns', 'IamRoleArn', 'ConfigurationName', ], 'members' => [ 'WebhookDescription' => [ 'shape' => 'ChimeWebhookDescription', ], 'WebhookUrl' => [ 'shape' => 'ChimeWebhookUrl', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'ConfigurationName' => [ 'shape' => 'ConfigurationName', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateChimeWebhookConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'WebhookConfiguration' => [ 'shape' => 'ChimeWebhookConfiguration', ], ], ], 'CreateCustomActionRequest' => [ 'type' => 'structure', 'required' => [ 'Definition', 'ActionName', ], 'members' => [ 'Definition' => [ 'shape' => 'CustomActionDefinition', ], 'AliasName' => [ 'shape' => 'CustomActionAliasName', ], 'Attachments' => [ 'shape' => 'CustomActionAttachmentList', ], 'Tags' => [ 'shape' => 'TagList', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'ActionName' => [ 'shape' => 'CustomActionName', ], ], ], 'CreateCustomActionResult' => [ 'type' => 'structure', 'required' => [ 'CustomActionArn', ], 'members' => [ 'CustomActionArn' => [ 'shape' => 'CustomActionArn', ], ], ], 'CreateSlackChannelConfigurationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'CreateSlackChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'SlackTeamId', 'SlackChannelId', 'IamRoleArn', 'ConfigurationName', ], 'members' => [ 'SlackTeamId' => [ 'shape' => 'SlackTeamId', ], 'SlackChannelId' => [ 'shape' => 'SlackChannelId', ], 'SlackChannelName' => [ 'shape' => 'SlackChannelDisplayName', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'ConfigurationName' => [ 'shape' => 'ConfigurationName', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'GuardrailPolicyArns' => [ 'shape' => 'GuardrailPolicyArnList', ], 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateSlackChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'ChannelConfiguration' => [ 'shape' => 'SlackChannelConfiguration', ], ], ], 'CreateTeamsChannelConfigurationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'CreateTeamsChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChannelId', 'TeamId', 'TenantId', 'IamRoleArn', 'ConfigurationName', ], 'members' => [ 'ChannelId' => [ 'shape' => 'TeamsChannelId', ], 'ChannelName' => [ 'shape' => 'TeamsChannelName', ], 'TeamId' => [ 'shape' => 'UUID', ], 'TeamName' => [ 'shape' => 'TeamName', ], 'TenantId' => [ 'shape' => 'UUID', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'ConfigurationName' => [ 'shape' => 'ConfigurationName', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'GuardrailPolicyArns' => [ 'shape' => 'GuardrailPolicyArnList', ], 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateTeamsChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'ChannelConfiguration' => [ 'shape' => 'TeamsChannelConfiguration', ], ], ], 'CustomAction' => [ 'type' => 'structure', 'required' => [ 'CustomActionArn', 'Definition', ], 'members' => [ 'CustomActionArn' => [ 'shape' => 'CustomActionArn', ], 'Definition' => [ 'shape' => 'CustomActionDefinition', ], 'AliasName' => [ 'shape' => 'CustomActionAliasName', ], 'Attachments' => [ 'shape' => 'CustomActionAttachmentList', ], 'ActionName' => [ 'shape' => 'CustomActionName', ], ], ], 'CustomActionAliasName' => [ 'type' => 'string', 'max' => 30, 'min' => 1, 'pattern' => '[A-Za-z0-9-_]+', ], 'CustomActionArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:aws:chatbot:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9_/.-]{0,63}:custom-action/[a-zA-Z0-9_-]{1,64}', ], 'CustomActionArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomActionArn', ], ], 'CustomActionAttachment' => [ 'type' => 'structure', 'members' => [ 'NotificationType' => [ 'shape' => 'CustomActionAttachmentNotificationType', ], 'ButtonText' => [ 'shape' => 'CustomActionButtonText', ], 'Criteria' => [ 'shape' => 'CustomActionAttachmentCriteriaList', ], 'Variables' => [ 'shape' => 'CustomActionAttachmentVariables', ], ], ], 'CustomActionAttachmentCriteria' => [ 'type' => 'structure', 'required' => [ 'Operator', 'VariableName', ], 'members' => [ 'Operator' => [ 'shape' => 'CustomActionAttachmentCriteriaOperator', ], 'VariableName' => [ 'shape' => 'CustomActionAttachmentCriteriaVariableNameString', ], 'Value' => [ 'shape' => 'CustomActionAttachmentCriteriaValueString', ], ], ], 'CustomActionAttachmentCriteriaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomActionAttachmentCriteria', ], 'max' => 5, 'min' => 1, ], 'CustomActionAttachmentCriteriaOperator' => [ 'type' => 'string', 'enum' => [ 'HAS_VALUE', 'EQUALS', ], ], 'CustomActionAttachmentCriteriaValueString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[\\S\\s]+', ], 'CustomActionAttachmentCriteriaVariableNameString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[A-Za-z0-9-_]+', ], 'CustomActionAttachmentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomActionAttachment', ], ], 'CustomActionAttachmentNotificationType' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[a-zA-Z0-9-]+', ], 'CustomActionAttachmentVariables' => [ 'type' => 'map', 'key' => [ 'shape' => 'CustomActionAttachmentVariablesKeyString', ], 'value' => [ 'shape' => 'CustomActionAttachmentVariablesValueString', ], 'max' => 5, 'min' => 1, ], 'CustomActionAttachmentVariablesKeyString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[A-Za-z0-9-_]+', ], 'CustomActionAttachmentVariablesValueString' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[\\x21-\\x7F]+', ], 'CustomActionButtonText' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '[\\S\\s]+', ], 'CustomActionDefinition' => [ 'type' => 'structure', 'required' => [ 'CommandText', ], 'members' => [ 'CommandText' => [ 'shape' => 'CustomActionDefinitionCommandTextString', ], ], ], 'CustomActionDefinitionCommandTextString' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '[\\S\\s]+', ], 'CustomActionName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]{1,64}', ], 'CustomerCwLogLevel' => [ 'type' => 'string', 'max' => 5, 'min' => 4, 'pattern' => '(ERROR|INFO|NONE)', ], 'DeleteChimeWebhookConfigurationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DeleteChimeWebhookConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], ], ], 'DeleteChimeWebhookConfigurationResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCustomActionRequest' => [ 'type' => 'structure', 'required' => [ 'CustomActionArn', ], 'members' => [ 'CustomActionArn' => [ 'shape' => 'CustomActionArn', ], ], ], 'DeleteCustomActionResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteMicrosoftTeamsUserIdentityException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DeleteMicrosoftTeamsUserIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', 'UserId', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'UserId' => [ 'shape' => 'UUID', ], ], ], 'DeleteMicrosoftTeamsUserIdentityResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSlackChannelConfigurationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DeleteSlackChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], ], ], 'DeleteSlackChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSlackUserIdentityException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DeleteSlackUserIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', 'SlackTeamId', 'SlackUserId', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'SlackTeamId' => [ 'shape' => 'SlackTeamId', ], 'SlackUserId' => [ 'shape' => 'SlackUserId', ], ], ], 'DeleteSlackUserIdentityResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSlackWorkspaceAuthorizationFault' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DeleteSlackWorkspaceAuthorizationRequest' => [ 'type' => 'structure', 'required' => [ 'SlackTeamId', ], 'members' => [ 'SlackTeamId' => [ 'shape' => 'SlackTeamId', ], ], ], 'DeleteSlackWorkspaceAuthorizationResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTeamsChannelConfigurationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DeleteTeamsChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], ], ], 'DeleteTeamsChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTeamsConfiguredTeamException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DeleteTeamsConfiguredTeamRequest' => [ 'type' => 'structure', 'required' => [ 'TeamId', ], 'members' => [ 'TeamId' => [ 'shape' => 'UUID', ], ], ], 'DeleteTeamsConfiguredTeamResult' => [ 'type' => 'structure', 'members' => [], ], 'DescribeChimeWebhookConfigurationsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DescribeChimeWebhookConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], ], ], 'DescribeChimeWebhookConfigurationsResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'WebhookConfigurations' => [ 'shape' => 'ChimeWebhookConfigurationList', ], ], ], 'DescribeSlackChannelConfigurationsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DescribeSlackChannelConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], ], ], 'DescribeSlackChannelConfigurationsResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'SlackChannelConfigurations' => [ 'shape' => 'SlackChannelConfigurationList', ], ], ], 'DescribeSlackUserIdentitiesException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DescribeSlackUserIdentitiesRequest' => [ 'type' => 'structure', 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeSlackUserIdentitiesResult' => [ 'type' => 'structure', 'members' => [ 'SlackUserIdentities' => [ 'shape' => 'SlackUserIdentitiesList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeSlackWorkspacesException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'DescribeSlackWorkspacesRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DescribeSlackWorkspacesResult' => [ 'type' => 'structure', 'members' => [ 'SlackWorkspaces' => [ 'shape' => 'SlackWorkspacesList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'DisassociateFromConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', 'ChatConfiguration', ], 'members' => [ 'Resource' => [ 'shape' => 'ResourceIdentifier', ], 'ChatConfiguration' => [ 'shape' => 'ChatConfigurationArn', ], ], ], 'DisassociateFromConfigurationResult' => [ 'type' => 'structure', 'members' => [], ], 'ErrorMessage' => [ 'type' => 'string', ], 'GetAccountPreferencesException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'GetAccountPreferencesRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetAccountPreferencesResult' => [ 'type' => 'structure', 'members' => [ 'AccountPreferences' => [ 'shape' => 'AccountPreferences', ], ], ], 'GetCustomActionRequest' => [ 'type' => 'structure', 'required' => [ 'CustomActionArn', ], 'members' => [ 'CustomActionArn' => [ 'shape' => 'CustomActionArn', ], ], ], 'GetCustomActionResult' => [ 'type' => 'structure', 'members' => [ 'CustomAction' => [ 'shape' => 'CustomAction', ], ], ], 'GetTeamsChannelConfigurationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'GetTeamsChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], ], ], 'GetTeamsChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'ChannelConfiguration' => [ 'shape' => 'TeamsChannelConfiguration', ], ], ], 'GuardrailPolicyArn' => [ 'type' => 'string', 'max' => 1163, 'min' => 11, 'pattern' => '(^$|(?!.*\\/aws-service-role\\/.*)arn:aws:iam:[A-Za-z0-9_\\/.-]{0,63}:[A-Za-z0-9_\\/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_\\/+=,@.-]{0,1023})', ], 'GuardrailPolicyArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GuardrailPolicyArn', ], ], 'InternalServiceError' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ListAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfiguration', ], 'members' => [ 'ChatConfiguration' => [ 'shape' => 'ChatConfigurationArn', ], 'MaxResults' => [ 'shape' => 'ListAssociationsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'ListAssociationsRequestNextTokenString', ], ], ], 'ListAssociationsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListAssociationsRequestNextTokenString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\x20-\\x7F]+', ], 'ListAssociationsResult' => [ 'type' => 'structure', 'required' => [ 'Associations', ], 'members' => [ 'Associations' => [ 'shape' => 'AssociationList', ], 'NextToken' => [ 'shape' => 'ListAssociationsResultNextTokenString', ], ], ], 'ListAssociationsResultNextTokenString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\x20-\\x7F]+', ], 'ListCustomActionsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'ListCustomActionsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'ListCustomActionsRequestNextTokenString', ], ], ], 'ListCustomActionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListCustomActionsRequestNextTokenString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\x20-\\x7F]+', ], 'ListCustomActionsResult' => [ 'type' => 'structure', 'required' => [ 'CustomActions', ], 'members' => [ 'CustomActions' => [ 'shape' => 'CustomActionArnList', ], 'NextToken' => [ 'shape' => 'ListCustomActionsResultNextTokenString', ], ], ], 'ListCustomActionsResultNextTokenString' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[\\x20-\\x7F]+', ], 'ListMicrosoftTeamsConfiguredTeamsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListMicrosoftTeamsConfiguredTeamsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListMicrosoftTeamsConfiguredTeamsResult' => [ 'type' => 'structure', 'members' => [ 'ConfiguredTeams' => [ 'shape' => 'ConfiguredTeamsList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListMicrosoftTeamsUserIdentitiesException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListMicrosoftTeamsUserIdentitiesRequest' => [ 'type' => 'structure', 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListMicrosoftTeamsUserIdentitiesResult' => [ 'type' => 'structure', 'members' => [ 'TeamsUserIdentities' => [ 'shape' => 'TeamsUserIdentitiesList', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListTeamsChannelConfigurationsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListTeamsChannelConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'PaginationToken', ], 'TeamId' => [ 'shape' => 'UUID', ], ], ], 'ListTeamsChannelConfigurationsResult' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'PaginationToken', ], 'TeamChannelConfigurations' => [ 'shape' => 'TeamChannelConfigurationsList', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'PaginationToken' => [ 'type' => 'string', 'max' => 1276, 'min' => 1, 'pattern' => '[a-zA-Z0-9=\\/+_.\\-,#:\\\\"{}]{4,1276}', ], 'ResourceIdentifier' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, 'pattern' => 'arn:aws:chatbot:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9_/.-]{0,63}:custom-action/[a-zA-Z0-9_-]{1,64}', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceState' => [ 'type' => 'string', 'pattern' => '(ENABLED|DISABLED)', ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'SlackChannelConfiguration' => [ 'type' => 'structure', 'required' => [ 'SlackTeamName', 'SlackTeamId', 'SlackChannelId', 'SlackChannelName', 'ChatConfigurationArn', 'IamRoleArn', 'SnsTopicArns', ], 'members' => [ 'SlackTeamName' => [ 'shape' => 'SlackTeamName', ], 'SlackTeamId' => [ 'shape' => 'SlackTeamId', ], 'SlackChannelId' => [ 'shape' => 'SlackChannelId', ], 'SlackChannelName' => [ 'shape' => 'SlackChannelDisplayName', ], 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'ConfigurationName' => [ 'shape' => 'ConfigurationName', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'GuardrailPolicyArns' => [ 'shape' => 'GuardrailPolicyArnList', ], 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], 'Tags' => [ 'shape' => 'Tags', ], 'State' => [ 'shape' => 'ResourceState', ], 'StateReason' => [ 'shape' => 'String', ], ], ], 'SlackChannelConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlackChannelConfiguration', ], ], 'SlackChannelDisplayName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'sensitive' => true, ], 'SlackChannelId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[A-Za-z0-9]+', ], 'SlackTeamId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[0-9A-Z]{1,255}', ], 'SlackTeamName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'SlackUserId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '(.*)', ], 'SlackUserIdentitiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlackUserIdentity', ], ], 'SlackUserIdentity' => [ 'type' => 'structure', 'required' => [ 'IamRoleArn', 'ChatConfigurationArn', 'SlackTeamId', 'SlackUserId', ], 'members' => [ 'IamRoleArn' => [ 'shape' => 'Arn', ], 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'SlackTeamId' => [ 'shape' => 'SlackTeamId', ], 'SlackUserId' => [ 'shape' => 'SlackUserId', ], 'AwsUserIdentity' => [ 'shape' => 'AwsUserIdentity', ], ], ], 'SlackWorkspace' => [ 'type' => 'structure', 'required' => [ 'SlackTeamId', 'SlackTeamName', ], 'members' => [ 'SlackTeamId' => [ 'shape' => 'SlackTeamId', ], 'SlackTeamName' => [ 'shape' => 'SlackTeamName', ], 'State' => [ 'shape' => 'ResourceState', ], 'StateReason' => [ 'shape' => 'String', ], ], ], 'SlackWorkspacesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SlackWorkspace', ], ], 'SnsTopicArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Arn', ], ], 'String' => [ 'type' => 'string', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'TagKey', 'TagValue', ], 'members' => [ 'TagKey' => [ 'shape' => 'TagKey', ], 'TagValue' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TeamChannelConfigurationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TeamsChannelConfiguration', ], ], 'TeamName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '(.*)', 'sensitive' => true, ], 'TeamsChannelConfiguration' => [ 'type' => 'structure', 'required' => [ 'ChannelId', 'TeamId', 'TenantId', 'ChatConfigurationArn', 'IamRoleArn', 'SnsTopicArns', ], 'members' => [ 'ChannelId' => [ 'shape' => 'TeamsChannelId', ], 'ChannelName' => [ 'shape' => 'TeamsChannelName', ], 'TeamId' => [ 'shape' => 'UUID', ], 'TeamName' => [ 'shape' => 'TeamName', ], 'TenantId' => [ 'shape' => 'UUID', ], 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'ConfigurationName' => [ 'shape' => 'ConfigurationName', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'GuardrailPolicyArns' => [ 'shape' => 'GuardrailPolicyArnList', ], 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], 'Tags' => [ 'shape' => 'Tags', ], 'State' => [ 'shape' => 'ResourceState', ], 'StateReason' => [ 'shape' => 'String', ], ], ], 'TeamsChannelId' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '([a-zA-Z0-9-_=+\\/.,])*%3[aA]([a-zA-Z0-9-_=+\\/.,])*%40([a-zA-Z0-9-_=+\\/.,])*', ], 'TeamsChannelName' => [ 'type' => 'string', 'max' => 1000, 'min' => 1, 'pattern' => '(.*)', 'sensitive' => true, ], 'TeamsUserIdentitiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TeamsUserIdentity', ], ], 'TeamsUserIdentity' => [ 'type' => 'structure', 'required' => [ 'IamRoleArn', 'ChatConfigurationArn', 'TeamId', ], 'members' => [ 'IamRoleArn' => [ 'shape' => 'Arn', ], 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'TeamId' => [ 'shape' => 'UUID', ], 'UserId' => [ 'shape' => 'UUID', ], 'AwsUserIdentity' => [ 'shape' => 'AwsUserIdentity', ], 'TeamsChannelId' => [ 'shape' => 'TeamsChannelId', ], 'TeamsTenantId' => [ 'shape' => 'UUID', ], ], ], 'TooManyTagsException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'UUID' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[0-9A-Fa-f]{8}(?:-[0-9A-Fa-f]{4}){3}-[0-9A-Fa-f]{12}', ], 'UnauthorizedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateAccountPreferencesException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'UpdateAccountPreferencesRequest' => [ 'type' => 'structure', 'members' => [ 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], 'TrainingDataCollectionEnabled' => [ 'shape' => 'BooleanAccountPreference', ], ], ], 'UpdateAccountPreferencesResult' => [ 'type' => 'structure', 'members' => [ 'AccountPreferences' => [ 'shape' => 'AccountPreferences', ], ], ], 'UpdateChimeWebhookConfigurationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'UpdateChimeWebhookConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'WebhookDescription' => [ 'shape' => 'ChimeWebhookDescription', ], 'WebhookUrl' => [ 'shape' => 'ChimeWebhookUrl', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], ], ], 'UpdateChimeWebhookConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'WebhookConfiguration' => [ 'shape' => 'ChimeWebhookConfiguration', ], ], ], 'UpdateCustomActionRequest' => [ 'type' => 'structure', 'required' => [ 'CustomActionArn', 'Definition', ], 'members' => [ 'CustomActionArn' => [ 'shape' => 'CustomActionArn', ], 'Definition' => [ 'shape' => 'CustomActionDefinition', ], 'AliasName' => [ 'shape' => 'CustomActionAliasName', ], 'Attachments' => [ 'shape' => 'CustomActionAttachmentList', ], ], ], 'UpdateCustomActionResult' => [ 'type' => 'structure', 'required' => [ 'CustomActionArn', ], 'members' => [ 'CustomActionArn' => [ 'shape' => 'CustomActionArn', ], ], ], 'UpdateSlackChannelConfigurationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'UpdateSlackChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', 'SlackChannelId', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'SlackChannelId' => [ 'shape' => 'SlackChannelId', ], 'SlackChannelName' => [ 'shape' => 'SlackChannelDisplayName', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'GuardrailPolicyArns' => [ 'shape' => 'GuardrailPolicyArnList', ], 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], ], ], 'UpdateSlackChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'ChannelConfiguration' => [ 'shape' => 'SlackChannelConfiguration', ], ], ], 'UpdateTeamsChannelConfigurationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'UpdateTeamsChannelConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ChatConfigurationArn', 'ChannelId', ], 'members' => [ 'ChatConfigurationArn' => [ 'shape' => 'ChatConfigurationArn', ], 'ChannelId' => [ 'shape' => 'TeamsChannelId', ], 'ChannelName' => [ 'shape' => 'TeamsChannelName', ], 'SnsTopicArns' => [ 'shape' => 'SnsTopicArnList', ], 'IamRoleArn' => [ 'shape' => 'Arn', ], 'LoggingLevel' => [ 'shape' => 'CustomerCwLogLevel', ], 'GuardrailPolicyArns' => [ 'shape' => 'GuardrailPolicyArnList', ], 'UserAuthorizationRequired' => [ 'shape' => 'BooleanAccountPreference', ], ], ], 'UpdateTeamsChannelConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'ChannelConfiguration' => [ 'shape' => 'TeamsChannelConfiguration', ], ], ], ],];
