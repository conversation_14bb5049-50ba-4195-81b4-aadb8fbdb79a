<?php
// This file was auto-generated from sdk-root/src/data/ivs-realtime/2020-07-14/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-07-14', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'ivsrealtime', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceAbbreviation' => 'ivsrealtime', 'serviceFullName' => 'Amazon Interactive Video Service RealTime', 'serviceId' => 'IVS RealTime', 'signatureVersion' => 'v4', 'signingName' => 'ivs', 'uid' => 'ivs-realtime-2020-07-14', ], 'operations' => [ 'CreateEncoderConfiguration' => [ 'name' => 'CreateEncoderConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateEncoderConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateEncoderConfigurationRequest', ], 'output' => [ 'shape' => 'CreateEncoderConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PendingVerification', ], ], ], 'CreateIngestConfiguration' => [ 'name' => 'CreateIngestConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateIngestConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIngestConfigurationRequest', ], 'output' => [ 'shape' => 'CreateIngestConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'PendingVerification', ], ], ], 'CreateParticipantToken' => [ 'name' => 'CreateParticipantToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateParticipantToken', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateParticipantTokenRequest', ], 'output' => [ 'shape' => 'CreateParticipantTokenResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'PendingVerification', ], ], ], 'CreateStage' => [ 'name' => 'CreateStage', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateStage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateStageRequest', ], 'output' => [ 'shape' => 'CreateStageResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'PendingVerification', ], ], ], 'CreateStorageConfiguration' => [ 'name' => 'CreateStorageConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/CreateStorageConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateStorageConfigurationRequest', ], 'output' => [ 'shape' => 'CreateStorageConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PendingVerification', ], ], ], 'DeleteEncoderConfiguration' => [ 'name' => 'DeleteEncoderConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteEncoderConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteEncoderConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteEncoderConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'DeleteIngestConfiguration' => [ 'name' => 'DeleteIngestConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteIngestConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteIngestConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteIngestConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PendingVerification', ], ], ], 'DeletePublicKey' => [ 'name' => 'DeletePublicKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeletePublicKey', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePublicKeyRequest', ], 'output' => [ 'shape' => 'DeletePublicKeyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PendingVerification', ], ], ], 'DeleteStage' => [ 'name' => 'DeleteStage', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteStage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteStageRequest', ], 'output' => [ 'shape' => 'DeleteStageResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PendingVerification', ], ], ], 'DeleteStorageConfiguration' => [ 'name' => 'DeleteStorageConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/DeleteStorageConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteStorageConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteStorageConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'DisconnectParticipant' => [ 'name' => 'DisconnectParticipant', 'http' => [ 'method' => 'POST', 'requestUri' => '/DisconnectParticipant', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisconnectParticipantRequest', ], 'output' => [ 'shape' => 'DisconnectParticipantResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'PendingVerification', ], ], ], 'GetComposition' => [ 'name' => 'GetComposition', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetComposition', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCompositionRequest', ], 'output' => [ 'shape' => 'GetCompositionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetEncoderConfiguration' => [ 'name' => 'GetEncoderConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetEncoderConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetEncoderConfigurationRequest', ], 'output' => [ 'shape' => 'GetEncoderConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'GetIngestConfiguration' => [ 'name' => 'GetIngestConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetIngestConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIngestConfigurationRequest', ], 'output' => [ 'shape' => 'GetIngestConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetParticipant' => [ 'name' => 'GetParticipant', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetParticipant', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetParticipantRequest', ], 'output' => [ 'shape' => 'GetParticipantResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetPublicKey' => [ 'name' => 'GetPublicKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetPublicKey', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPublicKeyRequest', ], 'output' => [ 'shape' => 'GetPublicKeyResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetStage' => [ 'name' => 'GetStage', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetStage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStageRequest', ], 'output' => [ 'shape' => 'GetStageResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetStageSession' => [ 'name' => 'GetStageSession', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetStageSession', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStageSessionRequest', ], 'output' => [ 'shape' => 'GetStageSessionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetStorageConfiguration' => [ 'name' => 'GetStorageConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/GetStorageConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStorageConfigurationRequest', ], 'output' => [ 'shape' => 'GetStorageConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'ImportPublicKey' => [ 'name' => 'ImportPublicKey', 'http' => [ 'method' => 'POST', 'requestUri' => '/ImportPublicKey', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ImportPublicKeyRequest', ], 'output' => [ 'shape' => 'ImportPublicKeyResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PendingVerification', ], ], ], 'ListCompositions' => [ 'name' => 'ListCompositions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListCompositions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCompositionsRequest', ], 'output' => [ 'shape' => 'ListCompositionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListEncoderConfigurations' => [ 'name' => 'ListEncoderConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListEncoderConfigurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListEncoderConfigurationsRequest', ], 'output' => [ 'shape' => 'ListEncoderConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListIngestConfigurations' => [ 'name' => 'ListIngestConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListIngestConfigurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIngestConfigurationsRequest', ], 'output' => [ 'shape' => 'ListIngestConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListParticipantEvents' => [ 'name' => 'ListParticipantEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListParticipantEvents', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListParticipantEventsRequest', ], 'output' => [ 'shape' => 'ListParticipantEventsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListParticipantReplicas' => [ 'name' => 'ListParticipantReplicas', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListParticipantReplicas', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListParticipantReplicasRequest', ], 'output' => [ 'shape' => 'ListParticipantReplicasResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListParticipants' => [ 'name' => 'ListParticipants', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListParticipants', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListParticipantsRequest', ], 'output' => [ 'shape' => 'ListParticipantsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListPublicKeys' => [ 'name' => 'ListPublicKeys', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListPublicKeys', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPublicKeysRequest', ], 'output' => [ 'shape' => 'ListPublicKeysResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListStageSessions' => [ 'name' => 'ListStageSessions', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListStageSessions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStageSessionsRequest', ], 'output' => [ 'shape' => 'ListStageSessionsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'ListStages' => [ 'name' => 'ListStages', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListStages', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStagesRequest', ], 'output' => [ 'shape' => 'ListStagesResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListStorageConfigurations' => [ 'name' => 'ListStorageConfigurations', 'http' => [ 'method' => 'POST', 'requestUri' => '/ListStorageConfigurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStorageConfigurationsRequest', ], 'output' => [ 'shape' => 'ListStorageConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartComposition' => [ 'name' => 'StartComposition', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartComposition', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartCompositionRequest', ], 'output' => [ 'shape' => 'StartCompositionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PendingVerification', ], ], ], 'StartParticipantReplication' => [ 'name' => 'StartParticipantReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/StartParticipantReplication', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartParticipantReplicationRequest', ], 'output' => [ 'shape' => 'StartParticipantReplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PendingVerification', ], ], ], 'StopComposition' => [ 'name' => 'StopComposition', 'http' => [ 'method' => 'POST', 'requestUri' => '/StopComposition', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopCompositionRequest', ], 'output' => [ 'shape' => 'StopCompositionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], ], ], 'StopParticipantReplication' => [ 'name' => 'StopParticipantReplication', 'http' => [ 'method' => 'POST', 'requestUri' => '/StopParticipantReplication', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StopParticipantReplicationRequest', ], 'output' => [ 'shape' => 'StopParticipantReplicationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateIngestConfiguration' => [ 'name' => 'UpdateIngestConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateIngestConfiguration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIngestConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateIngestConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PendingVerification', ], ], ], 'UpdateStage' => [ 'name' => 'UpdateStage', 'http' => [ 'method' => 'POST', 'requestUri' => '/UpdateStage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateStageRequest', ], 'output' => [ 'shape' => 'UpdateStageResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'PendingVerification', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'accessControlAllowOrigin' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Allow-Origin', ], 'accessControlExposeHeaders' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Expose-Headers', ], 'cacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], 'contentSecurityPolicy' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Security-Policy', ], 'strictTransportSecurity' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Strict-Transport-Security', ], 'xContentTypeOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Content-Type-Options', ], 'xFrameOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Frame-Options', ], 'xAmznErrorType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AttributeKey' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[a-zA-Z0-9-_]*', ], 'AutoParticipantRecordingConfiguration' => [ 'type' => 'structure', 'required' => [ 'storageConfigurationArn', ], 'members' => [ 'storageConfigurationArn' => [ 'shape' => 'AutoParticipantRecordingStorageConfigurationArn', ], 'mediaTypes' => [ 'shape' => 'ParticipantRecordingMediaTypeList', ], 'thumbnailConfiguration' => [ 'shape' => 'ParticipantThumbnailConfiguration', ], 'recordingReconnectWindowSeconds' => [ 'shape' => 'ParticipantRecordingReconnectWindowSeconds', ], 'hlsConfiguration' => [ 'shape' => 'ParticipantRecordingHlsConfiguration', ], 'recordParticipantReplicas' => [ 'shape' => 'RecordParticipantReplicas', ], ], ], 'AutoParticipantRecordingStorageConfigurationArn' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^$|^arn:aws:ivs:[a-z0-9-]+:[0-9]+:storage-configuration/[a-zA-Z0-9-]+$', ], 'Bitrate' => [ 'type' => 'integer', 'box' => true, 'max' => 8500000, 'min' => 1, ], 'Boolean' => [ 'type' => 'boolean', ], 'ChannelArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:aws:ivs:[a-z0-9-]+:[0-9]+:channel/[a-zA-Z0-9-]+', ], 'ChannelDestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'channelArn', ], 'members' => [ 'channelArn' => [ 'shape' => 'ChannelArn', ], 'encoderConfigurationArn' => [ 'shape' => 'EncoderConfigurationArn', ], ], ], 'Composition' => [ 'type' => 'structure', 'required' => [ 'arn', 'stageArn', 'state', 'layout', 'destinations', ], 'members' => [ 'arn' => [ 'shape' => 'CompositionArn', ], 'stageArn' => [ 'shape' => 'StageArn', ], 'state' => [ 'shape' => 'CompositionState', ], 'layout' => [ 'shape' => 'LayoutConfiguration', ], 'destinations' => [ 'shape' => 'DestinationList', ], 'tags' => [ 'shape' => 'Tags', ], 'startTime' => [ 'shape' => 'Time', ], 'endTime' => [ 'shape' => 'Time', ], ], ], 'CompositionArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:aws:ivs:[a-z0-9-]+:[0-9]+:composition/[a-zA-Z0-9-]+', ], 'CompositionClientToken' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_]*', ], 'CompositionRecordingHlsConfiguration' => [ 'type' => 'structure', 'members' => [ 'targetSegmentDurationSeconds' => [ 'shape' => 'CompositionRecordingTargetSegmentDurationSeconds', ], ], ], 'CompositionRecordingTargetSegmentDurationSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 2, ], 'CompositionState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'ACTIVE', 'STOPPING', 'FAILED', 'STOPPED', ], ], 'CompositionSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'stageArn', 'destinations', 'state', ], 'members' => [ 'arn' => [ 'shape' => 'CompositionArn', ], 'stageArn' => [ 'shape' => 'StageArn', ], 'destinations' => [ 'shape' => 'DestinationSummaryList', ], 'state' => [ 'shape' => 'CompositionState', ], 'tags' => [ 'shape' => 'Tags', ], 'startTime' => [ 'shape' => 'Time', ], 'endTime' => [ 'shape' => 'Time', ], ], ], 'CompositionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompositionSummary', ], ], 'CompositionThumbnailConfiguration' => [ 'type' => 'structure', 'members' => [ 'targetIntervalSeconds' => [ 'shape' => 'ThumbnailIntervalSeconds', ], 'storage' => [ 'shape' => 'ThumbnailStorageTypeList', ], ], ], 'CompositionThumbnailConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CompositionThumbnailConfiguration', ], 'max' => 1, 'min' => 0, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'accessControlAllowOrigin' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Allow-Origin', ], 'accessControlExposeHeaders' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Expose-Headers', ], 'cacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], 'contentSecurityPolicy' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Security-Policy', ], 'strictTransportSecurity' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Strict-Transport-Security', ], 'xContentTypeOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Content-Type-Options', ], 'xFrameOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Frame-Options', ], 'xAmznErrorType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateEncoderConfigurationRequest' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'EncoderConfigurationName', ], 'video' => [ 'shape' => 'Video', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateEncoderConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'encoderConfiguration' => [ 'shape' => 'EncoderConfiguration', ], ], ], 'CreateIngestConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'ingestProtocol', ], 'members' => [ 'name' => [ 'shape' => 'IngestConfigurationName', ], 'stageArn' => [ 'shape' => 'IngestConfigurationStageArn', ], 'userId' => [ 'shape' => 'UserId', ], 'attributes' => [ 'shape' => 'ParticipantAttributes', ], 'ingestProtocol' => [ 'shape' => 'IngestProtocol', ], 'insecureIngest' => [ 'shape' => 'InsecureIngest', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateIngestConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'ingestConfiguration' => [ 'shape' => 'IngestConfiguration', ], ], ], 'CreateParticipantTokenRequest' => [ 'type' => 'structure', 'required' => [ 'stageArn', ], 'members' => [ 'stageArn' => [ 'shape' => 'StageArn', ], 'duration' => [ 'shape' => 'ParticipantTokenDurationMinutes', ], 'userId' => [ 'shape' => 'ParticipantTokenUserId', ], 'attributes' => [ 'shape' => 'ParticipantTokenAttributes', ], 'capabilities' => [ 'shape' => 'ParticipantTokenCapabilities', ], ], ], 'CreateParticipantTokenResponse' => [ 'type' => 'structure', 'members' => [ 'participantToken' => [ 'shape' => 'ParticipantToken', ], ], ], 'CreateStageRequest' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'StageName', ], 'participantTokenConfigurations' => [ 'shape' => 'ParticipantTokenConfigurations', ], 'tags' => [ 'shape' => 'Tags', ], 'autoParticipantRecordingConfiguration' => [ 'shape' => 'AutoParticipantRecordingConfiguration', ], ], ], 'CreateStageResponse' => [ 'type' => 'structure', 'members' => [ 'stage' => [ 'shape' => 'Stage', ], 'participantTokens' => [ 'shape' => 'ParticipantTokenList', ], ], ], 'CreateStorageConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 's3', ], 'members' => [ 'name' => [ 'shape' => 'StorageConfigurationName', ], 's3' => [ 'shape' => 'S3StorageConfiguration', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateStorageConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'storageConfiguration' => [ 'shape' => 'StorageConfiguration', ], ], ], 'DeleteEncoderConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EncoderConfigurationArn', ], ], ], 'DeleteEncoderConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteIngestConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'IngestConfigurationArn', ], 'force' => [ 'shape' => 'Boolean', ], ], ], 'DeleteIngestConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeletePublicKeyRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'PublicKeyArn', ], ], ], 'DeletePublicKeyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteStageRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StageArn', ], ], ], 'DeleteStageResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteStorageConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StorageConfigurationArn', ], ], ], 'DeleteStorageConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'Destination' => [ 'type' => 'structure', 'required' => [ 'id', 'state', 'configuration', ], 'members' => [ 'id' => [ 'shape' => 'String', ], 'state' => [ 'shape' => 'DestinationState', ], 'startTime' => [ 'shape' => 'Time', ], 'endTime' => [ 'shape' => 'Time', ], 'configuration' => [ 'shape' => 'DestinationConfiguration', ], 'detail' => [ 'shape' => 'DestinationDetail', ], ], ], 'DestinationConfiguration' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'DestinationConfigurationName', ], 'channel' => [ 'shape' => 'ChannelDestinationConfiguration', ], 's3' => [ 'shape' => 'S3DestinationConfiguration', ], ], ], 'DestinationConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DestinationConfiguration', ], 'max' => 2, 'min' => 1, ], 'DestinationConfigurationName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[a-zA-Z0-9-_]*', ], 'DestinationDetail' => [ 'type' => 'structure', 'members' => [ 's3' => [ 'shape' => 'S3Detail', ], ], ], 'DestinationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Destination', ], 'max' => 2, 'min' => 1, ], 'DestinationState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'ACTIVE', 'STOPPING', 'RECONNECTING', 'FAILED', 'STOPPED', ], ], 'DestinationSummary' => [ 'type' => 'structure', 'required' => [ 'id', 'state', ], 'members' => [ 'id' => [ 'shape' => 'String', ], 'state' => [ 'shape' => 'DestinationState', ], 'startTime' => [ 'shape' => 'Time', ], 'endTime' => [ 'shape' => 'Time', ], ], ], 'DestinationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DestinationSummary', ], 'max' => 2, 'min' => 1, ], 'DisconnectParticipantReason' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'DisconnectParticipantRequest' => [ 'type' => 'structure', 'required' => [ 'stageArn', 'participantId', ], 'members' => [ 'stageArn' => [ 'shape' => 'StageArn', ], 'participantId' => [ 'shape' => 'ParticipantTokenId', ], 'reason' => [ 'shape' => 'DisconnectParticipantReason', ], ], ], 'DisconnectParticipantResponse' => [ 'type' => 'structure', 'members' => [], ], 'EncoderConfiguration' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EncoderConfigurationArn', ], 'name' => [ 'shape' => 'EncoderConfigurationName', ], 'video' => [ 'shape' => 'Video', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'EncoderConfigurationArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:aws:ivs:[a-z0-9-]+:[0-9]+:encoder-configuration/[a-zA-Z0-9-]+', ], 'EncoderConfigurationArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EncoderConfigurationArn', ], 'max' => 1, 'min' => 1, ], 'EncoderConfigurationName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[a-zA-Z0-9-_]*', ], 'EncoderConfigurationSummary' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EncoderConfigurationArn', ], 'name' => [ 'shape' => 'EncoderConfigurationName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'EncoderConfigurationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EncoderConfigurationSummary', ], ], 'Event' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'EventName', ], 'participantId' => [ 'shape' => 'ParticipantId', ], 'eventTime' => [ 'shape' => 'Time', ], 'remoteParticipantId' => [ 'shape' => 'ParticipantId', ], 'errorCode' => [ 'shape' => 'EventErrorCode', ], 'destinationStageArn' => [ 'shape' => 'StageArn', ], 'destinationSessionId' => [ 'shape' => 'StageSessionId', ], 'replica' => [ 'shape' => 'Replica', ], ], ], 'EventErrorCode' => [ 'type' => 'string', 'enum' => [ 'INSUFFICIENT_CAPABILITIES', 'QUOTA_EXCEEDED', 'PUBLISHER_NOT_FOUND', 'BITRATE_EXCEEDED', 'RESOLUTION_EXCEEDED', 'STREAM_DURATION_EXCEEDED', 'INVALID_AUDIO_CODEC', 'INVALID_VIDEO_CODEC', 'INVALID_PROTOCOL', 'INVALID_STREAM_KEY', 'REUSE_OF_STREAM_KEY', 'B_FRAME_PRESENT', 'INVALID_INPUT', 'INTERNAL_SERVER_EXCEPTION', ], ], 'EventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Event', ], ], 'EventName' => [ 'type' => 'string', 'enum' => [ 'JOINED', 'LEFT', 'PUBLISH_STARTED', 'PUBLISH_STOPPED', 'SUBSCRIBE_STARTED', 'SUBSCRIBE_STOPPED', 'PUBLISH_ERROR', 'SUBSCRIBE_ERROR', 'JOIN_ERROR', 'REPLICATION_STARTED', 'REPLICATION_STOPPED', ], ], 'Framerate' => [ 'type' => 'float', 'box' => true, 'max' => 60, 'min' => 1, ], 'GetCompositionRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'CompositionArn', ], ], ], 'GetCompositionResponse' => [ 'type' => 'structure', 'members' => [ 'composition' => [ 'shape' => 'Composition', ], ], ], 'GetEncoderConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'EncoderConfigurationArn', ], ], ], 'GetEncoderConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'encoderConfiguration' => [ 'shape' => 'EncoderConfiguration', ], ], ], 'GetIngestConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'IngestConfigurationArn', ], ], ], 'GetIngestConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'ingestConfiguration' => [ 'shape' => 'IngestConfiguration', ], ], ], 'GetParticipantRequest' => [ 'type' => 'structure', 'required' => [ 'stageArn', 'sessionId', 'participantId', ], 'members' => [ 'stageArn' => [ 'shape' => 'StageArn', ], 'sessionId' => [ 'shape' => 'StageSessionId', ], 'participantId' => [ 'shape' => 'ParticipantId', ], ], ], 'GetParticipantResponse' => [ 'type' => 'structure', 'members' => [ 'participant' => [ 'shape' => 'Participant', ], ], ], 'GetPublicKeyRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'PublicKeyArn', ], ], ], 'GetPublicKeyResponse' => [ 'type' => 'structure', 'members' => [ 'publicKey' => [ 'shape' => 'PublicKey', ], ], ], 'GetStageRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StageArn', ], ], ], 'GetStageResponse' => [ 'type' => 'structure', 'members' => [ 'stage' => [ 'shape' => 'Stage', ], ], ], 'GetStageSessionRequest' => [ 'type' => 'structure', 'required' => [ 'stageArn', 'sessionId', ], 'members' => [ 'stageArn' => [ 'shape' => 'StageArn', ], 'sessionId' => [ 'shape' => 'StageSessionId', ], ], ], 'GetStageSessionResponse' => [ 'type' => 'structure', 'members' => [ 'stageSession' => [ 'shape' => 'StageSession', ], ], ], 'GetStorageConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StorageConfigurationArn', ], ], ], 'GetStorageConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'storageConfiguration' => [ 'shape' => 'StorageConfiguration', ], ], ], 'GridConfiguration' => [ 'type' => 'structure', 'members' => [ 'featuredParticipantAttribute' => [ 'shape' => 'AttributeKey', ], 'omitStoppedVideo' => [ 'shape' => 'OmitStoppedVideo', ], 'videoAspectRatio' => [ 'shape' => 'VideoAspectRatio', ], 'videoFillMode' => [ 'shape' => 'VideoFillMode', ], 'gridGap' => [ 'shape' => 'GridGap', ], ], ], 'GridGap' => [ 'type' => 'integer', 'min' => 0, ], 'Height' => [ 'type' => 'integer', 'box' => true, 'max' => 1920, 'min' => 2, ], 'ImportPublicKeyRequest' => [ 'type' => 'structure', 'required' => [ 'publicKeyMaterial', ], 'members' => [ 'publicKeyMaterial' => [ 'shape' => 'PublicKeyMaterial', ], 'name' => [ 'shape' => 'PublicKeyName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'ImportPublicKeyResponse' => [ 'type' => 'structure', 'members' => [ 'publicKey' => [ 'shape' => 'PublicKey', ], ], ], 'IngestConfiguration' => [ 'type' => 'structure', 'required' => [ 'arn', 'ingestProtocol', 'streamKey', 'stageArn', 'participantId', 'state', ], 'members' => [ 'name' => [ 'shape' => 'IngestConfigurationName', ], 'arn' => [ 'shape' => 'IngestConfigurationArn', ], 'ingestProtocol' => [ 'shape' => 'IngestProtocol', ], 'streamKey' => [ 'shape' => 'StreamKey', ], 'stageArn' => [ 'shape' => 'IngestConfigurationStageArn', ], 'participantId' => [ 'shape' => 'ParticipantId', ], 'state' => [ 'shape' => 'IngestConfigurationState', ], 'userId' => [ 'shape' => 'UserId', ], 'attributes' => [ 'shape' => 'ParticipantAttributes', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'IngestConfigurationArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:aws:ivs:[a-z0-9-]+:[0-9]+:ingest-configuration/[a-zA-Z0-9-]+', ], 'IngestConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IngestConfigurationSummary', ], ], 'IngestConfigurationName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[a-zA-Z0-9-_]*', ], 'IngestConfigurationStageArn' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '^$|^arn:aws:ivs:[a-z0-9-]+:[0-9]+:stage/[a-zA-Z0-9-]+$', ], 'IngestConfigurationState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'INACTIVE', ], ], 'IngestConfigurationSummary' => [ 'type' => 'structure', 'required' => [ 'arn', 'ingestProtocol', 'stageArn', 'participantId', 'state', ], 'members' => [ 'name' => [ 'shape' => 'IngestConfigurationName', ], 'arn' => [ 'shape' => 'IngestConfigurationArn', ], 'ingestProtocol' => [ 'shape' => 'IngestProtocol', ], 'stageArn' => [ 'shape' => 'IngestConfigurationStageArn', ], 'participantId' => [ 'shape' => 'ParticipantId', ], 'state' => [ 'shape' => 'IngestConfigurationState', ], 'userId' => [ 'shape' => 'UserId', ], ], ], 'IngestProtocol' => [ 'type' => 'string', 'enum' => [ 'RTMP', 'RTMPS', ], ], 'InsecureIngest' => [ 'type' => 'boolean', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'accessControlAllowOrigin' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Allow-Origin', ], 'accessControlExposeHeaders' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Expose-Headers', ], 'cacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], 'contentSecurityPolicy' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Security-Policy', ], 'strictTransportSecurity' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Strict-Transport-Security', ], 'xContentTypeOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Content-Type-Options', ], 'xFrameOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Frame-Options', ], 'xAmznErrorType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'LayoutConfiguration' => [ 'type' => 'structure', 'members' => [ 'grid' => [ 'shape' => 'GridConfiguration', ], 'pip' => [ 'shape' => 'PipConfiguration', ], ], ], 'ListCompositionsRequest' => [ 'type' => 'structure', 'members' => [ 'filterByStageArn' => [ 'shape' => 'StageArn', ], 'filterByEncoderConfigurationArn' => [ 'shape' => 'EncoderConfigurationArn', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxCompositionResults', ], ], ], 'ListCompositionsResponse' => [ 'type' => 'structure', 'required' => [ 'compositions', ], 'members' => [ 'compositions' => [ 'shape' => 'CompositionSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListEncoderConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxEncoderConfigurationResults', ], ], ], 'ListEncoderConfigurationsResponse' => [ 'type' => 'structure', 'required' => [ 'encoderConfigurations', ], 'members' => [ 'encoderConfigurations' => [ 'shape' => 'EncoderConfigurationSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListIngestConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'filterByStageArn' => [ 'shape' => 'StageArn', ], 'filterByState' => [ 'shape' => 'IngestConfigurationState', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxIngestConfigurationResults', ], ], ], 'ListIngestConfigurationsResponse' => [ 'type' => 'structure', 'required' => [ 'ingestConfigurations', ], 'members' => [ 'ingestConfigurations' => [ 'shape' => 'IngestConfigurationList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListParticipantEventsRequest' => [ 'type' => 'structure', 'required' => [ 'stageArn', 'sessionId', 'participantId', ], 'members' => [ 'stageArn' => [ 'shape' => 'StageArn', ], 'sessionId' => [ 'shape' => 'StageSessionId', ], 'participantId' => [ 'shape' => 'ParticipantId', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxParticipantEventResults', ], ], ], 'ListParticipantEventsResponse' => [ 'type' => 'structure', 'required' => [ 'events', ], 'members' => [ 'events' => [ 'shape' => 'EventList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListParticipantReplicasRequest' => [ 'type' => 'structure', 'required' => [ 'sourceStageArn', 'participantId', ], 'members' => [ 'sourceStageArn' => [ 'shape' => 'StageArn', ], 'participantId' => [ 'shape' => 'ParticipantId', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxParticipantReplicaResults', ], ], ], 'ListParticipantReplicasResponse' => [ 'type' => 'structure', 'required' => [ 'replicas', ], 'members' => [ 'replicas' => [ 'shape' => 'ParticipantReplicaList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListParticipantsRequest' => [ 'type' => 'structure', 'required' => [ 'stageArn', 'sessionId', ], 'members' => [ 'stageArn' => [ 'shape' => 'StageArn', ], 'sessionId' => [ 'shape' => 'StageSessionId', ], 'filterByUserId' => [ 'shape' => 'UserId', ], 'filterByPublished' => [ 'shape' => 'Published', ], 'filterByState' => [ 'shape' => 'ParticipantState', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxParticipantResults', ], 'filterByRecordingState' => [ 'shape' => 'ParticipantRecordingFilterByRecordingState', ], ], ], 'ListParticipantsResponse' => [ 'type' => 'structure', 'required' => [ 'participants', ], 'members' => [ 'participants' => [ 'shape' => 'ParticipantList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListPublicKeysRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxPublicKeyResults', ], ], ], 'ListPublicKeysResponse' => [ 'type' => 'structure', 'required' => [ 'publicKeys', ], 'members' => [ 'publicKeys' => [ 'shape' => 'PublicKeyList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListStageSessionsRequest' => [ 'type' => 'structure', 'required' => [ 'stageArn', ], 'members' => [ 'stageArn' => [ 'shape' => 'StageArn', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxStageSessionResults', ], ], ], 'ListStageSessionsResponse' => [ 'type' => 'structure', 'required' => [ 'stageSessions', ], 'members' => [ 'stageSessions' => [ 'shape' => 'StageSessionList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListStagesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxStageResults', ], ], ], 'ListStagesResponse' => [ 'type' => 'structure', 'required' => [ 'stages', ], 'members' => [ 'stages' => [ 'shape' => 'StageSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListStorageConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PaginationToken', ], 'maxResults' => [ 'shape' => 'MaxStorageConfigurationResults', ], ], ], 'ListStorageConfigurationsResponse' => [ 'type' => 'structure', 'required' => [ 'storageConfigurations', ], 'members' => [ 'storageConfigurations' => [ 'shape' => 'StorageConfigurationSummaryList', ], 'nextToken' => [ 'shape' => 'PaginationToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'MaxCompositionResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxEncoderConfigurationResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxIngestConfigurationResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxParticipantEventResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxParticipantReplicaResults' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'MaxParticipantResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxPublicKeyResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxStageResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxStageSessionResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MaxStorageConfigurationResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'OmitStoppedVideo' => [ 'type' => 'boolean', ], 'PaginationToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[a-zA-Z0-9+/=_-]*', ], 'Participant' => [ 'type' => 'structure', 'members' => [ 'participantId' => [ 'shape' => 'ParticipantId', ], 'userId' => [ 'shape' => 'UserId', ], 'state' => [ 'shape' => 'ParticipantState', ], 'firstJoinTime' => [ 'shape' => 'Time', ], 'attributes' => [ 'shape' => 'ParticipantAttributes', ], 'published' => [ 'shape' => 'Published', ], 'ispName' => [ 'shape' => 'ParticipantClientAttribute', ], 'osName' => [ 'shape' => 'ParticipantClientAttribute', ], 'osVersion' => [ 'shape' => 'ParticipantClientAttribute', ], 'browserName' => [ 'shape' => 'ParticipantClientAttribute', ], 'browserVersion' => [ 'shape' => 'ParticipantClientAttribute', ], 'sdkVersion' => [ 'shape' => 'ParticipantClientAttribute', ], 'recordingS3BucketName' => [ 'shape' => 'ParticipantRecordingS3BucketName', ], 'recordingS3Prefix' => [ 'shape' => 'ParticipantRecordingS3Prefix', ], 'recordingState' => [ 'shape' => 'ParticipantRecordingState', ], 'protocol' => [ 'shape' => 'ParticipantProtocol', ], 'replicationType' => [ 'shape' => 'ReplicationType', ], 'replicationState' => [ 'shape' => 'ReplicationState', ], 'sourceStageArn' => [ 'shape' => 'StageArn', ], 'sourceSessionId' => [ 'shape' => 'StageSessionId', ], ], ], 'ParticipantAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'ParticipantClientAttribute' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[a-zA-Z0-9-_.,:;\\s]*', ], 'ParticipantId' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '[a-zA-Z0-9-]*', ], 'ParticipantList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipantSummary', ], ], 'ParticipantProtocol' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN', 'WHIP', 'RTMP', 'RTMPS', ], ], 'ParticipantRecordingFilterByRecordingState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'ACTIVE', 'STOPPING', 'STOPPED', 'FAILED', ], ], 'ParticipantRecordingHlsConfiguration' => [ 'type' => 'structure', 'members' => [ 'targetSegmentDurationSeconds' => [ 'shape' => 'ParticipantRecordingTargetSegmentDurationSeconds', ], ], ], 'ParticipantRecordingMediaType' => [ 'type' => 'string', 'enum' => [ 'AUDIO_VIDEO', 'AUDIO_ONLY', 'NONE', ], ], 'ParticipantRecordingMediaTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipantRecordingMediaType', ], 'max' => 1, 'min' => 0, ], 'ParticipantRecordingReconnectWindowSeconds' => [ 'type' => 'integer', 'max' => 300, 'min' => 0, ], 'ParticipantRecordingS3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 0, 'pattern' => '[a-z0-9-.]*', ], 'ParticipantRecordingS3Prefix' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '[a-zA-Z0-9-]*', ], 'ParticipantRecordingState' => [ 'type' => 'string', 'enum' => [ 'STARTING', 'ACTIVE', 'STOPPING', 'STOPPED', 'FAILED', 'DISABLED', ], ], 'ParticipantRecordingTargetSegmentDurationSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 10, 'min' => 2, ], 'ParticipantReplica' => [ 'type' => 'structure', 'required' => [ 'sourceStageArn', 'participantId', 'sourceSessionId', 'destinationStageArn', 'destinationSessionId', 'replicationState', ], 'members' => [ 'sourceStageArn' => [ 'shape' => 'StageArn', ], 'participantId' => [ 'shape' => 'ParticipantId', ], 'sourceSessionId' => [ 'shape' => 'StageSessionId', ], 'destinationStageArn' => [ 'shape' => 'StageArn', ], 'destinationSessionId' => [ 'shape' => 'StageSessionId', ], 'replicationState' => [ 'shape' => 'ReplicationState', ], ], ], 'ParticipantReplicaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipantReplica', ], ], 'ParticipantState' => [ 'type' => 'string', 'enum' => [ 'CONNECTED', 'DISCONNECTED', ], ], 'ParticipantSummary' => [ 'type' => 'structure', 'members' => [ 'participantId' => [ 'shape' => 'ParticipantId', ], 'userId' => [ 'shape' => 'UserId', ], 'state' => [ 'shape' => 'ParticipantState', ], 'firstJoinTime' => [ 'shape' => 'Time', ], 'published' => [ 'shape' => 'Published', ], 'recordingState' => [ 'shape' => 'ParticipantRecordingState', ], 'replicationType' => [ 'shape' => 'ReplicationType', ], 'replicationState' => [ 'shape' => 'ReplicationState', ], 'sourceStageArn' => [ 'shape' => 'StageArn', ], 'sourceSessionId' => [ 'shape' => 'StageSessionId', ], ], ], 'ParticipantThumbnailConfiguration' => [ 'type' => 'structure', 'members' => [ 'targetIntervalSeconds' => [ 'shape' => 'ThumbnailIntervalSeconds', ], 'storage' => [ 'shape' => 'ThumbnailStorageTypeList', ], 'recordingMode' => [ 'shape' => 'ThumbnailRecordingMode', ], ], ], 'ParticipantToken' => [ 'type' => 'structure', 'members' => [ 'participantId' => [ 'shape' => 'ParticipantTokenId', ], 'token' => [ 'shape' => 'ParticipantTokenString', ], 'userId' => [ 'shape' => 'ParticipantTokenUserId', ], 'attributes' => [ 'shape' => 'ParticipantTokenAttributes', ], 'duration' => [ 'shape' => 'ParticipantTokenDurationMinutes', ], 'capabilities' => [ 'shape' => 'ParticipantTokenCapabilities', ], 'expirationTime' => [ 'shape' => 'ParticipantTokenExpirationTime', ], ], ], 'ParticipantTokenAttributes' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'ParticipantTokenCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipantTokenCapability', ], 'max' => 2, 'min' => 0, ], 'ParticipantTokenCapability' => [ 'type' => 'string', 'enum' => [ 'PUBLISH', 'SUBSCRIBE', ], ], 'ParticipantTokenConfiguration' => [ 'type' => 'structure', 'members' => [ 'duration' => [ 'shape' => 'ParticipantTokenDurationMinutes', ], 'userId' => [ 'shape' => 'ParticipantTokenUserId', ], 'attributes' => [ 'shape' => 'ParticipantTokenAttributes', ], 'capabilities' => [ 'shape' => 'ParticipantTokenCapabilities', ], ], ], 'ParticipantTokenConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipantTokenConfiguration', ], 'max' => 12, 'min' => 0, ], 'ParticipantTokenDurationMinutes' => [ 'type' => 'integer', 'box' => true, 'max' => 20160, 'min' => 1, ], 'ParticipantTokenExpirationTime' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'ParticipantTokenId' => [ 'type' => 'string', 'max' => 64, 'min' => 0, 'pattern' => '[a-zA-Z0-9-_]*', ], 'ParticipantTokenList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParticipantToken', ], ], 'ParticipantTokenString' => [ 'type' => 'string', 'sensitive' => true, ], 'ParticipantTokenUserId' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'PendingVerification' => [ 'type' => 'structure', 'members' => [ 'accessControlAllowOrigin' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Allow-Origin', ], 'accessControlExposeHeaders' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Expose-Headers', ], 'cacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], 'contentSecurityPolicy' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Security-Policy', ], 'strictTransportSecurity' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Strict-Transport-Security', ], 'xContentTypeOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Content-Type-Options', ], 'xFrameOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Frame-Options', ], 'xAmznErrorType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'PipBehavior' => [ 'type' => 'string', 'enum' => [ 'STATIC', 'DYNAMIC', ], ], 'PipConfiguration' => [ 'type' => 'structure', 'members' => [ 'featuredParticipantAttribute' => [ 'shape' => 'AttributeKey', ], 'omitStoppedVideo' => [ 'shape' => 'OmitStoppedVideo', ], 'videoFillMode' => [ 'shape' => 'VideoFillMode', ], 'gridGap' => [ 'shape' => 'GridGap', ], 'pipParticipantAttribute' => [ 'shape' => 'AttributeKey', ], 'pipBehavior' => [ 'shape' => 'PipBehavior', ], 'pipOffset' => [ 'shape' => 'PipOffset', ], 'pipPosition' => [ 'shape' => 'PipPosition', ], 'pipWidth' => [ 'shape' => 'PipWidth', ], 'pipHeight' => [ 'shape' => 'PipHeight', ], ], ], 'PipHeight' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'PipOffset' => [ 'type' => 'integer', 'min' => 0, ], 'PipPosition' => [ 'type' => 'string', 'enum' => [ 'TOP_LEFT', 'TOP_RIGHT', 'BOTTOM_LEFT', 'BOTTOM_RIGHT', ], ], 'PipWidth' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'PublicKey' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'PublicKeyArn', ], 'name' => [ 'shape' => 'PublicKeyName', ], 'publicKeyMaterial' => [ 'shape' => 'PublicKeyMaterial', ], 'fingerprint' => [ 'shape' => 'PublicKeyFingerprint', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'PublicKeyArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:aws:ivs:[a-z0-9-]+:[0-9]+:public-key/[a-zA-Z0-9-]+', ], 'PublicKeyFingerprint' => [ 'type' => 'string', ], 'PublicKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PublicKeySummary', ], ], 'PublicKeyMaterial' => [ 'type' => 'string', 'pattern' => '.*-----BEGIN PUBLIC KEY-----\\r?\\n([a-zA-Z0-9+/=\\r\\n]+)\\r?\\n-----END PUBLIC KEY-----(\\r?\\n)?.*', ], 'PublicKeyName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[a-zA-Z0-9-_]*', ], 'PublicKeySummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'PublicKeyArn', ], 'name' => [ 'shape' => 'PublicKeyName', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'Published' => [ 'type' => 'boolean', ], 'ReconnectWindowSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 60, 'min' => 0, ], 'RecordParticipantReplicas' => [ 'type' => 'boolean', ], 'RecordingConfiguration' => [ 'type' => 'structure', 'members' => [ 'hlsConfiguration' => [ 'shape' => 'CompositionRecordingHlsConfiguration', ], 'format' => [ 'shape' => 'RecordingConfigurationFormat', ], ], ], 'RecordingConfigurationFormat' => [ 'type' => 'string', 'enum' => [ 'HLS', ], ], 'Replica' => [ 'type' => 'boolean', ], 'ReplicationState' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'STOPPED', ], ], 'ReplicationType' => [ 'type' => 'string', 'enum' => [ 'SOURCE', 'REPLICA', 'NONE', ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:aws:ivs:[a-z0-9-]+:[0-9]+:[a-z-]/[a-zA-Z0-9-]+', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'accessControlAllowOrigin' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Allow-Origin', ], 'accessControlExposeHeaders' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Expose-Headers', ], 'cacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], 'contentSecurityPolicy' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Security-Policy', ], 'strictTransportSecurity' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Strict-Transport-Security', ], 'xContentTypeOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Content-Type-Options', ], 'xFrameOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Frame-Options', ], 'xAmznErrorType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'S3BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, 'pattern' => '[a-z0-9-.]+', ], 'S3DestinationConfiguration' => [ 'type' => 'structure', 'required' => [ 'storageConfigurationArn', 'encoderConfigurationArns', ], 'members' => [ 'storageConfigurationArn' => [ 'shape' => 'StorageConfigurationArn', ], 'encoderConfigurationArns' => [ 'shape' => 'EncoderConfigurationArnList', ], 'recordingConfiguration' => [ 'shape' => 'RecordingConfiguration', ], 'thumbnailConfigurations' => [ 'shape' => 'CompositionThumbnailConfigurationList', ], ], ], 'S3Detail' => [ 'type' => 'structure', 'required' => [ 'recordingPrefix', ], 'members' => [ 'recordingPrefix' => [ 'shape' => 'String', ], ], ], 'S3StorageConfiguration' => [ 'type' => 'structure', 'required' => [ 'bucketName', ], 'members' => [ 'bucketName' => [ 'shape' => 'S3BucketName', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'accessControlAllowOrigin' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Allow-Origin', ], 'accessControlExposeHeaders' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Expose-Headers', ], 'cacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], 'contentSecurityPolicy' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Security-Policy', ], 'strictTransportSecurity' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Strict-Transport-Security', ], 'xContentTypeOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Content-Type-Options', ], 'xFrameOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Frame-Options', ], 'xAmznErrorType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'Stage' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StageArn', ], 'name' => [ 'shape' => 'StageName', ], 'activeSessionId' => [ 'shape' => 'StageSessionId', ], 'tags' => [ 'shape' => 'Tags', ], 'autoParticipantRecordingConfiguration' => [ 'shape' => 'AutoParticipantRecordingConfiguration', ], 'endpoints' => [ 'shape' => 'StageEndpoints', ], ], ], 'StageArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:aws:ivs:[a-z0-9-]+:[0-9]+:stage/[a-zA-Z0-9-]+', ], 'StageEndpoint' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, ], 'StageEndpoints' => [ 'type' => 'structure', 'members' => [ 'events' => [ 'shape' => 'StageEndpoint', ], 'whip' => [ 'shape' => 'StageEndpoint', ], 'rtmp' => [ 'shape' => 'StageEndpoint', ], 'rtmps' => [ 'shape' => 'StageEndpoint', ], ], ], 'StageName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[a-zA-Z0-9-_]*', ], 'StageSession' => [ 'type' => 'structure', 'members' => [ 'sessionId' => [ 'shape' => 'StageSessionId', ], 'startTime' => [ 'shape' => 'Time', ], 'endTime' => [ 'shape' => 'Time', ], ], ], 'StageSessionId' => [ 'type' => 'string', 'max' => 16, 'min' => 16, 'pattern' => 'st-[a-zA-Z0-9]+', ], 'StageSessionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StageSessionSummary', ], ], 'StageSessionSummary' => [ 'type' => 'structure', 'members' => [ 'sessionId' => [ 'shape' => 'StageSessionId', ], 'startTime' => [ 'shape' => 'Time', ], 'endTime' => [ 'shape' => 'Time', ], ], ], 'StageSummary' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StageArn', ], 'name' => [ 'shape' => 'StageName', ], 'activeSessionId' => [ 'shape' => 'StageSessionId', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'StageSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StageSummary', ], ], 'StartCompositionRequest' => [ 'type' => 'structure', 'required' => [ 'stageArn', 'destinations', ], 'members' => [ 'stageArn' => [ 'shape' => 'StageArn', ], 'idempotencyToken' => [ 'shape' => 'CompositionClientToken', 'idempotencyToken' => true, ], 'layout' => [ 'shape' => 'LayoutConfiguration', ], 'destinations' => [ 'shape' => 'DestinationConfigurationList', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'StartCompositionResponse' => [ 'type' => 'structure', 'members' => [ 'composition' => [ 'shape' => 'Composition', ], ], ], 'StartParticipantReplicationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceStageArn', 'destinationStageArn', 'participantId', ], 'members' => [ 'sourceStageArn' => [ 'shape' => 'StageArn', ], 'destinationStageArn' => [ 'shape' => 'StageArn', ], 'participantId' => [ 'shape' => 'ParticipantId', ], 'reconnectWindowSeconds' => [ 'shape' => 'ReconnectWindowSeconds', ], 'attributes' => [ 'shape' => 'ParticipantAttributes', ], ], ], 'StartParticipantReplicationResponse' => [ 'type' => 'structure', 'members' => [ 'accessControlAllowOrigin' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Allow-Origin', ], 'accessControlExposeHeaders' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Expose-Headers', ], 'cacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], 'contentSecurityPolicy' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Security-Policy', ], 'strictTransportSecurity' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Strict-Transport-Security', ], 'xContentTypeOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Content-Type-Options', ], 'xFrameOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Frame-Options', ], ], ], 'StopCompositionRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'CompositionArn', ], ], ], 'StopCompositionResponse' => [ 'type' => 'structure', 'members' => [], ], 'StopParticipantReplicationRequest' => [ 'type' => 'structure', 'required' => [ 'sourceStageArn', 'destinationStageArn', 'participantId', ], 'members' => [ 'sourceStageArn' => [ 'shape' => 'StageArn', ], 'destinationStageArn' => [ 'shape' => 'StageArn', ], 'participantId' => [ 'shape' => 'ParticipantId', ], ], ], 'StopParticipantReplicationResponse' => [ 'type' => 'structure', 'members' => [ 'accessControlAllowOrigin' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Allow-Origin', ], 'accessControlExposeHeaders' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Expose-Headers', ], 'cacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], 'contentSecurityPolicy' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Security-Policy', ], 'strictTransportSecurity' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Strict-Transport-Security', ], 'xContentTypeOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Content-Type-Options', ], 'xFrameOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Frame-Options', ], ], ], 'StorageConfiguration' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StorageConfigurationArn', ], 'name' => [ 'shape' => 'StorageConfigurationName', ], 's3' => [ 'shape' => 'S3StorageConfiguration', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'StorageConfigurationArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:aws:ivs:[a-z0-9-]+:[0-9]+:storage-configuration/[a-zA-Z0-9-]+', ], 'StorageConfigurationName' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => '[a-zA-Z0-9-_]*', ], 'StorageConfigurationSummary' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StorageConfigurationArn', ], 'name' => [ 'shape' => 'StorageConfigurationName', ], 's3' => [ 'shape' => 'S3StorageConfiguration', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'StorageConfigurationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StorageConfigurationSummary', ], ], 'StreamKey' => [ 'type' => 'string', 'pattern' => 'rt_[0-9]+_[a-z0-9-]+_[a-zA-Z0-9-]+_.+', 'sensitive' => true, ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 0, ], 'ThumbnailIntervalSeconds' => [ 'type' => 'integer', 'box' => true, 'max' => 86400, 'min' => 1, ], 'ThumbnailRecordingMode' => [ 'type' => 'string', 'enum' => [ 'INTERVAL', 'DISABLED', ], ], 'ThumbnailStorageType' => [ 'type' => 'string', 'enum' => [ 'SEQUENTIAL', 'LATEST', ], ], 'ThumbnailStorageTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThumbnailStorageType', ], 'max' => 2, 'min' => 0, ], 'Time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateIngestConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'IngestConfigurationArn', ], 'stageArn' => [ 'shape' => 'IngestConfigurationStageArn', ], ], ], 'UpdateIngestConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'ingestConfiguration' => [ 'shape' => 'IngestConfiguration', ], ], ], 'UpdateStageRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'StageArn', ], 'name' => [ 'shape' => 'StageName', ], 'autoParticipantRecordingConfiguration' => [ 'shape' => 'AutoParticipantRecordingConfiguration', ], ], ], 'UpdateStageResponse' => [ 'type' => 'structure', 'members' => [ 'stage' => [ 'shape' => 'Stage', ], ], ], 'UserId' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'accessControlAllowOrigin' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Allow-Origin', ], 'accessControlExposeHeaders' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Access-Control-Expose-Headers', ], 'cacheControl' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Cache-Control', ], 'contentSecurityPolicy' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Content-Security-Policy', ], 'strictTransportSecurity' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'Strict-Transport-Security', ], 'xContentTypeOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Content-Type-Options', ], 'xFrameOptions' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'X-Frame-Options', ], 'xAmznErrorType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], 'exceptionMessage' => [ 'shape' => 'errorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Video' => [ 'type' => 'structure', 'members' => [ 'width' => [ 'shape' => 'Width', ], 'height' => [ 'shape' => 'Height', ], 'framerate' => [ 'shape' => 'Framerate', ], 'bitrate' => [ 'shape' => 'Bitrate', ], ], ], 'VideoAspectRatio' => [ 'type' => 'string', 'enum' => [ 'AUTO', 'VIDEO', 'SQUARE', 'PORTRAIT', ], ], 'VideoFillMode' => [ 'type' => 'string', 'enum' => [ 'FILL', 'COVER', 'CONTAIN', ], ], 'Width' => [ 'type' => 'integer', 'box' => true, 'max' => 1920, 'min' => 2, ], 'errorMessage' => [ 'type' => 'string', ], ],];
