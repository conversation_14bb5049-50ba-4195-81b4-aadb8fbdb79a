<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Growth Consultant - Application Form</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="style.css">
    <style>
        .step {
            display: none;
        }

        .step.active {
            display: block;
        }

        .progress-bar {
            transition: width 0.3s ease;
        }
    </style>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="max-w-4xl mx-auto mb-8">
            <h1 class="text-3xl font-bold text-slate-800 text-center mb-2">Digital Growth Consultant</h1>
            <p class="text-slate-200 text-center">Application Form</p>
        </div>

        <!-- Progress Bar -->
        <div class="max-w-4xl mx-auto mb-8">
            <div class="progress-container">
                <div class="flex justify-between items-center mb-4">
                    <span class="text-sm font-medium text-slate-200">Progress</span>
                    <span class="text-sm font-medium text-slate-200" id="progress-text">Step 1 of 6</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="progress-bar h-2 rounded-full" id="progress-bar" style="width: 16.67%"></div>
                </div>
            </div>
        </div>

        <!-- Form Container -->
        <form id="multiStepForm" class="max-w-4xl mx-auto" enctype="multipart/form-data">

            <!-- Step 1 -->
            <div class="step active" data-step="1">


                <div class="info-box">
                    <p class="text-neutral-700 leading-relaxed">
                        Buat kamu yang jago ngobrol, suka bantu closing, dan tertarik belajar AI + iklan digital...
                        Kami sedang cari partner untuk bantu bisnis-bisnis Indonesia berkembang lewat iklan dan WA.
                        Kamu nggak harus jago Meta Ads — asal niat belajar dan berani komunikasi, kita akan bantu
                        upgrade kamu jadi digital consultant yang paham teknis.
                    </p>
                </div>

                <div class="mb-6">
                    <label class="block text-lg font-medium text-white mb-4">
                        Mana yang paling menggambarkan kamu saat ini?
                    </label>
                    <div class="space-y-3">
                        <label class="radio-option">
                            <input type="radio" name="profile_type" value="A" required>
                            <span class="text-neutral-700">A) Jago komunikasi dan closing, tapi belum ngerti teknis
                                iklan</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="profile_type" value="B" required>
                            <span class="text-neutral-700">B) Sudah biasa pegang Meta Ads dan tracking</span>
                        </label>
                        <label class="radio-option">
                            <input type="radio" name="profile_type" value="C" required>
                            <span class="text-neutral-700">C) Kuat di dua-duanya</span>
                        </label>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="button" onclick="nextStep()" class="btn-primary">
                        Lanjut
                    </button>
                </div>
            </div>

            <!-- Step 2 -->
            <div class="step" data-step="2">


                <div class="grid md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">Nama Lengkap *</label>
                        <input type="text" name="full_name" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">Whatsapp Number *</label>
                        <input type="number" name="phone" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="081234567890">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">Email *</label>
                        <input type="email" name="email" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">Domisili Saat Ini *</label>
                        <input type="text" name="domicile" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-white mb-2">
                        Apakah Anda memiliki pengalaman sebagai Digital Growth Consultant atau posisi serupa minimal 1
                        tahun? *
                    </label>
                    <div class="flex space-x-4">
                        <label class="flex items-center space-x-2">
                            <input type="radio" name="has_experience" value="ya" class="text-blue-600"
                                onchange="toggleExperienceDetail(true)" required>
                            <span class="text-white">Ya</span>
                        </label>
                        <label class="flex items-center space-x-2">
                            <input type="radio" name="has_experience" value="tidak" class="text-blue-600"
                                onchange="toggleExperienceDetail(false)" required>
                            <span class="text-white">Tidak</span>
                        </label>
                    </div>
                </div>

                <div id="experience_detail" class="mb-6" style="display: none;">
                    <label class="block text-sm font-medium text-white mb-2">Jika Ya, ceritakan sedikit:</label>
                    <textarea name="experience_description" rows="4"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Ceritakan pengalaman Anda..."></textarea>
                </div>

                <div class="flex justify-between">
                    <button type="button" onclick="prevStep()" class="btn-secondary">
                        Kembali
                    </button>
                    <button type="button" onclick="nextStep()" class="btn-primary">
                        Lanjut
                    </button>
                </div>
            </div>

            <!-- Step 3 -->
            <div class="step" data-step="3">


                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Seberapa sering Anda melakukan presentasi melalui Zoom, Google Meet, atau WhatsApp Call? *
                        </label>
                        <textarea name="presentation_frequency" rows="3" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Contoh: Setiap hari, seminggu sekali, atau jarang..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Pernahkah Anda memberikan ide atau saran untuk meningkatkan performa iklan klien? Jika ya,
                            ceritakan singkat.
                        </label>
                        <textarea name="ads_improvement_ideas" rows="4"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Ceritakan pengalaman Anda memberikan saran..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Seberapa nyaman Anda memberikan voice note atau call untuk menjelaskan produk ke prospek? *
                        </label>
                        <select name="voice_comfort_level" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">Pilih tingkat kenyamanan...</option>
                            <option value="sangat_nyaman">Sangat nyaman, sering lakukan</option>
                            <option value="nyaman">Nyaman, sudah biasa</option>
                            <option value="cukup_nyaman">Cukup nyaman, masih belajar</option>
                            <option value="kurang_nyaman">Kurang nyaman, perlu latihan</option>
                        </select>
                    </div>
                </div>

                <div class="flex justify-between mt-8">
                    <button type="button" onclick="prevStep()" class="btn-secondary">
                        Kembali
                    </button>
                    <button type="button" onclick="nextStep()" class="btn-primary">
                        Lanjut
                    </button>
                </div>
            </div>

            <!-- Step 4 -->
            <div class="step" data-step="4">


                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Kalau Anda dikasih tools baru yang belum pernah Anda pakai, apa langkah pertama yang Anda
                            lakukan? *
                        </label>
                        <textarea name="new_tools_approach" rows="3" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Ceritakan pendekatan Anda..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Dalam 30 hari ke depan, Anda diwajibkan menguasai Meta Ads. Rencana belajar Anda seperti
                            apa? *
                        </label>
                        <textarea name="meta_ads_learning_plan" rows="4" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Jelaskan rencana belajar Anda..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Pernahkah Anda merasa frustrasi saat belajar hal baru? Ceritakan contohnya.
                        </label>
                        <textarea name="learning_frustration" rows="3"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Ceritakan pengalaman Anda..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Apa keputusan paling cepat yang pernah Anda ambil untuk belajar sesuatu yang bikin performa
                            kerja Anda langsung naik?
                        </label>
                        <textarea name="quick_learning_decision" rows="3"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Ceritakan keputusan cepat Anda..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Pernah pakai AI untuk bantu kerja? Ceritakan contoh nyatanya.
                        </label>
                        <textarea name="ai_usage_experience" rows="3"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Ceritakan pengalaman menggunakan AI..."></textarea>
                    </div>
                </div>

                <div class="flex justify-between mt-8">
                    <button type="button" onclick="prevStep()" class="btn-secondary">
                        Kembali
                    </button>
                    <button type="button" onclick="nextStep()" class="btn-primary">
                        Lanjut
                    </button>
                </div>
            </div>

            <!-- Step 5 -->
            <div class="step" data-step="5">


                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-white mb-4">
                            Jika dalam sebuah proyek tiba-tiba muncul tantangan besar (misalnya target tak tercapai, tim
                            kurang personel, atau ada kendala internal), apa yang akan kamu lakukan? *
                        </label>
                        <div class="space-y-3">
                            <label class="radio-option">
                                <input type="radio" name="challenge_response" value="A" required>
                                <span class="text-neutral-700">A) Ambil inisiatif bantu semampunya dan jaga komunikasi
                                    terbuka</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="challenge_response" value="B" required>
                                <span class="text-neutral-700">B) Lihat situasi dulu sambil bantu di bagian yang saya
                                    yakin</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="challenge_response" value="C" required>
                                <span class="text-neutral-700">C) Ajak tim diskusi untuk pahami arah ke depan sebelum
                                    tentukan sikap</span>
                            </label>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Kenapa Anda rela berjuang bareng kami? Ceritakan alasan terdalam Anda. *
                        </label>
                        <textarea name="motivation_reason" rows="4" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Ceritakan motivasi terdalam Anda..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Bagaimana sikap Anda jika ide Anda dikritik oleh rekan kerja atau user? *
                        </label>
                        <textarea name="criticism_response" rows="3" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Jelaskan sikap Anda..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Ceritakan satu momen ketika Anda mengambil inisiatif atau memberi saran penting tanpa
                            disuruh. Apa hasilnya?
                        </label>
                        <textarea name="initiative_moment" rows="4"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Ceritakan momen inisiatif Anda..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-4">
                            Mana yang lebih Anda pilih: *
                        </label>
                        <div class="space-y-3 mb-4">
                            <label class="radio-option">
                                <input type="radio" name="work_preference" value="A" required>
                                <span class="text-neutral-700">A) Fokus menyelesaikan task</span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="work_preference" value="B" required>
                                <span class="text-neutral-700">B) Fokus menghasilkan dampak dari task</span>
                            </label>
                        </div>
                        <label class="block text-sm font-medium text-white mb-2">Jelaskan alasannya:</label>
                        <textarea name="work_preference_reason" rows="3"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Jelaskan alasan pilihan Anda..."></textarea>
                    </div>
                </div>

                <div class="flex justify-between mt-8">
                    <button type="button" onclick="prevStep()" class="btn-secondary">
                        Kembali
                    </button>
                    <button type="button" onclick="nextStep()" class="btn-primary">
                        Lanjut
                    </button>
                </div>
            </div>

            <!-- Step 6 -->
            <div class="step" data-step="6">


                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Apa tujuan karier Anda 2–3 tahun ke depan? Seberapa besar peran yang ingin Anda ambil di
                            perusahaan ini jika cocok? *
                        </label>
                        <textarea name="career_goals" rows="4" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Jelaskan tujuan karier Anda..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Motivasi utama Anda melamar posisi ini: *
                        </label>
                        <textarea name="main_motivation" rows="3" required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            placeholder="Jelaskan motivasi utama Anda..."></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white mb-2">
                            Upload bukti nyatanya (portofolio, voice note, dashboard, rekaman presentasi, dsb)
                        </label>

                        <div class="file-upload-container">
                            <div class="file-upload-area" id="fileUploadArea">
                                <div class="file-upload-content">
                                    <div class="file-upload-icon">
                                        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor"
                                            viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12">
                                            </path>
                                        </svg>
                                    </div>
                                    <div class="file-upload-text">
                                        <p class="text-lg font-medium text-gray-700">Drag & drop files here</p>
                                        <p class="text-sm text-gray-500">atau klik untuk memilih file</p>
                                    </div>
                                    <button type="button" class="file-upload-button">
                                        Pilih File
                                    </button>
                                </div>
                                <input type="file" name="portfolio_files" multiple
                                    accept=".pdf,.jpg,.jpeg,.png,.mp3,.mp4,.wav,.doc,.docx" class="file-input-hidden"
                                    id="fileInput">
                            </div>

                            <!-- File Preview Area -->
                            <div class="file-preview-container" id="filePreviewContainer" style="display: none;">
                                <h4 class="text-sm font-medium text-white mb-3">File yang dipilih:</h4>
                                <div class="file-preview-list" id="filePreviewList">
                                    <!-- File previews will be inserted here -->
                                </div>
                            </div>

                            <!-- Upload Progress -->
                            <div class="upload-progress-container" id="uploadProgressContainer" style="display: none;">
                                <div class="upload-progress-bar">
                                    <div class="upload-progress-fill" id="uploadProgressFill"></div>
                                </div>
                                <p class="upload-progress-text" id="uploadProgressText">Uploading...</p>
                            </div>
                        </div>

                        <p class="text-sm text-slate-400 mt-2">
                            Format yang diterima: PDF, gambar (JPG, PNG), audio (MP3, WAV), video (MP4), dokumen (DOC,
                            DOCX)
                            <br>Maksimal 10MB per file
                        </p>
                    </div>

                    <div class="alert-warning">
                        <p class="text-sm">
                            <strong>Tips:</strong> Pastikan semua data yang Anda masukkan sudah benar sebelum mengirim
                            formulir.
                            Setelah dikirim, data tidak dapat diubah.
                        </p>
                    </div>
                </div>

                <div class="flex justify-between mt-8">
                    <button type="button" onclick="prevStep()" class="btn-secondary">
                        Kembali
                    </button>
                    <button type="submit" class="btn-success">
                        Kirim Aplikasi
                    </button>
                </div>
            </div>

        </form>

        <!-- Success Message -->
        <div id="successMessage" class="max-w-4xl mx-auto hidden">
            <div class="text-center p-8">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                    <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <h3 class="text-2xl font-medium text-white mb-2">Aplikasi Berhasil Dikirim!</h3>
                <p class="text-slate-200">
                    Terima kasih telah melamar. Tim kami akan meninjau aplikasi Anda dan menghubungi dalam 3-5 hari
                    kerja.
                </p>
            </div>
        </div>
    </div>

    <script src="form1.js"></script>

    <script>
    // Tambahkan fungsi untuk handle submit form
    document.getElementById('multiStepForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        try {
            const formData = new FormData(this);
            
            // Tampilkan loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = 'Mengirim...';
            
            const response = await fetch('process_form.php', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (result.success) {
                // Tampilkan pesan sukses
                document.getElementById('multiStepForm').style.display = 'none';
                document.getElementById('successMessage').classList.remove('hidden');
            } else {
                throw new Error(result.error || 'Terjadi kesalahan saat mengirim aplikasi');
            }
            
        } catch (error) {
            alert(error.message);
        } finally {
            // Reset button state
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    });
    </script>
</body>

</html>