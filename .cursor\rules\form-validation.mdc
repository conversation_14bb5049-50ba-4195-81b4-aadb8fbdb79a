---
description: 
globs: 
alwaysApply: false
---
# Validasi Form Aplikasi

## Field Wajib
1. Profile Type (A/B/C)
2. <PERSON><PERSON>
3. <PERSON><PERSON> Tel<PERSON>on (10-15 digit)
4. <PERSON><PERSON> (format valid)
5. <PERSON><PERSON><PERSON>
6. <PERSON><PERSON><PERSON> (Ya/Tidak)
7. Frekuensi Presentasi
8. Tingkat Kenyamanan Voice
9. Pendekatan Tools Baru
10. Rencana Belajar Meta Ads
11. Response Tantangan (A/B/C)
12. Alasan Motivasi
13. Response Kritik
14. Preferensi Kerja (A/B)
15. <PERSON><PERSON><PERSON>
16. Motivasi Utama

## Field Opsional
1. <PERSON><PERSON><PERSON><PERSON> (jika has_experience = 'ya')
2. Ide Peningkatan Iklan
3. Frustrasi Belajar
4. Keputusan Belajar Cepat
5. Pengalaman AI
6. Momen Inisiatif
7. Alasan Preferensi Kerja
8. File Portfolio

## Validasi File Portfolio
- Format yang didukung:
  - PDF (.pdf)
  - Gambar (.jpg, .jpeg, .png)
  - Audio (.mp3, .wav)
  - Video (.mp4)
  - Dokumen (.doc, .docx)
- Ukuran maksimal: 10MB per file
- Multiple file upload diperbolehkan
- Nama file akan di-generate secara unik

## Error Messages
- "Field {field} harus diisi" - untuk field wajib yang kosong
- "Format email tidak valid" - untuk email yang tidak sesuai format
- "Format nomor telepon tidak valid" - untuk nomor telepon yang tidak sesuai format
- "Tipe file {filename} tidak diizinkan" - untuk file dengan format tidak didukung
- "Ukuran file {filename} melebihi batas maksimal (10MB)" - untuk file yang terlalu besar
- "Gagal menyimpan file {filename}" - untuk error saat upload file

