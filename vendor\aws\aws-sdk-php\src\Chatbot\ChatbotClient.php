<?php
namespace Aws\Chatbot;

use Aws\AwsClient;

/**
 * This client is used to interact with the **chatbot** service.
 * @method \Aws\Result associateToConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise associateToConfigurationAsync(array $args = [])
 * @method \Aws\Result createChimeWebhookConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createChimeWebhookConfigurationAsync(array $args = [])
 * @method \Aws\Result createCustomAction(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createCustomActionAsync(array $args = [])
 * @method \Aws\Result createMicrosoftTeamsChannelConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createMicrosoftTeamsChannelConfigurationAsync(array $args = [])
 * @method \Aws\Result createSlackChannelConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise createSlackChannelConfigurationAsync(array $args = [])
 * @method \Aws\Result deleteChimeWebhookConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteChimeWebhookConfigurationAsync(array $args = [])
 * @method \Aws\Result deleteCustomAction(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteCustomActionAsync(array $args = [])
 * @method \Aws\Result deleteMicrosoftTeamsChannelConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMicrosoftTeamsChannelConfigurationAsync(array $args = [])
 * @method \Aws\Result deleteMicrosoftTeamsConfiguredTeam(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMicrosoftTeamsConfiguredTeamAsync(array $args = [])
 * @method \Aws\Result deleteMicrosoftTeamsUserIdentity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteMicrosoftTeamsUserIdentityAsync(array $args = [])
 * @method \Aws\Result deleteSlackChannelConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSlackChannelConfigurationAsync(array $args = [])
 * @method \Aws\Result deleteSlackUserIdentity(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSlackUserIdentityAsync(array $args = [])
 * @method \Aws\Result deleteSlackWorkspaceAuthorization(array $args = [])
 * @method \GuzzleHttp\Promise\Promise deleteSlackWorkspaceAuthorizationAsync(array $args = [])
 * @method \Aws\Result describeChimeWebhookConfigurations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeChimeWebhookConfigurationsAsync(array $args = [])
 * @method \Aws\Result describeSlackChannelConfigurations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeSlackChannelConfigurationsAsync(array $args = [])
 * @method \Aws\Result describeSlackUserIdentities(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeSlackUserIdentitiesAsync(array $args = [])
 * @method \Aws\Result describeSlackWorkspaces(array $args = [])
 * @method \GuzzleHttp\Promise\Promise describeSlackWorkspacesAsync(array $args = [])
 * @method \Aws\Result disassociateFromConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise disassociateFromConfigurationAsync(array $args = [])
 * @method \Aws\Result getAccountPreferences(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getAccountPreferencesAsync(array $args = [])
 * @method \Aws\Result getCustomAction(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getCustomActionAsync(array $args = [])
 * @method \Aws\Result getMicrosoftTeamsChannelConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise getMicrosoftTeamsChannelConfigurationAsync(array $args = [])
 * @method \Aws\Result listAssociations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listAssociationsAsync(array $args = [])
 * @method \Aws\Result listCustomActions(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listCustomActionsAsync(array $args = [])
 * @method \Aws\Result listMicrosoftTeamsChannelConfigurations(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMicrosoftTeamsChannelConfigurationsAsync(array $args = [])
 * @method \Aws\Result listMicrosoftTeamsConfiguredTeams(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMicrosoftTeamsConfiguredTeamsAsync(array $args = [])
 * @method \Aws\Result listMicrosoftTeamsUserIdentities(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listMicrosoftTeamsUserIdentitiesAsync(array $args = [])
 * @method \Aws\Result listTagsForResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise listTagsForResourceAsync(array $args = [])
 * @method \Aws\Result tagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise tagResourceAsync(array $args = [])
 * @method \Aws\Result untagResource(array $args = [])
 * @method \GuzzleHttp\Promise\Promise untagResourceAsync(array $args = [])
 * @method \Aws\Result updateAccountPreferences(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateAccountPreferencesAsync(array $args = [])
 * @method \Aws\Result updateChimeWebhookConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateChimeWebhookConfigurationAsync(array $args = [])
 * @method \Aws\Result updateCustomAction(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateCustomActionAsync(array $args = [])
 * @method \Aws\Result updateMicrosoftTeamsChannelConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateMicrosoftTeamsChannelConfigurationAsync(array $args = [])
 * @method \Aws\Result updateSlackChannelConfiguration(array $args = [])
 * @method \GuzzleHttp\Promise\Promise updateSlackChannelConfigurationAsync(array $args = [])
 */
class ChatbotClient extends AwsClient {}
