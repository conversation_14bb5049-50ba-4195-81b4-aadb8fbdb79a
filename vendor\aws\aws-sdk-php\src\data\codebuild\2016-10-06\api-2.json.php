<?php
// This file was auto-generated from sdk-root/src/data/codebuild/2016-10-06/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2016-10-06', 'endpointPrefix' => 'codebuild', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'AWS CodeBuild', 'serviceId' => 'CodeBuild', 'signatureVersion' => 'v4', 'targetPrefix' => 'CodeBuild_20161006', 'uid' => 'codebuild-2016-10-06', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'BatchDeleteBuilds' => [ 'name' => 'BatchDeleteBuilds', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchDeleteBuildsInput', ], 'output' => [ 'shape' => 'BatchDeleteBuildsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetBuildBatches' => [ 'name' => 'BatchGetBuildBatches', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetBuildBatchesInput', ], 'output' => [ 'shape' => 'BatchGetBuildBatchesOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetBuilds' => [ 'name' => 'BatchGetBuilds', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetBuildsInput', ], 'output' => [ 'shape' => 'BatchGetBuildsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetCommandExecutions' => [ 'name' => 'BatchGetCommandExecutions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetCommandExecutionsInput', ], 'output' => [ 'shape' => 'BatchGetCommandExecutionsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetFleets' => [ 'name' => 'BatchGetFleets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetFleetsInput', ], 'output' => [ 'shape' => 'BatchGetFleetsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetProjects' => [ 'name' => 'BatchGetProjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetProjectsInput', ], 'output' => [ 'shape' => 'BatchGetProjectsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetReportGroups' => [ 'name' => 'BatchGetReportGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetReportGroupsInput', ], 'output' => [ 'shape' => 'BatchGetReportGroupsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetReports' => [ 'name' => 'BatchGetReports', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetReportsInput', ], 'output' => [ 'shape' => 'BatchGetReportsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'BatchGetSandboxes' => [ 'name' => 'BatchGetSandboxes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'BatchGetSandboxesInput', ], 'output' => [ 'shape' => 'BatchGetSandboxesOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'CreateFleet' => [ 'name' => 'CreateFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateFleetInput', ], 'output' => [ 'shape' => 'CreateFleetOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'AccountLimitExceededException', ], ], ], 'CreateProject' => [ 'name' => 'CreateProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateProjectInput', ], 'output' => [ 'shape' => 'CreateProjectOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'AccountLimitExceededException', ], ], ], 'CreateReportGroup' => [ 'name' => 'CreateReportGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateReportGroupInput', ], 'output' => [ 'shape' => 'CreateReportGroupOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'AccountLimitExceededException', ], ], ], 'CreateWebhook' => [ 'name' => 'CreateWebhook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateWebhookInput', ], 'output' => [ 'shape' => 'CreateWebhookOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'OAuthProviderException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteBuildBatch' => [ 'name' => 'DeleteBuildBatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBuildBatchInput', ], 'output' => [ 'shape' => 'DeleteBuildBatchOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'DeleteFleet' => [ 'name' => 'DeleteFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteFleetInput', ], 'output' => [ 'shape' => 'DeleteFleetOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'DeleteProject' => [ 'name' => 'DeleteProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteProjectInput', ], 'output' => [ 'shape' => 'DeleteProjectOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'DeleteReport' => [ 'name' => 'DeleteReport', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteReportInput', ], 'output' => [ 'shape' => 'DeleteReportOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'DeleteReportGroup' => [ 'name' => 'DeleteReportGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteReportGroupInput', ], 'output' => [ 'shape' => 'DeleteReportGroupOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'DeleteResourcePolicy' => [ 'name' => 'DeleteResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourcePolicyInput', ], 'output' => [ 'shape' => 'DeleteResourcePolicyOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'DeleteSourceCredentials' => [ 'name' => 'DeleteSourceCredentials', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteSourceCredentialsInput', ], 'output' => [ 'shape' => 'DeleteSourceCredentialsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteWebhook' => [ 'name' => 'DeleteWebhook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteWebhookInput', ], 'output' => [ 'shape' => 'DeleteWebhookOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OAuthProviderException', ], ], ], 'DescribeCodeCoverages' => [ 'name' => 'DescribeCodeCoverages', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeCodeCoveragesInput', ], 'output' => [ 'shape' => 'DescribeCodeCoveragesOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'DescribeTestCases' => [ 'name' => 'DescribeTestCases', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeTestCasesInput', ], 'output' => [ 'shape' => 'DescribeTestCasesOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetReportGroupTrend' => [ 'name' => 'GetReportGroupTrend', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetReportGroupTrendInput', ], 'output' => [ 'shape' => 'GetReportGroupTrendOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetResourcePolicy' => [ 'name' => 'GetResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourcePolicyInput', ], 'output' => [ 'shape' => 'GetResourcePolicyOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'ImportSourceCredentials' => [ 'name' => 'ImportSourceCredentials', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ImportSourceCredentialsInput', ], 'output' => [ 'shape' => 'ImportSourceCredentialsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'AccountLimitExceededException', ], [ 'shape' => 'ResourceAlreadyExistsException', ], ], ], 'InvalidateProjectCache' => [ 'name' => 'InvalidateProjectCache', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'InvalidateProjectCacheInput', ], 'output' => [ 'shape' => 'InvalidateProjectCacheOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListBuildBatches' => [ 'name' => 'ListBuildBatches', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBuildBatchesInput', ], 'output' => [ 'shape' => 'ListBuildBatchesOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'ListBuildBatchesForProject' => [ 'name' => 'ListBuildBatchesForProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBuildBatchesForProjectInput', ], 'output' => [ 'shape' => 'ListBuildBatchesForProjectOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListBuilds' => [ 'name' => 'ListBuilds', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBuildsInput', ], 'output' => [ 'shape' => 'ListBuildsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'ListBuildsForProject' => [ 'name' => 'ListBuildsForProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBuildsForProjectInput', ], 'output' => [ 'shape' => 'ListBuildsForProjectOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListCommandExecutionsForSandbox' => [ 'name' => 'ListCommandExecutionsForSandbox', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCommandExecutionsForSandboxInput', ], 'output' => [ 'shape' => 'ListCommandExecutionsForSandboxOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListCuratedEnvironmentImages' => [ 'name' => 'ListCuratedEnvironmentImages', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCuratedEnvironmentImagesInput', ], 'output' => [ 'shape' => 'ListCuratedEnvironmentImagesOutput', ], ], 'ListFleets' => [ 'name' => 'ListFleets', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListFleetsInput', ], 'output' => [ 'shape' => 'ListFleetsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'ListProjects' => [ 'name' => 'ListProjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListProjectsInput', ], 'output' => [ 'shape' => 'ListProjectsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'ListReportGroups' => [ 'name' => 'ListReportGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListReportGroupsInput', ], 'output' => [ 'shape' => 'ListReportGroupsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'ListReports' => [ 'name' => 'ListReports', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListReportsInput', ], 'output' => [ 'shape' => 'ListReportsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'ListReportsForReportGroup' => [ 'name' => 'ListReportsForReportGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListReportsForReportGroupInput', ], 'output' => [ 'shape' => 'ListReportsForReportGroupOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListSandboxes' => [ 'name' => 'ListSandboxes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSandboxesInput', ], 'output' => [ 'shape' => 'ListSandboxesOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'ListSandboxesForProject' => [ 'name' => 'ListSandboxesForProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSandboxesForProjectInput', ], 'output' => [ 'shape' => 'ListSandboxesForProjectOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListSharedProjects' => [ 'name' => 'ListSharedProjects', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSharedProjectsInput', ], 'output' => [ 'shape' => 'ListSharedProjectsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'ListSharedReportGroups' => [ 'name' => 'ListSharedReportGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSharedReportGroupsInput', ], 'output' => [ 'shape' => 'ListSharedReportGroupsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'ListSourceCredentials' => [ 'name' => 'ListSourceCredentials', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSourceCredentialsInput', ], 'output' => [ 'shape' => 'ListSourceCredentialsOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], ], ], 'PutResourcePolicy' => [ 'name' => 'PutResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutResourcePolicyInput', ], 'output' => [ 'shape' => 'PutResourcePolicyOutput', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidInputException', ], ], ], 'RetryBuild' => [ 'name' => 'RetryBuild', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RetryBuildInput', ], 'output' => [ 'shape' => 'RetryBuildOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccountLimitExceededException', ], ], ], 'RetryBuildBatch' => [ 'name' => 'RetryBuildBatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RetryBuildBatchInput', ], 'output' => [ 'shape' => 'RetryBuildBatchOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartBuild' => [ 'name' => 'StartBuild', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartBuildInput', ], 'output' => [ 'shape' => 'StartBuildOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccountLimitExceededException', ], ], ], 'StartBuildBatch' => [ 'name' => 'StartBuildBatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartBuildBatchInput', ], 'output' => [ 'shape' => 'StartBuildBatchOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartCommandExecution' => [ 'name' => 'StartCommandExecution', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartCommandExecutionInput', ], 'output' => [ 'shape' => 'StartCommandExecutionOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StartSandbox' => [ 'name' => 'StartSandbox', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartSandboxInput', ], 'output' => [ 'shape' => 'StartSandboxOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccountSuspendedException', ], ], ], 'StartSandboxConnection' => [ 'name' => 'StartSandboxConnection', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartSandboxConnectionInput', ], 'output' => [ 'shape' => 'StartSandboxConnectionOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StopBuild' => [ 'name' => 'StopBuild', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopBuildInput', ], 'output' => [ 'shape' => 'StopBuildOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StopBuildBatch' => [ 'name' => 'StopBuildBatch', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopBuildBatchInput', ], 'output' => [ 'shape' => 'StopBuildBatchOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'StopSandbox' => [ 'name' => 'StopSandbox', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopSandboxInput', ], 'output' => [ 'shape' => 'StopSandboxOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateFleet' => [ 'name' => 'UpdateFleet', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateFleetInput', ], 'output' => [ 'shape' => 'UpdateFleetOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccountLimitExceededException', ], ], ], 'UpdateProject' => [ 'name' => 'UpdateProject', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateProjectInput', ], 'output' => [ 'shape' => 'UpdateProjectOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateProjectVisibility' => [ 'name' => 'UpdateProjectVisibility', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateProjectVisibilityInput', ], 'output' => [ 'shape' => 'UpdateProjectVisibilityOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateReportGroup' => [ 'name' => 'UpdateReportGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateReportGroupInput', ], 'output' => [ 'shape' => 'UpdateReportGroupOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'UpdateWebhook' => [ 'name' => 'UpdateWebhook', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateWebhookInput', ], 'output' => [ 'shape' => 'UpdateWebhookOutput', ], 'errors' => [ [ 'shape' => 'InvalidInputException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'OAuthProviderException', ], ], ], ], 'shapes' => [ 'AccountLimitExceededException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'AccountSuspendedException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ArtifactNamespace' => [ 'type' => 'string', 'enum' => [ 'NONE', 'BUILD_ID', ], ], 'ArtifactPackaging' => [ 'type' => 'string', 'enum' => [ 'NONE', 'ZIP', ], ], 'ArtifactsType' => [ 'type' => 'string', 'enum' => [ 'CODEPIPELINE', 'S3', 'NO_ARTIFACTS', ], ], 'AuthType' => [ 'type' => 'string', 'enum' => [ 'OAUTH', 'BASIC_AUTH', 'PERSONAL_ACCESS_TOKEN', 'CODECONNECTIONS', 'SECRETS_MANAGER', ], ], 'AutoRetryConfig' => [ 'type' => 'structure', 'members' => [ 'autoRetryLimit' => [ 'shape' => 'WrapperInt', ], 'autoRetryNumber' => [ 'shape' => 'WrapperInt', ], 'nextAutoRetry' => [ 'shape' => 'String', ], 'previousAutoRetry' => [ 'shape' => 'String', ], ], ], 'BatchDeleteBuildsInput' => [ 'type' => 'structure', 'required' => [ 'ids', ], 'members' => [ 'ids' => [ 'shape' => 'BuildIds', ], ], ], 'BatchDeleteBuildsOutput' => [ 'type' => 'structure', 'members' => [ 'buildsDeleted' => [ 'shape' => 'BuildIds', ], 'buildsNotDeleted' => [ 'shape' => 'BuildsNotDeleted', ], ], ], 'BatchGetBuildBatchesInput' => [ 'type' => 'structure', 'required' => [ 'ids', ], 'members' => [ 'ids' => [ 'shape' => 'BuildBatchIds', ], ], ], 'BatchGetBuildBatchesOutput' => [ 'type' => 'structure', 'members' => [ 'buildBatches' => [ 'shape' => 'BuildBatches', ], 'buildBatchesNotFound' => [ 'shape' => 'BuildBatchIds', ], ], ], 'BatchGetBuildsInput' => [ 'type' => 'structure', 'required' => [ 'ids', ], 'members' => [ 'ids' => [ 'shape' => 'BuildIds', ], ], ], 'BatchGetBuildsOutput' => [ 'type' => 'structure', 'members' => [ 'builds' => [ 'shape' => 'Builds', ], 'buildsNotFound' => [ 'shape' => 'BuildIds', ], ], ], 'BatchGetCommandExecutionsInput' => [ 'type' => 'structure', 'required' => [ 'sandboxId', 'commandExecutionIds', ], 'members' => [ 'sandboxId' => [ 'shape' => 'NonEmptyString', ], 'commandExecutionIds' => [ 'shape' => 'CommandExecutionIds', ], ], ], 'BatchGetCommandExecutionsOutput' => [ 'type' => 'structure', 'members' => [ 'commandExecutions' => [ 'shape' => 'CommandExecutions', ], 'commandExecutionsNotFound' => [ 'shape' => 'CommandExecutionIds', ], ], ], 'BatchGetFleetsInput' => [ 'type' => 'structure', 'required' => [ 'names', ], 'members' => [ 'names' => [ 'shape' => 'FleetNames', ], ], ], 'BatchGetFleetsOutput' => [ 'type' => 'structure', 'members' => [ 'fleets' => [ 'shape' => 'Fleets', ], 'fleetsNotFound' => [ 'shape' => 'FleetNames', ], ], ], 'BatchGetProjectsInput' => [ 'type' => 'structure', 'required' => [ 'names', ], 'members' => [ 'names' => [ 'shape' => 'ProjectNames', ], ], ], 'BatchGetProjectsOutput' => [ 'type' => 'structure', 'members' => [ 'projects' => [ 'shape' => 'Projects', ], 'projectsNotFound' => [ 'shape' => 'ProjectNames', ], ], ], 'BatchGetReportGroupsInput' => [ 'type' => 'structure', 'required' => [ 'reportGroupArns', ], 'members' => [ 'reportGroupArns' => [ 'shape' => 'ReportGroupArns', ], ], ], 'BatchGetReportGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'reportGroups' => [ 'shape' => 'ReportGroups', ], 'reportGroupsNotFound' => [ 'shape' => 'ReportGroupArns', ], ], ], 'BatchGetReportsInput' => [ 'type' => 'structure', 'required' => [ 'reportArns', ], 'members' => [ 'reportArns' => [ 'shape' => 'ReportArns', ], ], ], 'BatchGetReportsOutput' => [ 'type' => 'structure', 'members' => [ 'reports' => [ 'shape' => 'Reports', ], 'reportsNotFound' => [ 'shape' => 'ReportArns', ], ], ], 'BatchGetSandboxesInput' => [ 'type' => 'structure', 'required' => [ 'ids', ], 'members' => [ 'ids' => [ 'shape' => 'SandboxIds', ], ], ], 'BatchGetSandboxesOutput' => [ 'type' => 'structure', 'members' => [ 'sandboxes' => [ 'shape' => 'Sandboxes', ], 'sandboxesNotFound' => [ 'shape' => 'SandboxIds', ], ], ], 'BatchReportModeType' => [ 'type' => 'string', 'enum' => [ 'REPORT_INDIVIDUAL_BUILDS', 'REPORT_AGGREGATED_BATCH', ], ], 'BatchRestrictions' => [ 'type' => 'structure', 'members' => [ 'maximumBuildsAllowed' => [ 'shape' => 'WrapperInt', ], 'computeTypesAllowed' => [ 'shape' => 'ComputeTypesAllowed', ], 'fleetsAllowed' => [ 'shape' => 'FleetsAllowed', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BucketOwnerAccess' => [ 'type' => 'string', 'enum' => [ 'NONE', 'READ_ONLY', 'FULL', ], ], 'Build' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'buildNumber' => [ 'shape' => 'WrapperLong', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'currentPhase' => [ 'shape' => 'String', ], 'buildStatus' => [ 'shape' => 'StatusType', ], 'sourceVersion' => [ 'shape' => 'NonEmptyString', ], 'resolvedSourceVersion' => [ 'shape' => 'NonEmptyString', ], 'projectName' => [ 'shape' => 'NonEmptyString', ], 'phases' => [ 'shape' => 'BuildPhases', ], 'source' => [ 'shape' => 'ProjectSource', ], 'secondarySources' => [ 'shape' => 'ProjectSources', ], 'secondarySourceVersions' => [ 'shape' => 'ProjectSecondarySourceVersions', ], 'artifacts' => [ 'shape' => 'BuildArtifacts', ], 'secondaryArtifacts' => [ 'shape' => 'BuildArtifactsList', ], 'cache' => [ 'shape' => 'ProjectCache', ], 'environment' => [ 'shape' => 'ProjectEnvironment', ], 'serviceRole' => [ 'shape' => 'NonEmptyString', ], 'logs' => [ 'shape' => 'LogsLocation', ], 'timeoutInMinutes' => [ 'shape' => 'WrapperInt', ], 'queuedTimeoutInMinutes' => [ 'shape' => 'WrapperInt', ], 'buildComplete' => [ 'shape' => 'Boolean', ], 'initiator' => [ 'shape' => 'String', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], 'networkInterface' => [ 'shape' => 'NetworkInterface', ], 'encryptionKey' => [ 'shape' => 'NonEmptyString', ], 'exportedEnvironmentVariables' => [ 'shape' => 'ExportedEnvironmentVariables', ], 'reportArns' => [ 'shape' => 'BuildReportArns', ], 'fileSystemLocations' => [ 'shape' => 'ProjectFileSystemLocations', ], 'debugSession' => [ 'shape' => 'DebugSession', ], 'buildBatchArn' => [ 'shape' => 'String', ], 'autoRetryConfig' => [ 'shape' => 'AutoRetryConfig', ], ], ], 'BuildArtifacts' => [ 'type' => 'structure', 'members' => [ 'location' => [ 'shape' => 'String', ], 'sha256sum' => [ 'shape' => 'String', ], 'md5sum' => [ 'shape' => 'String', ], 'overrideArtifactName' => [ 'shape' => 'WrapperBoolean', ], 'encryptionDisabled' => [ 'shape' => 'WrapperBoolean', ], 'artifactIdentifier' => [ 'shape' => 'String', ], 'bucketOwnerAccess' => [ 'shape' => 'BucketOwnerAccess', ], ], ], 'BuildArtifactsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BuildArtifacts', ], 'max' => 12, 'min' => 0, ], 'BuildBatch' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'currentPhase' => [ 'shape' => 'String', ], 'buildBatchStatus' => [ 'shape' => 'StatusType', ], 'sourceVersion' => [ 'shape' => 'NonEmptyString', ], 'resolvedSourceVersion' => [ 'shape' => 'NonEmptyString', ], 'projectName' => [ 'shape' => 'NonEmptyString', ], 'phases' => [ 'shape' => 'BuildBatchPhases', ], 'source' => [ 'shape' => 'ProjectSource', ], 'secondarySources' => [ 'shape' => 'ProjectSources', ], 'secondarySourceVersions' => [ 'shape' => 'ProjectSecondarySourceVersions', ], 'artifacts' => [ 'shape' => 'BuildArtifacts', ], 'secondaryArtifacts' => [ 'shape' => 'BuildArtifactsList', ], 'cache' => [ 'shape' => 'ProjectCache', ], 'environment' => [ 'shape' => 'ProjectEnvironment', ], 'serviceRole' => [ 'shape' => 'NonEmptyString', ], 'logConfig' => [ 'shape' => 'LogsConfig', ], 'buildTimeoutInMinutes' => [ 'shape' => 'WrapperInt', ], 'queuedTimeoutInMinutes' => [ 'shape' => 'WrapperInt', ], 'complete' => [ 'shape' => 'Boolean', ], 'initiator' => [ 'shape' => 'String', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], 'encryptionKey' => [ 'shape' => 'NonEmptyString', ], 'buildBatchNumber' => [ 'shape' => 'WrapperLong', ], 'fileSystemLocations' => [ 'shape' => 'ProjectFileSystemLocations', ], 'buildBatchConfig' => [ 'shape' => 'ProjectBuildBatchConfig', ], 'buildGroups' => [ 'shape' => 'BuildGroups', ], 'debugSessionEnabled' => [ 'shape' => 'WrapperBoolean', ], 'reportArns' => [ 'shape' => 'BuildReportArns', ], ], ], 'BuildBatchFilter' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'StatusType', ], ], ], 'BuildBatchIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 100, 'min' => 0, ], 'BuildBatchPhase' => [ 'type' => 'structure', 'members' => [ 'phaseType' => [ 'shape' => 'BuildBatchPhaseType', ], 'phaseStatus' => [ 'shape' => 'StatusType', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'durationInSeconds' => [ 'shape' => 'WrapperLong', ], 'contexts' => [ 'shape' => 'PhaseContexts', ], ], ], 'BuildBatchPhaseType' => [ 'type' => 'string', 'enum' => [ 'SUBMITTED', 'DOWNLOAD_BATCHSPEC', 'IN_PROGRESS', 'COMBINE_ARTIFACTS', 'SUCCEEDED', 'FAILED', 'STOPPED', ], ], 'BuildBatchPhases' => [ 'type' => 'list', 'member' => [ 'shape' => 'BuildBatchPhase', ], ], 'BuildBatches' => [ 'type' => 'list', 'member' => [ 'shape' => 'BuildBatch', ], 'max' => 100, 'min' => 0, ], 'BuildGroup' => [ 'type' => 'structure', 'members' => [ 'identifier' => [ 'shape' => 'String', ], 'dependsOn' => [ 'shape' => 'Identifiers', ], 'ignoreFailure' => [ 'shape' => 'Boolean', ], 'currentBuildSummary' => [ 'shape' => 'BuildSummary', ], 'priorBuildSummaryList' => [ 'shape' => 'BuildSummaries', ], ], ], 'BuildGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'BuildGroup', ], ], 'BuildIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 100, 'min' => 1, ], 'BuildNotDeleted' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], 'statusCode' => [ 'shape' => 'String', ], ], ], 'BuildPhase' => [ 'type' => 'structure', 'members' => [ 'phaseType' => [ 'shape' => 'BuildPhaseType', ], 'phaseStatus' => [ 'shape' => 'StatusType', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'durationInSeconds' => [ 'shape' => 'WrapperLong', ], 'contexts' => [ 'shape' => 'PhaseContexts', ], ], ], 'BuildPhaseType' => [ 'type' => 'string', 'enum' => [ 'SUBMITTED', 'QUEUED', 'PROVISIONING', 'DOWNLOAD_SOURCE', 'INSTALL', 'PRE_BUILD', 'BUILD', 'POST_BUILD', 'UPLOAD_ARTIFACTS', 'FINALIZING', 'COMPLETED', ], ], 'BuildPhases' => [ 'type' => 'list', 'member' => [ 'shape' => 'BuildPhase', ], ], 'BuildReportArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'BuildStatusConfig' => [ 'type' => 'structure', 'members' => [ 'context' => [ 'shape' => 'String', ], 'targetUrl' => [ 'shape' => 'String', ], ], ], 'BuildSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'BuildSummary', ], ], 'BuildSummary' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'String', ], 'requestedOn' => [ 'shape' => 'Timestamp', ], 'buildStatus' => [ 'shape' => 'StatusType', ], 'primaryArtifact' => [ 'shape' => 'ResolvedArtifact', ], 'secondaryArtifacts' => [ 'shape' => 'ResolvedSecondaryArtifacts', ], ], ], 'BuildTimeOut' => [ 'type' => 'integer', 'max' => 2160, 'min' => 5, ], 'Builds' => [ 'type' => 'list', 'member' => [ 'shape' => 'Build', ], ], 'BuildsNotDeleted' => [ 'type' => 'list', 'member' => [ 'shape' => 'BuildNotDeleted', ], ], 'CacheMode' => [ 'type' => 'string', 'enum' => [ 'LOCAL_DOCKER_LAYER_CACHE', 'LOCAL_SOURCE_CACHE', 'LOCAL_CUSTOM_CACHE', ], ], 'CacheType' => [ 'type' => 'string', 'enum' => [ 'NO_CACHE', 'S3', 'LOCAL', ], ], 'CloudWatchLogsConfig' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'LogsConfigStatusType', ], 'groupName' => [ 'shape' => 'String', ], 'streamName' => [ 'shape' => 'String', ], ], ], 'CodeCoverage' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], 'reportARN' => [ 'shape' => 'NonEmptyString', ], 'filePath' => [ 'shape' => 'NonEmptyString', ], 'lineCoveragePercentage' => [ 'shape' => 'Percentage', ], 'linesCovered' => [ 'shape' => 'NonNegativeInt', ], 'linesMissed' => [ 'shape' => 'NonNegativeInt', ], 'branchCoveragePercentage' => [ 'shape' => 'Percentage', ], 'branchesCovered' => [ 'shape' => 'NonNegativeInt', ], 'branchesMissed' => [ 'shape' => 'NonNegativeInt', ], 'expired' => [ 'shape' => 'Timestamp', ], ], ], 'CodeCoverageReportSummary' => [ 'type' => 'structure', 'members' => [ 'lineCoveragePercentage' => [ 'shape' => 'Percentage', ], 'linesCovered' => [ 'shape' => 'NonNegativeInt', ], 'linesMissed' => [ 'shape' => 'NonNegativeInt', ], 'branchCoveragePercentage' => [ 'shape' => 'Percentage', ], 'branchesCovered' => [ 'shape' => 'NonNegativeInt', ], 'branchesMissed' => [ 'shape' => 'NonNegativeInt', ], ], ], 'CodeCoverages' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodeCoverage', ], ], 'CommandExecution' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], 'sandboxId' => [ 'shape' => 'NonEmptyString', ], 'submitTime' => [ 'shape' => 'Timestamp', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'NonEmptyString', ], 'command' => [ 'shape' => 'SensitiveNonEmptyString', ], 'type' => [ 'shape' => 'CommandType', ], 'exitCode' => [ 'shape' => 'NonEmptyString', ], 'standardOutputContent' => [ 'shape' => 'SensitiveNonEmptyString', ], 'standardErrContent' => [ 'shape' => 'SensitiveNonEmptyString', ], 'logs' => [ 'shape' => 'LogsLocation', ], 'sandboxArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'CommandExecutionIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 100, 'min' => 1, ], 'CommandExecutions' => [ 'type' => 'list', 'member' => [ 'shape' => 'CommandExecution', ], ], 'CommandType' => [ 'type' => 'string', 'enum' => [ 'SHELL', ], ], 'ComputeConfiguration' => [ 'type' => 'structure', 'members' => [ 'vCpu' => [ 'shape' => 'WrapperLong', ], 'memory' => [ 'shape' => 'WrapperLong', ], 'disk' => [ 'shape' => 'WrapperLong', ], 'machineType' => [ 'shape' => 'MachineType', ], 'instanceType' => [ 'shape' => 'NonEmptyString', ], ], ], 'ComputeType' => [ 'type' => 'string', 'enum' => [ 'BUILD_GENERAL1_SMALL', 'BUILD_GENERAL1_MEDIUM', 'BUILD_GENERAL1_LARGE', 'BUILD_GENERAL1_XLARGE', 'BUILD_GENERAL1_2XLARGE', 'BUILD_LAMBDA_1GB', 'BUILD_LAMBDA_2GB', 'BUILD_LAMBDA_4GB', 'BUILD_LAMBDA_8GB', 'BUILD_LAMBDA_10GB', 'ATTRIBUTE_BASED_COMPUTE', 'CUSTOM_INSTANCE_TYPE', ], ], 'ComputeTypesAllowed' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'CreateFleetInput' => [ 'type' => 'structure', 'required' => [ 'name', 'baseCapacity', 'environmentType', 'computeType', ], 'members' => [ 'name' => [ 'shape' => 'FleetName', ], 'baseCapacity' => [ 'shape' => 'FleetCapacity', ], 'environmentType' => [ 'shape' => 'EnvironmentType', ], 'computeType' => [ 'shape' => 'ComputeType', ], 'computeConfiguration' => [ 'shape' => 'ComputeConfiguration', ], 'scalingConfiguration' => [ 'shape' => 'ScalingConfigurationInput', ], 'overflowBehavior' => [ 'shape' => 'FleetOverflowBehavior', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], 'proxyConfiguration' => [ 'shape' => 'ProxyConfiguration', ], 'imageId' => [ 'shape' => 'NonEmptyString', ], 'fleetServiceRole' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateFleetOutput' => [ 'type' => 'structure', 'members' => [ 'fleet' => [ 'shape' => 'Fleet', ], ], ], 'CreateProjectInput' => [ 'type' => 'structure', 'required' => [ 'name', 'source', 'artifacts', 'environment', 'serviceRole', ], 'members' => [ 'name' => [ 'shape' => 'ProjectName', ], 'description' => [ 'shape' => 'ProjectDescription', ], 'source' => [ 'shape' => 'ProjectSource', ], 'secondarySources' => [ 'shape' => 'ProjectSources', ], 'sourceVersion' => [ 'shape' => 'String', ], 'secondarySourceVersions' => [ 'shape' => 'ProjectSecondarySourceVersions', ], 'artifacts' => [ 'shape' => 'ProjectArtifacts', ], 'secondaryArtifacts' => [ 'shape' => 'ProjectArtifactsList', ], 'cache' => [ 'shape' => 'ProjectCache', ], 'environment' => [ 'shape' => 'ProjectEnvironment', ], 'serviceRole' => [ 'shape' => 'NonEmptyString', ], 'timeoutInMinutes' => [ 'shape' => 'BuildTimeOut', ], 'queuedTimeoutInMinutes' => [ 'shape' => 'TimeOut', ], 'encryptionKey' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagList', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], 'badgeEnabled' => [ 'shape' => 'WrapperBoolean', ], 'logsConfig' => [ 'shape' => 'LogsConfig', ], 'fileSystemLocations' => [ 'shape' => 'ProjectFileSystemLocations', ], 'buildBatchConfig' => [ 'shape' => 'ProjectBuildBatchConfig', ], 'concurrentBuildLimit' => [ 'shape' => 'WrapperInt', ], 'autoRetryLimit' => [ 'shape' => 'WrapperInt', ], ], ], 'CreateProjectOutput' => [ 'type' => 'structure', 'members' => [ 'project' => [ 'shape' => 'Project', ], ], ], 'CreateReportGroupInput' => [ 'type' => 'structure', 'required' => [ 'name', 'type', 'exportConfig', ], 'members' => [ 'name' => [ 'shape' => 'ReportGroupName', ], 'type' => [ 'shape' => 'ReportType', ], 'exportConfig' => [ 'shape' => 'ReportExportConfig', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateReportGroupOutput' => [ 'type' => 'structure', 'members' => [ 'reportGroup' => [ 'shape' => 'ReportGroup', ], ], ], 'CreateWebhookInput' => [ 'type' => 'structure', 'required' => [ 'projectName', ], 'members' => [ 'projectName' => [ 'shape' => 'ProjectName', ], 'branchFilter' => [ 'shape' => 'String', ], 'filterGroups' => [ 'shape' => 'FilterGroups', ], 'buildType' => [ 'shape' => 'WebhookBuildType', ], 'manualCreation' => [ 'shape' => 'WrapperBoolean', ], 'scopeConfiguration' => [ 'shape' => 'ScopeConfiguration', ], ], ], 'CreateWebhookOutput' => [ 'type' => 'structure', 'members' => [ 'webhook' => [ 'shape' => 'Webhook', ], ], ], 'CredentialProviderType' => [ 'type' => 'string', 'enum' => [ 'SECRETS_MANAGER', ], ], 'DebugSession' => [ 'type' => 'structure', 'members' => [ 'sessionEnabled' => [ 'shape' => 'WrapperBoolean', ], 'sessionTarget' => [ 'shape' => 'NonEmptyString', ], ], ], 'DeleteBuildBatchInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], ], ], 'DeleteBuildBatchOutput' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'String', ], 'buildsDeleted' => [ 'shape' => 'BuildIds', ], 'buildsNotDeleted' => [ 'shape' => 'BuildsNotDeleted', ], ], ], 'DeleteFleetInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'NonEmptyString', ], ], ], 'DeleteFleetOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteProjectInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], ], ], 'DeleteProjectOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteReportGroupInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'NonEmptyString', ], 'deleteReports' => [ 'shape' => 'Boolean', ], ], ], 'DeleteReportGroupOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteReportInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'NonEmptyString', ], ], ], 'DeleteReportOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteResourcePolicyInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'DeleteResourcePolicyOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteSourceCredentialsInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'NonEmptyString', ], ], ], 'DeleteSourceCredentialsOutput' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'NonEmptyString', ], ], ], 'DeleteWebhookInput' => [ 'type' => 'structure', 'required' => [ 'projectName', ], 'members' => [ 'projectName' => [ 'shape' => 'ProjectName', ], ], ], 'DeleteWebhookOutput' => [ 'type' => 'structure', 'members' => [], ], 'DescribeCodeCoveragesInput' => [ 'type' => 'structure', 'required' => [ 'reportArn', ], 'members' => [ 'reportArn' => [ 'shape' => 'NonEmptyString', ], 'nextToken' => [ 'shape' => 'String', ], 'maxResults' => [ 'shape' => 'PageSize', ], 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'sortBy' => [ 'shape' => 'ReportCodeCoverageSortByType', ], 'minLineCoveragePercentage' => [ 'shape' => 'Percentage', ], 'maxLineCoveragePercentage' => [ 'shape' => 'Percentage', ], ], ], 'DescribeCodeCoveragesOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'codeCoverages' => [ 'shape' => 'CodeCoverages', ], ], ], 'DescribeTestCasesInput' => [ 'type' => 'structure', 'required' => [ 'reportArn', ], 'members' => [ 'reportArn' => [ 'shape' => 'String', ], 'nextToken' => [ 'shape' => 'String', ], 'maxResults' => [ 'shape' => 'PageSize', ], 'filter' => [ 'shape' => 'TestCaseFilter', ], ], ], 'DescribeTestCasesOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'testCases' => [ 'shape' => 'TestCases', ], ], ], 'DockerServer' => [ 'type' => 'structure', 'required' => [ 'computeType', ], 'members' => [ 'computeType' => [ 'shape' => 'ComputeType', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], 'status' => [ 'shape' => 'DockerServerStatus', ], ], ], 'DockerServerStatus' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'EnvironmentImage' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'String', ], 'versions' => [ 'shape' => 'ImageVersions', ], ], ], 'EnvironmentImages' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentImage', ], ], 'EnvironmentLanguage' => [ 'type' => 'structure', 'members' => [ 'language' => [ 'shape' => 'LanguageType', ], 'images' => [ 'shape' => 'EnvironmentImages', ], ], ], 'EnvironmentLanguages' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentLanguage', ], ], 'EnvironmentPlatform' => [ 'type' => 'structure', 'members' => [ 'platform' => [ 'shape' => 'PlatformType', ], 'languages' => [ 'shape' => 'EnvironmentLanguages', ], ], ], 'EnvironmentPlatforms' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentPlatform', ], ], 'EnvironmentType' => [ 'type' => 'string', 'enum' => [ 'WINDOWS_CONTAINER', 'LINUX_CONTAINER', 'LINUX_GPU_CONTAINER', 'ARM_CONTAINER', 'WINDOWS_SERVER_2019_CONTAINER', 'WINDOWS_SERVER_2022_CONTAINER', 'LINUX_LAMBDA_CONTAINER', 'ARM_LAMBDA_CONTAINER', 'LINUX_EC2', 'ARM_EC2', 'WINDOWS_EC2', 'MAC_ARM', ], ], 'EnvironmentVariable' => [ 'type' => 'structure', 'required' => [ 'name', 'value', ], 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'EnvironmentVariableType', ], ], ], 'EnvironmentVariableType' => [ 'type' => 'string', 'enum' => [ 'PLAINTEXT', 'PARAMETER_STORE', 'SECRETS_MANAGER', ], ], 'EnvironmentVariables' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnvironmentVariable', ], ], 'ExportedEnvironmentVariable' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'value' => [ 'shape' => 'String', ], ], ], 'ExportedEnvironmentVariables' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportedEnvironmentVariable', ], ], 'FileSystemType' => [ 'type' => 'string', 'enum' => [ 'EFS', ], ], 'FilterGroup' => [ 'type' => 'list', 'member' => [ 'shape' => 'WebhookFilter', ], ], 'FilterGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterGroup', ], ], 'Fleet' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'NonEmptyString', ], 'name' => [ 'shape' => 'FleetName', ], 'id' => [ 'shape' => 'NonEmptyString', ], 'created' => [ 'shape' => 'Timestamp', ], 'lastModified' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'FleetStatus', ], 'baseCapacity' => [ 'shape' => 'FleetCapacity', ], 'environmentType' => [ 'shape' => 'EnvironmentType', ], 'computeType' => [ 'shape' => 'ComputeType', ], 'computeConfiguration' => [ 'shape' => 'ComputeConfiguration', ], 'scalingConfiguration' => [ 'shape' => 'ScalingConfigurationOutput', ], 'overflowBehavior' => [ 'shape' => 'FleetOverflowBehavior', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], 'proxyConfiguration' => [ 'shape' => 'ProxyConfiguration', ], 'imageId' => [ 'shape' => 'NonEmptyString', ], 'fleetServiceRole' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'FleetArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 100, 'min' => 1, ], 'FleetCapacity' => [ 'type' => 'integer', ], 'FleetContextCode' => [ 'type' => 'string', 'enum' => [ 'CREATE_FAILED', 'UPDATE_FAILED', 'ACTION_REQUIRED', 'PENDING_DELETION', 'INSUFFICIENT_CAPACITY', ], ], 'FleetName' => [ 'type' => 'string', 'max' => 128, 'min' => 2, 'pattern' => '[A-Za-z0-9][A-Za-z0-9\\-_]{1,127}', ], 'FleetNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 100, 'min' => 1, ], 'FleetOverflowBehavior' => [ 'type' => 'string', 'enum' => [ 'QUEUE', 'ON_DEMAND', ], ], 'FleetProxyRule' => [ 'type' => 'structure', 'required' => [ 'type', 'effect', 'entities', ], 'members' => [ 'type' => [ 'shape' => 'FleetProxyRuleType', ], 'effect' => [ 'shape' => 'FleetProxyRuleEffectType', ], 'entities' => [ 'shape' => 'FleetProxyRuleEntities', ], ], ], 'FleetProxyRuleBehavior' => [ 'type' => 'string', 'enum' => [ 'ALLOW_ALL', 'DENY_ALL', ], ], 'FleetProxyRuleEffectType' => [ 'type' => 'string', 'enum' => [ 'ALLOW', 'DENY', ], ], 'FleetProxyRuleEntities' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], 'max' => 100, 'min' => 1, ], 'FleetProxyRuleType' => [ 'type' => 'string', 'enum' => [ 'DOMAIN', 'IP', ], ], 'FleetProxyRules' => [ 'type' => 'list', 'member' => [ 'shape' => 'FleetProxyRule', ], 'max' => 100, ], 'FleetScalingMetricType' => [ 'type' => 'string', 'enum' => [ 'FLEET_UTILIZATION_RATE', ], ], 'FleetScalingType' => [ 'type' => 'string', 'enum' => [ 'TARGET_TRACKING_SCALING', ], ], 'FleetSortByType' => [ 'type' => 'string', 'enum' => [ 'NAME', 'CREATED_TIME', 'LAST_MODIFIED_TIME', ], ], 'FleetStatus' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'FleetStatusCode', ], 'context' => [ 'shape' => 'FleetContextCode', ], 'message' => [ 'shape' => 'String', ], ], ], 'FleetStatusCode' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'UPDATING', 'ROTATING', 'PENDING_DELETION', 'DELETING', 'CREATE_FAILED', 'UPDATE_ROLLBACK_FAILED', 'ACTIVE', ], ], 'Fleets' => [ 'type' => 'list', 'member' => [ 'shape' => 'Fleet', ], 'max' => 100, 'min' => 1, ], 'FleetsAllowed' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'GetReportGroupTrendInput' => [ 'type' => 'structure', 'required' => [ 'reportGroupArn', 'trendField', ], 'members' => [ 'reportGroupArn' => [ 'shape' => 'NonEmptyString', ], 'numOfReports' => [ 'shape' => 'PageSize', ], 'trendField' => [ 'shape' => 'ReportGroupTrendFieldType', ], ], ], 'GetReportGroupTrendOutput' => [ 'type' => 'structure', 'members' => [ 'stats' => [ 'shape' => 'ReportGroupTrendStats', ], 'rawData' => [ 'shape' => 'ReportGroupTrendRawDataList', ], ], ], 'GetResourcePolicyInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'GetResourcePolicyOutput' => [ 'type' => 'structure', 'members' => [ 'policy' => [ 'shape' => 'NonEmptyString', ], ], ], 'GitCloneDepth' => [ 'type' => 'integer', 'min' => 0, ], 'GitSubmodulesConfig' => [ 'type' => 'structure', 'required' => [ 'fetchSubmodules', ], 'members' => [ 'fetchSubmodules' => [ 'shape' => 'WrapperBoolean', ], ], ], 'Identifiers' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'ImagePullCredentialsType' => [ 'type' => 'string', 'enum' => [ 'CODEBUILD', 'SERVICE_ROLE', ], ], 'ImageVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ImportSourceCredentialsInput' => [ 'type' => 'structure', 'required' => [ 'token', 'serverType', 'authType', ], 'members' => [ 'username' => [ 'shape' => 'NonEmptyString', ], 'token' => [ 'shape' => 'SensitiveNonEmptyString', ], 'serverType' => [ 'shape' => 'ServerType', ], 'authType' => [ 'shape' => 'AuthType', ], 'shouldOverwrite' => [ 'shape' => 'WrapperBoolean', ], ], ], 'ImportSourceCredentialsOutput' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'NonEmptyString', ], ], ], 'InvalidInputException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'InvalidateProjectCacheInput' => [ 'type' => 'structure', 'required' => [ 'projectName', ], 'members' => [ 'projectName' => [ 'shape' => 'NonEmptyString', ], ], ], 'InvalidateProjectCacheOutput' => [ 'type' => 'structure', 'members' => [], ], 'KeyInput' => [ 'type' => 'string', 'max' => 127, 'min' => 1, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=@+\\-]*)$', ], 'LanguageType' => [ 'type' => 'string', 'enum' => [ 'JAVA', 'PYTHON', 'NODE_JS', 'RUBY', 'GOLANG', 'DOCKER', 'ANDROID', 'DOTNET', 'BASE', 'PHP', ], ], 'ListBuildBatchesForProjectInput' => [ 'type' => 'structure', 'members' => [ 'projectName' => [ 'shape' => 'NonEmptyString', ], 'filter' => [ 'shape' => 'BuildBatchFilter', ], 'maxResults' => [ 'shape' => 'PageSize', ], 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListBuildBatchesForProjectOutput' => [ 'type' => 'structure', 'members' => [ 'ids' => [ 'shape' => 'BuildBatchIds', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListBuildBatchesInput' => [ 'type' => 'structure', 'members' => [ 'filter' => [ 'shape' => 'BuildBatchFilter', ], 'maxResults' => [ 'shape' => 'PageSize', ], 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListBuildBatchesOutput' => [ 'type' => 'structure', 'members' => [ 'ids' => [ 'shape' => 'BuildBatchIds', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListBuildsForProjectInput' => [ 'type' => 'structure', 'required' => [ 'projectName', ], 'members' => [ 'projectName' => [ 'shape' => 'NonEmptyString', ], 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListBuildsForProjectOutput' => [ 'type' => 'structure', 'members' => [ 'ids' => [ 'shape' => 'BuildIds', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListBuildsInput' => [ 'type' => 'structure', 'members' => [ 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListBuildsOutput' => [ 'type' => 'structure', 'members' => [ 'ids' => [ 'shape' => 'BuildIds', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListCommandExecutionsForSandboxInput' => [ 'type' => 'structure', 'required' => [ 'sandboxId', ], 'members' => [ 'sandboxId' => [ 'shape' => 'NonEmptyString', ], 'maxResults' => [ 'shape' => 'PageSize', ], 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'nextToken' => [ 'shape' => 'SensitiveString', ], ], ], 'ListCommandExecutionsForSandboxOutput' => [ 'type' => 'structure', 'members' => [ 'commandExecutions' => [ 'shape' => 'CommandExecutions', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListCuratedEnvironmentImagesInput' => [ 'type' => 'structure', 'members' => [], ], 'ListCuratedEnvironmentImagesOutput' => [ 'type' => 'structure', 'members' => [ 'platforms' => [ 'shape' => 'EnvironmentPlatforms', ], ], ], 'ListFleetsInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'SensitiveString', ], 'maxResults' => [ 'shape' => 'PageSize', ], 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'sortBy' => [ 'shape' => 'FleetSortByType', ], ], ], 'ListFleetsOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'fleets' => [ 'shape' => 'FleetArns', ], ], ], 'ListProjectsInput' => [ 'type' => 'structure', 'members' => [ 'sortBy' => [ 'shape' => 'ProjectSortByType', ], 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'nextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListProjectsOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'projects' => [ 'shape' => 'ProjectNames', ], ], ], 'ListReportGroupsInput' => [ 'type' => 'structure', 'members' => [ 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'sortBy' => [ 'shape' => 'ReportGroupSortByType', ], 'nextToken' => [ 'shape' => 'String', ], 'maxResults' => [ 'shape' => 'PageSize', ], ], ], 'ListReportGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'reportGroups' => [ 'shape' => 'ReportGroupArns', ], ], ], 'ListReportsForReportGroupInput' => [ 'type' => 'structure', 'required' => [ 'reportGroupArn', ], 'members' => [ 'reportGroupArn' => [ 'shape' => 'String', ], 'nextToken' => [ 'shape' => 'String', ], 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'maxResults' => [ 'shape' => 'PageSize', ], 'filter' => [ 'shape' => 'ReportFilter', ], ], ], 'ListReportsForReportGroupOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'reports' => [ 'shape' => 'ReportArns', ], ], ], 'ListReportsInput' => [ 'type' => 'structure', 'members' => [ 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'nextToken' => [ 'shape' => 'String', ], 'maxResults' => [ 'shape' => 'PageSize', ], 'filter' => [ 'shape' => 'ReportFilter', ], ], ], 'ListReportsOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'reports' => [ 'shape' => 'ReportArns', ], ], ], 'ListSandboxesForProjectInput' => [ 'type' => 'structure', 'required' => [ 'projectName', ], 'members' => [ 'projectName' => [ 'shape' => 'NonEmptyString', ], 'maxResults' => [ 'shape' => 'PageSize', ], 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'nextToken' => [ 'shape' => 'SensitiveString', ], ], ], 'ListSandboxesForProjectOutput' => [ 'type' => 'structure', 'members' => [ 'ids' => [ 'shape' => 'SandboxIds', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListSandboxesInput' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'PageSize', ], 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListSandboxesOutput' => [ 'type' => 'structure', 'members' => [ 'ids' => [ 'shape' => 'SandboxIds', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListSharedProjectsInput' => [ 'type' => 'structure', 'members' => [ 'sortBy' => [ 'shape' => 'SharedResourceSortByType', ], 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'maxResults' => [ 'shape' => 'PageSize', ], 'nextToken' => [ 'shape' => 'NonEmptyString', ], ], ], 'ListSharedProjectsOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'projects' => [ 'shape' => 'ProjectArns', ], ], ], 'ListSharedReportGroupsInput' => [ 'type' => 'structure', 'members' => [ 'sortOrder' => [ 'shape' => 'SortOrderType', ], 'sortBy' => [ 'shape' => 'SharedResourceSortByType', ], 'nextToken' => [ 'shape' => 'String', ], 'maxResults' => [ 'shape' => 'PageSize', ], ], ], 'ListSharedReportGroupsOutput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'reportGroups' => [ 'shape' => 'ReportGroupArns', ], ], ], 'ListSourceCredentialsInput' => [ 'type' => 'structure', 'members' => [], ], 'ListSourceCredentialsOutput' => [ 'type' => 'structure', 'members' => [ 'sourceCredentialsInfos' => [ 'shape' => 'SourceCredentialsInfos', ], ], ], 'LogsConfig' => [ 'type' => 'structure', 'members' => [ 'cloudWatchLogs' => [ 'shape' => 'CloudWatchLogsConfig', ], 's3Logs' => [ 'shape' => 'S3LogsConfig', ], ], ], 'LogsConfigStatusType' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'LogsLocation' => [ 'type' => 'structure', 'members' => [ 'groupName' => [ 'shape' => 'String', ], 'streamName' => [ 'shape' => 'String', ], 'deepLink' => [ 'shape' => 'String', ], 's3DeepLink' => [ 'shape' => 'String', ], 'cloudWatchLogsArn' => [ 'shape' => 'String', ], 's3LogsArn' => [ 'shape' => 'String', ], 'cloudWatchLogs' => [ 'shape' => 'CloudWatchLogsConfig', ], 's3Logs' => [ 'shape' => 'S3LogsConfig', ], ], ], 'MachineType' => [ 'type' => 'string', 'enum' => [ 'GENERAL', 'NVME', ], ], 'NetworkInterface' => [ 'type' => 'structure', 'members' => [ 'subnetId' => [ 'shape' => 'NonEmptyString', ], 'networkInterfaceId' => [ 'shape' => 'NonEmptyString', ], ], ], 'NonEmptyString' => [ 'type' => 'string', 'min' => 1, ], 'NonNegativeInt' => [ 'type' => 'integer', 'min' => 0, ], 'OAuthProviderException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'PageSize' => [ 'type' => 'integer', 'max' => 100, 'min' => 1, ], 'Percentage' => [ 'type' => 'double', 'max' => 100, 'min' => 0, ], 'PhaseContext' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'PhaseContexts' => [ 'type' => 'list', 'member' => [ 'shape' => 'PhaseContext', ], ], 'PlatformType' => [ 'type' => 'string', 'enum' => [ 'DEBIAN', 'AMAZON_LINUX', 'UBUNTU', 'WINDOWS_SERVER', ], ], 'Project' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ProjectName', ], 'arn' => [ 'shape' => 'String', ], 'description' => [ 'shape' => 'ProjectDescription', ], 'source' => [ 'shape' => 'ProjectSource', ], 'secondarySources' => [ 'shape' => 'ProjectSources', ], 'sourceVersion' => [ 'shape' => 'String', ], 'secondarySourceVersions' => [ 'shape' => 'ProjectSecondarySourceVersions', ], 'artifacts' => [ 'shape' => 'ProjectArtifacts', ], 'secondaryArtifacts' => [ 'shape' => 'ProjectArtifactsList', ], 'cache' => [ 'shape' => 'ProjectCache', ], 'environment' => [ 'shape' => 'ProjectEnvironment', ], 'serviceRole' => [ 'shape' => 'NonEmptyString', ], 'timeoutInMinutes' => [ 'shape' => 'BuildTimeOut', ], 'queuedTimeoutInMinutes' => [ 'shape' => 'TimeOut', ], 'encryptionKey' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagList', ], 'created' => [ 'shape' => 'Timestamp', ], 'lastModified' => [ 'shape' => 'Timestamp', ], 'webhook' => [ 'shape' => 'Webhook', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], 'badge' => [ 'shape' => 'ProjectBadge', ], 'logsConfig' => [ 'shape' => 'LogsConfig', ], 'fileSystemLocations' => [ 'shape' => 'ProjectFileSystemLocations', ], 'buildBatchConfig' => [ 'shape' => 'ProjectBuildBatchConfig', ], 'concurrentBuildLimit' => [ 'shape' => 'WrapperInt', ], 'projectVisibility' => [ 'shape' => 'ProjectVisibilityType', ], 'publicProjectAlias' => [ 'shape' => 'NonEmptyString', ], 'resourceAccessRole' => [ 'shape' => 'NonEmptyString', ], 'autoRetryLimit' => [ 'shape' => 'WrapperInt', ], ], ], 'ProjectArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 100, 'min' => 1, ], 'ProjectArtifacts' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'ArtifactsType', ], 'location' => [ 'shape' => 'String', ], 'path' => [ 'shape' => 'String', ], 'namespaceType' => [ 'shape' => 'ArtifactNamespace', ], 'name' => [ 'shape' => 'String', ], 'packaging' => [ 'shape' => 'ArtifactPackaging', ], 'overrideArtifactName' => [ 'shape' => 'WrapperBoolean', ], 'encryptionDisabled' => [ 'shape' => 'WrapperBoolean', ], 'artifactIdentifier' => [ 'shape' => 'String', ], 'bucketOwnerAccess' => [ 'shape' => 'BucketOwnerAccess', ], ], ], 'ProjectArtifactsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectArtifacts', ], 'max' => 12, 'min' => 0, ], 'ProjectBadge' => [ 'type' => 'structure', 'members' => [ 'badgeEnabled' => [ 'shape' => 'Boolean', ], 'badgeRequestUrl' => [ 'shape' => 'String', ], ], ], 'ProjectBuildBatchConfig' => [ 'type' => 'structure', 'members' => [ 'serviceRole' => [ 'shape' => 'NonEmptyString', ], 'combineArtifacts' => [ 'shape' => 'WrapperBoolean', ], 'restrictions' => [ 'shape' => 'BatchRestrictions', ], 'timeoutInMins' => [ 'shape' => 'WrapperInt', ], 'batchReportMode' => [ 'shape' => 'BatchReportModeType', ], ], ], 'ProjectCache' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'CacheType', ], 'location' => [ 'shape' => 'String', ], 'modes' => [ 'shape' => 'ProjectCacheModes', ], 'cacheNamespace' => [ 'shape' => 'String', ], ], ], 'ProjectCacheModes' => [ 'type' => 'list', 'member' => [ 'shape' => 'CacheMode', ], ], 'ProjectDescription' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'ProjectEnvironment' => [ 'type' => 'structure', 'required' => [ 'type', 'image', 'computeType', ], 'members' => [ 'type' => [ 'shape' => 'EnvironmentType', ], 'image' => [ 'shape' => 'NonEmptyString', ], 'computeType' => [ 'shape' => 'ComputeType', ], 'computeConfiguration' => [ 'shape' => 'ComputeConfiguration', ], 'fleet' => [ 'shape' => 'ProjectFleet', ], 'environmentVariables' => [ 'shape' => 'EnvironmentVariables', ], 'privilegedMode' => [ 'shape' => 'WrapperBoolean', ], 'certificate' => [ 'shape' => 'String', ], 'registryCredential' => [ 'shape' => 'RegistryCredential', ], 'imagePullCredentialsType' => [ 'shape' => 'ImagePullCredentialsType', ], 'dockerServer' => [ 'shape' => 'DockerServer', ], ], ], 'ProjectFileSystemLocation' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'FileSystemType', ], 'location' => [ 'shape' => 'String', ], 'mountPoint' => [ 'shape' => 'String', ], 'identifier' => [ 'shape' => 'String', ], 'mountOptions' => [ 'shape' => 'String', ], ], ], 'ProjectFileSystemLocations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectFileSystemLocation', ], ], 'ProjectFleet' => [ 'type' => 'structure', 'members' => [ 'fleetArn' => [ 'shape' => 'String', ], ], ], 'ProjectName' => [ 'type' => 'string', 'max' => 150, 'min' => 2, 'pattern' => '[A-Za-z0-9][A-Za-z0-9\\-_]{1,149}', ], 'ProjectNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 100, 'min' => 1, ], 'ProjectSecondarySourceVersions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectSourceVersion', ], 'max' => 12, 'min' => 0, ], 'ProjectSortByType' => [ 'type' => 'string', 'enum' => [ 'NAME', 'CREATED_TIME', 'LAST_MODIFIED_TIME', ], ], 'ProjectSource' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'SourceType', ], 'location' => [ 'shape' => 'String', ], 'gitCloneDepth' => [ 'shape' => 'GitCloneDepth', ], 'gitSubmodulesConfig' => [ 'shape' => 'GitSubmodulesConfig', ], 'buildspec' => [ 'shape' => 'String', ], 'auth' => [ 'shape' => 'SourceAuth', ], 'reportBuildStatus' => [ 'shape' => 'WrapperBoolean', ], 'buildStatusConfig' => [ 'shape' => 'BuildStatusConfig', ], 'insecureSsl' => [ 'shape' => 'WrapperBoolean', ], 'sourceIdentifier' => [ 'shape' => 'String', ], ], ], 'ProjectSourceVersion' => [ 'type' => 'structure', 'required' => [ 'sourceIdentifier', 'sourceVersion', ], 'members' => [ 'sourceIdentifier' => [ 'shape' => 'String', ], 'sourceVersion' => [ 'shape' => 'String', ], ], ], 'ProjectSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectSource', ], 'max' => 12, 'min' => 0, ], 'ProjectVisibilityType' => [ 'type' => 'string', 'enum' => [ 'PUBLIC_READ', 'PRIVATE', ], ], 'Projects' => [ 'type' => 'list', 'member' => [ 'shape' => 'Project', ], ], 'ProxyConfiguration' => [ 'type' => 'structure', 'members' => [ 'defaultBehavior' => [ 'shape' => 'FleetProxyRuleBehavior', ], 'orderedProxyRules' => [ 'shape' => 'FleetProxyRules', ], ], ], 'PutResourcePolicyInput' => [ 'type' => 'structure', 'required' => [ 'policy', 'resourceArn', ], 'members' => [ 'policy' => [ 'shape' => 'NonEmptyString', ], 'resourceArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'PutResourcePolicyOutput' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'NonEmptyString', ], ], ], 'RegistryCredential' => [ 'type' => 'structure', 'required' => [ 'credential', 'credentialProvider', ], 'members' => [ 'credential' => [ 'shape' => 'NonEmptyString', ], 'credentialProvider' => [ 'shape' => 'CredentialProviderType', ], ], ], 'Report' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'NonEmptyString', ], 'type' => [ 'shape' => 'ReportType', ], 'name' => [ 'shape' => 'String', ], 'reportGroupArn' => [ 'shape' => 'NonEmptyString', ], 'executionId' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'ReportStatusType', ], 'created' => [ 'shape' => 'Timestamp', ], 'expired' => [ 'shape' => 'Timestamp', ], 'exportConfig' => [ 'shape' => 'ReportExportConfig', ], 'truncated' => [ 'shape' => 'WrapperBoolean', ], 'testSummary' => [ 'shape' => 'TestReportSummary', ], 'codeCoverageSummary' => [ 'shape' => 'CodeCoverageReportSummary', ], ], ], 'ReportArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 100, 'min' => 1, ], 'ReportCodeCoverageSortByType' => [ 'type' => 'string', 'enum' => [ 'LINE_COVERAGE_PERCENTAGE', 'FILE_PATH', ], ], 'ReportExportConfig' => [ 'type' => 'structure', 'members' => [ 'exportConfigType' => [ 'shape' => 'ReportExportConfigType', ], 's3Destination' => [ 'shape' => 'S3ReportExportConfig', ], ], ], 'ReportExportConfigType' => [ 'type' => 'string', 'enum' => [ 'S3', 'NO_EXPORT', ], ], 'ReportFilter' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'ReportStatusType', ], ], ], 'ReportGroup' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'NonEmptyString', ], 'name' => [ 'shape' => 'ReportGroupName', ], 'type' => [ 'shape' => 'ReportType', ], 'exportConfig' => [ 'shape' => 'ReportExportConfig', ], 'created' => [ 'shape' => 'Timestamp', ], 'lastModified' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'TagList', ], 'status' => [ 'shape' => 'ReportGroupStatusType', ], ], ], 'ReportGroupArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 100, 'min' => 1, ], 'ReportGroupName' => [ 'type' => 'string', 'max' => 128, 'min' => 2, ], 'ReportGroupSortByType' => [ 'type' => 'string', 'enum' => [ 'NAME', 'CREATED_TIME', 'LAST_MODIFIED_TIME', ], ], 'ReportGroupStatusType' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'DELETING', ], ], 'ReportGroupTrendFieldType' => [ 'type' => 'string', 'enum' => [ 'PASS_RATE', 'DURATION', 'TOTAL', 'LINE_COVERAGE', 'LINES_COVERED', 'LINES_MISSED', 'BRANCH_COVERAGE', 'BRANCHES_COVERED', 'BRANCHES_MISSED', ], ], 'ReportGroupTrendRawDataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReportWithRawData', ], ], 'ReportGroupTrendStats' => [ 'type' => 'structure', 'members' => [ 'average' => [ 'shape' => 'String', ], 'max' => [ 'shape' => 'String', ], 'min' => [ 'shape' => 'String', ], ], ], 'ReportGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReportGroup', ], 'max' => 100, 'min' => 1, ], 'ReportPackagingType' => [ 'type' => 'string', 'enum' => [ 'ZIP', 'NONE', ], ], 'ReportStatusCounts' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'WrapperInt', ], ], 'ReportStatusType' => [ 'type' => 'string', 'enum' => [ 'GENERATING', 'SUCCEEDED', 'FAILED', 'INCOMPLETE', 'DELETING', ], ], 'ReportType' => [ 'type' => 'string', 'enum' => [ 'TEST', 'CODE_COVERAGE', ], ], 'ReportWithRawData' => [ 'type' => 'structure', 'members' => [ 'reportArn' => [ 'shape' => 'NonEmptyString', ], 'data' => [ 'shape' => 'String', ], ], ], 'Reports' => [ 'type' => 'list', 'member' => [ 'shape' => 'Report', ], 'max' => 100, 'min' => 1, ], 'ResolvedArtifact' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'ArtifactsType', ], 'location' => [ 'shape' => 'String', ], 'identifier' => [ 'shape' => 'String', ], ], ], 'ResolvedSecondaryArtifacts' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResolvedArtifact', ], ], 'ResourceAlreadyExistsException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [], 'exception' => true, ], 'RetryBuildBatchInput' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], 'idempotencyToken' => [ 'shape' => 'String', ], 'retryType' => [ 'shape' => 'RetryBuildBatchType', ], ], ], 'RetryBuildBatchOutput' => [ 'type' => 'structure', 'members' => [ 'buildBatch' => [ 'shape' => 'BuildBatch', ], ], ], 'RetryBuildBatchType' => [ 'type' => 'string', 'enum' => [ 'RETRY_ALL_BUILDS', 'RETRY_FAILED_BUILDS', ], ], 'RetryBuildInput' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], 'idempotencyToken' => [ 'shape' => 'String', ], ], ], 'RetryBuildOutput' => [ 'type' => 'structure', 'members' => [ 'build' => [ 'shape' => 'Build', ], ], ], 'S3LogsConfig' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'LogsConfigStatusType', ], 'location' => [ 'shape' => 'String', ], 'encryptionDisabled' => [ 'shape' => 'WrapperBoolean', ], 'bucketOwnerAccess' => [ 'shape' => 'BucketOwnerAccess', ], ], ], 'S3ReportExportConfig' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'NonEmptyString', ], 'bucketOwner' => [ 'shape' => 'String', ], 'path' => [ 'shape' => 'String', ], 'packaging' => [ 'shape' => 'ReportPackagingType', ], 'encryptionKey' => [ 'shape' => 'NonEmptyString', ], 'encryptionDisabled' => [ 'shape' => 'WrapperBoolean', ], ], ], 'SSMSession' => [ 'type' => 'structure', 'members' => [ 'sessionId' => [ 'shape' => 'String', ], 'tokenValue' => [ 'shape' => 'String', ], 'streamUrl' => [ 'shape' => 'String', ], ], ], 'Sandbox' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], 'arn' => [ 'shape' => 'NonEmptyString', ], 'projectName' => [ 'shape' => 'NonEmptyString', ], 'requestTime' => [ 'shape' => 'Timestamp', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'String', ], 'source' => [ 'shape' => 'ProjectSource', ], 'sourceVersion' => [ 'shape' => 'NonEmptyString', ], 'secondarySources' => [ 'shape' => 'ProjectSources', ], 'secondarySourceVersions' => [ 'shape' => 'ProjectSecondarySourceVersions', ], 'environment' => [ 'shape' => 'ProjectEnvironment', ], 'fileSystemLocations' => [ 'shape' => 'ProjectFileSystemLocations', ], 'timeoutInMinutes' => [ 'shape' => 'WrapperInt', ], 'queuedTimeoutInMinutes' => [ 'shape' => 'WrapperInt', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], 'logConfig' => [ 'shape' => 'LogsConfig', ], 'encryptionKey' => [ 'shape' => 'NonEmptyString', ], 'serviceRole' => [ 'shape' => 'NonEmptyString', ], 'currentSession' => [ 'shape' => 'SandboxSession', ], ], ], 'SandboxIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], ], 'SandboxSession' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], 'status' => [ 'shape' => 'String', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'currentPhase' => [ 'shape' => 'String', ], 'phases' => [ 'shape' => 'SandboxSessionPhases', ], 'resolvedSourceVersion' => [ 'shape' => 'NonEmptyString', ], 'logs' => [ 'shape' => 'LogsLocation', ], 'networkInterface' => [ 'shape' => 'NetworkInterface', ], ], ], 'SandboxSessionPhase' => [ 'type' => 'structure', 'members' => [ 'phaseType' => [ 'shape' => 'String', ], 'phaseStatus' => [ 'shape' => 'StatusType', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'durationInSeconds' => [ 'shape' => 'WrapperLong', ], 'contexts' => [ 'shape' => 'PhaseContexts', ], ], ], 'SandboxSessionPhases' => [ 'type' => 'list', 'member' => [ 'shape' => 'SandboxSessionPhase', ], ], 'Sandboxes' => [ 'type' => 'list', 'member' => [ 'shape' => 'Sandbox', ], ], 'ScalingConfigurationInput' => [ 'type' => 'structure', 'members' => [ 'scalingType' => [ 'shape' => 'FleetScalingType', ], 'targetTrackingScalingConfigs' => [ 'shape' => 'TargetTrackingScalingConfigurations', ], 'maxCapacity' => [ 'shape' => 'FleetCapacity', ], ], ], 'ScalingConfigurationOutput' => [ 'type' => 'structure', 'members' => [ 'scalingType' => [ 'shape' => 'FleetScalingType', ], 'targetTrackingScalingConfigs' => [ 'shape' => 'TargetTrackingScalingConfigurations', ], 'maxCapacity' => [ 'shape' => 'FleetCapacity', ], 'desiredCapacity' => [ 'shape' => 'FleetCapacity', ], ], ], 'ScopeConfiguration' => [ 'type' => 'structure', 'required' => [ 'name', 'scope', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'domain' => [ 'shape' => 'String', ], 'scope' => [ 'shape' => 'WebhookScopeType', ], ], ], 'SecurityGroupIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 5, ], 'SensitiveNonEmptyString' => [ 'type' => 'string', 'min' => 1, 'sensitive' => true, ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'ServerType' => [ 'type' => 'string', 'enum' => [ 'GITHUB', 'BITBUCKET', 'GITHUB_ENTERPRISE', 'GITLAB', 'GITLAB_SELF_MANAGED', ], ], 'SharedResourceSortByType' => [ 'type' => 'string', 'enum' => [ 'ARN', 'MODIFIED_TIME', ], ], 'SortOrderType' => [ 'type' => 'string', 'enum' => [ 'ASCENDING', 'DESCENDING', ], ], 'SourceAuth' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'SourceAuthType', ], 'resource' => [ 'shape' => 'String', ], ], ], 'SourceAuthType' => [ 'type' => 'string', 'enum' => [ 'OAUTH', 'CODECONNECTIONS', 'SECRETS_MANAGER', ], ], 'SourceCredentialsInfo' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'NonEmptyString', ], 'serverType' => [ 'shape' => 'ServerType', ], 'authType' => [ 'shape' => 'AuthType', ], 'resource' => [ 'shape' => 'String', ], ], ], 'SourceCredentialsInfos' => [ 'type' => 'list', 'member' => [ 'shape' => 'SourceCredentialsInfo', ], ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 'CODECOMMIT', 'CODEPIPELINE', 'GITHUB', 'GITLAB', 'GITLAB_SELF_MANAGED', 'S3', 'BITBUCKET', 'GITHUB_ENTERPRISE', 'NO_SOURCE', ], ], 'StartBuildBatchInput' => [ 'type' => 'structure', 'required' => [ 'projectName', ], 'members' => [ 'projectName' => [ 'shape' => 'NonEmptyString', ], 'secondarySourcesOverride' => [ 'shape' => 'ProjectSources', ], 'secondarySourcesVersionOverride' => [ 'shape' => 'ProjectSecondarySourceVersions', ], 'sourceVersion' => [ 'shape' => 'String', ], 'artifactsOverride' => [ 'shape' => 'ProjectArtifacts', ], 'secondaryArtifactsOverride' => [ 'shape' => 'ProjectArtifactsList', ], 'environmentVariablesOverride' => [ 'shape' => 'EnvironmentVariables', ], 'sourceTypeOverride' => [ 'shape' => 'SourceType', ], 'sourceLocationOverride' => [ 'shape' => 'String', ], 'sourceAuthOverride' => [ 'shape' => 'SourceAuth', ], 'gitCloneDepthOverride' => [ 'shape' => 'GitCloneDepth', ], 'gitSubmodulesConfigOverride' => [ 'shape' => 'GitSubmodulesConfig', ], 'buildspecOverride' => [ 'shape' => 'String', ], 'insecureSslOverride' => [ 'shape' => 'WrapperBoolean', ], 'reportBuildBatchStatusOverride' => [ 'shape' => 'WrapperBoolean', ], 'environmentTypeOverride' => [ 'shape' => 'EnvironmentType', ], 'imageOverride' => [ 'shape' => 'NonEmptyString', ], 'computeTypeOverride' => [ 'shape' => 'ComputeType', ], 'certificateOverride' => [ 'shape' => 'String', ], 'cacheOverride' => [ 'shape' => 'ProjectCache', ], 'serviceRoleOverride' => [ 'shape' => 'NonEmptyString', ], 'privilegedModeOverride' => [ 'shape' => 'WrapperBoolean', ], 'buildTimeoutInMinutesOverride' => [ 'shape' => 'BuildTimeOut', ], 'queuedTimeoutInMinutesOverride' => [ 'shape' => 'TimeOut', ], 'encryptionKeyOverride' => [ 'shape' => 'NonEmptyString', ], 'idempotencyToken' => [ 'shape' => 'String', ], 'logsConfigOverride' => [ 'shape' => 'LogsConfig', ], 'registryCredentialOverride' => [ 'shape' => 'RegistryCredential', ], 'imagePullCredentialsTypeOverride' => [ 'shape' => 'ImagePullCredentialsType', ], 'buildBatchConfigOverride' => [ 'shape' => 'ProjectBuildBatchConfig', ], 'debugSessionEnabled' => [ 'shape' => 'WrapperBoolean', ], ], ], 'StartBuildBatchOutput' => [ 'type' => 'structure', 'members' => [ 'buildBatch' => [ 'shape' => 'BuildBatch', ], ], ], 'StartBuildInput' => [ 'type' => 'structure', 'required' => [ 'projectName', ], 'members' => [ 'projectName' => [ 'shape' => 'NonEmptyString', ], 'secondarySourcesOverride' => [ 'shape' => 'ProjectSources', ], 'secondarySourcesVersionOverride' => [ 'shape' => 'ProjectSecondarySourceVersions', ], 'sourceVersion' => [ 'shape' => 'String', ], 'artifactsOverride' => [ 'shape' => 'ProjectArtifacts', ], 'secondaryArtifactsOverride' => [ 'shape' => 'ProjectArtifactsList', ], 'environmentVariablesOverride' => [ 'shape' => 'EnvironmentVariables', ], 'sourceTypeOverride' => [ 'shape' => 'SourceType', ], 'sourceLocationOverride' => [ 'shape' => 'String', ], 'sourceAuthOverride' => [ 'shape' => 'SourceAuth', ], 'gitCloneDepthOverride' => [ 'shape' => 'GitCloneDepth', ], 'gitSubmodulesConfigOverride' => [ 'shape' => 'GitSubmodulesConfig', ], 'buildspecOverride' => [ 'shape' => 'String', ], 'insecureSslOverride' => [ 'shape' => 'WrapperBoolean', ], 'reportBuildStatusOverride' => [ 'shape' => 'WrapperBoolean', ], 'buildStatusConfigOverride' => [ 'shape' => 'BuildStatusConfig', ], 'environmentTypeOverride' => [ 'shape' => 'EnvironmentType', ], 'imageOverride' => [ 'shape' => 'NonEmptyString', ], 'computeTypeOverride' => [ 'shape' => 'ComputeType', ], 'certificateOverride' => [ 'shape' => 'String', ], 'cacheOverride' => [ 'shape' => 'ProjectCache', ], 'serviceRoleOverride' => [ 'shape' => 'NonEmptyString', ], 'privilegedModeOverride' => [ 'shape' => 'WrapperBoolean', ], 'timeoutInMinutesOverride' => [ 'shape' => 'BuildTimeOut', ], 'queuedTimeoutInMinutesOverride' => [ 'shape' => 'TimeOut', ], 'encryptionKeyOverride' => [ 'shape' => 'NonEmptyString', ], 'idempotencyToken' => [ 'shape' => 'String', ], 'logsConfigOverride' => [ 'shape' => 'LogsConfig', ], 'registryCredentialOverride' => [ 'shape' => 'RegistryCredential', ], 'imagePullCredentialsTypeOverride' => [ 'shape' => 'ImagePullCredentialsType', ], 'debugSessionEnabled' => [ 'shape' => 'WrapperBoolean', ], 'fleetOverride' => [ 'shape' => 'ProjectFleet', ], 'autoRetryLimitOverride' => [ 'shape' => 'WrapperInt', ], ], ], 'StartBuildOutput' => [ 'type' => 'structure', 'members' => [ 'build' => [ 'shape' => 'Build', ], ], ], 'StartCommandExecutionInput' => [ 'type' => 'structure', 'required' => [ 'sandboxId', 'command', ], 'members' => [ 'sandboxId' => [ 'shape' => 'NonEmptyString', ], 'command' => [ 'shape' => 'SensitiveNonEmptyString', ], 'type' => [ 'shape' => 'CommandType', ], ], ], 'StartCommandExecutionOutput' => [ 'type' => 'structure', 'members' => [ 'commandExecution' => [ 'shape' => 'CommandExecution', ], ], ], 'StartSandboxConnectionInput' => [ 'type' => 'structure', 'required' => [ 'sandboxId', ], 'members' => [ 'sandboxId' => [ 'shape' => 'NonEmptyString', ], ], ], 'StartSandboxConnectionOutput' => [ 'type' => 'structure', 'members' => [ 'ssmSession' => [ 'shape' => 'SSMSession', ], ], ], 'StartSandboxInput' => [ 'type' => 'structure', 'members' => [ 'projectName' => [ 'shape' => 'NonEmptyString', ], 'idempotencyToken' => [ 'shape' => 'SensitiveString', ], ], ], 'StartSandboxOutput' => [ 'type' => 'structure', 'members' => [ 'sandbox' => [ 'shape' => 'Sandbox', ], ], ], 'StatusType' => [ 'type' => 'string', 'enum' => [ 'SUCCEEDED', 'FAILED', 'FAULT', 'TIMED_OUT', 'IN_PROGRESS', 'STOPPED', ], ], 'StopBuildBatchInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], ], ], 'StopBuildBatchOutput' => [ 'type' => 'structure', 'members' => [ 'buildBatch' => [ 'shape' => 'BuildBatch', ], ], ], 'StopBuildInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], ], ], 'StopBuildOutput' => [ 'type' => 'structure', 'members' => [ 'build' => [ 'shape' => 'Build', ], ], ], 'StopSandboxInput' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'NonEmptyString', ], ], ], 'StopSandboxOutput' => [ 'type' => 'structure', 'members' => [ 'sandbox' => [ 'shape' => 'Sandbox', ], ], ], 'String' => [ 'type' => 'string', ], 'Subnets' => [ 'type' => 'list', 'member' => [ 'shape' => 'NonEmptyString', ], 'max' => 16, ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'KeyInput', ], 'value' => [ 'shape' => 'ValueInput', ], ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 50, 'min' => 0, ], 'TargetTrackingScalingConfiguration' => [ 'type' => 'structure', 'members' => [ 'metricType' => [ 'shape' => 'FleetScalingMetricType', ], 'targetValue' => [ 'shape' => 'WrapperDouble', ], ], ], 'TargetTrackingScalingConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'TargetTrackingScalingConfiguration', ], ], 'TestCase' => [ 'type' => 'structure', 'members' => [ 'reportArn' => [ 'shape' => 'NonEmptyString', ], 'testRawDataPath' => [ 'shape' => 'String', ], 'prefix' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'String', ], 'durationInNanoSeconds' => [ 'shape' => 'WrapperLong', ], 'message' => [ 'shape' => 'String', ], 'expired' => [ 'shape' => 'Timestamp', ], 'testSuiteName' => [ 'shape' => 'String', ], ], ], 'TestCaseFilter' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'String', ], 'keyword' => [ 'shape' => 'String', ], ], ], 'TestCases' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestCase', ], ], 'TestReportSummary' => [ 'type' => 'structure', 'required' => [ 'total', 'statusCounts', 'durationInNanoSeconds', ], 'members' => [ 'total' => [ 'shape' => 'WrapperInt', ], 'statusCounts' => [ 'shape' => 'ReportStatusCounts', ], 'durationInNanoSeconds' => [ 'shape' => 'WrapperLong', ], ], ], 'TimeOut' => [ 'type' => 'integer', 'max' => 480, 'min' => 5, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UpdateFleetInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'NonEmptyString', ], 'baseCapacity' => [ 'shape' => 'FleetCapacity', ], 'environmentType' => [ 'shape' => 'EnvironmentType', ], 'computeType' => [ 'shape' => 'ComputeType', ], 'computeConfiguration' => [ 'shape' => 'ComputeConfiguration', ], 'scalingConfiguration' => [ 'shape' => 'ScalingConfigurationInput', ], 'overflowBehavior' => [ 'shape' => 'FleetOverflowBehavior', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], 'proxyConfiguration' => [ 'shape' => 'ProxyConfiguration', ], 'imageId' => [ 'shape' => 'NonEmptyString', ], 'fleetServiceRole' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'UpdateFleetOutput' => [ 'type' => 'structure', 'members' => [ 'fleet' => [ 'shape' => 'Fleet', ], ], ], 'UpdateProjectInput' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'NonEmptyString', ], 'description' => [ 'shape' => 'ProjectDescription', ], 'source' => [ 'shape' => 'ProjectSource', ], 'secondarySources' => [ 'shape' => 'ProjectSources', ], 'sourceVersion' => [ 'shape' => 'String', ], 'secondarySourceVersions' => [ 'shape' => 'ProjectSecondarySourceVersions', ], 'artifacts' => [ 'shape' => 'ProjectArtifacts', ], 'secondaryArtifacts' => [ 'shape' => 'ProjectArtifactsList', ], 'cache' => [ 'shape' => 'ProjectCache', ], 'environment' => [ 'shape' => 'ProjectEnvironment', ], 'serviceRole' => [ 'shape' => 'NonEmptyString', ], 'timeoutInMinutes' => [ 'shape' => 'BuildTimeOut', ], 'queuedTimeoutInMinutes' => [ 'shape' => 'TimeOut', ], 'encryptionKey' => [ 'shape' => 'NonEmptyString', ], 'tags' => [ 'shape' => 'TagList', ], 'vpcConfig' => [ 'shape' => 'VpcConfig', ], 'badgeEnabled' => [ 'shape' => 'WrapperBoolean', ], 'logsConfig' => [ 'shape' => 'LogsConfig', ], 'fileSystemLocations' => [ 'shape' => 'ProjectFileSystemLocations', ], 'buildBatchConfig' => [ 'shape' => 'ProjectBuildBatchConfig', ], 'concurrentBuildLimit' => [ 'shape' => 'WrapperInt', ], 'autoRetryLimit' => [ 'shape' => 'WrapperInt', ], ], ], 'UpdateProjectOutput' => [ 'type' => 'structure', 'members' => [ 'project' => [ 'shape' => 'Project', ], ], ], 'UpdateProjectVisibilityInput' => [ 'type' => 'structure', 'required' => [ 'projectArn', 'projectVisibility', ], 'members' => [ 'projectArn' => [ 'shape' => 'NonEmptyString', ], 'projectVisibility' => [ 'shape' => 'ProjectVisibilityType', ], 'resourceAccessRole' => [ 'shape' => 'NonEmptyString', ], ], ], 'UpdateProjectVisibilityOutput' => [ 'type' => 'structure', 'members' => [ 'projectArn' => [ 'shape' => 'NonEmptyString', ], 'publicProjectAlias' => [ 'shape' => 'NonEmptyString', ], 'projectVisibility' => [ 'shape' => 'ProjectVisibilityType', ], ], ], 'UpdateReportGroupInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'NonEmptyString', ], 'exportConfig' => [ 'shape' => 'ReportExportConfig', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'UpdateReportGroupOutput' => [ 'type' => 'structure', 'members' => [ 'reportGroup' => [ 'shape' => 'ReportGroup', ], ], ], 'UpdateWebhookInput' => [ 'type' => 'structure', 'required' => [ 'projectName', ], 'members' => [ 'projectName' => [ 'shape' => 'ProjectName', ], 'branchFilter' => [ 'shape' => 'String', ], 'rotateSecret' => [ 'shape' => 'Boolean', ], 'filterGroups' => [ 'shape' => 'FilterGroups', ], 'buildType' => [ 'shape' => 'WebhookBuildType', ], ], ], 'UpdateWebhookOutput' => [ 'type' => 'structure', 'members' => [ 'webhook' => [ 'shape' => 'Webhook', ], ], ], 'ValueInput' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '^([\\p{L}\\p{Z}\\p{N}_.:/=@+\\-]*)$', ], 'VpcConfig' => [ 'type' => 'structure', 'members' => [ 'vpcId' => [ 'shape' => 'NonEmptyString', ], 'subnets' => [ 'shape' => 'Subnets', ], 'securityGroupIds' => [ 'shape' => 'SecurityGroupIds', ], ], ], 'Webhook' => [ 'type' => 'structure', 'members' => [ 'url' => [ 'shape' => 'NonEmptyString', ], 'payloadUrl' => [ 'shape' => 'NonEmptyString', ], 'secret' => [ 'shape' => 'NonEmptyString', ], 'branchFilter' => [ 'shape' => 'String', ], 'filterGroups' => [ 'shape' => 'FilterGroups', ], 'buildType' => [ 'shape' => 'WebhookBuildType', ], 'manualCreation' => [ 'shape' => 'WrapperBoolean', ], 'lastModifiedSecret' => [ 'shape' => 'Timestamp', ], 'scopeConfiguration' => [ 'shape' => 'ScopeConfiguration', ], 'status' => [ 'shape' => 'WebhookStatus', ], 'statusMessage' => [ 'shape' => 'String', ], ], ], 'WebhookBuildType' => [ 'type' => 'string', 'enum' => [ 'BUILD', 'BUILD_BATCH', 'RUNNER_BUILDKITE_BUILD', ], ], 'WebhookFilter' => [ 'type' => 'structure', 'required' => [ 'type', 'pattern', ], 'members' => [ 'type' => [ 'shape' => 'WebhookFilterType', ], 'pattern' => [ 'shape' => 'String', ], 'excludeMatchedPattern' => [ 'shape' => 'WrapperBoolean', ], ], ], 'WebhookFilterType' => [ 'type' => 'string', 'enum' => [ 'EVENT', 'BASE_REF', 'HEAD_REF', 'ACTOR_ACCOUNT_ID', 'FILE_PATH', 'COMMIT_MESSAGE', 'WORKFLOW_NAME', 'TAG_NAME', 'RELEASE_NAME', 'REPOSITORY_NAME', 'ORGANIZATION_NAME', ], ], 'WebhookScopeType' => [ 'type' => 'string', 'enum' => [ 'GITHUB_ORGANIZATION', 'GITHUB_GLOBAL', 'GITLAB_GROUP', ], ], 'WebhookStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'CREATE_FAILED', 'ACTIVE', 'DELETING', ], ], 'WrapperBoolean' => [ 'type' => 'boolean', ], 'WrapperDouble' => [ 'type' => 'double', ], 'WrapperInt' => [ 'type' => 'integer', ], 'WrapperLong' => [ 'type' => 'long', ], ],];
