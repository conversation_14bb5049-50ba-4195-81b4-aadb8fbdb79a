<?php
// This file was auto-generated from sdk-root/src/data/appconfig/2019-10-09/waiters-2.json
return [ 'version' => 2, 'waiters' => [ 'EnvironmentReadyForDeployment' => [ 'operation' => 'GetEnvironment', 'delay' => 30, 'maxAttempts' => 999, 'acceptors' => [ [ 'state' => 'success', 'matcher' => 'path', 'argument' => 'State', 'expected' => 'ReadyForDeployment', ], [ 'state' => 'failure', 'matcher' => 'path', 'argument' => 'State', 'expected' => 'RolledBack', ], [ 'state' => 'failure', 'matcher' => 'path', 'argument' => 'State', 'expected' => 'Reverted', ], ], ], 'DeploymentComplete' => [ 'operation' => 'GetDeployment', 'delay' => 30, 'maxAttempts' => 999, 'acceptors' => [ [ 'state' => 'success', 'matcher' => 'path', 'argument' => 'State', 'expected' => 'COMPLETE', ], [ 'state' => 'failure', 'matcher' => 'path', 'argument' => 'State', 'expected' => 'ROLLED_BACK', ], [ 'state' => 'failure', 'matcher' => 'path', 'argument' => 'State', 'expected' => 'REVERTED', ], ], ], ],];
