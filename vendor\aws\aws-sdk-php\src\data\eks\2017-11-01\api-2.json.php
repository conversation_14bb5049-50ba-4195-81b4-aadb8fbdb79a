<?php
// This file was auto-generated from sdk-root/src/data/eks/2017-11-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2017-11-01', 'endpointPrefix' => 'eks', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceAbbreviation' => 'Amazon EKS', 'serviceFullName' => 'Amazon Elastic Kubernetes Service', 'serviceId' => 'EKS', 'signatureVersion' => 'v4', 'signingName' => 'eks', 'uid' => 'eks-2017-11-01', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AssociateAccessPolicy' => [ 'name' => 'AssociateAccessPolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/access-entries/{principalArn}/access-policies', ], 'input' => [ 'shape' => 'AssociateAccessPolicyRequest', ], 'output' => [ 'shape' => 'AssociateAccessPolicyResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'AssociateEncryptionConfig' => [ 'name' => 'AssociateEncryptionConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/encryption-config/associate', ], 'input' => [ 'shape' => 'AssociateEncryptionConfigRequest', ], 'output' => [ 'shape' => 'AssociateEncryptionConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'AssociateIdentityProviderConfig' => [ 'name' => 'AssociateIdentityProviderConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/identity-provider-configs/associate', ], 'input' => [ 'shape' => 'AssociateIdentityProviderConfigRequest', ], 'output' => [ 'shape' => 'AssociateIdentityProviderConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'CreateAccessEntry' => [ 'name' => 'CreateAccessEntry', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/access-entries', ], 'input' => [ 'shape' => 'CreateAccessEntryRequest', ], 'output' => [ 'shape' => 'CreateAccessEntryResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'CreateAddon' => [ 'name' => 'CreateAddon', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/addons', ], 'input' => [ 'shape' => 'CreateAddonRequest', ], 'output' => [ 'shape' => 'CreateAddonResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], ], ], 'CreateCluster' => [ 'name' => 'CreateCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters', ], 'input' => [ 'shape' => 'CreateClusterRequest', ], 'output' => [ 'shape' => 'CreateClusterResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'UnsupportedAvailabilityZoneException', ], ], ], 'CreateEksAnywhereSubscription' => [ 'name' => 'CreateEksAnywhereSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/eks-anywhere-subscriptions', ], 'input' => [ 'shape' => 'CreateEksAnywhereSubscriptionRequest', ], 'output' => [ 'shape' => 'CreateEksAnywhereSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreateFargateProfile' => [ 'name' => 'CreateFargateProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/fargate-profiles', ], 'input' => [ 'shape' => 'CreateFargateProfileRequest', ], 'output' => [ 'shape' => 'CreateFargateProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'UnsupportedAvailabilityZoneException', ], ], ], 'CreateNodegroup' => [ 'name' => 'CreateNodegroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/node-groups', ], 'input' => [ 'shape' => 'CreateNodegroupRequest', ], 'output' => [ 'shape' => 'CreateNodegroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'CreatePodIdentityAssociation' => [ 'name' => 'CreatePodIdentityAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/pod-identity-associations', ], 'input' => [ 'shape' => 'CreatePodIdentityAssociationRequest', ], 'output' => [ 'shape' => 'CreatePodIdentityAssociationResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'ResourceInUseException', ], ], ], 'DeleteAccessEntry' => [ 'name' => 'DeleteAccessEntry', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/clusters/{name}/access-entries/{principalArn}', ], 'input' => [ 'shape' => 'DeleteAccessEntryRequest', ], 'output' => [ 'shape' => 'DeleteAccessEntryResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DeleteAddon' => [ 'name' => 'DeleteAddon', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/clusters/{name}/addons/{addonName}', ], 'input' => [ 'shape' => 'DeleteAddonRequest', ], 'output' => [ 'shape' => 'DeleteAddonResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], ], ], 'DeleteCluster' => [ 'name' => 'DeleteCluster', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/clusters/{name}', ], 'input' => [ 'shape' => 'DeleteClusterRequest', ], 'output' => [ 'shape' => 'DeleteClusterResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeleteEksAnywhereSubscription' => [ 'name' => 'DeleteEksAnywhereSubscription', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/eks-anywhere-subscriptions/{id}', ], 'input' => [ 'shape' => 'DeleteEksAnywhereSubscriptionRequest', ], 'output' => [ 'shape' => 'DeleteEksAnywhereSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ServerException', ], ], ], 'DeleteFargateProfile' => [ 'name' => 'DeleteFargateProfile', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/clusters/{name}/fargate-profiles/{fargateProfileName}', ], 'input' => [ 'shape' => 'DeleteFargateProfileRequest', ], 'output' => [ 'shape' => 'DeleteFargateProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DeleteNodegroup' => [ 'name' => 'DeleteNodegroup', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/clusters/{name}/node-groups/{nodegroupName}', ], 'input' => [ 'shape' => 'DeleteNodegroupRequest', ], 'output' => [ 'shape' => 'DeleteNodegroupResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DeletePodIdentityAssociation' => [ 'name' => 'DeletePodIdentityAssociation', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/clusters/{name}/pod-identity-associations/{associationId}', ], 'input' => [ 'shape' => 'DeletePodIdentityAssociationRequest', ], 'output' => [ 'shape' => 'DeletePodIdentityAssociationResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'DeregisterCluster' => [ 'name' => 'DeregisterCluster', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/cluster-registrations/{name}', ], 'input' => [ 'shape' => 'DeregisterClusterRequest', ], 'output' => [ 'shape' => 'DeregisterClusterResponse', ], 'errors' => [ [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'DescribeAccessEntry' => [ 'name' => 'DescribeAccessEntry', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/access-entries/{principalArn}', ], 'input' => [ 'shape' => 'DescribeAccessEntryRequest', ], 'output' => [ 'shape' => 'DescribeAccessEntryResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DescribeAddon' => [ 'name' => 'DescribeAddon', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/addons/{addonName}', ], 'input' => [ 'shape' => 'DescribeAddonRequest', ], 'output' => [ 'shape' => 'DescribeAddonResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], ], ], 'DescribeAddonConfiguration' => [ 'name' => 'DescribeAddonConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/addons/configuration-schemas', ], 'input' => [ 'shape' => 'DescribeAddonConfigurationRequest', ], 'output' => [ 'shape' => 'DescribeAddonConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'DescribeAddonVersions' => [ 'name' => 'DescribeAddonVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/addons/supported-versions', ], 'input' => [ 'shape' => 'DescribeAddonVersionsRequest', ], 'output' => [ 'shape' => 'DescribeAddonVersionsResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'DescribeCluster' => [ 'name' => 'DescribeCluster', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}', ], 'input' => [ 'shape' => 'DescribeClusterRequest', ], 'output' => [ 'shape' => 'DescribeClusterResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeClusterVersions' => [ 'name' => 'DescribeClusterVersions', 'http' => [ 'method' => 'GET', 'requestUri' => '/cluster-versions', ], 'input' => [ 'shape' => 'DescribeClusterVersionsRequest', ], 'output' => [ 'shape' => 'DescribeClusterVersionsResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DescribeEksAnywhereSubscription' => [ 'name' => 'DescribeEksAnywhereSubscription', 'http' => [ 'method' => 'GET', 'requestUri' => '/eks-anywhere-subscriptions/{id}', ], 'input' => [ 'shape' => 'DescribeEksAnywhereSubscriptionRequest', ], 'output' => [ 'shape' => 'DescribeEksAnywhereSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeFargateProfile' => [ 'name' => 'DescribeFargateProfile', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/fargate-profiles/{fargateProfileName}', ], 'input' => [ 'shape' => 'DescribeFargateProfileRequest', ], 'output' => [ 'shape' => 'DescribeFargateProfileResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DescribeIdentityProviderConfig' => [ 'name' => 'DescribeIdentityProviderConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/identity-provider-configs/describe', ], 'input' => [ 'shape' => 'DescribeIdentityProviderConfigRequest', ], 'output' => [ 'shape' => 'DescribeIdentityProviderConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribeInsight' => [ 'name' => 'DescribeInsight', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/insights/{id}', ], 'input' => [ 'shape' => 'DescribeInsightRequest', ], 'output' => [ 'shape' => 'DescribeInsightResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'DescribeNodegroup' => [ 'name' => 'DescribeNodegroup', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/node-groups/{nodegroupName}', ], 'input' => [ 'shape' => 'DescribeNodegroupRequest', ], 'output' => [ 'shape' => 'DescribeNodegroupResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'DescribePodIdentityAssociation' => [ 'name' => 'DescribePodIdentityAssociation', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/pod-identity-associations/{associationId}', ], 'input' => [ 'shape' => 'DescribePodIdentityAssociationRequest', ], 'output' => [ 'shape' => 'DescribePodIdentityAssociationResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'DescribeUpdate' => [ 'name' => 'DescribeUpdate', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/updates/{updateId}', ], 'input' => [ 'shape' => 'DescribeUpdateRequest', ], 'output' => [ 'shape' => 'DescribeUpdateResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'DisassociateAccessPolicy' => [ 'name' => 'DisassociateAccessPolicy', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/clusters/{name}/access-entries/{principalArn}/access-policies/{policyArn}', ], 'input' => [ 'shape' => 'DisassociateAccessPolicyRequest', ], 'output' => [ 'shape' => 'DisassociateAccessPolicyResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'DisassociateIdentityProviderConfig' => [ 'name' => 'DisassociateIdentityProviderConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/identity-provider-configs/disassociate', ], 'input' => [ 'shape' => 'DisassociateIdentityProviderConfigRequest', ], 'output' => [ 'shape' => 'DisassociateIdentityProviderConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListAccessEntries' => [ 'name' => 'ListAccessEntries', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/access-entries', ], 'input' => [ 'shape' => 'ListAccessEntriesRequest', ], 'output' => [ 'shape' => 'ListAccessEntriesResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'ListAccessPolicies' => [ 'name' => 'ListAccessPolicies', 'http' => [ 'method' => 'GET', 'requestUri' => '/access-policies', ], 'input' => [ 'shape' => 'ListAccessPoliciesRequest', ], 'output' => [ 'shape' => 'ListAccessPoliciesResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], ], ], 'ListAddons' => [ 'name' => 'ListAddons', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/addons', ], 'input' => [ 'shape' => 'ListAddonsRequest', ], 'output' => [ 'shape' => 'ListAddonsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServerException', ], ], ], 'ListAssociatedAccessPolicies' => [ 'name' => 'ListAssociatedAccessPolicies', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/access-entries/{principalArn}/access-policies', ], 'input' => [ 'shape' => 'ListAssociatedAccessPoliciesRequest', ], 'output' => [ 'shape' => 'ListAssociatedAccessPoliciesResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'ListClusters' => [ 'name' => 'ListClusters', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters', ], 'input' => [ 'shape' => 'ListClustersRequest', ], 'output' => [ 'shape' => 'ListClustersResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListEksAnywhereSubscriptions' => [ 'name' => 'ListEksAnywhereSubscriptions', 'http' => [ 'method' => 'GET', 'requestUri' => '/eks-anywhere-subscriptions', ], 'input' => [ 'shape' => 'ListEksAnywhereSubscriptionsRequest', ], 'output' => [ 'shape' => 'ListEksAnywhereSubscriptionsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], ], ], 'ListFargateProfiles' => [ 'name' => 'ListFargateProfiles', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/fargate-profiles', ], 'input' => [ 'shape' => 'ListFargateProfilesRequest', ], 'output' => [ 'shape' => 'ListFargateProfilesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], ], ], 'ListIdentityProviderConfigs' => [ 'name' => 'ListIdentityProviderConfigs', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/identity-provider-configs', ], 'input' => [ 'shape' => 'ListIdentityProviderConfigsRequest', ], 'output' => [ 'shape' => 'ListIdentityProviderConfigsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListInsights' => [ 'name' => 'ListInsights', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/insights', ], 'input' => [ 'shape' => 'ListInsightsRequest', ], 'output' => [ 'shape' => 'ListInsightsResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'ListNodegroups' => [ 'name' => 'ListNodegroups', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/node-groups', ], 'input' => [ 'shape' => 'ListNodegroupsRequest', ], 'output' => [ 'shape' => 'ListNodegroupsResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListPodIdentityAssociations' => [ 'name' => 'ListPodIdentityAssociations', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/pod-identity-associations', ], 'input' => [ 'shape' => 'ListPodIdentityAssociationsRequest', ], 'output' => [ 'shape' => 'ListPodIdentityAssociationsResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], ], ], 'ListUpdates' => [ 'name' => 'ListUpdates', 'http' => [ 'method' => 'GET', 'requestUri' => '/clusters/{name}/updates', ], 'input' => [ 'shape' => 'ListUpdatesRequest', ], 'output' => [ 'shape' => 'ListUpdatesResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'RegisterCluster' => [ 'name' => 'RegisterCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/cluster-registrations', ], 'input' => [ 'shape' => 'RegisterClusterRequest', ], 'output' => [ 'shape' => 'RegisterClusterResponse', ], 'errors' => [ [ 'shape' => 'ResourceLimitExceededException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourcePropagationDelayException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'BadRequestException', ], [ 'shape' => 'NotFoundException', ], ], ], 'UpdateAccessEntry' => [ 'name' => 'UpdateAccessEntry', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/access-entries/{principalArn}', ], 'input' => [ 'shape' => 'UpdateAccessEntryRequest', ], 'output' => [ 'shape' => 'UpdateAccessEntryResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'UpdateAddon' => [ 'name' => 'UpdateAddon', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/addons/{addonName}/update', ], 'input' => [ 'shape' => 'UpdateAddonRequest', ], 'output' => [ 'shape' => 'UpdateAddonResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], ], ], 'UpdateClusterConfig' => [ 'name' => 'UpdateClusterConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/update-config', ], 'input' => [ 'shape' => 'UpdateClusterConfigRequest', ], 'output' => [ 'shape' => 'UpdateClusterConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateClusterVersion' => [ 'name' => 'UpdateClusterVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/updates', ], 'input' => [ 'shape' => 'UpdateClusterVersionRequest', ], 'output' => [ 'shape' => 'UpdateClusterVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InvalidStateException', ], ], ], 'UpdateEksAnywhereSubscription' => [ 'name' => 'UpdateEksAnywhereSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/eks-anywhere-subscriptions/{id}', ], 'input' => [ 'shape' => 'UpdateEksAnywhereSubscriptionRequest', ], 'output' => [ 'shape' => 'UpdateEksAnywhereSubscriptionResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'UpdateNodegroupConfig' => [ 'name' => 'UpdateNodegroupConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/node-groups/{nodegroupName}/update-config', ], 'input' => [ 'shape' => 'UpdateNodegroupConfigRequest', ], 'output' => [ 'shape' => 'UpdateNodegroupConfigResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'UpdateNodegroupVersion' => [ 'name' => 'UpdateNodegroupVersion', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/node-groups/{nodegroupName}/update-version', ], 'input' => [ 'shape' => 'UpdateNodegroupVersionRequest', ], 'output' => [ 'shape' => 'UpdateNodegroupVersionResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ClientException', ], [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceInUseException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], ], ], 'UpdatePodIdentityAssociation' => [ 'name' => 'UpdatePodIdentityAssociation', 'http' => [ 'method' => 'POST', 'requestUri' => '/clusters/{name}/pod-identity-associations/{associationId}', ], 'input' => [ 'shape' => 'UpdatePodIdentityAssociationRequest', ], 'output' => [ 'shape' => 'UpdatePodIdentityAssociationResponse', ], 'errors' => [ [ 'shape' => 'ServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'InvalidParameterException', ], ], ], ], 'shapes' => [ 'AMITypes' => [ 'type' => 'string', 'enum' => [ 'AL2_x86_64', 'AL2_x86_64_GPU', 'AL2_ARM_64', 'CUSTOM', 'BOTTLEROCKET_ARM_64', 'BOTTLEROCKET_x86_64', 'BOTTLEROCKET_ARM_64_FIPS', 'BOTTLEROCKET_x86_64_FIPS', 'BOTTLEROCKET_ARM_64_NVIDIA', 'BOTTLEROCKET_x86_64_NVIDIA', 'WINDOWS_CORE_2019_x86_64', 'WINDOWS_FULL_2019_x86_64', 'WINDOWS_CORE_2022_x86_64', 'WINDOWS_FULL_2022_x86_64', 'AL2023_x86_64_STANDARD', 'AL2023_ARM_64_STANDARD', 'AL2023_x86_64_NEURON', 'AL2023_x86_64_NVIDIA', 'AL2023_ARM_64_NVIDIA', ], ], 'AccessConfigResponse' => [ 'type' => 'structure', 'members' => [ 'bootstrapClusterCreatorAdminPermissions' => [ 'shape' => 'BoxedBoolean', ], 'authenticationMode' => [ 'shape' => 'AuthenticationMode', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, ], 'exception' => true, ], 'AccessEntry' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'principalArn' => [ 'shape' => 'String', ], 'kubernetesGroups' => [ 'shape' => 'StringList', ], 'accessEntryArn' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'modifiedAt' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'TagMap', ], 'username' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], ], ], 'AccessPoliciesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccessPolicy', ], ], 'AccessPolicy' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'arn' => [ 'shape' => 'String', ], ], ], 'AccessScope' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'AccessScopeType', ], 'namespaces' => [ 'shape' => 'StringList', ], ], ], 'AccessScopeType' => [ 'type' => 'string', 'enum' => [ 'cluster', 'namespace', ], ], 'AdditionalInfoMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'Addon' => [ 'type' => 'structure', 'members' => [ 'addonName' => [ 'shape' => 'String', ], 'clusterName' => [ 'shape' => 'ClusterName', ], 'status' => [ 'shape' => 'AddonStatus', ], 'addonVersion' => [ 'shape' => 'String', ], 'health' => [ 'shape' => 'AddonHealth', ], 'addonArn' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'modifiedAt' => [ 'shape' => 'Timestamp', ], 'serviceAccountRoleArn' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], 'publisher' => [ 'shape' => 'String', ], 'owner' => [ 'shape' => 'String', ], 'marketplaceInformation' => [ 'shape' => 'MarketplaceInformation', ], 'configurationValues' => [ 'shape' => 'String', ], 'podIdentityAssociations' => [ 'shape' => 'StringList', ], ], ], 'AddonCompatibilityDetail' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'compatibleVersions' => [ 'shape' => 'StringList', ], ], ], 'AddonCompatibilityDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddonCompatibilityDetail', ], ], 'AddonHealth' => [ 'type' => 'structure', 'members' => [ 'issues' => [ 'shape' => 'AddonIssueList', ], ], ], 'AddonInfo' => [ 'type' => 'structure', 'members' => [ 'addonName' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], 'addonVersions' => [ 'shape' => 'AddonVersionInfoList', ], 'publisher' => [ 'shape' => 'String', ], 'owner' => [ 'shape' => 'String', ], 'marketplaceInformation' => [ 'shape' => 'MarketplaceInformation', ], ], ], 'AddonIssue' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'AddonIssueCode', ], 'message' => [ 'shape' => 'String', ], 'resourceIds' => [ 'shape' => 'StringList', ], ], ], 'AddonIssueCode' => [ 'type' => 'string', 'enum' => [ 'AccessDenied', 'InternalFailure', 'ClusterUnreachable', 'InsufficientNumberOfReplicas', 'ConfigurationConflict', 'AdmissionRequestDenied', 'UnsupportedAddonModification', 'K8sResourceNotFound', 'AddonSubscriptionNeeded', 'AddonPermissionFailure', ], ], 'AddonIssueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddonIssue', ], ], 'AddonPodIdentityAssociations' => [ 'type' => 'structure', 'required' => [ 'serviceAccount', 'roleArn', ], 'members' => [ 'serviceAccount' => [ 'shape' => 'String', ], 'roleArn' => [ 'shape' => 'String', ], ], ], 'AddonPodIdentityAssociationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddonPodIdentityAssociations', ], ], 'AddonPodIdentityConfiguration' => [ 'type' => 'structure', 'members' => [ 'serviceAccount' => [ 'shape' => 'String', ], 'recommendedManagedPolicies' => [ 'shape' => 'StringList', ], ], ], 'AddonPodIdentityConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddonPodIdentityConfiguration', ], ], 'AddonStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'CREATE_FAILED', 'UPDATING', 'DELETING', 'DELETE_FAILED', 'DEGRADED', 'UPDATE_FAILED', ], ], 'AddonVersionInfo' => [ 'type' => 'structure', 'members' => [ 'addonVersion' => [ 'shape' => 'String', ], 'architecture' => [ 'shape' => 'StringList', ], 'computeTypes' => [ 'shape' => 'StringList', ], 'compatibilities' => [ 'shape' => 'Compatibilities', ], 'requiresConfiguration' => [ 'shape' => 'Boolean', ], 'requiresIamPermissions' => [ 'shape' => 'Boolean', ], ], ], 'AddonVersionInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddonVersionInfo', ], ], 'Addons' => [ 'type' => 'list', 'member' => [ 'shape' => 'AddonInfo', ], ], 'AssociateAccessPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'principalArn', 'policyArn', 'accessScope', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'principalArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'principalArn', ], 'policyArn' => [ 'shape' => 'String', ], 'accessScope' => [ 'shape' => 'AccessScope', ], ], ], 'AssociateAccessPolicyResponse' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'principalArn' => [ 'shape' => 'String', ], 'associatedAccessPolicy' => [ 'shape' => 'AssociatedAccessPolicy', ], ], ], 'AssociateEncryptionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'encryptionConfig', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'encryptionConfig' => [ 'shape' => 'EncryptionConfigList', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'AssociateEncryptionConfigResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'AssociateIdentityProviderConfigRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'oidc', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'oidc' => [ 'shape' => 'OidcIdentityProviderConfigRequest', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'AssociateIdentityProviderConfigResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'AssociatedAccessPoliciesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AssociatedAccessPolicy', ], ], 'AssociatedAccessPolicy' => [ 'type' => 'structure', 'members' => [ 'policyArn' => [ 'shape' => 'String', ], 'accessScope' => [ 'shape' => 'AccessScope', ], 'associatedAt' => [ 'shape' => 'Timestamp', ], 'modifiedAt' => [ 'shape' => 'Timestamp', ], ], ], 'AuthenticationMode' => [ 'type' => 'string', 'enum' => [ 'API', 'API_AND_CONFIG_MAP', 'CONFIG_MAP', ], ], 'AutoScalingGroup' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], ], ], 'AutoScalingGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoScalingGroup', ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'BlockStorage' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'BoxedBoolean', ], ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BoxedBoolean' => [ 'type' => 'boolean', 'box' => true, ], 'BoxedInteger' => [ 'type' => 'integer', 'box' => true, ], 'Capacity' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'CapacityTypes' => [ 'type' => 'string', 'enum' => [ 'ON_DEMAND', 'SPOT', 'CAPACITY_BLOCK', ], ], 'Category' => [ 'type' => 'string', 'enum' => [ 'UPGRADE_READINESS', 'MISCONFIGURATION', ], ], 'CategoryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Category', ], ], 'Certificate' => [ 'type' => 'structure', 'members' => [ 'data' => [ 'shape' => 'String', ], ], ], 'ClientException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'addonName' => [ 'shape' => 'String', ], 'subscriptionId' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ClientStat' => [ 'type' => 'structure', 'members' => [ 'userAgent' => [ 'shape' => 'String', ], 'numberOfRequestsLast30Days' => [ 'shape' => 'Integer', ], 'lastRequestTime' => [ 'shape' => 'Timestamp', ], ], ], 'ClientStats' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClientStat', ], ], 'Cluster' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'arn' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'version' => [ 'shape' => 'String', ], 'endpoint' => [ 'shape' => 'String', ], 'roleArn' => [ 'shape' => 'String', ], 'resourcesVpcConfig' => [ 'shape' => 'VpcConfigResponse', ], 'kubernetesNetworkConfig' => [ 'shape' => 'KubernetesNetworkConfigResponse', ], 'logging' => [ 'shape' => 'Logging', ], 'identity' => [ 'shape' => 'Identity', ], 'status' => [ 'shape' => 'ClusterStatus', ], 'certificateAuthority' => [ 'shape' => 'Certificate', ], 'clientRequestToken' => [ 'shape' => 'String', ], 'platformVersion' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], 'encryptionConfig' => [ 'shape' => 'EncryptionConfigList', ], 'connectorConfig' => [ 'shape' => 'ConnectorConfigResponse', ], 'id' => [ 'shape' => 'String', ], 'health' => [ 'shape' => 'ClusterHealth', ], 'outpostConfig' => [ 'shape' => 'OutpostConfigResponse', ], 'accessConfig' => [ 'shape' => 'AccessConfigResponse', ], 'upgradePolicy' => [ 'shape' => 'UpgradePolicyResponse', ], 'zonalShiftConfig' => [ 'shape' => 'ZonalShiftConfigResponse', ], 'remoteNetworkConfig' => [ 'shape' => 'RemoteNetworkConfigResponse', ], 'computeConfig' => [ 'shape' => 'ComputeConfigResponse', ], 'storageConfig' => [ 'shape' => 'StorageConfigResponse', ], ], ], 'ClusterHealth' => [ 'type' => 'structure', 'members' => [ 'issues' => [ 'shape' => 'ClusterIssueList', ], ], ], 'ClusterIssue' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'ClusterIssueCode', ], 'message' => [ 'shape' => 'String', ], 'resourceIds' => [ 'shape' => 'StringList', ], ], ], 'ClusterIssueCode' => [ 'type' => 'string', 'enum' => [ 'AccessDenied', 'ClusterUnreachable', 'ConfigurationConflict', 'InternalFailure', 'ResourceLimitExceeded', 'ResourceNotFound', 'IamRoleNotFound', 'VpcNotFound', 'InsufficientFreeAddresses', 'Ec2ServiceNotSubscribed', 'Ec2SubnetNotFound', 'Ec2SecurityGroupNotFound', 'KmsGrantRevoked', 'KmsKeyNotFound', 'KmsKeyMarkedForDeletion', 'KmsKeyDisabled', 'StsRegionalEndpointDisabled', 'UnsupportedVersion', 'Other', ], ], 'ClusterIssueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClusterIssue', ], ], 'ClusterName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[0-9A-Za-z][A-Za-z0-9\\-_]*', ], 'ClusterStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'FAILED', 'UPDATING', 'PENDING', ], ], 'ClusterVersionInformation' => [ 'type' => 'structure', 'members' => [ 'clusterVersion' => [ 'shape' => 'String', ], 'clusterType' => [ 'shape' => 'String', ], 'defaultPlatformVersion' => [ 'shape' => 'String', ], 'defaultVersion' => [ 'shape' => 'Boolean', ], 'releaseDate' => [ 'shape' => 'Timestamp', ], 'endOfStandardSupportDate' => [ 'shape' => 'Timestamp', ], 'endOfExtendedSupportDate' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'ClusterVersionStatus', ], 'versionStatus' => [ 'shape' => 'VersionStatus', ], 'kubernetesPatchVersion' => [ 'shape' => 'String', ], ], ], 'ClusterVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ClusterVersionInformation', ], ], 'ClusterVersionStatus' => [ 'type' => 'string', 'enum' => [ 'unsupported', 'standard-support', 'extended-support', ], ], 'Compatibilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'Compatibility', ], ], 'Compatibility' => [ 'type' => 'structure', 'members' => [ 'clusterVersion' => [ 'shape' => 'String', ], 'platformVersions' => [ 'shape' => 'StringList', ], 'defaultVersion' => [ 'shape' => 'Boolean', ], ], ], 'ComputeConfigRequest' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'BoxedBoolean', ], 'nodePools' => [ 'shape' => 'StringList', ], 'nodeRoleArn' => [ 'shape' => 'String', ], ], ], 'ComputeConfigResponse' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'BoxedBoolean', ], 'nodePools' => [ 'shape' => 'StringList', ], 'nodeRoleArn' => [ 'shape' => 'String', ], ], ], 'ConnectorConfigProvider' => [ 'type' => 'string', 'enum' => [ 'EKS_ANYWHERE', 'ANTHOS', 'GKE', 'AKS', 'OPENSHIFT', 'TANZU', 'RANCHER', 'EC2', 'OTHER', ], ], 'ConnectorConfigRequest' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'provider', ], 'members' => [ 'roleArn' => [ 'shape' => 'String', ], 'provider' => [ 'shape' => 'ConnectorConfigProvider', ], ], ], 'ConnectorConfigResponse' => [ 'type' => 'structure', 'members' => [ 'activationId' => [ 'shape' => 'String', ], 'activationCode' => [ 'shape' => 'String', ], 'activationExpiry' => [ 'shape' => 'Timestamp', ], 'provider' => [ 'shape' => 'String', ], 'roleArn' => [ 'shape' => 'String', ], ], ], 'ControlPlanePlacementRequest' => [ 'type' => 'structure', 'members' => [ 'groupName' => [ 'shape' => 'String', ], ], ], 'ControlPlanePlacementResponse' => [ 'type' => 'structure', 'members' => [ 'groupName' => [ 'shape' => 'String', ], ], ], 'CreateAccessConfigRequest' => [ 'type' => 'structure', 'members' => [ 'bootstrapClusterCreatorAdminPermissions' => [ 'shape' => 'BoxedBoolean', ], 'authenticationMode' => [ 'shape' => 'AuthenticationMode', ], ], ], 'CreateAccessEntryRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'principalArn', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'principalArn' => [ 'shape' => 'String', ], 'kubernetesGroups' => [ 'shape' => 'StringList', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'username' => [ 'shape' => 'String', ], 'type' => [ 'shape' => 'String', ], ], ], 'CreateAccessEntryResponse' => [ 'type' => 'structure', 'members' => [ 'accessEntry' => [ 'shape' => 'AccessEntry', ], ], ], 'CreateAddonRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'addonName', ], 'members' => [ 'clusterName' => [ 'shape' => 'ClusterName', 'location' => 'uri', 'locationName' => 'name', ], 'addonName' => [ 'shape' => 'String', ], 'addonVersion' => [ 'shape' => 'String', ], 'serviceAccountRoleArn' => [ 'shape' => 'RoleArn', ], 'resolveConflicts' => [ 'shape' => 'ResolveConflicts', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], 'configurationValues' => [ 'shape' => 'String', ], 'podIdentityAssociations' => [ 'shape' => 'AddonPodIdentityAssociationsList', ], ], ], 'CreateAddonResponse' => [ 'type' => 'structure', 'members' => [ 'addon' => [ 'shape' => 'Addon', ], ], ], 'CreateClusterRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'roleArn', 'resourcesVpcConfig', ], 'members' => [ 'name' => [ 'shape' => 'ClusterName', ], 'version' => [ 'shape' => 'String', ], 'roleArn' => [ 'shape' => 'String', ], 'resourcesVpcConfig' => [ 'shape' => 'VpcConfigRequest', ], 'kubernetesNetworkConfig' => [ 'shape' => 'KubernetesNetworkConfigRequest', ], 'logging' => [ 'shape' => 'Logging', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], 'encryptionConfig' => [ 'shape' => 'EncryptionConfigList', ], 'outpostConfig' => [ 'shape' => 'OutpostConfigRequest', ], 'accessConfig' => [ 'shape' => 'CreateAccessConfigRequest', ], 'bootstrapSelfManagedAddons' => [ 'shape' => 'BoxedBoolean', ], 'upgradePolicy' => [ 'shape' => 'UpgradePolicyRequest', ], 'zonalShiftConfig' => [ 'shape' => 'ZonalShiftConfigRequest', ], 'remoteNetworkConfig' => [ 'shape' => 'RemoteNetworkConfigRequest', ], 'computeConfig' => [ 'shape' => 'ComputeConfigRequest', ], 'storageConfig' => [ 'shape' => 'StorageConfigRequest', ], ], ], 'CreateClusterResponse' => [ 'type' => 'structure', 'members' => [ 'cluster' => [ 'shape' => 'Cluster', ], ], ], 'CreateEksAnywhereSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'term', ], 'members' => [ 'name' => [ 'shape' => 'EksAnywhereSubscriptionName', ], 'term' => [ 'shape' => 'EksAnywhereSubscriptionTerm', ], 'licenseQuantity' => [ 'shape' => 'Integer', ], 'licenseType' => [ 'shape' => 'EksAnywhereSubscriptionLicenseType', ], 'autoRenew' => [ 'shape' => 'Boolean', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateEksAnywhereSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'subscription' => [ 'shape' => 'EksAnywhereSubscription', ], ], ], 'CreateFargateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'fargateProfileName', 'clusterName', 'podExecutionRoleArn', ], 'members' => [ 'fargateProfileName' => [ 'shape' => 'String', ], 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'podExecutionRoleArn' => [ 'shape' => 'String', ], 'subnets' => [ 'shape' => 'StringList', ], 'selectors' => [ 'shape' => 'FargateProfileSelectors', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateFargateProfileResponse' => [ 'type' => 'structure', 'members' => [ 'fargateProfile' => [ 'shape' => 'FargateProfile', ], ], ], 'CreateNodegroupRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'nodegroupName', 'subnets', 'nodeRole', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'nodegroupName' => [ 'shape' => 'String', ], 'scalingConfig' => [ 'shape' => 'NodegroupScalingConfig', ], 'diskSize' => [ 'shape' => 'BoxedInteger', ], 'subnets' => [ 'shape' => 'StringList', ], 'instanceTypes' => [ 'shape' => 'StringList', ], 'amiType' => [ 'shape' => 'AMITypes', ], 'remoteAccess' => [ 'shape' => 'RemoteAccessConfig', ], 'nodeRole' => [ 'shape' => 'String', ], 'labels' => [ 'shape' => 'labelsMap', ], 'taints' => [ 'shape' => 'taintsList', ], 'tags' => [ 'shape' => 'TagMap', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'launchTemplate' => [ 'shape' => 'LaunchTemplateSpecification', ], 'updateConfig' => [ 'shape' => 'NodegroupUpdateConfig', ], 'nodeRepairConfig' => [ 'shape' => 'NodeRepairConfig', ], 'capacityType' => [ 'shape' => 'CapacityTypes', ], 'version' => [ 'shape' => 'String', ], 'releaseVersion' => [ 'shape' => 'String', ], ], ], 'CreateNodegroupResponse' => [ 'type' => 'structure', 'members' => [ 'nodegroup' => [ 'shape' => 'Nodegroup', ], ], ], 'CreatePodIdentityAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'namespace', 'serviceAccount', 'roleArn', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'namespace' => [ 'shape' => 'String', ], 'serviceAccount' => [ 'shape' => 'String', ], 'roleArn' => [ 'shape' => 'String', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreatePodIdentityAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'association' => [ 'shape' => 'PodIdentityAssociation', ], ], ], 'DeleteAccessEntryRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'principalArn', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'principalArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'principalArn', ], ], ], 'DeleteAccessEntryResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteAddonRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'addonName', ], 'members' => [ 'clusterName' => [ 'shape' => 'ClusterName', 'location' => 'uri', 'locationName' => 'name', ], 'addonName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'addonName', ], 'preserve' => [ 'shape' => 'Boolean', 'location' => 'querystring', 'locationName' => 'preserve', ], ], ], 'DeleteAddonResponse' => [ 'type' => 'structure', 'members' => [ 'addon' => [ 'shape' => 'Addon', ], ], ], 'DeleteClusterRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeleteClusterResponse' => [ 'type' => 'structure', 'members' => [ 'cluster' => [ 'shape' => 'Cluster', ], ], ], 'DeleteEksAnywhereSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteEksAnywhereSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'subscription' => [ 'shape' => 'EksAnywhereSubscription', ], ], ], 'DeleteFargateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'fargateProfileName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'fargateProfileName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'fargateProfileName', ], ], ], 'DeleteFargateProfileResponse' => [ 'type' => 'structure', 'members' => [ 'fargateProfile' => [ 'shape' => 'FargateProfile', ], ], ], 'DeleteNodegroupRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'nodegroupName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'nodegroupName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'nodegroupName', ], ], ], 'DeleteNodegroupResponse' => [ 'type' => 'structure', 'members' => [ 'nodegroup' => [ 'shape' => 'Nodegroup', ], ], ], 'DeletePodIdentityAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'associationId', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'associationId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'associationId', ], ], ], 'DeletePodIdentityAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'association' => [ 'shape' => 'PodIdentityAssociation', ], ], ], 'DeprecationDetail' => [ 'type' => 'structure', 'members' => [ 'usage' => [ 'shape' => 'String', ], 'replacedWith' => [ 'shape' => 'String', ], 'stopServingVersion' => [ 'shape' => 'String', ], 'startServingReplacementVersion' => [ 'shape' => 'String', ], 'clientStats' => [ 'shape' => 'ClientStats', ], ], ], 'DeprecationDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeprecationDetail', ], ], 'DeregisterClusterRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DeregisterClusterResponse' => [ 'type' => 'structure', 'members' => [ 'cluster' => [ 'shape' => 'Cluster', ], ], ], 'DescribeAccessEntryRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'principalArn', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'principalArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'principalArn', ], ], ], 'DescribeAccessEntryResponse' => [ 'type' => 'structure', 'members' => [ 'accessEntry' => [ 'shape' => 'AccessEntry', ], ], ], 'DescribeAddonConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'addonName', 'addonVersion', ], 'members' => [ 'addonName' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'addonName', ], 'addonVersion' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'addonVersion', ], ], ], 'DescribeAddonConfigurationResponse' => [ 'type' => 'structure', 'members' => [ 'addonName' => [ 'shape' => 'String', ], 'addonVersion' => [ 'shape' => 'String', ], 'configurationSchema' => [ 'shape' => 'String', ], 'podIdentityConfiguration' => [ 'shape' => 'AddonPodIdentityConfigurationList', ], ], ], 'DescribeAddonRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'addonName', ], 'members' => [ 'clusterName' => [ 'shape' => 'ClusterName', 'location' => 'uri', 'locationName' => 'name', ], 'addonName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'addonName', ], ], ], 'DescribeAddonResponse' => [ 'type' => 'structure', 'members' => [ 'addon' => [ 'shape' => 'Addon', ], ], ], 'DescribeAddonVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'kubernetesVersion' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'kubernetesVersion', ], 'maxResults' => [ 'shape' => 'DescribeAddonVersionsRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'addonName' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'addonName', ], 'types' => [ 'shape' => 'StringList', 'location' => 'querystring', 'locationName' => 'types', ], 'publishers' => [ 'shape' => 'StringList', 'location' => 'querystring', 'locationName' => 'publishers', ], 'owners' => [ 'shape' => 'StringList', 'location' => 'querystring', 'locationName' => 'owners', ], ], ], 'DescribeAddonVersionsRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'DescribeAddonVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'addons' => [ 'shape' => 'Addons', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'DescribeClusterRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], ], ], 'DescribeClusterResponse' => [ 'type' => 'structure', 'members' => [ 'cluster' => [ 'shape' => 'Cluster', ], ], ], 'DescribeClusterVersionMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'DescribeClusterVersionsRequest' => [ 'type' => 'structure', 'members' => [ 'clusterType' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'clusterType', ], 'maxResults' => [ 'shape' => 'DescribeClusterVersionMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'defaultOnly' => [ 'shape' => 'BoxedBoolean', 'location' => 'querystring', 'locationName' => 'defaultOnly', ], 'includeAll' => [ 'shape' => 'BoxedBoolean', 'location' => 'querystring', 'locationName' => 'includeAll', ], 'clusterVersions' => [ 'shape' => 'StringList', 'location' => 'querystring', 'locationName' => 'clusterVersions', ], 'status' => [ 'shape' => 'ClusterVersionStatus', 'deprecated' => true, 'deprecatedMessage' => 'status has been replaced by versionStatus', 'deprecatedSince' => '2025-02-15', 'location' => 'querystring', 'locationName' => 'status', ], 'versionStatus' => [ 'shape' => 'VersionStatus', 'location' => 'querystring', 'locationName' => 'versionStatus', ], ], ], 'DescribeClusterVersionsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'clusterVersions' => [ 'shape' => 'ClusterVersionList', ], ], ], 'DescribeEksAnywhereSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'id', ], 'members' => [ 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DescribeEksAnywhereSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'subscription' => [ 'shape' => 'EksAnywhereSubscription', ], ], ], 'DescribeFargateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'fargateProfileName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'fargateProfileName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'fargateProfileName', ], ], ], 'DescribeFargateProfileResponse' => [ 'type' => 'structure', 'members' => [ 'fargateProfile' => [ 'shape' => 'FargateProfile', ], ], ], 'DescribeIdentityProviderConfigRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'identityProviderConfig', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'identityProviderConfig' => [ 'shape' => 'IdentityProviderConfig', ], ], ], 'DescribeIdentityProviderConfigResponse' => [ 'type' => 'structure', 'members' => [ 'identityProviderConfig' => [ 'shape' => 'IdentityProviderConfigResponse', ], ], ], 'DescribeInsightRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'id', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DescribeInsightResponse' => [ 'type' => 'structure', 'members' => [ 'insight' => [ 'shape' => 'Insight', ], ], ], 'DescribeNodegroupRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'nodegroupName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'nodegroupName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'nodegroupName', ], ], ], 'DescribeNodegroupResponse' => [ 'type' => 'structure', 'members' => [ 'nodegroup' => [ 'shape' => 'Nodegroup', ], ], ], 'DescribePodIdentityAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'associationId', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'associationId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'associationId', ], ], ], 'DescribePodIdentityAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'association' => [ 'shape' => 'PodIdentityAssociation', ], ], ], 'DescribeUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'updateId', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'updateId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'updateId', ], 'nodegroupName' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nodegroupName', ], 'addonName' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'addonName', ], ], ], 'DescribeUpdateResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'DisassociateAccessPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'principalArn', 'policyArn', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'principalArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'principalArn', ], 'policyArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'policyArn', ], ], ], 'DisassociateAccessPolicyResponse' => [ 'type' => 'structure', 'members' => [], ], 'DisassociateIdentityProviderConfigRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'identityProviderConfig', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'identityProviderConfig' => [ 'shape' => 'IdentityProviderConfig', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'DisassociateIdentityProviderConfigResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'EksAnywhereSubscription' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'arn' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'effectiveDate' => [ 'shape' => 'Timestamp', ], 'expirationDate' => [ 'shape' => 'Timestamp', ], 'licenseQuantity' => [ 'shape' => 'Integer', ], 'licenseType' => [ 'shape' => 'EksAnywhereSubscriptionLicenseType', ], 'term' => [ 'shape' => 'EksAnywhereSubscriptionTerm', ], 'status' => [ 'shape' => 'String', ], 'autoRenew' => [ 'shape' => 'Boolean', ], 'licenseArns' => [ 'shape' => 'StringList', ], 'licenses' => [ 'shape' => 'LicenseList', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'EksAnywhereSubscriptionLicenseType' => [ 'type' => 'string', 'enum' => [ 'Cluster', ], ], 'EksAnywhereSubscriptionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EksAnywhereSubscription', ], ], 'EksAnywhereSubscriptionName' => [ 'type' => 'string', 'max' => 100, 'min' => 1, 'pattern' => '^[0-9A-Za-z][A-Za-z0-9\\-_]*', ], 'EksAnywhereSubscriptionStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'EXPIRING', 'EXPIRED', 'DELETING', ], ], 'EksAnywhereSubscriptionStatusValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'EksAnywhereSubscriptionStatus', ], ], 'EksAnywhereSubscriptionTerm' => [ 'type' => 'structure', 'members' => [ 'duration' => [ 'shape' => 'Integer', ], 'unit' => [ 'shape' => 'EksAnywhereSubscriptionTermUnit', ], ], ], 'EksAnywhereSubscriptionTermUnit' => [ 'type' => 'string', 'enum' => [ 'MONTHS', ], ], 'ElasticLoadBalancing' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'BoxedBoolean', ], ], ], 'EncryptionConfig' => [ 'type' => 'structure', 'members' => [ 'resources' => [ 'shape' => 'StringList', ], 'provider' => [ 'shape' => 'Provider', ], ], ], 'EncryptionConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EncryptionConfig', ], 'max' => 1, ], 'ErrorCode' => [ 'type' => 'string', 'enum' => [ 'SubnetNotFound', 'SecurityGroupNotFound', 'EniLimitReached', 'IpNotAvailable', 'AccessDenied', 'OperationNotPermitted', 'VpcIdNotFound', 'Unknown', 'NodeCreationFailure', 'PodEvictionFailure', 'InsufficientFreeAddresses', 'ClusterUnreachable', 'InsufficientNumberOfReplicas', 'ConfigurationConflict', 'AdmissionRequestDenied', 'UnsupportedAddonModification', 'K8sResourceNotFound', ], ], 'ErrorDetail' => [ 'type' => 'structure', 'members' => [ 'errorCode' => [ 'shape' => 'ErrorCode', ], 'errorMessage' => [ 'shape' => 'String', ], 'resourceIds' => [ 'shape' => 'StringList', ], ], ], 'ErrorDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'ErrorDetail', ], ], 'FargateProfile' => [ 'type' => 'structure', 'members' => [ 'fargateProfileName' => [ 'shape' => 'String', ], 'fargateProfileArn' => [ 'shape' => 'String', ], 'clusterName' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'podExecutionRoleArn' => [ 'shape' => 'String', ], 'subnets' => [ 'shape' => 'StringList', ], 'selectors' => [ 'shape' => 'FargateProfileSelectors', ], 'status' => [ 'shape' => 'FargateProfileStatus', ], 'tags' => [ 'shape' => 'TagMap', ], 'health' => [ 'shape' => 'FargateProfileHealth', ], ], ], 'FargateProfileHealth' => [ 'type' => 'structure', 'members' => [ 'issues' => [ 'shape' => 'FargateProfileIssueList', ], ], ], 'FargateProfileIssue' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'FargateProfileIssueCode', ], 'message' => [ 'shape' => 'String', ], 'resourceIds' => [ 'shape' => 'StringList', ], ], ], 'FargateProfileIssueCode' => [ 'type' => 'string', 'enum' => [ 'PodExecutionRoleAlreadyInUse', 'AccessDenied', 'ClusterUnreachable', 'InternalFailure', ], ], 'FargateProfileIssueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FargateProfileIssue', ], ], 'FargateProfileLabel' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'FargateProfileSelector' => [ 'type' => 'structure', 'members' => [ 'namespace' => [ 'shape' => 'String', ], 'labels' => [ 'shape' => 'FargateProfileLabel', ], ], ], 'FargateProfileSelectors' => [ 'type' => 'list', 'member' => [ 'shape' => 'FargateProfileSelector', ], ], 'FargateProfileStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'DELETING', 'CREATE_FAILED', 'DELETE_FAILED', ], ], 'FargateProfilesRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'Identity' => [ 'type' => 'structure', 'members' => [ 'oidc' => [ 'shape' => 'OIDC', ], ], ], 'IdentityProviderConfig' => [ 'type' => 'structure', 'required' => [ 'type', 'name', ], 'members' => [ 'type' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], ], ], 'IdentityProviderConfigResponse' => [ 'type' => 'structure', 'members' => [ 'oidc' => [ 'shape' => 'OidcIdentityProviderConfig', ], ], ], 'IdentityProviderConfigs' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdentityProviderConfig', ], ], 'IncludeClustersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Insight' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'category' => [ 'shape' => 'Category', ], 'kubernetesVersion' => [ 'shape' => 'String', ], 'lastRefreshTime' => [ 'shape' => 'Timestamp', ], 'lastTransitionTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'String', ], 'insightStatus' => [ 'shape' => 'InsightStatus', ], 'recommendation' => [ 'shape' => 'String', ], 'additionalInfo' => [ 'shape' => 'AdditionalInfoMap', ], 'resources' => [ 'shape' => 'InsightResourceDetails', ], 'categorySpecificSummary' => [ 'shape' => 'InsightCategorySpecificSummary', ], ], ], 'InsightCategorySpecificSummary' => [ 'type' => 'structure', 'members' => [ 'deprecationDetails' => [ 'shape' => 'DeprecationDetails', ], 'addonCompatibilityDetails' => [ 'shape' => 'AddonCompatibilityDetails', ], ], ], 'InsightResourceDetail' => [ 'type' => 'structure', 'members' => [ 'insightStatus' => [ 'shape' => 'InsightStatus', ], 'kubernetesResourceUri' => [ 'shape' => 'String', ], 'arn' => [ 'shape' => 'String', ], ], ], 'InsightResourceDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightResourceDetail', ], ], 'InsightStatus' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'InsightStatusValue', ], 'reason' => [ 'shape' => 'String', ], ], ], 'InsightStatusValue' => [ 'type' => 'string', 'enum' => [ 'PASSING', 'WARNING', 'ERROR', 'UNKNOWN', ], ], 'InsightStatusValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightStatusValue', ], ], 'InsightSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'InsightSummary', ], ], 'InsightSummary' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'category' => [ 'shape' => 'Category', ], 'kubernetesVersion' => [ 'shape' => 'String', ], 'lastRefreshTime' => [ 'shape' => 'Timestamp', ], 'lastTransitionTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'String', ], 'insightStatus' => [ 'shape' => 'InsightStatus', ], ], ], 'InsightsFilter' => [ 'type' => 'structure', 'members' => [ 'categories' => [ 'shape' => 'CategoryList', ], 'kubernetesVersions' => [ 'shape' => 'StringList', ], 'statuses' => [ 'shape' => 'InsightStatusValueList', ], ], ], 'Integer' => [ 'type' => 'integer', ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'fargateProfileName' => [ 'shape' => 'String', ], 'addonName' => [ 'shape' => 'String', ], 'subscriptionId' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'addonName' => [ 'shape' => 'String', ], 'subscriptionId' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'InvalidStateException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'IpFamily' => [ 'type' => 'string', 'enum' => [ 'ipv4', 'ipv6', ], ], 'Issue' => [ 'type' => 'structure', 'members' => [ 'code' => [ 'shape' => 'NodegroupIssueCode', ], 'message' => [ 'shape' => 'String', ], 'resourceIds' => [ 'shape' => 'StringList', ], ], ], 'IssueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Issue', ], ], 'KubernetesNetworkConfigRequest' => [ 'type' => 'structure', 'members' => [ 'serviceIpv4Cidr' => [ 'shape' => 'String', ], 'ipFamily' => [ 'shape' => 'IpFamily', ], 'elasticLoadBalancing' => [ 'shape' => 'ElasticLoadBalancing', ], ], ], 'KubernetesNetworkConfigResponse' => [ 'type' => 'structure', 'members' => [ 'serviceIpv4Cidr' => [ 'shape' => 'String', ], 'serviceIpv6Cidr' => [ 'shape' => 'String', ], 'ipFamily' => [ 'shape' => 'IpFamily', ], 'elasticLoadBalancing' => [ 'shape' => 'ElasticLoadBalancing', ], ], ], 'LaunchTemplateSpecification' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'version' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'String', ], ], ], 'License' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'token' => [ 'shape' => 'String', ], ], ], 'LicenseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'License', ], ], 'ListAccessEntriesRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'associatedPolicyArn' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'associatedPolicyArn', ], 'maxResults' => [ 'shape' => 'ListAccessEntriesRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAccessEntriesRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListAccessEntriesResponse' => [ 'type' => 'structure', 'members' => [ 'accessEntries' => [ 'shape' => 'StringList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListAccessPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListAccessPoliciesRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAccessPoliciesRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListAccessPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'accessPolicies' => [ 'shape' => 'AccessPoliciesList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListAddonsRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', ], 'members' => [ 'clusterName' => [ 'shape' => 'ClusterName', 'location' => 'uri', 'locationName' => 'name', ], 'maxResults' => [ 'shape' => 'ListAddonsRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAddonsRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListAddonsResponse' => [ 'type' => 'structure', 'members' => [ 'addons' => [ 'shape' => 'StringList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListAssociatedAccessPoliciesRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'principalArn', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'principalArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'principalArn', ], 'maxResults' => [ 'shape' => 'ListAssociatedAccessPoliciesRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListAssociatedAccessPoliciesRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListAssociatedAccessPoliciesResponse' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'principalArn' => [ 'shape' => 'String', ], 'nextToken' => [ 'shape' => 'String', ], 'associatedAccessPolicies' => [ 'shape' => 'AssociatedAccessPoliciesList', ], ], ], 'ListClustersRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListClustersRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'include' => [ 'shape' => 'IncludeClustersList', 'location' => 'querystring', 'locationName' => 'include', ], ], ], 'ListClustersRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListClustersResponse' => [ 'type' => 'structure', 'members' => [ 'clusters' => [ 'shape' => 'StringList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListEksAnywhereSubscriptionsRequest' => [ 'type' => 'structure', 'members' => [ 'maxResults' => [ 'shape' => 'ListEksAnywhereSubscriptionsRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'includeStatus' => [ 'shape' => 'EksAnywhereSubscriptionStatusValues', 'location' => 'querystring', 'locationName' => 'includeStatus', ], ], ], 'ListEksAnywhereSubscriptionsRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListEksAnywhereSubscriptionsResponse' => [ 'type' => 'structure', 'members' => [ 'subscriptions' => [ 'shape' => 'EksAnywhereSubscriptionList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListFargateProfilesRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'maxResults' => [ 'shape' => 'FargateProfilesRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListFargateProfilesResponse' => [ 'type' => 'structure', 'members' => [ 'fargateProfileNames' => [ 'shape' => 'StringList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListIdentityProviderConfigsRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'maxResults' => [ 'shape' => 'ListIdentityProviderConfigsRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListIdentityProviderConfigsRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListIdentityProviderConfigsResponse' => [ 'type' => 'structure', 'members' => [ 'identityProviderConfigs' => [ 'shape' => 'IdentityProviderConfigs', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListInsightsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListInsightsRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'filter' => [ 'shape' => 'InsightsFilter', ], 'maxResults' => [ 'shape' => 'ListInsightsMaxResults', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListInsightsResponse' => [ 'type' => 'structure', 'members' => [ 'insights' => [ 'shape' => 'InsightSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListNodegroupsRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'maxResults' => [ 'shape' => 'ListNodegroupsRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListNodegroupsRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListNodegroupsResponse' => [ 'type' => 'structure', 'members' => [ 'nodegroups' => [ 'shape' => 'StringList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListPodIdentityAssociationsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListPodIdentityAssociationsRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'namespace' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'namespace', ], 'serviceAccount' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'serviceAccount', ], 'maxResults' => [ 'shape' => 'ListPodIdentityAssociationsMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ListPodIdentityAssociationsResponse' => [ 'type' => 'structure', 'members' => [ 'associations' => [ 'shape' => 'PodIdentityAssociationSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListUpdatesRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'nodegroupName' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nodegroupName', ], 'addonName' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'addonName', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListUpdatesRequestMaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListUpdatesRequestMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListUpdatesResponse' => [ 'type' => 'structure', 'members' => [ 'updateIds' => [ 'shape' => 'StringList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'LogSetup' => [ 'type' => 'structure', 'members' => [ 'types' => [ 'shape' => 'LogTypes', ], 'enabled' => [ 'shape' => 'BoxedBoolean', ], ], ], 'LogSetups' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogSetup', ], ], 'LogType' => [ 'type' => 'string', 'enum' => [ 'api', 'audit', 'authenticator', 'controllerManager', 'scheduler', ], ], 'LogTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'LogType', ], ], 'Logging' => [ 'type' => 'structure', 'members' => [ 'clusterLogging' => [ 'shape' => 'LogSetups', ], ], ], 'MarketplaceInformation' => [ 'type' => 'structure', 'members' => [ 'productId' => [ 'shape' => 'String', ], 'productUrl' => [ 'shape' => 'String', ], ], ], 'NodeRepairConfig' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'BoxedBoolean', ], ], ], 'Nodegroup' => [ 'type' => 'structure', 'members' => [ 'nodegroupName' => [ 'shape' => 'String', ], 'nodegroupArn' => [ 'shape' => 'String', ], 'clusterName' => [ 'shape' => 'String', ], 'version' => [ 'shape' => 'String', ], 'releaseVersion' => [ 'shape' => 'String', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'modifiedAt' => [ 'shape' => 'Timestamp', ], 'status' => [ 'shape' => 'NodegroupStatus', ], 'capacityType' => [ 'shape' => 'CapacityTypes', ], 'scalingConfig' => [ 'shape' => 'NodegroupScalingConfig', ], 'instanceTypes' => [ 'shape' => 'StringList', ], 'subnets' => [ 'shape' => 'StringList', ], 'remoteAccess' => [ 'shape' => 'RemoteAccessConfig', ], 'amiType' => [ 'shape' => 'AMITypes', ], 'nodeRole' => [ 'shape' => 'String', ], 'labels' => [ 'shape' => 'labelsMap', ], 'taints' => [ 'shape' => 'taintsList', ], 'resources' => [ 'shape' => 'NodegroupResources', ], 'diskSize' => [ 'shape' => 'BoxedInteger', ], 'health' => [ 'shape' => 'NodegroupHealth', ], 'updateConfig' => [ 'shape' => 'NodegroupUpdateConfig', ], 'nodeRepairConfig' => [ 'shape' => 'NodeRepairConfig', ], 'launchTemplate' => [ 'shape' => 'LaunchTemplateSpecification', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'NodegroupHealth' => [ 'type' => 'structure', 'members' => [ 'issues' => [ 'shape' => 'IssueList', ], ], ], 'NodegroupIssueCode' => [ 'type' => 'string', 'enum' => [ 'AutoScalingGroupNotFound', 'AutoScalingGroupInvalidConfiguration', 'Ec2SecurityGroupNotFound', 'Ec2SecurityGroupDeletionFailure', 'Ec2LaunchTemplateNotFound', 'Ec2LaunchTemplateVersionMismatch', 'Ec2SubnetNotFound', 'Ec2SubnetInvalidConfiguration', 'IamInstanceProfileNotFound', 'Ec2SubnetMissingIpv6Assignment', 'IamLimitExceeded', 'IamNodeRoleNotFound', 'NodeCreationFailure', 'AsgInstanceLaunchFailures', 'InstanceLimitExceeded', 'InsufficientFreeAddresses', 'AccessDenied', 'InternalFailure', 'ClusterUnreachable', 'AmiIdNotFound', 'AutoScalingGroupOptInRequired', 'AutoScalingGroupRateLimitExceeded', 'Ec2LaunchTemplateDeletionFailure', 'Ec2LaunchTemplateInvalidConfiguration', 'Ec2LaunchTemplateMaxLimitExceeded', 'Ec2SubnetListTooLong', 'IamThrottling', 'NodeTerminationFailure', 'PodEvictionFailure', 'SourceEc2LaunchTemplateNotFound', 'LimitExceeded', 'Unknown', 'AutoScalingGroupInstanceRefreshActive', 'KubernetesLabelInvalid', 'Ec2LaunchTemplateVersionMaxLimitExceeded', 'Ec2InstanceTypeDoesNotExist', ], ], 'NodegroupResources' => [ 'type' => 'structure', 'members' => [ 'autoScalingGroups' => [ 'shape' => 'AutoScalingGroupList', ], 'remoteAccessSecurityGroup' => [ 'shape' => 'String', ], ], ], 'NodegroupScalingConfig' => [ 'type' => 'structure', 'members' => [ 'minSize' => [ 'shape' => 'ZeroCapacity', ], 'maxSize' => [ 'shape' => 'Capacity', ], 'desiredSize' => [ 'shape' => 'ZeroCapacity', ], ], ], 'NodegroupStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'ACTIVE', 'UPDATING', 'DELETING', 'CREATE_FAILED', 'DELETE_FAILED', 'DEGRADED', ], ], 'NodegroupUpdateConfig' => [ 'type' => 'structure', 'members' => [ 'maxUnavailable' => [ 'shape' => 'NonZeroInteger', ], 'maxUnavailablePercentage' => [ 'shape' => 'PercentCapacity', ], 'updateStrategy' => [ 'shape' => 'NodegroupUpdateStrategies', ], ], ], 'NodegroupUpdateStrategies' => [ 'type' => 'string', 'enum' => [ 'DEFAULT', 'MINIMAL', ], ], 'NonZeroInteger' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'OIDC' => [ 'type' => 'structure', 'members' => [ 'issuer' => [ 'shape' => 'String', ], ], ], 'OidcIdentityProviderConfig' => [ 'type' => 'structure', 'members' => [ 'identityProviderConfigName' => [ 'shape' => 'String', ], 'identityProviderConfigArn' => [ 'shape' => 'String', ], 'clusterName' => [ 'shape' => 'String', ], 'issuerUrl' => [ 'shape' => 'String', ], 'clientId' => [ 'shape' => 'String', ], 'usernameClaim' => [ 'shape' => 'String', ], 'usernamePrefix' => [ 'shape' => 'String', ], 'groupsClaim' => [ 'shape' => 'String', ], 'groupsPrefix' => [ 'shape' => 'String', ], 'requiredClaims' => [ 'shape' => 'requiredClaimsMap', ], 'tags' => [ 'shape' => 'TagMap', ], 'status' => [ 'shape' => 'configStatus', ], ], ], 'OidcIdentityProviderConfigRequest' => [ 'type' => 'structure', 'required' => [ 'identityProviderConfigName', 'issuerUrl', 'clientId', ], 'members' => [ 'identityProviderConfigName' => [ 'shape' => 'String', ], 'issuerUrl' => [ 'shape' => 'String', ], 'clientId' => [ 'shape' => 'String', ], 'usernameClaim' => [ 'shape' => 'String', ], 'usernamePrefix' => [ 'shape' => 'String', ], 'groupsClaim' => [ 'shape' => 'String', ], 'groupsPrefix' => [ 'shape' => 'String', ], 'requiredClaims' => [ 'shape' => 'requiredClaimsMap', ], ], ], 'OutpostConfigRequest' => [ 'type' => 'structure', 'required' => [ 'outpostArns', 'controlPlaneInstanceType', ], 'members' => [ 'outpostArns' => [ 'shape' => 'StringList', ], 'controlPlaneInstanceType' => [ 'shape' => 'String', ], 'controlPlanePlacement' => [ 'shape' => 'ControlPlanePlacementRequest', ], ], ], 'OutpostConfigResponse' => [ 'type' => 'structure', 'required' => [ 'outpostArns', 'controlPlaneInstanceType', ], 'members' => [ 'outpostArns' => [ 'shape' => 'StringList', ], 'controlPlaneInstanceType' => [ 'shape' => 'String', ], 'controlPlanePlacement' => [ 'shape' => 'ControlPlanePlacementResponse', ], ], ], 'PercentCapacity' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'PodIdentityAssociation' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'namespace' => [ 'shape' => 'String', ], 'serviceAccount' => [ 'shape' => 'String', ], 'roleArn' => [ 'shape' => 'String', ], 'associationArn' => [ 'shape' => 'String', ], 'associationId' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'modifiedAt' => [ 'shape' => 'Timestamp', ], 'ownerArn' => [ 'shape' => 'String', ], ], ], 'PodIdentityAssociationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'PodIdentityAssociationSummary', ], ], 'PodIdentityAssociationSummary' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'namespace' => [ 'shape' => 'String', ], 'serviceAccount' => [ 'shape' => 'String', ], 'associationArn' => [ 'shape' => 'String', ], 'associationId' => [ 'shape' => 'String', ], 'ownerArn' => [ 'shape' => 'String', ], ], ], 'Provider' => [ 'type' => 'structure', 'members' => [ 'keyArn' => [ 'shape' => 'String', ], ], ], 'RegisterClusterRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'connectorConfig', ], 'members' => [ 'name' => [ 'shape' => 'ClusterName', ], 'connectorConfig' => [ 'shape' => 'ConnectorConfigRequest', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'RegisterClusterResponse' => [ 'type' => 'structure', 'members' => [ 'cluster' => [ 'shape' => 'Cluster', ], ], ], 'RemoteAccessConfig' => [ 'type' => 'structure', 'members' => [ 'ec2SshKey' => [ 'shape' => 'String', ], 'sourceSecurityGroups' => [ 'shape' => 'StringList', ], ], ], 'RemoteNetworkConfigRequest' => [ 'type' => 'structure', 'members' => [ 'remoteNodeNetworks' => [ 'shape' => 'RemoteNodeNetworkList', ], 'remotePodNetworks' => [ 'shape' => 'RemotePodNetworkList', ], ], ], 'RemoteNetworkConfigResponse' => [ 'type' => 'structure', 'members' => [ 'remoteNodeNetworks' => [ 'shape' => 'RemoteNodeNetworkList', ], 'remotePodNetworks' => [ 'shape' => 'RemotePodNetworkList', ], ], ], 'RemoteNodeNetwork' => [ 'type' => 'structure', 'members' => [ 'cidrs' => [ 'shape' => 'StringList', ], ], ], 'RemoteNodeNetworkList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RemoteNodeNetwork', ], 'max' => 1, ], 'RemotePodNetwork' => [ 'type' => 'structure', 'members' => [ 'cidrs' => [ 'shape' => 'StringList', ], ], ], 'RemotePodNetworkList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RemotePodNetwork', ], 'max' => 1, ], 'ResolveConflicts' => [ 'type' => 'string', 'enum' => [ 'OVERWRITE', 'NONE', 'PRESERVE', ], ], 'ResourceInUseException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'addonName' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, ], 'exception' => true, ], 'ResourceLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'subscriptionId' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'fargateProfileName' => [ 'shape' => 'String', ], 'addonName' => [ 'shape' => 'String', ], 'subscriptionId' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, ], 'exception' => true, ], 'ResourcePropagationDelayException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 428, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ServerException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'addonName' => [ 'shape' => 'String', ], 'subscriptionId' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 503, ], 'exception' => true, 'fault' => true, ], 'StorageConfigRequest' => [ 'type' => 'structure', 'members' => [ 'blockStorage' => [ 'shape' => 'BlockStorage', ], ], ], 'StorageConfigResponse' => [ 'type' => 'structure', 'members' => [ 'blockStorage' => [ 'shape' => 'BlockStorage', ], ], ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SupportType' => [ 'type' => 'string', 'enum' => [ 'STANDARD', 'EXTENDED', ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 50, 'min' => 1, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 50, 'min' => 1, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, ], 'Taint' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'taintKey', ], 'value' => [ 'shape' => 'taintValue', ], 'effect' => [ 'shape' => 'TaintEffect', ], ], ], 'TaintEffect' => [ 'type' => 'string', 'enum' => [ 'NO_SCHEDULE', 'NO_EXECUTE', 'PREFER_NO_SCHEDULE', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'clusterName' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UnsupportedAvailabilityZoneException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], 'clusterName' => [ 'shape' => 'String', ], 'nodegroupName' => [ 'shape' => 'String', ], 'validZones' => [ 'shape' => 'StringList', ], ], 'error' => [ 'httpStatusCode' => 400, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'Update' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'String', ], 'status' => [ 'shape' => 'UpdateStatus', ], 'type' => [ 'shape' => 'UpdateType', ], 'params' => [ 'shape' => 'UpdateParams', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'errors' => [ 'shape' => 'ErrorDetails', ], ], ], 'UpdateAccessConfigRequest' => [ 'type' => 'structure', 'members' => [ 'authenticationMode' => [ 'shape' => 'AuthenticationMode', ], ], ], 'UpdateAccessEntryRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'principalArn', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'principalArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'principalArn', ], 'kubernetesGroups' => [ 'shape' => 'StringList', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'username' => [ 'shape' => 'String', ], ], ], 'UpdateAccessEntryResponse' => [ 'type' => 'structure', 'members' => [ 'accessEntry' => [ 'shape' => 'AccessEntry', ], ], ], 'UpdateAddonRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'addonName', ], 'members' => [ 'clusterName' => [ 'shape' => 'ClusterName', 'location' => 'uri', 'locationName' => 'name', ], 'addonName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'addonName', ], 'addonVersion' => [ 'shape' => 'String', ], 'serviceAccountRoleArn' => [ 'shape' => 'RoleArn', ], 'resolveConflicts' => [ 'shape' => 'ResolveConflicts', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'configurationValues' => [ 'shape' => 'String', ], 'podIdentityAssociations' => [ 'shape' => 'AddonPodIdentityAssociationsList', ], ], ], 'UpdateAddonResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'UpdateClusterConfigRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'resourcesVpcConfig' => [ 'shape' => 'VpcConfigRequest', ], 'logging' => [ 'shape' => 'Logging', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'accessConfig' => [ 'shape' => 'UpdateAccessConfigRequest', ], 'upgradePolicy' => [ 'shape' => 'UpgradePolicyRequest', ], 'zonalShiftConfig' => [ 'shape' => 'ZonalShiftConfigRequest', ], 'computeConfig' => [ 'shape' => 'ComputeConfigRequest', ], 'kubernetesNetworkConfig' => [ 'shape' => 'KubernetesNetworkConfigRequest', ], 'storageConfig' => [ 'shape' => 'StorageConfigRequest', ], 'remoteNetworkConfig' => [ 'shape' => 'RemoteNetworkConfigRequest', ], ], ], 'UpdateClusterConfigResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'UpdateClusterVersionRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'version', ], 'members' => [ 'name' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'version' => [ 'shape' => 'String', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'force' => [ 'shape' => 'Boolean', ], ], ], 'UpdateClusterVersionResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'UpdateEksAnywhereSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'id', 'autoRenew', ], 'members' => [ 'id' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'id', ], 'autoRenew' => [ 'shape' => 'Boolean', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'UpdateEksAnywhereSubscriptionResponse' => [ 'type' => 'structure', 'members' => [ 'subscription' => [ 'shape' => 'EksAnywhereSubscription', ], ], ], 'UpdateLabelsPayload' => [ 'type' => 'structure', 'members' => [ 'addOrUpdateLabels' => [ 'shape' => 'labelsMap', ], 'removeLabels' => [ 'shape' => 'labelsKeyList', ], ], ], 'UpdateNodegroupConfigRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'nodegroupName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'nodegroupName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'nodegroupName', ], 'labels' => [ 'shape' => 'UpdateLabelsPayload', ], 'taints' => [ 'shape' => 'UpdateTaintsPayload', ], 'scalingConfig' => [ 'shape' => 'NodegroupScalingConfig', ], 'updateConfig' => [ 'shape' => 'NodegroupUpdateConfig', ], 'nodeRepairConfig' => [ 'shape' => 'NodeRepairConfig', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'UpdateNodegroupConfigResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'UpdateNodegroupVersionRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'nodegroupName', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'nodegroupName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'nodegroupName', ], 'version' => [ 'shape' => 'String', ], 'releaseVersion' => [ 'shape' => 'String', ], 'launchTemplate' => [ 'shape' => 'LaunchTemplateSpecification', ], 'force' => [ 'shape' => 'Boolean', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'UpdateNodegroupVersionResponse' => [ 'type' => 'structure', 'members' => [ 'update' => [ 'shape' => 'Update', ], ], ], 'UpdateParam' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'UpdateParamType', ], 'value' => [ 'shape' => 'String', ], ], ], 'UpdateParamType' => [ 'type' => 'string', 'enum' => [ 'Version', 'PlatformVersion', 'EndpointPrivateAccess', 'EndpointPublicAccess', 'ClusterLogging', 'DesiredSize', 'LabelsToAdd', 'LabelsToRemove', 'TaintsToAdd', 'TaintsToRemove', 'MaxSize', 'MinSize', 'ReleaseVersion', 'PublicAccessCidrs', 'LaunchTemplateName', 'LaunchTemplateVersion', 'IdentityProviderConfig', 'EncryptionConfig', 'AddonVersion', 'ServiceAccountRoleArn', 'ResolveConflicts', 'MaxUnavailable', 'MaxUnavailablePercentage', 'NodeRepairEnabled', 'UpdateStrategy', 'ConfigurationValues', 'SecurityGroups', 'Subnets', 'AuthenticationMode', 'PodIdentityAssociations', 'UpgradePolicy', 'ZonalShiftConfig', 'ComputeConfig', 'StorageConfig', 'KubernetesNetworkConfig', 'RemoteNetworkConfig', ], ], 'UpdateParams' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpdateParam', ], ], 'UpdatePodIdentityAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'clusterName', 'associationId', ], 'members' => [ 'clusterName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'name', ], 'associationId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'associationId', ], 'roleArn' => [ 'shape' => 'String', ], 'clientRequestToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'UpdatePodIdentityAssociationResponse' => [ 'type' => 'structure', 'members' => [ 'association' => [ 'shape' => 'PodIdentityAssociation', ], ], ], 'UpdateStatus' => [ 'type' => 'string', 'enum' => [ 'InProgress', 'Failed', 'Cancelled', 'Successful', ], ], 'UpdateTaintsPayload' => [ 'type' => 'structure', 'members' => [ 'addOrUpdateTaints' => [ 'shape' => 'taintsList', ], 'removeTaints' => [ 'shape' => 'taintsList', ], ], ], 'UpdateType' => [ 'type' => 'string', 'enum' => [ 'VersionUpdate', 'EndpointAccessUpdate', 'LoggingUpdate', 'ConfigUpdate', 'AssociateIdentityProviderConfig', 'DisassociateIdentityProviderConfig', 'AssociateEncryptionConfig', 'AddonUpdate', 'VpcConfigUpdate', 'AccessConfigUpdate', 'UpgradePolicyUpdate', 'ZonalShiftConfigUpdate', 'AutoModeUpdate', 'RemoteNetworkConfigUpdate', ], ], 'UpgradePolicyRequest' => [ 'type' => 'structure', 'members' => [ 'supportType' => [ 'shape' => 'SupportType', ], ], ], 'UpgradePolicyResponse' => [ 'type' => 'structure', 'members' => [ 'supportType' => [ 'shape' => 'SupportType', ], ], ], 'VersionStatus' => [ 'type' => 'string', 'enum' => [ 'UNSUPPORTED', 'STANDARD_SUPPORT', 'EXTENDED_SUPPORT', ], ], 'VpcConfigRequest' => [ 'type' => 'structure', 'members' => [ 'subnetIds' => [ 'shape' => 'StringList', ], 'securityGroupIds' => [ 'shape' => 'StringList', ], 'endpointPublicAccess' => [ 'shape' => 'BoxedBoolean', ], 'endpointPrivateAccess' => [ 'shape' => 'BoxedBoolean', ], 'publicAccessCidrs' => [ 'shape' => 'StringList', ], ], ], 'VpcConfigResponse' => [ 'type' => 'structure', 'members' => [ 'subnetIds' => [ 'shape' => 'StringList', ], 'securityGroupIds' => [ 'shape' => 'StringList', ], 'clusterSecurityGroupId' => [ 'shape' => 'String', ], 'vpcId' => [ 'shape' => 'String', ], 'endpointPublicAccess' => [ 'shape' => 'Boolean', ], 'endpointPrivateAccess' => [ 'shape' => 'Boolean', ], 'publicAccessCidrs' => [ 'shape' => 'StringList', ], ], ], 'ZeroCapacity' => [ 'type' => 'integer', 'box' => true, 'min' => 0, ], 'ZonalShiftConfigRequest' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'BoxedBoolean', ], ], ], 'ZonalShiftConfigResponse' => [ 'type' => 'structure', 'members' => [ 'enabled' => [ 'shape' => 'BoxedBoolean', ], ], ], 'configStatus' => [ 'type' => 'string', 'enum' => [ 'CREATING', 'DELETING', 'ACTIVE', ], ], 'labelKey' => [ 'type' => 'string', 'max' => 63, 'min' => 1, ], 'labelValue' => [ 'type' => 'string', 'max' => 63, 'min' => 1, ], 'labelsKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'labelsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'labelKey', ], 'value' => [ 'shape' => 'labelValue', ], ], 'requiredClaimsKey' => [ 'type' => 'string', 'max' => 63, 'min' => 1, ], 'requiredClaimsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'requiredClaimsKey', ], 'value' => [ 'shape' => 'requiredClaimsValue', ], ], 'requiredClaimsValue' => [ 'type' => 'string', 'max' => 253, 'min' => 1, ], 'taintKey' => [ 'type' => 'string', 'max' => 63, 'min' => 1, ], 'taintValue' => [ 'type' => 'string', 'max' => 63, 'min' => 0, ], 'taintsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Taint', ], ], ],];
