<?php
// This file was auto-generated from sdk-root/src/data/bedrock-agent-runtime/2023-07-26/paginators-1.json
return [ 'pagination' => [ 'GetAgentMemory' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxItems', 'result_key' => 'memoryContents', ], 'ListFlowExecutionEvents' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'flowExecutionEvents', ], 'ListFlowExecutions' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'flowExecutionSummaries', ], 'ListInvocationSteps' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'invocationStepSummaries', ], 'ListInvocations' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'invocationSummaries', ], 'ListSessions' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'sessionSummaries', ], 'Rerank' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'result_key' => 'results', ], 'Retrieve' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'result_key' => 'retrievalResults', ], ],];
