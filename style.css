/* Polymorphism Professional IT Startup Styles */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* CSS Variables untuk Design System */
:root {
    /* Primary Colors - Tech Blue Palette */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Secondary Colors - Purple Accent */
    --secondary-50: #faf5ff;
    --secondary-100: #f3e8ff;
    --secondary-200: #e9d5ff;
    --secondary-300: #d8b4fe;
    --secondary-400: #c084fc;
    --secondary-500: #a855f7;
    --secondary-600: #9333ea;
    --secondary-700: #7c3aed;
    --secondary-800: #6b21a8;
    --secondary-900: #581c87;

    /* Neutral Colors */
    --neutral-50: #f8fafc;
    --neutral-100: #f1f5f9;
    --neutral-200: #e2e8f0;
    --neutral-300: #cbd5e1;
    --neutral-400: #94a3b8;
    --neutral-500: #64748b;
    --neutral-600: #475569;
    --neutral-700: #334155;
    --neutral-800: #1e293b;
    --neutral-900: #0f172a;

    /* Accent Colors */
    --success: #10b981;
    --success-dim: #04895f;
    --warning: #f59e0b;
    --warning-dim: #b49c03;
    --error: #ef4444;
    --info: #06b6d4;
    --info-dim: #0087c5;

    /* Glassmorphism */
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

    /* Animations */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background: linear-gradient(135deg,
            var(--primary-50) 0%,
            var(--secondary-50) 50%,
            var(--neutral-50) 100%);
    background-attachment: fixed;
    color: var(--neutral-800);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* Header Styling */
.container h1 {
    background: linear-gradient(135deg, var(--primary-700), var(--secondary-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    letter-spacing: -0.025em;
    text-align: center;
    margin-bottom: 0.5rem;
}

.container p {
    font-weight: 500;
}

/* Progress Bar Container */
.progress-container {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    border-radius: 1rem;
    box-shadow: var(--shadow-lg);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
    border-radius: 9999px;
    height: 0.5rem;
    transition: width var(--transition-normal);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    border-radius: inherit;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

/* Step Container */
.step {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 1.5rem;
    box-shadow: var(--shadow-xl);
    padding: 2.5rem;
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    transition: all var(--transition-slow);
    position: relative;
    overflow: hidden;
}

.step::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 16px;
    background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
    border-radius: 1.5rem 1.5rem 0 0;
}

.step.active {
    opacity: 1;
    transform: translateY(0) scale(1);
}

/* Typography */
.step h2 {
    background: linear-gradient(135deg, var(--neutral-800), var(--neutral-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 1.875rem;
    margin-bottom: 1.5rem;
    letter-spacing: -0.025em;
}

/* Information Box */
.info-box {
    background: linear-gradient(135deg, var(--primary-50), var(--secondary-50));
    border-left: 8px solid var(--primary-500);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-top: 1rem;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

.info-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.1));
    pointer-events: none;
}

.info-box p {
    margin: 0;
    color: var(--neutral-700);
    font-weight: 500;
    line-height: 1.6;
}

/* Form Controls */
label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
}

/* Input Fields */
input[type="text"],
input[type="email"],
input[type="number"],
textarea,
select {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--neutral-200);
    border-radius: 0.75rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(8px);
    font-family: var(--font-primary);
    font-size: 0.9rem;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

select option {
    color: black;
    background: white;
}

input[type="text"]:focus,
input[type="email"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow-md);
    background: rgba(255, 255, 255, 0.3);
}

/* Radio Button Options */
.radio-option {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    border: 2px solid var(--neutral-200);
    border-radius: 0.75rem;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(8px);
    cursor: pointer;
    transition: all var(--transition-normal);
    margin-bottom: 0.75rem;
    position: relative;
    overflow: hidden;
}

.radio-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left var(--transition-normal);
}

.radio-option:hover::before {
    left: 100%;
}

.radio-option:hover {
    border-color: var(--primary-400);
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.radio-option input[type="radio"]:checked+span {
    color: var(--primary-700);
    font-weight: 600;
}

/* Custom Radio Buttons */
input[type="radio"] {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--neutral-300);
    border-radius: 50%;
    appearance: none;
    background: white;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

input[type="radio"]:checked {
    border-color: var(--primary-500);
    background: var(--primary-500);
    box-shadow: inset 0 0 0 3px white, 0 0 0 3px rgba(59, 130, 246, 0.1);
}

input[type="radio"]:hover {
    border-color: var(--primary-400);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Custom Checkboxes */
input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--neutral-300);
    border-radius: 0.375rem;
    appearance: none;
    background: white;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    flex-shrink: 0;
}

input[type="checkbox"]:checked {
    border-color: var(--primary-500);
    background: var(--primary-500);
}

input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.875rem;
    font-weight: bold;
}

input[type="checkbox"]:hover {
    border-color: var(--primary-400);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Checkbox Grid Layout */
.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px solid var(--neutral-200);
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(8px);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.checkbox-item:hover {
    border-color: var(--primary-400);
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.checkbox-item input[type="checkbox"]:checked+span {
    color: var(--primary-700);
    font-weight: 500;
}

/* Buttons */
button {
    font-family: var(--font-primary);
    font-weight: 600;
    padding: 0.875rem 1.5rem !important;
    border-radius: 0.75rem;
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: 0.9rem;
    letter-spacing: 0.025em;
    position: relative;
    overflow: hidden;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-normal);
}

button:hover::before {
    left: 100%;
}

/* Primary Button */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Secondary Button */
.btn-secondary {
    background: linear-gradient(135deg, var(--neutral-300), var(--neutral-400));
    color: var(--neutral-700);
    box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--neutral-400), var(--neutral-500));
    color: var(--neutral-800);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Success Button */
.btn-success {
    background: linear-gradient(135deg, var(--success), #059669);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* File Upload */
input[type="file"] {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 2px dashed var(--primary-300);
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all var(--transition-normal);
    cursor: pointer;
}

input[type="file"]:hover {
    border-color: var(--primary-500);
    background: rgba(59, 130, 246, 0.05);
}

/* Interactive File Upload Component */
.file-upload-container {
    margin-top: 1rem;
}

.file-upload-area {
    border: 2px dashed #e2e8f0;
    border-radius: 0.5rem;
    padding: 2rem;
    text-align: center;
    background-color: #f8fafc;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area.drag-over {
    border-color: #3b82f6;
    background-color: #eff6ff;
}

.file-upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.file-upload-icon {
    color: #94a3b8;
}

.file-upload-text {
    color: #64748b;
}

.file-upload-button {
    background-color: #3b82f6;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.file-upload-button:hover {
    background-color: #2563eb;
}

.file-input-hidden {
    display: none;
}

/* File Preview Styles */
.file-preview-container {
    margin-top: 1rem;
}

.file-preview-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.file-preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background-color: #f8fafc;
    border-radius: 0.375rem;
    border: 1px solid #e2e8f0;
}

.file-preview-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.file-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1e293b;
}

.file-size {
    font-size: 0.75rem;
    color: #64748b;
}

.file-preview-status {
    display: flex;
    align-items: center;
}

.file-preview-status.success {
    color: #22c55e;
}

.file-preview-status.error {
    color: #ef4444;
}

/* Upload Progress Styles */
.upload-progress-container {
    margin-top: 1rem;
}

.upload-progress-bar {
    width: 100%;
    height: 0.5rem;
    background-color: #e2e8f0;
    border-radius: 9999px;
    overflow: hidden;
}

.upload-progress-fill {
    height: 100%;
    background-color: #3b82f6;
    transition: width 0.3s ease;
}

.upload-progress-text {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #64748b;
    text-align: center;
}

/* Alert Styles */
.alert-warning {
    background-color: #fef3c7;
    border: 1px solid #fbbf24;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-top: 1rem;
}

.alert-warning p {
    color: #92400e;
    font-size: 0.875rem;
}

/* Button Styles */
.btn-primary {
    background-color: #3b82f6;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-secondary {
    background-color: #e2e8f0;
    color: #1e293b;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-secondary:hover {
    background-color: #cbd5e1;
}

.btn-success {
    background-color: #22c55e;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: background-color 0.3s ease;
}

.btn-success:hover {
    background-color: #16a34a;
}

/* Info Box Styles */
.info-box {
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

/* Radio Option Styles */
.radio-option {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.radio-option:hover {
    background-color: #f1f5f9;
}

.radio-option input[type="radio"] {
    margin-top: 0.25rem;
}

.radio-option span {
    flex: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    .step {
        padding: 3rem 1.5rem 1.5rem 1.5rem;
        border-radius: 1rem;
    }

    .step h2 {
        font-size: 1.5rem;
    }

    .grid,
    .checkbox-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem;
    }

    .flex.justify-between {
        flex-direction: column;
        gap: 1rem;
    }

    .flex.justify-between button {
        width: 100%;
    }

    .radio-option,
    .checkbox-item {
        padding: 0.875rem;
    }

    /* File Upload Responsive */
    .file-upload-content {
        padding: 2rem 1rem;
    }

    .file-preview-item {
        flex-direction: column;
        text-align: center;
    }

    .file-preview-icon,
    .file-preview-image {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .file-preview-remove {
        margin-left: 0;
        margin-top: 0.5rem;
    }
}

@media (max-width: 480px) {
    .step {
        padding: 2rem 1rem 1rem 1rem;
        margin: 0 0.5rem;
    }

    .progress-container {
        margin: 0 0.5rem 1rem;
        padding: 1rem;
    }

    .checkbox-grid {
        grid-template-columns: 1fr;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --glass-bg: rgba(0, 0, 0, 0.25);
        --glass-border: rgba(255, 255, 255, 0.1);
    }

    body {
        background: linear-gradient(135deg,
                var(--neutral-900) 0%,
                var(--primary-900) 50%,
                var(--secondary-900) 100%);
        color: var(--neutral-100);
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print styles */
@media print {
    body {
        background: white;
    }

    .step {
        background: white;
        box-shadow: none;
        border: 1px solid var(--neutral-300);
    }

    .progress-container,
    button {
        display: none;
    }
}