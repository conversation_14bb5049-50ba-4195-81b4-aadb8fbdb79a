/* Polymorphism Professional IT Startup Styles */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* CSS Variables untuk Design System */
:root {
    /* Primary Colors - Tech Blue Palette */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Secondary Colors - Purple Accent */
    --secondary-50: #faf5ff;
    --secondary-100: #f3e8ff;
    --secondary-200: #e9d5ff;
    --secondary-300: #d8b4fe;
    --secondary-400: #c084fc;
    --secondary-500: #a855f7;
    --secondary-600: #9333ea;
    --secondary-700: #7c3aed;
    --secondary-800: #6b21a8;
    --secondary-900: #581c87;

    /* Neutral Colors */
    --neutral-50: #f8fafc;
    --neutral-100: #f1f5f9;
    --neutral-200: #e2e8f0;
    --neutral-300: #cbd5e1;
    --neutral-400: #94a3b8;
    --neutral-500: #64748b;
    --neutral-600: #475569;
    --neutral-700: #334155;
    --neutral-800: #1e293b;
    --neutral-900: #0f172a;

    /* Accent Colors */
    --success: #10b981;
    --success-dim: #04895f;
    --warning: #f59e0b;
    --warning-dim: #b49c03;
    --error: #ef4444;
    --info: #06b6d4;
    --info-dim: #0087c5;

    /* Glassmorphism */
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

    /* Animations */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background: linear-gradient(135deg,
            var(--primary-50) 0%,
            var(--secondary-50) 50%,
            var(--neutral-50) 100%);
    background-attachment: fixed;
    color: var(--neutral-800);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow-x: hidden;
}

/* Header Styling */
.container h1 {
    background: linear-gradient(135deg, var(--primary-700), var(--secondary-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    letter-spacing: -0.025em;
    text-align: center;
    margin-bottom: 0.5rem;
}

.container p {
    font-weight: 500;
}

/* Progress Bar Container */
.progress-container {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    border-radius: 1rem;
    box-shadow: var(--shadow-lg);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
    border-radius: 9999px;
    height: 0.5rem;
    transition: width var(--transition-normal);
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4);
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    border-radius: inherit;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

/* Step Container */
.step {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 1.5rem;
    box-shadow: var(--shadow-xl);
    padding: 2.5rem;
    opacity: 0;
    transform: translateY(30px) scale(0.95);
    transition: all var(--transition-slow);
    position: relative;
    overflow: hidden;
}

.step::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 16px;
    background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
    border-radius: 1.5rem 1.5rem 0 0;
}

.step.active {
    opacity: 1;
    transform: translateY(0) scale(1);
}

/* Typography */
.step h2 {
    background: linear-gradient(135deg, var(--neutral-800), var(--neutral-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 1.875rem;
    margin-bottom: 1.5rem;
    letter-spacing: -0.025em;
}

/* Information Box */
.info-box {
    background: linear-gradient(135deg, var(--primary-50), var(--secondary-50));
    border-left: 8px solid var(--primary-500);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-top: 1rem;
    margin-bottom: 1.5rem;
    position: relative;
    overflow: hidden;
}

.info-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.1));
    pointer-events: none;
}

.info-box p {
    margin: 0;
    color: var(--neutral-700);
    font-weight: 500;
    line-height: 1.6;
}

/* Form Controls */
label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
    font-size: 0.875rem;
    letter-spacing: 0.025em;
}

/* Input Fields */
input[type="text"],
input[type="email"],
textarea,
select {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--neutral-200);
    border-radius: 0.75rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(8px);
    font-family: var(--font-primary);
    font-size: 0.9rem;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

select option {
    color: black;
    background: white;
}

input[type="text"]:focus,
input[type="email"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--shadow-md);
    background: rgba(255, 255, 255, 0.3);
}

/* Radio Button Options */
.radio-option {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    border: 2px solid var(--neutral-200);
    border-radius: 0.75rem;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(8px);
    cursor: pointer;
    transition: all var(--transition-normal);
    margin-bottom: 0.75rem;
    position: relative;
    overflow: hidden;
}

.radio-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left var(--transition-normal);
}

.radio-option:hover::before {
    left: 100%;
}

.radio-option:hover {
    border-color: var(--primary-400);
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.radio-option input[type="radio"]:checked+span {
    color: var(--primary-700);
    font-weight: 600;
}

/* Custom Radio Buttons */
input[type="radio"] {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--neutral-300);
    border-radius: 50%;
    appearance: none;
    background: white;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

input[type="radio"]:checked {
    border-color: var(--primary-500);
    background: var(--primary-500);
    box-shadow: inset 0 0 0 3px white, 0 0 0 3px rgba(59, 130, 246, 0.1);
}

input[type="radio"]:hover {
    border-color: var(--primary-400);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Custom Checkboxes */
input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--neutral-300);
    border-radius: 0.375rem;
    appearance: none;
    background: white;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    flex-shrink: 0;
}

input[type="checkbox"]:checked {
    border-color: var(--primary-500);
    background: var(--primary-500);
}

input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.875rem;
    font-weight: bold;
}

input[type="checkbox"]:hover {
    border-color: var(--primary-400);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Checkbox Grid Layout */
.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.checkbox-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px solid var(--neutral-200);
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(8px);
    cursor: pointer;
    transition: all var(--transition-normal);
}

.checkbox-item:hover {
    border-color: var(--primary-400);
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.checkbox-item input[type="checkbox"]:checked+span {
    color: var(--primary-700);
    font-weight: 500;
}

/* Buttons */
button {
    font-family: var(--font-primary);
    font-weight: 600;
    padding: 0.875rem 1.5rem !important;
    border-radius: 0.75rem;
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: 0.9rem;
    letter-spacing: 0.025em;
    position: relative;
    overflow: hidden;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-normal);
}

button:hover::before {
    left: 100%;
}

/* Primary Button */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Secondary Button */
.btn-secondary {
    background: linear-gradient(135deg, var(--neutral-300), var(--neutral-400));
    color: var(--neutral-700);
    box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--neutral-400), var(--neutral-500));
    color: var(--neutral-800);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Success Button */
.btn-success {
    background: linear-gradient(135deg, var(--success), #059669);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* File Upload */
input[type="file"] {
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 2px dashed var(--primary-300);
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all var(--transition-normal);
    cursor: pointer;
}

input[type="file"]:hover {
    border-color: var(--primary-500);
    background: rgba(59, 130, 246, 0.05);
}

/* Interactive File Upload Component */
.file-upload-container {
    margin-bottom: 1rem;
}

.file-upload-area {
    position: relative;
    border: 2px dashed var(--primary-300);
    border-radius: 1rem;
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    transition: all var(--transition-normal);
    cursor: pointer;
    overflow: hidden;
}

.file-upload-area:hover {
    border-color: var(--primary-500);
    background: rgba(59, 130, 246, 0.05);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.file-upload-area.drag-over {
    border-color: var(--primary-600);
    background: rgba(59, 130, 246, 0.1);
    transform: scale(1.02);
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
}

.file-upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    text-align: center;
}

.file-upload-icon {
    margin-bottom: 1rem;
    opacity: 0.7;
    transition: all var(--transition-normal);
}

.file-upload-area:hover .file-upload-icon {
    opacity: 1;
    transform: translateY(-4px);
}

.file-upload-text {
    margin-bottom: 1.5rem;
}

.file-upload-button {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    border: none;
    border-radius: 0.75rem;
    padding: 0.75rem 2rem;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.file-upload-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-normal);
}

.file-upload-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.file-upload-button:hover::before {
    left: 100%;
}

.file-input-hidden {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 10;
}

/* File Preview Styles */
.file-preview-container {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
}

.file-preview-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.file-preview-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid var(--neutral-200);
    border-radius: 0.75rem;
    transition: all var(--transition-normal);
    animation: slideInUp 0.3s ease-out;
}

.file-preview-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-preview-icon {
    flex-shrink: 0;
    width: 3rem;
    height: 3rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    color: white;
}

.file-preview-icon.pdf {
    background: linear-gradient(135deg, #dc2626, #ef4444);
}

.file-preview-icon.image {
    background: linear-gradient(135deg, #059669, #10b981);
}

.file-preview-icon.audio {
    background: linear-gradient(135deg, #7c3aed, #a855f7);
}

.file-preview-icon.video {
    background: linear-gradient(135deg, #ea580c, #f97316);
}

.file-preview-icon.document {
    background: linear-gradient(135deg, #2563eb, #3b82f6);
}

.file-preview-icon.spreadsheet {
    background: linear-gradient(135deg, #059669, #10b981);
}

.file-preview-icon.presentation {
    background: linear-gradient(135deg, #ea580c, #f97316);
}

.file-preview-info {
    flex: 1;
    min-width: 0;
}

.file-preview-name {
    font-weight: 600;
    color: var(--neutral-800);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-preview-size {
    font-size: 0.875rem;
    color: var(--neutral-600);
}

.file-preview-remove {
    flex-shrink: 0;
    width: 2rem;
    height: 2rem;
    border: none;
    padding: 0 !important;
    background: var(--error);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
    margin-left: 1rem;
}

.file-preview-remove:hover {
    background: #dc2626;
    transform: scale(1.1);
}

.file-preview-image {
    width: 3rem;
    height: 3rem;
    object-fit: cover;
    border-radius: 0.5rem;
    margin-right: 1rem;
    border: 2px solid var(--neutral-200);
}

/* Upload Progress Styles */
.upload-progress-container {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--glass-bg);
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    border-radius: 0.75rem;
}

.upload-progress-bar {
    width: 100%;
    height: 0.5rem;
    background: var(--neutral-200);
    border-radius: 9999px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.upload-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
    border-radius: 9999px;
    transition: width var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.upload-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    border-radius: inherit;
    animation: shimmer 2s infinite;
}

.upload-progress-text {
    font-size: 0.875rem;
    color: var(--neutral-600);
    text-align: center;
    margin: 0;
}

/* File Type Indicators */
.file-type-indicator {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background: var(--primary-100);
    color: var(--primary-700);
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    margin-left: 0.5rem;
}

/* Error States */
.file-upload-error {
    border-color: var(--error) !important;
    background: rgba(239, 68, 68, 0.05) !important;
}

.file-error-message {
    color: var(--error);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: rgba(239, 68, 68, 0.1);
    border-radius: 0.375rem;
    border-left: 3px solid var(--error);
}

/* Success States */
.file-upload-success {
    border-color: var(--success) !important;
    background: rgba(16, 185, 129, 0.05) !important;
}

.file-success-message {
    color: var(--success);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 0.375rem;
    border-left: 3px solid var(--success);
}

/* Alert Boxes */
.alert-info {
    background: white;
    border-left: 8px solid var(--info);
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    color: var(--info-dim);
    position: relative;
    overflow: hidden;
}

.alert-success {
    background: white;
    border-left: 8px solid var(--success);
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    color: var(--success-dim);
    position: relative;
    overflow: hidden;
}

.alert-warning {
    background: white;
    border-left: 8px solid var(--warning);
    border-radius: 0.75rem;
    padding: 1rem;
    margin-bottom: 1.5rem;
    color: var(--warning-dim);
    position: relative;
    overflow: hidden;
}

.alert-info::before,
.alert-success::before,
.alert-warning::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.1));
    pointer-events: none;
}

/* Success Message */
#successMessage {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 1.5rem;
    box-shadow: var(--shadow-2xl);
    animation: successAppear var(--transition-slow) cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes successAppear {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(30px);
    }

    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Loading States */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: translate(-50%, -50%) rotate(0deg);
    }

    100% {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    .step {
        padding: 3rem 1.5rem 1.5rem 1.5rem;
        border-radius: 1rem;
    }

    .step h2 {
        font-size: 1.5rem;
    }

    .grid,
    .checkbox-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem;
    }

    .flex.justify-between {
        flex-direction: column;
        gap: 1rem;
    }

    .flex.justify-between button {
        width: 100%;
    }

    .radio-option,
    .checkbox-item {
        padding: 0.875rem;
    }

    /* File Upload Responsive */
    .file-upload-content {
        padding: 2rem 1rem;
    }

    .file-preview-item {
        flex-direction: column;
        text-align: center;
    }

    .file-preview-icon,
    .file-preview-image {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }

    .file-preview-remove {
        margin-left: 0;
        margin-top: 0.5rem;
    }
}

@media (max-width: 480px) {
    .step {
        padding: 2rem 1rem 1rem 1rem;
        margin: 0 0.5rem;
    }

    .progress-container {
        margin: 0 0.5rem 1rem;
        padding: 1rem;
    }

    .checkbox-grid {
        grid-template-columns: 1fr;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --glass-bg: rgba(0, 0, 0, 0.25);
        --glass-border: rgba(255, 255, 255, 0.1);
    }

    body {
        background: linear-gradient(135deg,
                var(--neutral-900) 0%,
                var(--primary-900) 50%,
                var(--secondary-900) 100%);
        color: var(--neutral-100);
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Print styles */
@media print {
    body {
        background: white;
    }

    .step {
        background: white;
        box-shadow: none;
        border: 1px solid var(--neutral-300);
    }

    .progress-container,
    button {
        display: none;
    }
}