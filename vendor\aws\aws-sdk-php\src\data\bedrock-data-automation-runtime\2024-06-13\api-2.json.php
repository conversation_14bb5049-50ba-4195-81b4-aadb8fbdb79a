<?php
// This file was auto-generated from sdk-root/src/data/bedrock-data-automation-runtime/2024-06-13/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2024-06-13', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'bedrock-data-automation-runtime', 'jsonVersion' => '1.1', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'Runtime for Amazon Bedrock Data Automation', 'serviceId' => 'Bedrock Data Automation Runtime', 'signatureVersion' => 'v4', 'signingName' => 'bedrock', 'targetPrefix' => 'AmazonBedrockKeystoneRuntimeService', 'uid' => 'bedrock-data-automation-runtime-2024-06-13', ], 'operations' => [ 'GetDataAutomationStatus' => [ 'name' => 'GetDataAutomationStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetDataAutomationStatusRequest', ], 'output' => [ 'shape' => 'GetDataAutomationStatusResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'InvokeDataAutomationAsync' => [ 'name' => 'InvokeDataAutomationAsync', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'InvokeDataAutomationAsyncRequest', ], 'output' => [ 'shape' => 'InvokeDataAutomationAsyncResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], ], 'idempotent' => true, ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'exception' => true, ], 'AssetProcessingConfiguration' => [ 'type' => 'structure', 'members' => [ 'video' => [ 'shape' => 'VideoAssetProcessingConfiguration', ], ], ], 'AutomationJobStatus' => [ 'type' => 'string', 'enum' => [ 'Created', 'InProgress', 'Success', 'ServiceError', 'ClientError', ], ], 'Blueprint' => [ 'type' => 'structure', 'required' => [ 'blueprintArn', ], 'members' => [ 'blueprintArn' => [ 'shape' => 'BlueprintArn', ], 'version' => [ 'shape' => 'BlueprintVersion', ], 'stage' => [ 'shape' => 'BlueprintStage', ], ], ], 'BlueprintArn' => [ 'type' => 'string', 'max' => 128, 'min' => 0, 'pattern' => 'arn:aws(|-cn|-us-gov):bedrock:[a-zA-Z0-9-]*:(aws|[0-9]{12}):blueprint/(bedrock-data-insights-public-[a-zA-Z0-9-_]{1,30}|bedrock-data-automation-public-[a-zA-Z0-9-_]{1,30}|[a-zA-Z0-9-]{12,36})', ], 'BlueprintList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Blueprint', ], 'max' => 40, 'min' => 1, ], 'BlueprintStage' => [ 'type' => 'string', 'enum' => [ 'DEVELOPMENT', 'LIVE', ], ], 'BlueprintVersion' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[0-9]*', ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'DataAutomationArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:aws(|-cn|-us-gov):bedrock:[a-zA-Z0-9-]*:(aws|[0-9]{12}):data-automation-project/[a-zA-Z0-9-_]+', ], 'DataAutomationConfiguration' => [ 'type' => 'structure', 'required' => [ 'dataAutomationProjectArn', ], 'members' => [ 'dataAutomationProjectArn' => [ 'shape' => 'DataAutomationArn', ], 'stage' => [ 'shape' => 'DataAutomationStage', ], ], ], 'DataAutomationProfileArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:aws(|-cn|-us-gov):bedrock:[a-zA-Z0-9-]*:(aws|[0-9]{12}):data-automation-profile/[a-zA-Z0-9-_.]+', ], 'DataAutomationStage' => [ 'type' => 'string', 'enum' => [ 'LIVE', 'DEVELOPMENT', ], ], 'EncryptionConfiguration' => [ 'type' => 'structure', 'required' => [ 'kmsKeyId', ], 'members' => [ 'kmsKeyId' => [ 'shape' => 'KMSKeyId', ], 'kmsEncryptionContext' => [ 'shape' => 'EncryptionContextMap', ], ], ], 'EncryptionContextKey' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => '.*\\S.*', ], 'EncryptionContextMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'EncryptionContextKey', ], 'value' => [ 'shape' => 'EncryptionContextValue', ], 'max' => 10, 'min' => 1, ], 'EncryptionContextValue' => [ 'type' => 'string', 'max' => 2000, 'min' => 1, 'pattern' => '.*\\S.*', ], 'EventBridgeConfiguration' => [ 'type' => 'structure', 'required' => [ 'eventBridgeEnabled', ], 'members' => [ 'eventBridgeEnabled' => [ 'shape' => 'Boolean', ], ], ], 'GetDataAutomationStatusRequest' => [ 'type' => 'structure', 'required' => [ 'invocationArn', ], 'members' => [ 'invocationArn' => [ 'shape' => 'InvocationArn', ], ], ], 'GetDataAutomationStatusResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'AutomationJobStatus', ], 'errorType' => [ 'shape' => 'String', ], 'errorMessage' => [ 'shape' => 'String', ], 'outputConfiguration' => [ 'shape' => 'OutputConfiguration', ], ], ], 'IdempotencyToken' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'pattern' => '[a-zA-Z0-9](-*[a-zA-Z0-9])*', ], 'InputConfiguration' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3Uri' => [ 'shape' => 'S3Uri', ], 'assetProcessingConfiguration' => [ 'shape' => 'AssetProcessingConfiguration', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'exception' => true, 'fault' => true, ], 'InvocationArn' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => 'arn:aws(|-cn|-us-gov):bedrock:[a-zA-Z0-9-]*:[0-9]{12}:(insights-invocation|data-automation-invocation)/[a-zA-Z0-9-_]+', ], 'InvokeDataAutomationAsyncRequest' => [ 'type' => 'structure', 'required' => [ 'inputConfiguration', 'outputConfiguration', 'dataAutomationProfileArn', ], 'members' => [ 'clientToken' => [ 'shape' => 'IdempotencyToken', 'idempotencyToken' => true, ], 'inputConfiguration' => [ 'shape' => 'InputConfiguration', ], 'outputConfiguration' => [ 'shape' => 'OutputConfiguration', ], 'dataAutomationConfiguration' => [ 'shape' => 'DataAutomationConfiguration', ], 'encryptionConfiguration' => [ 'shape' => 'EncryptionConfiguration', ], 'notificationConfiguration' => [ 'shape' => 'NotificationConfiguration', ], 'blueprints' => [ 'shape' => 'BlueprintList', ], 'dataAutomationProfileArn' => [ 'shape' => 'DataAutomationProfileArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'InvokeDataAutomationAsyncResponse' => [ 'type' => 'structure', 'required' => [ 'invocationArn', ], 'members' => [ 'invocationArn' => [ 'shape' => 'InvocationArn', ], ], ], 'KMSKeyId' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[A-Za-z0-9][A-Za-z0-9:_/+=,@.-]+', ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TaggableResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'tags' => [ 'shape' => 'TagList', ], ], ], 'NonBlankString' => [ 'type' => 'string', 'pattern' => '[\\s\\S]*', ], 'NotificationConfiguration' => [ 'type' => 'structure', 'required' => [ 'eventBridgeConfiguration', ], 'members' => [ 'eventBridgeConfiguration' => [ 'shape' => 'EventBridgeConfiguration', ], ], ], 'OutputConfiguration' => [ 'type' => 'structure', 'required' => [ 's3Uri', ], 'members' => [ 's3Uri' => [ 'shape' => 'S3Uri', ], ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'exception' => true, ], 'S3Uri' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 's3://[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9](/[^\\x00-\\x1F\\x7F\\{^}%`\\]">\\[~<#|]*)?', ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'key', 'value', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '(?!aws:)[\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tags', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TaggableResourceArn', ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)', ], 'TaggableResourceArn' => [ 'type' => 'string', 'max' => 1011, 'min' => 20, 'pattern' => 'arn:aws(|-cn|-us-gov):bedrock:[a-zA-Z0-9-]*:[0-9]{12}:data-automation-invocation/[a-zA-Z0-9-_]+', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'exception' => true, ], 'TimestampSegment' => [ 'type' => 'structure', 'required' => [ 'startTimeMillis', 'endTimeMillis', ], 'members' => [ 'startTimeMillis' => [ 'shape' => 'TimestampSegmentStartTimeMillisLong', ], 'endTimeMillis' => [ 'shape' => 'TimestampSegmentEndTimeMillisLong', ], ], ], 'TimestampSegmentEndTimeMillisLong' => [ 'type' => 'long', 'box' => true, 'min' => 300000, ], 'TimestampSegmentStartTimeMillisLong' => [ 'type' => 'long', 'box' => true, 'min' => 0, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceARN', 'tagKeys', ], 'members' => [ 'resourceARN' => [ 'shape' => 'TaggableResourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'NonBlankString', ], ], 'exception' => true, ], 'VideoAssetProcessingConfiguration' => [ 'type' => 'structure', 'members' => [ 'segmentConfiguration' => [ 'shape' => 'VideoSegmentConfiguration', ], ], ], 'VideoSegmentConfiguration' => [ 'type' => 'structure', 'members' => [ 'timestampSegment' => [ 'shape' => 'TimestampSegment', ], ], 'union' => true, ], ],];
