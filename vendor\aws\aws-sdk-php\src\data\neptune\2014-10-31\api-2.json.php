<?php
// This file was auto-generated from sdk-root/src/data/neptune/2014-10-31/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2014-10-31', 'endpointPrefix' => 'rds', 'protocol' => 'query', 'protocols' => [ 'query', ], 'serviceAbbreviation' => 'Amazon Neptune', 'serviceFullName' => 'Amazon Neptune', 'serviceId' => 'Neptune', 'signatureVersion' => 'v4', 'signingName' => 'rds', 'uid' => 'neptune-2014-10-31', 'xmlNamespace' => 'http://rds.amazonaws.com/doc/2014-10-31/', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AddRoleToDBCluster' => [ 'name' => 'AddRoleToDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddRoleToDBClusterMessage', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBClusterRoleAlreadyExistsFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBClusterRoleQuotaExceededFault', ], ], ], 'AddSourceIdentifierToSubscription' => [ 'name' => 'AddSourceIdentifierToSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddSourceIdentifierToSubscriptionMessage', ], 'output' => [ 'shape' => 'AddSourceIdentifierToSubscriptionResult', 'resultWrapper' => 'AddSourceIdentifierToSubscriptionResult', ], 'errors' => [ [ 'shape' => 'SubscriptionNotFoundFault', ], [ 'shape' => 'SourceNotFoundFault', ], ], ], 'AddTagsToResource' => [ 'name' => 'AddTagsToResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'AddTagsToResourceMessage', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBSnapshotNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'ApplyPendingMaintenanceAction' => [ 'name' => 'ApplyPendingMaintenanceAction', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ApplyPendingMaintenanceActionMessage', ], 'output' => [ 'shape' => 'ApplyPendingMaintenanceActionResult', 'resultWrapper' => 'ApplyPendingMaintenanceActionResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'CopyDBClusterParameterGroup' => [ 'name' => 'CopyDBClusterParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopyDBClusterParameterGroupMessage', ], 'output' => [ 'shape' => 'CopyDBClusterParameterGroupResult', 'resultWrapper' => 'CopyDBClusterParameterGroupResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'DBParameterGroupQuotaExceededFault', ], [ 'shape' => 'DBParameterGroupAlreadyExistsFault', ], ], ], 'CopyDBClusterSnapshot' => [ 'name' => 'CopyDBClusterSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopyDBClusterSnapshotMessage', ], 'output' => [ 'shape' => 'CopyDBClusterSnapshotResult', 'resultWrapper' => 'CopyDBClusterSnapshotResult', ], 'errors' => [ [ 'shape' => 'DBClusterSnapshotAlreadyExistsFault', ], [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBClusterSnapshotStateFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], ], ], 'CopyDBParameterGroup' => [ 'name' => 'CopyDBParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CopyDBParameterGroupMessage', ], 'output' => [ 'shape' => 'CopyDBParameterGroupResult', 'resultWrapper' => 'CopyDBParameterGroupResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'DBParameterGroupAlreadyExistsFault', ], [ 'shape' => 'DBParameterGroupQuotaExceededFault', ], ], ], 'CreateDBCluster' => [ 'name' => 'CreateDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBClusterMessage', ], 'output' => [ 'shape' => 'CreateDBClusterResult', 'resultWrapper' => 'CreateDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterAlreadyExistsFault', ], [ 'shape' => 'InsufficientStorageClusterCapacityFault', ], [ 'shape' => 'DBClusterQuotaExceededFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBSubnetGroupStateFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'DBClusterParameterGroupNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'GlobalClusterNotFoundFault', ], [ 'shape' => 'InvalidGlobalClusterStateFault', ], ], ], 'CreateDBClusterEndpoint' => [ 'name' => 'CreateDBClusterEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBClusterEndpointMessage', ], 'output' => [ 'shape' => 'CreateDBClusterEndpointOutput', 'resultWrapper' => 'CreateDBClusterEndpointResult', ], 'errors' => [ [ 'shape' => 'DBClusterEndpointQuotaExceededFault', ], [ 'shape' => 'DBClusterEndpointAlreadyExistsFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'CreateDBClusterParameterGroup' => [ 'name' => 'CreateDBClusterParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBClusterParameterGroupMessage', ], 'output' => [ 'shape' => 'CreateDBClusterParameterGroupResult', 'resultWrapper' => 'CreateDBClusterParameterGroupResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupQuotaExceededFault', ], [ 'shape' => 'DBParameterGroupAlreadyExistsFault', ], ], ], 'CreateDBClusterSnapshot' => [ 'name' => 'CreateDBClusterSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBClusterSnapshotMessage', ], 'output' => [ 'shape' => 'CreateDBClusterSnapshotResult', 'resultWrapper' => 'CreateDBClusterSnapshotResult', ], 'errors' => [ [ 'shape' => 'DBClusterSnapshotAlreadyExistsFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'InvalidDBClusterSnapshotStateFault', ], ], ], 'CreateDBInstance' => [ 'name' => 'CreateDBInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBInstanceMessage', ], 'output' => [ 'shape' => 'CreateDBInstanceResult', 'resultWrapper' => 'CreateDBInstanceResult', ], 'errors' => [ [ 'shape' => 'DBInstanceAlreadyExistsFault', ], [ 'shape' => 'InsufficientDBInstanceCapacityFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'DBSecurityGroupNotFoundFault', ], [ 'shape' => 'InstanceQuotaExceededFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'DBSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'ProvisionedIopsNotAvailableInAZFault', ], [ 'shape' => 'OptionGroupNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'StorageTypeNotSupportedFault', ], [ 'shape' => 'AuthorizationNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'DomainNotFoundFault', ], ], ], 'CreateDBParameterGroup' => [ 'name' => 'CreateDBParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBParameterGroupMessage', ], 'output' => [ 'shape' => 'CreateDBParameterGroupResult', 'resultWrapper' => 'CreateDBParameterGroupResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupQuotaExceededFault', ], [ 'shape' => 'DBParameterGroupAlreadyExistsFault', ], ], ], 'CreateDBSubnetGroup' => [ 'name' => 'CreateDBSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateDBSubnetGroupMessage', ], 'output' => [ 'shape' => 'CreateDBSubnetGroupResult', 'resultWrapper' => 'CreateDBSubnetGroupResult', ], 'errors' => [ [ 'shape' => 'DBSubnetGroupAlreadyExistsFault', ], [ 'shape' => 'DBSubnetGroupQuotaExceededFault', ], [ 'shape' => 'DBSubnetQuotaExceededFault', ], [ 'shape' => 'DBSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidSubnet', ], ], ], 'CreateEventSubscription' => [ 'name' => 'CreateEventSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateEventSubscriptionMessage', ], 'output' => [ 'shape' => 'CreateEventSubscriptionResult', 'resultWrapper' => 'CreateEventSubscriptionResult', ], 'errors' => [ [ 'shape' => 'EventSubscriptionQuotaExceededFault', ], [ 'shape' => 'SubscriptionAlreadyExistFault', ], [ 'shape' => 'SNSInvalidTopicFault', ], [ 'shape' => 'SNSNoAuthorizationFault', ], [ 'shape' => 'SNSTopicArnNotFoundFault', ], [ 'shape' => 'SubscriptionCategoryNotFoundFault', ], [ 'shape' => 'SourceNotFoundFault', ], ], ], 'CreateGlobalCluster' => [ 'name' => 'CreateGlobalCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateGlobalClusterMessage', ], 'output' => [ 'shape' => 'CreateGlobalClusterResult', 'resultWrapper' => 'CreateGlobalClusterResult', ], 'errors' => [ [ 'shape' => 'GlobalClusterAlreadyExistsFault', ], [ 'shape' => 'GlobalClusterQuotaExceededFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'DeleteDBCluster' => [ 'name' => 'DeleteDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBClusterMessage', ], 'output' => [ 'shape' => 'DeleteDBClusterResult', 'resultWrapper' => 'DeleteDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBClusterSnapshotAlreadyExistsFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'InvalidDBClusterSnapshotStateFault', ], ], ], 'DeleteDBClusterEndpoint' => [ 'name' => 'DeleteDBClusterEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBClusterEndpointMessage', ], 'output' => [ 'shape' => 'DeleteDBClusterEndpointOutput', 'resultWrapper' => 'DeleteDBClusterEndpointResult', ], 'errors' => [ [ 'shape' => 'InvalidDBClusterEndpointStateFault', ], [ 'shape' => 'DBClusterEndpointNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], ], ], 'DeleteDBClusterParameterGroup' => [ 'name' => 'DeleteDBClusterParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBClusterParameterGroupMessage', ], 'errors' => [ [ 'shape' => 'InvalidDBParameterGroupStateFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'DeleteDBClusterSnapshot' => [ 'name' => 'DeleteDBClusterSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBClusterSnapshotMessage', ], 'output' => [ 'shape' => 'DeleteDBClusterSnapshotResult', 'resultWrapper' => 'DeleteDBClusterSnapshotResult', ], 'errors' => [ [ 'shape' => 'InvalidDBClusterSnapshotStateFault', ], [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], ], ], 'DeleteDBInstance' => [ 'name' => 'DeleteDBInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBInstanceMessage', ], 'output' => [ 'shape' => 'DeleteDBInstanceResult', 'resultWrapper' => 'DeleteDBInstanceResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'DBSnapshotAlreadyExistsFault', ], [ 'shape' => 'SnapshotQuotaExceededFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], ], ], 'DeleteDBParameterGroup' => [ 'name' => 'DeleteDBParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBParameterGroupMessage', ], 'errors' => [ [ 'shape' => 'InvalidDBParameterGroupStateFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'DeleteDBSubnetGroup' => [ 'name' => 'DeleteDBSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteDBSubnetGroupMessage', ], 'errors' => [ [ 'shape' => 'InvalidDBSubnetGroupStateFault', ], [ 'shape' => 'InvalidDBSubnetStateFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], ], ], 'DeleteEventSubscription' => [ 'name' => 'DeleteEventSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteEventSubscriptionMessage', ], 'output' => [ 'shape' => 'DeleteEventSubscriptionResult', 'resultWrapper' => 'DeleteEventSubscriptionResult', ], 'errors' => [ [ 'shape' => 'SubscriptionNotFoundFault', ], [ 'shape' => 'InvalidEventSubscriptionStateFault', ], ], ], 'DeleteGlobalCluster' => [ 'name' => 'DeleteGlobalCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteGlobalClusterMessage', ], 'output' => [ 'shape' => 'DeleteGlobalClusterResult', 'resultWrapper' => 'DeleteGlobalClusterResult', ], 'errors' => [ [ 'shape' => 'GlobalClusterNotFoundFault', ], [ 'shape' => 'InvalidGlobalClusterStateFault', ], ], ], 'DescribeDBClusterEndpoints' => [ 'name' => 'DescribeDBClusterEndpoints', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBClusterEndpointsMessage', ], 'output' => [ 'shape' => 'DBClusterEndpointMessage', 'resultWrapper' => 'DescribeDBClusterEndpointsResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'DescribeDBClusterParameterGroups' => [ 'name' => 'DescribeDBClusterParameterGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBClusterParameterGroupsMessage', ], 'output' => [ 'shape' => 'DBClusterParameterGroupsMessage', 'resultWrapper' => 'DescribeDBClusterParameterGroupsResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'DescribeDBClusterParameters' => [ 'name' => 'DescribeDBClusterParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBClusterParametersMessage', ], 'output' => [ 'shape' => 'DBClusterParameterGroupDetails', 'resultWrapper' => 'DescribeDBClusterParametersResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'DescribeDBClusterSnapshotAttributes' => [ 'name' => 'DescribeDBClusterSnapshotAttributes', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBClusterSnapshotAttributesMessage', ], 'output' => [ 'shape' => 'DescribeDBClusterSnapshotAttributesResult', 'resultWrapper' => 'DescribeDBClusterSnapshotAttributesResult', ], 'errors' => [ [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], ], ], 'DescribeDBClusterSnapshots' => [ 'name' => 'DescribeDBClusterSnapshots', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBClusterSnapshotsMessage', ], 'output' => [ 'shape' => 'DBClusterSnapshotMessage', 'resultWrapper' => 'DescribeDBClusterSnapshotsResult', ], 'errors' => [ [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], ], ], 'DescribeDBClusters' => [ 'name' => 'DescribeDBClusters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBClustersMessage', ], 'output' => [ 'shape' => 'DBClusterMessage', 'resultWrapper' => 'DescribeDBClustersResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'DescribeDBEngineVersions' => [ 'name' => 'DescribeDBEngineVersions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBEngineVersionsMessage', ], 'output' => [ 'shape' => 'DBEngineVersionMessage', 'resultWrapper' => 'DescribeDBEngineVersionsResult', ], ], 'DescribeDBInstances' => [ 'name' => 'DescribeDBInstances', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBInstancesMessage', ], 'output' => [ 'shape' => 'DBInstanceMessage', 'resultWrapper' => 'DescribeDBInstancesResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], ], ], 'DescribeDBParameterGroups' => [ 'name' => 'DescribeDBParameterGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBParameterGroupsMessage', ], 'output' => [ 'shape' => 'DBParameterGroupsMessage', 'resultWrapper' => 'DescribeDBParameterGroupsResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'DescribeDBParameters' => [ 'name' => 'DescribeDBParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBParametersMessage', ], 'output' => [ 'shape' => 'DBParameterGroupDetails', 'resultWrapper' => 'DescribeDBParametersResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'DescribeDBSubnetGroups' => [ 'name' => 'DescribeDBSubnetGroups', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeDBSubnetGroupsMessage', ], 'output' => [ 'shape' => 'DBSubnetGroupMessage', 'resultWrapper' => 'DescribeDBSubnetGroupsResult', ], 'errors' => [ [ 'shape' => 'DBSubnetGroupNotFoundFault', ], ], ], 'DescribeEngineDefaultClusterParameters' => [ 'name' => 'DescribeEngineDefaultClusterParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEngineDefaultClusterParametersMessage', ], 'output' => [ 'shape' => 'DescribeEngineDefaultClusterParametersResult', 'resultWrapper' => 'DescribeEngineDefaultClusterParametersResult', ], ], 'DescribeEngineDefaultParameters' => [ 'name' => 'DescribeEngineDefaultParameters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEngineDefaultParametersMessage', ], 'output' => [ 'shape' => 'DescribeEngineDefaultParametersResult', 'resultWrapper' => 'DescribeEngineDefaultParametersResult', ], ], 'DescribeEventCategories' => [ 'name' => 'DescribeEventCategories', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventCategoriesMessage', ], 'output' => [ 'shape' => 'EventCategoriesMessage', 'resultWrapper' => 'DescribeEventCategoriesResult', ], ], 'DescribeEventSubscriptions' => [ 'name' => 'DescribeEventSubscriptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventSubscriptionsMessage', ], 'output' => [ 'shape' => 'EventSubscriptionsMessage', 'resultWrapper' => 'DescribeEventSubscriptionsResult', ], 'errors' => [ [ 'shape' => 'SubscriptionNotFoundFault', ], ], ], 'DescribeEvents' => [ 'name' => 'DescribeEvents', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeEventsMessage', ], 'output' => [ 'shape' => 'EventsMessage', 'resultWrapper' => 'DescribeEventsResult', ], ], 'DescribeGlobalClusters' => [ 'name' => 'DescribeGlobalClusters', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeGlobalClustersMessage', ], 'output' => [ 'shape' => 'GlobalClustersMessage', 'resultWrapper' => 'DescribeGlobalClustersResult', ], 'errors' => [ [ 'shape' => 'GlobalClusterNotFoundFault', ], ], ], 'DescribeOrderableDBInstanceOptions' => [ 'name' => 'DescribeOrderableDBInstanceOptions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeOrderableDBInstanceOptionsMessage', ], 'output' => [ 'shape' => 'OrderableDBInstanceOptionsMessage', 'resultWrapper' => 'DescribeOrderableDBInstanceOptionsResult', ], ], 'DescribePendingMaintenanceActions' => [ 'name' => 'DescribePendingMaintenanceActions', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribePendingMaintenanceActionsMessage', ], 'output' => [ 'shape' => 'PendingMaintenanceActionsMessage', 'resultWrapper' => 'DescribePendingMaintenanceActionsResult', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundFault', ], ], ], 'DescribeValidDBInstanceModifications' => [ 'name' => 'DescribeValidDBInstanceModifications', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeValidDBInstanceModificationsMessage', ], 'output' => [ 'shape' => 'DescribeValidDBInstanceModificationsResult', 'resultWrapper' => 'DescribeValidDBInstanceModificationsResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'FailoverDBCluster' => [ 'name' => 'FailoverDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'FailoverDBClusterMessage', ], 'output' => [ 'shape' => 'FailoverDBClusterResult', 'resultWrapper' => 'FailoverDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'FailoverGlobalCluster' => [ 'name' => 'FailoverGlobalCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'FailoverGlobalClusterMessage', ], 'output' => [ 'shape' => 'FailoverGlobalClusterResult', 'resultWrapper' => 'FailoverGlobalClusterResult', ], 'errors' => [ [ 'shape' => 'GlobalClusterNotFoundFault', ], [ 'shape' => 'InvalidGlobalClusterStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceMessage', ], 'output' => [ 'shape' => 'TagListMessage', 'resultWrapper' => 'ListTagsForResourceResult', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBSnapshotNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'ModifyDBCluster' => [ 'name' => 'ModifyDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBClusterMessage', ], 'output' => [ 'shape' => 'ModifyDBClusterResult', 'resultWrapper' => 'ModifyDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'InvalidDBSubnetGroupStateFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'DBClusterParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidDBSecurityGroupStateFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'DBClusterAlreadyExistsFault', ], [ 'shape' => 'StorageTypeNotSupportedFault', ], ], ], 'ModifyDBClusterEndpoint' => [ 'name' => 'ModifyDBClusterEndpoint', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBClusterEndpointMessage', ], 'output' => [ 'shape' => 'ModifyDBClusterEndpointOutput', 'resultWrapper' => 'ModifyDBClusterEndpointResult', ], 'errors' => [ [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBClusterEndpointStateFault', ], [ 'shape' => 'DBClusterEndpointNotFoundFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'ModifyDBClusterParameterGroup' => [ 'name' => 'ModifyDBClusterParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBClusterParameterGroupMessage', ], 'output' => [ 'shape' => 'DBClusterParameterGroupNameMessage', 'resultWrapper' => 'ModifyDBClusterParameterGroupResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidDBParameterGroupStateFault', ], ], ], 'ModifyDBClusterSnapshotAttribute' => [ 'name' => 'ModifyDBClusterSnapshotAttribute', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBClusterSnapshotAttributeMessage', ], 'output' => [ 'shape' => 'ModifyDBClusterSnapshotAttributeResult', 'resultWrapper' => 'ModifyDBClusterSnapshotAttributeResult', ], 'errors' => [ [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], [ 'shape' => 'InvalidDBClusterSnapshotStateFault', ], [ 'shape' => 'SharedSnapshotQuotaExceededFault', ], ], ], 'ModifyDBInstance' => [ 'name' => 'ModifyDBInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBInstanceMessage', ], 'output' => [ 'shape' => 'ModifyDBInstanceResult', 'resultWrapper' => 'ModifyDBInstanceResult', ], 'errors' => [ [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'InvalidDBSecurityGroupStateFault', ], [ 'shape' => 'DBInstanceAlreadyExistsFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBSecurityGroupNotFoundFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'InsufficientDBInstanceCapacityFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'ProvisionedIopsNotAvailableInAZFault', ], [ 'shape' => 'OptionGroupNotFoundFault', ], [ 'shape' => 'DBUpgradeDependencyFailureFault', ], [ 'shape' => 'StorageTypeNotSupportedFault', ], [ 'shape' => 'AuthorizationNotFoundFault', ], [ 'shape' => 'CertificateNotFoundFault', ], [ 'shape' => 'DomainNotFoundFault', ], ], ], 'ModifyDBParameterGroup' => [ 'name' => 'ModifyDBParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBParameterGroupMessage', ], 'output' => [ 'shape' => 'DBParameterGroupNameMessage', 'resultWrapper' => 'ModifyDBParameterGroupResult', ], 'errors' => [ [ 'shape' => 'DBParameterGroupNotFoundFault', ], [ 'shape' => 'InvalidDBParameterGroupStateFault', ], ], ], 'ModifyDBSubnetGroup' => [ 'name' => 'ModifyDBSubnetGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyDBSubnetGroupMessage', ], 'output' => [ 'shape' => 'ModifyDBSubnetGroupResult', 'resultWrapper' => 'ModifyDBSubnetGroupResult', ], 'errors' => [ [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'DBSubnetQuotaExceededFault', ], [ 'shape' => 'SubnetAlreadyInUse', ], [ 'shape' => 'DBSubnetGroupDoesNotCoverEnoughAZs', ], [ 'shape' => 'InvalidSubnet', ], ], ], 'ModifyEventSubscription' => [ 'name' => 'ModifyEventSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyEventSubscriptionMessage', ], 'output' => [ 'shape' => 'ModifyEventSubscriptionResult', 'resultWrapper' => 'ModifyEventSubscriptionResult', ], 'errors' => [ [ 'shape' => 'EventSubscriptionQuotaExceededFault', ], [ 'shape' => 'SubscriptionNotFoundFault', ], [ 'shape' => 'SNSInvalidTopicFault', ], [ 'shape' => 'SNSNoAuthorizationFault', ], [ 'shape' => 'SNSTopicArnNotFoundFault', ], [ 'shape' => 'SubscriptionCategoryNotFoundFault', ], ], ], 'ModifyGlobalCluster' => [ 'name' => 'ModifyGlobalCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ModifyGlobalClusterMessage', ], 'output' => [ 'shape' => 'ModifyGlobalClusterResult', 'resultWrapper' => 'ModifyGlobalClusterResult', ], 'errors' => [ [ 'shape' => 'GlobalClusterNotFoundFault', ], [ 'shape' => 'InvalidGlobalClusterStateFault', ], ], ], 'PromoteReadReplicaDBCluster' => [ 'name' => 'PromoteReadReplicaDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PromoteReadReplicaDBClusterMessage', ], 'output' => [ 'shape' => 'PromoteReadReplicaDBClusterResult', 'resultWrapper' => 'PromoteReadReplicaDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], ], ], 'RebootDBInstance' => [ 'name' => 'RebootDBInstance', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RebootDBInstanceMessage', ], 'output' => [ 'shape' => 'RebootDBInstanceResult', 'resultWrapper' => 'RebootDBInstanceResult', ], 'errors' => [ [ 'shape' => 'InvalidDBInstanceStateFault', ], [ 'shape' => 'DBInstanceNotFoundFault', ], ], ], 'RemoveFromGlobalCluster' => [ 'name' => 'RemoveFromGlobalCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveFromGlobalClusterMessage', ], 'output' => [ 'shape' => 'RemoveFromGlobalClusterResult', 'resultWrapper' => 'RemoveFromGlobalClusterResult', ], 'errors' => [ [ 'shape' => 'GlobalClusterNotFoundFault', ], [ 'shape' => 'InvalidGlobalClusterStateFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'RemoveRoleFromDBCluster' => [ 'name' => 'RemoveRoleFromDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveRoleFromDBClusterMessage', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBClusterRoleNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], ], ], 'RemoveSourceIdentifierFromSubscription' => [ 'name' => 'RemoveSourceIdentifierFromSubscription', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveSourceIdentifierFromSubscriptionMessage', ], 'output' => [ 'shape' => 'RemoveSourceIdentifierFromSubscriptionResult', 'resultWrapper' => 'RemoveSourceIdentifierFromSubscriptionResult', ], 'errors' => [ [ 'shape' => 'SubscriptionNotFoundFault', ], [ 'shape' => 'SourceNotFoundFault', ], ], ], 'RemoveTagsFromResource' => [ 'name' => 'RemoveTagsFromResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RemoveTagsFromResourceMessage', ], 'errors' => [ [ 'shape' => 'DBInstanceNotFoundFault', ], [ 'shape' => 'DBSnapshotNotFoundFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], ], ], 'ResetDBClusterParameterGroup' => [ 'name' => 'ResetDBClusterParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetDBClusterParameterGroupMessage', ], 'output' => [ 'shape' => 'DBClusterParameterGroupNameMessage', 'resultWrapper' => 'ResetDBClusterParameterGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidDBParameterGroupStateFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'ResetDBParameterGroup' => [ 'name' => 'ResetDBParameterGroup', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ResetDBParameterGroupMessage', ], 'output' => [ 'shape' => 'DBParameterGroupNameMessage', 'resultWrapper' => 'ResetDBParameterGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidDBParameterGroupStateFault', ], [ 'shape' => 'DBParameterGroupNotFoundFault', ], ], ], 'RestoreDBClusterFromSnapshot' => [ 'name' => 'RestoreDBClusterFromSnapshot', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreDBClusterFromSnapshotMessage', ], 'output' => [ 'shape' => 'RestoreDBClusterFromSnapshotResult', 'resultWrapper' => 'RestoreDBClusterFromSnapshotResult', ], 'errors' => [ [ 'shape' => 'DBClusterAlreadyExistsFault', ], [ 'shape' => 'DBClusterQuotaExceededFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'DBSnapshotNotFoundFault', ], [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], [ 'shape' => 'InsufficientDBClusterCapacityFault', ], [ 'shape' => 'InsufficientStorageClusterCapacityFault', ], [ 'shape' => 'InvalidDBSnapshotStateFault', ], [ 'shape' => 'InvalidDBClusterSnapshotStateFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'InvalidRestoreFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'OptionGroupNotFoundFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'DBClusterParameterGroupNotFoundFault', ], ], ], 'RestoreDBClusterToPointInTime' => [ 'name' => 'RestoreDBClusterToPointInTime', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'RestoreDBClusterToPointInTimeMessage', ], 'output' => [ 'shape' => 'RestoreDBClusterToPointInTimeResult', 'resultWrapper' => 'RestoreDBClusterToPointInTimeResult', ], 'errors' => [ [ 'shape' => 'DBClusterAlreadyExistsFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'DBClusterQuotaExceededFault', ], [ 'shape' => 'DBClusterSnapshotNotFoundFault', ], [ 'shape' => 'DBSubnetGroupNotFoundFault', ], [ 'shape' => 'InsufficientDBClusterCapacityFault', ], [ 'shape' => 'InsufficientStorageClusterCapacityFault', ], [ 'shape' => 'InvalidDBClusterSnapshotStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBSnapshotStateFault', ], [ 'shape' => 'InvalidRestoreFault', ], [ 'shape' => 'InvalidSubnet', ], [ 'shape' => 'InvalidVPCNetworkStateFault', ], [ 'shape' => 'KMSKeyNotAccessibleFault', ], [ 'shape' => 'OptionGroupNotFoundFault', ], [ 'shape' => 'StorageQuotaExceededFault', ], [ 'shape' => 'DBClusterParameterGroupNotFoundFault', ], ], ], 'StartDBCluster' => [ 'name' => 'StartDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartDBClusterMessage', ], 'output' => [ 'shape' => 'StartDBClusterResult', 'resultWrapper' => 'StartDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'StopDBCluster' => [ 'name' => 'StopDBCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StopDBClusterMessage', ], 'output' => [ 'shape' => 'StopDBClusterResult', 'resultWrapper' => 'StopDBClusterResult', ], 'errors' => [ [ 'shape' => 'DBClusterNotFoundFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'InvalidDBInstanceStateFault', ], ], ], 'SwitchoverGlobalCluster' => [ 'name' => 'SwitchoverGlobalCluster', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'SwitchoverGlobalClusterMessage', ], 'output' => [ 'shape' => 'SwitchoverGlobalClusterResult', 'resultWrapper' => 'SwitchoverGlobalClusterResult', ], 'errors' => [ [ 'shape' => 'GlobalClusterNotFoundFault', ], [ 'shape' => 'InvalidGlobalClusterStateFault', ], [ 'shape' => 'InvalidDBClusterStateFault', ], [ 'shape' => 'DBClusterNotFoundFault', ], ], ], ], 'shapes' => [ 'AddRoleToDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', 'RoleArn', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'RoleArn' => [ 'shape' => 'String', ], 'FeatureName' => [ 'shape' => 'String', ], ], ], 'AddSourceIdentifierToSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', 'SourceIdentifier', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'SourceIdentifier' => [ 'shape' => 'String', ], ], ], 'AddSourceIdentifierToSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'AddTagsToResourceMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceName', 'Tags', ], 'members' => [ 'ResourceName' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ApplyMethod' => [ 'type' => 'string', 'enum' => [ 'immediate', 'pending-reboot', ], ], 'ApplyPendingMaintenanceActionMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceIdentifier', 'ApplyAction', 'OptInType', ], 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'String', ], 'ApplyAction' => [ 'shape' => 'String', ], 'OptInType' => [ 'shape' => 'String', ], ], ], 'ApplyPendingMaintenanceActionResult' => [ 'type' => 'structure', 'members' => [ 'ResourcePendingMaintenanceActions' => [ 'shape' => 'ResourcePendingMaintenanceActions', ], ], ], 'AttributeValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'AttributeValue', ], ], 'AuthorizationNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'AuthorizationNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'AvailabilityZone' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'AvailabilityZoneList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZone', 'locationName' => 'AvailabilityZone', ], ], 'AvailabilityZones' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'AvailabilityZone', ], ], 'Boolean' => [ 'type' => 'boolean', ], 'BooleanOptional' => [ 'type' => 'boolean', ], 'CertificateNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'CertificateNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'CharacterSet' => [ 'type' => 'structure', 'members' => [ 'CharacterSetName' => [ 'shape' => 'String', ], 'CharacterSetDescription' => [ 'shape' => 'String', ], ], ], 'CloudwatchLogsExportConfiguration' => [ 'type' => 'structure', 'members' => [ 'EnableLogTypes' => [ 'shape' => 'LogTypeList', ], 'DisableLogTypes' => [ 'shape' => 'LogTypeList', ], ], ], 'ClusterPendingModifiedValues' => [ 'type' => 'structure', 'members' => [ 'PendingCloudwatchLogsExports' => [ 'shape' => 'PendingCloudwatchLogsExports', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'IAMDatabaseAuthenticationEnabled' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'StorageType' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], ], ], 'CopyDBClusterParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'SourceDBClusterParameterGroupIdentifier', 'TargetDBClusterParameterGroupIdentifier', 'TargetDBClusterParameterGroupDescription', ], 'members' => [ 'SourceDBClusterParameterGroupIdentifier' => [ 'shape' => 'String', ], 'TargetDBClusterParameterGroupIdentifier' => [ 'shape' => 'String', ], 'TargetDBClusterParameterGroupDescription' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CopyDBClusterParameterGroupResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterParameterGroup' => [ 'shape' => 'DBClusterParameterGroup', ], ], ], 'CopyDBClusterSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'SourceDBClusterSnapshotIdentifier', 'TargetDBClusterSnapshotIdentifier', ], 'members' => [ 'SourceDBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'TargetDBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'PreSignedUrl' => [ 'shape' => 'String', ], 'CopyTags' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CopyDBClusterSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterSnapshot' => [ 'shape' => 'DBClusterSnapshot', ], ], ], 'CopyDBParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'SourceDBParameterGroupIdentifier', 'TargetDBParameterGroupIdentifier', 'TargetDBParameterGroupDescription', ], 'members' => [ 'SourceDBParameterGroupIdentifier' => [ 'shape' => 'String', ], 'TargetDBParameterGroupIdentifier' => [ 'shape' => 'String', ], 'TargetDBParameterGroupDescription' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CopyDBParameterGroupResult' => [ 'type' => 'structure', 'members' => [ 'DBParameterGroup' => [ 'shape' => 'DBParameterGroup', ], ], ], 'CreateDBClusterEndpointMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', 'DBClusterEndpointIdentifier', 'EndpointType', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterEndpointIdentifier' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'String', ], 'StaticMembers' => [ 'shape' => 'StringList', ], 'ExcludedMembers' => [ 'shape' => 'StringList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBClusterEndpointOutput' => [ 'type' => 'structure', 'members' => [ 'DBClusterEndpointIdentifier' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterEndpointResourceIdentifier' => [ 'shape' => 'String', ], 'Endpoint' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'String', ], 'CustomEndpointType' => [ 'shape' => 'String', ], 'StaticMembers' => [ 'shape' => 'StringList', ], 'ExcludedMembers' => [ 'shape' => 'StringList', ], 'DBClusterEndpointArn' => [ 'shape' => 'String', ], ], ], 'CreateDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', 'Engine', ], 'members' => [ 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'CharacterSetName' => [ 'shape' => 'String', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'DatabaseName' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'MasterUsername' => [ 'shape' => 'String', ], 'MasterUserPassword' => [ 'shape' => 'String', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'ReplicationSourceIdentifier' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], 'StorageEncrypted' => [ 'shape' => 'BooleanOptional', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'PreSignedUrl' => [ 'shape' => 'String', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'EnableCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'ServerlessV2ScalingConfiguration' => [ 'shape' => 'ServerlessV2ScalingConfiguration', ], 'GlobalClusterIdentifier' => [ 'shape' => 'GlobalClusterIdentifier', ], 'StorageType' => [ 'shape' => 'String', ], ], ], 'CreateDBClusterParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterParameterGroupName', 'DBParameterGroupFamily', 'Description', ], 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBClusterParameterGroupResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterParameterGroup' => [ 'shape' => 'DBClusterParameterGroup', ], ], ], 'CreateDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'CreateDBClusterSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterSnapshotIdentifier', 'DBClusterIdentifier', ], 'members' => [ 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBClusterSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterSnapshot' => [ 'shape' => 'DBClusterSnapshot', ], ], ], 'CreateDBInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', 'DBInstanceClass', 'Engine', 'DBClusterIdentifier', ], 'members' => [ 'DBName' => [ 'shape' => 'String', ], 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'MasterUsername' => [ 'shape' => 'String', ], 'MasterUserPassword' => [ 'shape' => 'String', ], 'DBSecurityGroups' => [ 'shape' => 'DBSecurityGroupNameList', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'DBParameterGroupName' => [ 'shape' => 'String', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'LicenseModel' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'CharacterSetName' => [ 'shape' => 'String', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', 'deprecated' => true, ], 'Tags' => [ 'shape' => 'TagList', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'TdeCredentialArn' => [ 'shape' => 'String', ], 'TdeCredentialPassword' => [ 'shape' => 'SensitiveString', ], 'StorageEncrypted' => [ 'shape' => 'BooleanOptional', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'Domain' => [ 'shape' => 'String', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'MonitoringInterval' => [ 'shape' => 'IntegerOptional', ], 'MonitoringRoleArn' => [ 'shape' => 'String', ], 'DomainIAMRoleName' => [ 'shape' => 'String', ], 'PromotionTier' => [ 'shape' => 'IntegerOptional', ], 'Timezone' => [ 'shape' => 'String', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'EnablePerformanceInsights' => [ 'shape' => 'BooleanOptional', ], 'PerformanceInsightsKMSKeyId' => [ 'shape' => 'String', ], 'EnableCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], ], ], 'CreateDBInstanceResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'CreateDBParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBParameterGroupName', 'DBParameterGroupFamily', 'Description', ], 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBParameterGroupResult' => [ 'type' => 'structure', 'members' => [ 'DBParameterGroup' => [ 'shape' => 'DBParameterGroup', ], ], ], 'CreateDBSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBSubnetGroupName', 'DBSubnetGroupDescription', 'SubnetIds', ], 'members' => [ 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'DBSubnetGroupDescription' => [ 'shape' => 'String', ], 'SubnetIds' => [ 'shape' => 'SubnetIdentifierList', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateDBSubnetGroupResult' => [ 'type' => 'structure', 'members' => [ 'DBSubnetGroup' => [ 'shape' => 'DBSubnetGroup', ], ], ], 'CreateEventSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', 'SnsTopicArn', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'SourceIds' => [ 'shape' => 'SourceIdsList', ], 'Enabled' => [ 'shape' => 'BooleanOptional', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'CreateEventSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'CreateGlobalClusterMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalClusterIdentifier', ], 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'GlobalClusterIdentifier', ], 'SourceDBClusterIdentifier' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'StorageEncrypted' => [ 'shape' => 'BooleanOptional', ], ], ], 'CreateGlobalClusterResult' => [ 'type' => 'structure', 'members' => [ 'GlobalCluster' => [ 'shape' => 'GlobalCluster', ], ], ], 'DBCluster' => [ 'type' => 'structure', 'members' => [ 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'CharacterSetName' => [ 'shape' => 'String', ], 'DatabaseName' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterParameterGroup' => [ 'shape' => 'String', ], 'DBSubnetGroup' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'PercentProgress' => [ 'shape' => 'String', ], 'EarliestRestorableTime' => [ 'shape' => 'TStamp', ], 'Endpoint' => [ 'shape' => 'String', ], 'ReaderEndpoint' => [ 'shape' => 'String', ], 'MultiAZ' => [ 'shape' => 'Boolean', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'LatestRestorableTime' => [ 'shape' => 'TStamp', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'MasterUsername' => [ 'shape' => 'String', ], 'DBClusterOptionGroupMemberships' => [ 'shape' => 'DBClusterOptionGroupMemberships', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'ReplicationSourceIdentifier' => [ 'shape' => 'String', ], 'ReadReplicaIdentifiers' => [ 'shape' => 'ReadReplicaIdentifierList', ], 'DBClusterMembers' => [ 'shape' => 'DBClusterMemberList', ], 'VpcSecurityGroups' => [ 'shape' => 'VpcSecurityGroupMembershipList', ], 'HostedZoneId' => [ 'shape' => 'String', ], 'StorageEncrypted' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'DbClusterResourceId' => [ 'shape' => 'String', ], 'DBClusterArn' => [ 'shape' => 'String', ], 'AssociatedRoles' => [ 'shape' => 'DBClusterRoles', ], 'IAMDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], 'CloneGroupId' => [ 'shape' => 'String', ], 'ClusterCreateTime' => [ 'shape' => 'TStamp', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'EnabledCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'PendingModifiedValues' => [ 'shape' => 'ClusterPendingModifiedValues', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'CrossAccountClone' => [ 'shape' => 'BooleanOptional', ], 'AutomaticRestartTime' => [ 'shape' => 'TStamp', ], 'ServerlessV2ScalingConfiguration' => [ 'shape' => 'ServerlessV2ScalingConfigurationInfo', ], 'GlobalClusterIdentifier' => [ 'shape' => 'GlobalClusterIdentifier', ], 'IOOptimizedNextAllowedModificationTime' => [ 'shape' => 'TStamp', ], 'StorageType' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'DBClusterAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterEndpoint' => [ 'type' => 'structure', 'members' => [ 'DBClusterEndpointIdentifier' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterEndpointResourceIdentifier' => [ 'shape' => 'String', ], 'Endpoint' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'String', ], 'CustomEndpointType' => [ 'shape' => 'String', ], 'StaticMembers' => [ 'shape' => 'StringList', ], 'ExcludedMembers' => [ 'shape' => 'StringList', ], 'DBClusterEndpointArn' => [ 'shape' => 'String', ], ], ], 'DBClusterEndpointAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterEndpointAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterEndpointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterEndpoint', 'locationName' => 'DBClusterEndpointList', ], ], 'DBClusterEndpointMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBClusterEndpoints' => [ 'shape' => 'DBClusterEndpointList', ], ], ], 'DBClusterEndpointNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterEndpointNotFoundFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterEndpointQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterEndpointQuotaExceededFault', 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBCluster', 'locationName' => 'DBCluster', ], ], 'DBClusterMember' => [ 'type' => 'structure', 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'IsClusterWriter' => [ 'shape' => 'Boolean', ], 'DBClusterParameterGroupStatus' => [ 'shape' => 'String', ], 'PromotionTier' => [ 'shape' => 'IntegerOptional', ], ], 'wrapper' => true, ], 'DBClusterMemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterMember', 'locationName' => 'DBClusterMember', ], ], 'DBClusterMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBClusters' => [ 'shape' => 'DBClusterList', ], ], ], 'DBClusterNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterOptionGroupMemberships' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterOptionGroupStatus', 'locationName' => 'DBClusterOptionGroup', ], ], 'DBClusterOptionGroupStatus' => [ 'type' => 'structure', 'members' => [ 'DBClusterOptionGroupName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'DBClusterParameterGroup' => [ 'type' => 'structure', 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'DBClusterParameterGroupArn' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'DBClusterParameterGroupDetails' => [ 'type' => 'structure', 'members' => [ 'Parameters' => [ 'shape' => 'ParametersList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DBClusterParameterGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterParameterGroup', 'locationName' => 'DBClusterParameterGroup', ], ], 'DBClusterParameterGroupNameMessage' => [ 'type' => 'structure', 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], ], ], 'DBClusterParameterGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterParameterGroupNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterParameterGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBClusterParameterGroups' => [ 'shape' => 'DBClusterParameterGroupList', ], ], ], 'DBClusterQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterQuotaExceededFault', 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterRole' => [ 'type' => 'structure', 'members' => [ 'RoleArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'FeatureName' => [ 'shape' => 'String', ], ], ], 'DBClusterRoleAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterRoleAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterRoleNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterRoleNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterRoleQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterRoleQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterRoles' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterRole', 'locationName' => 'DBClusterRole', ], ], 'DBClusterSnapshot' => [ 'type' => 'structure', 'members' => [ 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'SnapshotCreateTime' => [ 'shape' => 'TStamp', ], 'Engine' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'Status' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'Integer', ], 'VpcId' => [ 'shape' => 'String', ], 'ClusterCreateTime' => [ 'shape' => 'TStamp', ], 'MasterUsername' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'LicenseModel' => [ 'shape' => 'String', ], 'SnapshotType' => [ 'shape' => 'String', ], 'PercentProgress' => [ 'shape' => 'Integer', ], 'StorageEncrypted' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'DBClusterSnapshotArn' => [ 'shape' => 'String', ], 'SourceDBClusterSnapshotArn' => [ 'shape' => 'String', ], 'IAMDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], 'StorageType' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'DBClusterSnapshotAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterSnapshotAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBClusterSnapshotAttribute' => [ 'type' => 'structure', 'members' => [ 'AttributeName' => [ 'shape' => 'String', ], 'AttributeValues' => [ 'shape' => 'AttributeValueList', ], ], ], 'DBClusterSnapshotAttributeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterSnapshotAttribute', 'locationName' => 'DBClusterSnapshotAttribute', ], ], 'DBClusterSnapshotAttributesResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'DBClusterSnapshotAttributes' => [ 'shape' => 'DBClusterSnapshotAttributeList', ], ], 'wrapper' => true, ], 'DBClusterSnapshotList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBClusterSnapshot', 'locationName' => 'DBClusterSnapshot', ], ], 'DBClusterSnapshotMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBClusterSnapshots' => [ 'shape' => 'DBClusterSnapshotList', ], ], ], 'DBClusterSnapshotNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBClusterSnapshotNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBEngineVersion' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'DBEngineDescription' => [ 'shape' => 'String', ], 'DBEngineVersionDescription' => [ 'shape' => 'String', ], 'DefaultCharacterSet' => [ 'shape' => 'CharacterSet', ], 'SupportedCharacterSets' => [ 'shape' => 'SupportedCharacterSetsList', ], 'ValidUpgradeTarget' => [ 'shape' => 'ValidUpgradeTargetList', ], 'SupportedTimezones' => [ 'shape' => 'SupportedTimezonesList', ], 'ExportableLogTypes' => [ 'shape' => 'LogTypeList', ], 'SupportsLogExportsToCloudwatchLogs' => [ 'shape' => 'Boolean', ], 'SupportsReadReplica' => [ 'shape' => 'Boolean', ], 'SupportsGlobalDatabases' => [ 'shape' => 'Boolean', ], ], ], 'DBEngineVersionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBEngineVersion', 'locationName' => 'DBEngineVersion', ], ], 'DBEngineVersionMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBEngineVersions' => [ 'shape' => 'DBEngineVersionList', ], ], ], 'DBInstance' => [ 'type' => 'structure', 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'DBInstanceStatus' => [ 'shape' => 'String', ], 'MasterUsername' => [ 'shape' => 'String', ], 'DBName' => [ 'shape' => 'String', ], 'Endpoint' => [ 'shape' => 'Endpoint', ], 'AllocatedStorage' => [ 'shape' => 'Integer', ], 'InstanceCreateTime' => [ 'shape' => 'TStamp', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], 'BackupRetentionPeriod' => [ 'shape' => 'Integer', ], 'DBSecurityGroups' => [ 'shape' => 'DBSecurityGroupMembershipList', ], 'VpcSecurityGroups' => [ 'shape' => 'VpcSecurityGroupMembershipList', ], 'DBParameterGroups' => [ 'shape' => 'DBParameterGroupStatusList', ], 'AvailabilityZone' => [ 'shape' => 'String', ], 'DBSubnetGroup' => [ 'shape' => 'DBSubnetGroup', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'PendingModifiedValues' => [ 'shape' => 'PendingModifiedValues', ], 'LatestRestorableTime' => [ 'shape' => 'TStamp', ], 'MultiAZ' => [ 'shape' => 'Boolean', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'ReadReplicaSourceDBInstanceIdentifier' => [ 'shape' => 'String', ], 'ReadReplicaDBInstanceIdentifiers' => [ 'shape' => 'ReadReplicaDBInstanceIdentifierList', ], 'ReadReplicaDBClusterIdentifiers' => [ 'shape' => 'ReadReplicaDBClusterIdentifierList', ], 'LicenseModel' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'OptionGroupMemberships' => [ 'shape' => 'OptionGroupMembershipList', ], 'CharacterSetName' => [ 'shape' => 'String', ], 'SecondaryAvailabilityZone' => [ 'shape' => 'String', ], 'PubliclyAccessible' => [ 'shape' => 'Boolean', 'deprecated' => true, ], 'StatusInfos' => [ 'shape' => 'DBInstanceStatusInfoList', ], 'StorageType' => [ 'shape' => 'String', ], 'TdeCredentialArn' => [ 'shape' => 'String', ], 'DbInstancePort' => [ 'shape' => 'Integer', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'StorageEncrypted' => [ 'shape' => 'Boolean', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'DbiResourceId' => [ 'shape' => 'String', ], 'CACertificateIdentifier' => [ 'shape' => 'String', ], 'DomainMemberships' => [ 'shape' => 'DomainMembershipList', ], 'CopyTagsToSnapshot' => [ 'shape' => 'Boolean', ], 'MonitoringInterval' => [ 'shape' => 'IntegerOptional', ], 'EnhancedMonitoringResourceArn' => [ 'shape' => 'String', ], 'MonitoringRoleArn' => [ 'shape' => 'String', ], 'PromotionTier' => [ 'shape' => 'IntegerOptional', ], 'DBInstanceArn' => [ 'shape' => 'String', ], 'Timezone' => [ 'shape' => 'String', ], 'IAMDatabaseAuthenticationEnabled' => [ 'shape' => 'Boolean', ], 'PerformanceInsightsEnabled' => [ 'shape' => 'BooleanOptional', ], 'PerformanceInsightsKMSKeyId' => [ 'shape' => 'String', ], 'EnabledCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], ], 'wrapper' => true, ], 'DBInstanceAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBInstanceAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBInstanceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBInstance', 'locationName' => 'DBInstance', ], ], 'DBInstanceMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBInstances' => [ 'shape' => 'DBInstanceList', ], ], ], 'DBInstanceNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBInstanceNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBInstanceStatusInfo' => [ 'type' => 'structure', 'members' => [ 'StatusType' => [ 'shape' => 'String', ], 'Normal' => [ 'shape' => 'Boolean', ], 'Status' => [ 'shape' => 'String', ], 'Message' => [ 'shape' => 'String', ], ], ], 'DBInstanceStatusInfoList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBInstanceStatusInfo', 'locationName' => 'DBInstanceStatusInfo', ], ], 'DBParameterGroup' => [ 'type' => 'structure', 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'DBParameterGroupArn' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'DBParameterGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBParameterGroupAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBParameterGroupDetails' => [ 'type' => 'structure', 'members' => [ 'Parameters' => [ 'shape' => 'ParametersList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DBParameterGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBParameterGroup', 'locationName' => 'DBParameterGroup', ], ], 'DBParameterGroupNameMessage' => [ 'type' => 'structure', 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], ], ], 'DBParameterGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBParameterGroupNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBParameterGroupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBParameterGroupQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBParameterGroupStatus' => [ 'type' => 'structure', 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], 'ParameterApplyStatus' => [ 'shape' => 'String', ], ], ], 'DBParameterGroupStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBParameterGroupStatus', 'locationName' => 'DBParameterGroup', ], ], 'DBParameterGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBParameterGroups' => [ 'shape' => 'DBParameterGroupList', ], ], ], 'DBSecurityGroupMembership' => [ 'type' => 'structure', 'members' => [ 'DBSecurityGroupName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'DBSecurityGroupMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBSecurityGroupMembership', 'locationName' => 'DBSecurityGroup', ], ], 'DBSecurityGroupNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'DBSecurityGroupName', ], ], 'DBSecurityGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSecurityGroupNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBSnapshotAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSnapshotAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBSnapshotNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSnapshotNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBSubnetGroup' => [ 'type' => 'structure', 'members' => [ 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'DBSubnetGroupDescription' => [ 'shape' => 'String', ], 'VpcId' => [ 'shape' => 'String', ], 'SubnetGroupStatus' => [ 'shape' => 'String', ], 'Subnets' => [ 'shape' => 'SubnetList', ], 'DBSubnetGroupArn' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'DBSubnetGroupAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSubnetGroupAlreadyExists', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBSubnetGroupDoesNotCoverEnoughAZs' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSubnetGroupDoesNotCoverEnoughAZs', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBSubnetGroupMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'DBSubnetGroups' => [ 'shape' => 'DBSubnetGroups', ], ], ], 'DBSubnetGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSubnetGroupNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'DBSubnetGroupQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSubnetGroupQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBSubnetGroups' => [ 'type' => 'list', 'member' => [ 'shape' => 'DBSubnetGroup', 'locationName' => 'DBSubnetGroup', ], ], 'DBSubnetQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBSubnetQuotaExceededFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DBUpgradeDependencyFailureFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DBUpgradeDependencyFailure', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'DeleteDBClusterEndpointMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterEndpointIdentifier', ], 'members' => [ 'DBClusterEndpointIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteDBClusterEndpointOutput' => [ 'type' => 'structure', 'members' => [ 'DBClusterEndpointIdentifier' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterEndpointResourceIdentifier' => [ 'shape' => 'String', ], 'Endpoint' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'String', ], 'CustomEndpointType' => [ 'shape' => 'String', ], 'StaticMembers' => [ 'shape' => 'StringList', ], 'ExcludedMembers' => [ 'shape' => 'StringList', ], 'DBClusterEndpointArn' => [ 'shape' => 'String', ], ], ], 'DeleteDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'SkipFinalSnapshot' => [ 'shape' => 'Boolean', ], 'FinalDBSnapshotIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteDBClusterParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterParameterGroupName', ], 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], ], ], 'DeleteDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'DeleteDBClusterSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterSnapshotIdentifier', ], 'members' => [ 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteDBClusterSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterSnapshot' => [ 'shape' => 'DBClusterSnapshot', ], ], ], 'DeleteDBInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'SkipFinalSnapshot' => [ 'shape' => 'Boolean', ], 'FinalDBSnapshotIdentifier' => [ 'shape' => 'String', ], ], ], 'DeleteDBInstanceResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'DeleteDBParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBParameterGroupName', ], 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], ], ], 'DeleteDBSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBSubnetGroupName', ], 'members' => [ 'DBSubnetGroupName' => [ 'shape' => 'String', ], ], ], 'DeleteEventSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], ], ], 'DeleteEventSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'DeleteGlobalClusterMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalClusterIdentifier', ], 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'GlobalClusterIdentifier', ], ], ], 'DeleteGlobalClusterResult' => [ 'type' => 'structure', 'members' => [ 'GlobalCluster' => [ 'shape' => 'GlobalCluster', ], ], ], 'DescribeDBClusterEndpointsMessage' => [ 'type' => 'structure', 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterEndpointIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBClusterParameterGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBClusterParametersMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterParameterGroupName', ], 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'Source' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBClusterSnapshotAttributesMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterSnapshotIdentifier', ], 'members' => [ 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], ], ], 'DescribeDBClusterSnapshotAttributesResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterSnapshotAttributesResult' => [ 'shape' => 'DBClusterSnapshotAttributesResult', ], ], ], 'DescribeDBClusterSnapshotsMessage' => [ 'type' => 'structure', 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'SnapshotType' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'IncludeShared' => [ 'shape' => 'Boolean', ], 'IncludePublic' => [ 'shape' => 'Boolean', ], ], ], 'DescribeDBClustersMessage' => [ 'type' => 'structure', 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBEngineVersionsMessage' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], 'DefaultOnly' => [ 'shape' => 'Boolean', ], 'ListSupportedCharacterSets' => [ 'shape' => 'BooleanOptional', ], 'ListSupportedTimezones' => [ 'shape' => 'BooleanOptional', ], ], ], 'DescribeDBInstancesMessage' => [ 'type' => 'structure', 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBParameterGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBParametersMessage' => [ 'type' => 'structure', 'required' => [ 'DBParameterGroupName', ], 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], 'Source' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeDBSubnetGroupsMessage' => [ 'type' => 'structure', 'members' => [ 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEngineDefaultClusterParametersMessage' => [ 'type' => 'structure', 'required' => [ 'DBParameterGroupFamily', ], 'members' => [ 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEngineDefaultClusterParametersResult' => [ 'type' => 'structure', 'members' => [ 'EngineDefaults' => [ 'shape' => 'EngineDefaults', ], ], ], 'DescribeEngineDefaultParametersMessage' => [ 'type' => 'structure', 'required' => [ 'DBParameterGroupFamily', ], 'members' => [ 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEngineDefaultParametersResult' => [ 'type' => 'structure', 'members' => [ 'EngineDefaults' => [ 'shape' => 'EngineDefaults', ], ], ], 'DescribeEventCategoriesMessage' => [ 'type' => 'structure', 'members' => [ 'SourceType' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], ], ], 'DescribeEventSubscriptionsMessage' => [ 'type' => 'structure', 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeEventsMessage' => [ 'type' => 'structure', 'members' => [ 'SourceIdentifier' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'StartTime' => [ 'shape' => 'TStamp', ], 'EndTime' => [ 'shape' => 'TStamp', ], 'Duration' => [ 'shape' => 'IntegerOptional', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeGlobalClustersMessage' => [ 'type' => 'structure', 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'GlobalClusterIdentifier', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribeOrderableDBInstanceOptionsMessage' => [ 'type' => 'structure', 'required' => [ 'Engine', ], 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'LicenseModel' => [ 'shape' => 'String', ], 'Vpc' => [ 'shape' => 'BooleanOptional', ], 'Filters' => [ 'shape' => 'FilterList', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'DescribePendingMaintenanceActionsMessage' => [ 'type' => 'structure', 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], 'Marker' => [ 'shape' => 'String', ], 'MaxRecords' => [ 'shape' => 'IntegerOptional', ], ], ], 'DescribeValidDBInstanceModificationsMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], ], ], 'DescribeValidDBInstanceModificationsResult' => [ 'type' => 'structure', 'members' => [ 'ValidDBInstanceModificationsMessage' => [ 'shape' => 'ValidDBInstanceModificationsMessage', ], ], ], 'DomainMembership' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'FQDN' => [ 'shape' => 'String', ], 'IAMRoleName' => [ 'shape' => 'String', ], ], ], 'DomainMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainMembership', 'locationName' => 'DomainMembership', ], ], 'DomainNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'DomainNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'Double' => [ 'type' => 'double', ], 'DoubleOptional' => [ 'type' => 'double', ], 'DoubleRange' => [ 'type' => 'structure', 'members' => [ 'From' => [ 'shape' => 'Double', ], 'To' => [ 'shape' => 'Double', ], ], ], 'DoubleRangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DoubleRange', 'locationName' => 'DoubleRange', ], ], 'Endpoint' => [ 'type' => 'structure', 'members' => [ 'Address' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'Integer', ], 'HostedZoneId' => [ 'shape' => 'String', ], ], ], 'EngineDefaults' => [ 'type' => 'structure', 'members' => [ 'DBParameterGroupFamily' => [ 'shape' => 'String', ], 'Marker' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'ParametersList', ], ], 'wrapper' => true, ], 'Event' => [ 'type' => 'structure', 'members' => [ 'SourceIdentifier' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'SourceType', ], 'Message' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'Date' => [ 'shape' => 'TStamp', ], 'SourceArn' => [ 'shape' => 'String', ], ], ], 'EventCategoriesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'EventCategory', ], ], 'EventCategoriesMap' => [ 'type' => 'structure', 'members' => [ 'SourceType' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], ], 'wrapper' => true, ], 'EventCategoriesMapList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventCategoriesMap', 'locationName' => 'EventCategoriesMap', ], ], 'EventCategoriesMessage' => [ 'type' => 'structure', 'members' => [ 'EventCategoriesMapList' => [ 'shape' => 'EventCategoriesMapList', ], ], ], 'EventList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Event', 'locationName' => 'Event', ], ], 'EventSubscription' => [ 'type' => 'structure', 'members' => [ 'CustomerAwsId' => [ 'shape' => 'String', ], 'CustSubscriptionId' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'SubscriptionCreationTime' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'String', ], 'SourceIdsList' => [ 'shape' => 'SourceIdsList', ], 'EventCategoriesList' => [ 'shape' => 'EventCategoriesList', ], 'Enabled' => [ 'shape' => 'Boolean', ], 'EventSubscriptionArn' => [ 'shape' => 'String', ], ], 'wrapper' => true, ], 'EventSubscriptionQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'EventSubscriptionQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'EventSubscriptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EventSubscription', 'locationName' => 'EventSubscription', ], ], 'EventSubscriptionsMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'EventSubscriptionsList' => [ 'shape' => 'EventSubscriptionsList', ], ], ], 'EventsMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'Events' => [ 'shape' => 'EventList', ], ], ], 'FailoverDBClusterMessage' => [ 'type' => 'structure', 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'TargetDBInstanceIdentifier' => [ 'shape' => 'String', ], ], ], 'FailoverDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'FailoverGlobalClusterMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalClusterIdentifier', 'TargetDbClusterIdentifier', ], 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'GlobalClusterIdentifier', ], 'TargetDbClusterIdentifier' => [ 'shape' => 'String', ], 'AllowDataLoss' => [ 'shape' => 'BooleanOptional', ], 'Switchover' => [ 'shape' => 'BooleanOptional', ], ], ], 'FailoverGlobalClusterResult' => [ 'type' => 'structure', 'members' => [ 'GlobalCluster' => [ 'shape' => 'GlobalCluster', ], ], ], 'FailoverState' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'FailoverStatus', ], 'FromDbClusterArn' => [ 'shape' => 'String', ], 'ToDbClusterArn' => [ 'shape' => 'String', ], 'IsDataLossAllowed' => [ 'shape' => 'Boolean', ], ], 'wrapper' => true, ], 'FailoverStatus' => [ 'type' => 'string', 'enum' => [ 'pending', 'failing-over', 'cancelling', ], ], 'Filter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Values', ], 'members' => [ 'Name' => [ 'shape' => 'String', ], 'Values' => [ 'shape' => 'FilterValueList', ], ], ], 'FilterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', 'locationName' => 'Filter', ], ], 'FilterValueList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'Value', ], ], 'GlobalCluster' => [ 'type' => 'structure', 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'GlobalClusterIdentifier', ], 'GlobalClusterResourceId' => [ 'shape' => 'String', ], 'GlobalClusterArn' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'StorageEncrypted' => [ 'shape' => 'BooleanOptional', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'GlobalClusterMembers' => [ 'shape' => 'GlobalClusterMemberList', ], 'FailoverState' => [ 'shape' => 'FailoverState', ], ], 'wrapper' => true, ], 'GlobalClusterAlreadyExistsFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'GlobalClusterAlreadyExistsFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'GlobalClusterIdentifier' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[A-Za-z][0-9A-Za-z-:._]*', ], 'GlobalClusterList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalCluster', 'locationName' => 'GlobalClusterMember', ], ], 'GlobalClusterMember' => [ 'type' => 'structure', 'members' => [ 'DBClusterArn' => [ 'shape' => 'String', ], 'Readers' => [ 'shape' => 'ReadersArnList', ], 'IsWriter' => [ 'shape' => 'Boolean', ], ], 'wrapper' => true, ], 'GlobalClusterMemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'GlobalClusterMember', 'locationName' => 'GlobalClusterMember', ], ], 'GlobalClusterNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'GlobalClusterNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'GlobalClusterQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'GlobalClusterQuotaExceededFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'GlobalClustersMessage' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'String', ], 'GlobalClusters' => [ 'shape' => 'GlobalClusterList', ], ], ], 'InstanceQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InstanceQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InsufficientDBClusterCapacityFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InsufficientDBClusterCapacityFault', 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'InsufficientDBInstanceCapacityFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InsufficientDBInstanceCapacity', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InsufficientStorageClusterCapacityFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InsufficientStorageClusterCapacity', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Integer' => [ 'type' => 'integer', ], 'IntegerOptional' => [ 'type' => 'integer', ], 'InvalidDBClusterEndpointStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBClusterEndpointStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBClusterSnapshotStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBClusterSnapshotStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBClusterStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBClusterStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBInstanceStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBInstanceState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBParameterGroupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBParameterGroupState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBSecurityGroupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBSecurityGroupState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBSnapshotStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBSnapshotState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBSubnetGroupStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBSubnetGroupStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDBSubnetStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidDBSubnetStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidEventSubscriptionStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidEventSubscriptionState', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidGlobalClusterStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidGlobalClusterStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidRestoreFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidRestoreFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidSubnet' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidSubnet', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidVPCNetworkStateFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'InvalidVPCNetworkStateFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'KMSKeyNotAccessibleFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'KMSKeyNotAccessibleFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'KeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ListTagsForResourceMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceName', ], 'members' => [ 'ResourceName' => [ 'shape' => 'String', ], 'Filters' => [ 'shape' => 'FilterList', ], ], ], 'LogTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ModifyDBClusterEndpointMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterEndpointIdentifier', ], 'members' => [ 'DBClusterEndpointIdentifier' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'String', ], 'StaticMembers' => [ 'shape' => 'StringList', ], 'ExcludedMembers' => [ 'shape' => 'StringList', ], ], ], 'ModifyDBClusterEndpointOutput' => [ 'type' => 'structure', 'members' => [ 'DBClusterEndpointIdentifier' => [ 'shape' => 'String', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'DBClusterEndpointResourceIdentifier' => [ 'shape' => 'String', ], 'Endpoint' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], 'EndpointType' => [ 'shape' => 'String', ], 'CustomEndpointType' => [ 'shape' => 'String', ], 'StaticMembers' => [ 'shape' => 'StringList', ], 'ExcludedMembers' => [ 'shape' => 'StringList', ], 'DBClusterEndpointArn' => [ 'shape' => 'String', ], ], ], 'ModifyDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'NewDBClusterIdentifier' => [ 'shape' => 'String', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'MasterUserPassword' => [ 'shape' => 'String', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'CloudwatchLogsExportConfiguration' => [ 'shape' => 'CloudwatchLogsExportConfiguration', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AllowMajorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'DBInstanceParameterGroupName' => [ 'shape' => 'String', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'ServerlessV2ScalingConfiguration' => [ 'shape' => 'ServerlessV2ScalingConfiguration', ], 'StorageType' => [ 'shape' => 'String', ], ], ], 'ModifyDBClusterParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterParameterGroupName', 'Parameters', ], 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'ParametersList', ], ], ], 'ModifyDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'ModifyDBClusterSnapshotAttributeMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterSnapshotIdentifier', 'AttributeName', ], 'members' => [ 'DBClusterSnapshotIdentifier' => [ 'shape' => 'String', ], 'AttributeName' => [ 'shape' => 'String', ], 'ValuesToAdd' => [ 'shape' => 'AttributeValueList', ], 'ValuesToRemove' => [ 'shape' => 'AttributeValueList', ], ], ], 'ModifyDBClusterSnapshotAttributeResult' => [ 'type' => 'structure', 'members' => [ 'DBClusterSnapshotAttributesResult' => [ 'shape' => 'DBClusterSnapshotAttributesResult', ], ], ], 'ModifyDBInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'DBSecurityGroups' => [ 'shape' => 'DBSecurityGroupNameList', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'ApplyImmediately' => [ 'shape' => 'Boolean', ], 'MasterUserPassword' => [ 'shape' => 'String', ], 'DBParameterGroupName' => [ 'shape' => 'String', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'PreferredBackupWindow' => [ 'shape' => 'String', ], 'PreferredMaintenanceWindow' => [ 'shape' => 'String', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AllowMajorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'AutoMinorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], 'LicenseModel' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'NewDBInstanceIdentifier' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'TdeCredentialArn' => [ 'shape' => 'String', ], 'TdeCredentialPassword' => [ 'shape' => 'SensitiveString', ], 'CACertificateIdentifier' => [ 'shape' => 'String', ], 'Domain' => [ 'shape' => 'String', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'MonitoringInterval' => [ 'shape' => 'IntegerOptional', ], 'DBPortNumber' => [ 'shape' => 'IntegerOptional', ], 'PubliclyAccessible' => [ 'shape' => 'BooleanOptional', 'deprecated' => true, ], 'MonitoringRoleArn' => [ 'shape' => 'String', ], 'DomainIAMRoleName' => [ 'shape' => 'String', ], 'PromotionTier' => [ 'shape' => 'IntegerOptional', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'EnablePerformanceInsights' => [ 'shape' => 'BooleanOptional', ], 'PerformanceInsightsKMSKeyId' => [ 'shape' => 'String', ], 'CloudwatchLogsExportConfiguration' => [ 'shape' => 'CloudwatchLogsExportConfiguration', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], ], ], 'ModifyDBInstanceResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'ModifyDBParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBParameterGroupName', 'Parameters', ], 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], 'Parameters' => [ 'shape' => 'ParametersList', ], ], ], 'ModifyDBSubnetGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBSubnetGroupName', 'SubnetIds', ], 'members' => [ 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'DBSubnetGroupDescription' => [ 'shape' => 'String', ], 'SubnetIds' => [ 'shape' => 'SubnetIdentifierList', ], ], ], 'ModifyDBSubnetGroupResult' => [ 'type' => 'structure', 'members' => [ 'DBSubnetGroup' => [ 'shape' => 'DBSubnetGroup', ], ], ], 'ModifyEventSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'SnsTopicArn' => [ 'shape' => 'String', ], 'SourceType' => [ 'shape' => 'String', ], 'EventCategories' => [ 'shape' => 'EventCategoriesList', ], 'Enabled' => [ 'shape' => 'BooleanOptional', ], ], ], 'ModifyEventSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'ModifyGlobalClusterMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalClusterIdentifier', ], 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'GlobalClusterIdentifier', ], 'NewGlobalClusterIdentifier' => [ 'shape' => 'GlobalClusterIdentifier', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'AllowMajorVersionUpgrade' => [ 'shape' => 'BooleanOptional', ], ], ], 'ModifyGlobalClusterResult' => [ 'type' => 'structure', 'members' => [ 'GlobalCluster' => [ 'shape' => 'GlobalCluster', ], ], ], 'OptionGroupMembership' => [ 'type' => 'structure', 'members' => [ 'OptionGroupName' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'OptionGroupMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OptionGroupMembership', 'locationName' => 'OptionGroupMembership', ], ], 'OptionGroupNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'OptionGroupNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'OrderableDBInstanceOption' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'DBInstanceClass' => [ 'shape' => 'String', ], 'LicenseModel' => [ 'shape' => 'String', ], 'AvailabilityZones' => [ 'shape' => 'AvailabilityZoneList', ], 'MultiAZCapable' => [ 'shape' => 'Boolean', ], 'ReadReplicaCapable' => [ 'shape' => 'Boolean', ], 'Vpc' => [ 'shape' => 'Boolean', ], 'SupportsStorageEncryption' => [ 'shape' => 'Boolean', ], 'StorageType' => [ 'shape' => 'String', ], 'SupportsIops' => [ 'shape' => 'Boolean', ], 'SupportsEnhancedMonitoring' => [ 'shape' => 'Boolean', ], 'SupportsIAMDatabaseAuthentication' => [ 'shape' => 'Boolean', ], 'SupportsPerformanceInsights' => [ 'shape' => 'Boolean', ], 'MinStorageSize' => [ 'shape' => 'IntegerOptional', ], 'MaxStorageSize' => [ 'shape' => 'IntegerOptional', ], 'MinIopsPerDbInstance' => [ 'shape' => 'IntegerOptional', ], 'MaxIopsPerDbInstance' => [ 'shape' => 'IntegerOptional', ], 'MinIopsPerGib' => [ 'shape' => 'DoubleOptional', ], 'MaxIopsPerGib' => [ 'shape' => 'DoubleOptional', ], 'SupportsGlobalDatabases' => [ 'shape' => 'Boolean', ], ], 'wrapper' => true, ], 'OrderableDBInstanceOptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OrderableDBInstanceOption', 'locationName' => 'OrderableDBInstanceOption', ], ], 'OrderableDBInstanceOptionsMessage' => [ 'type' => 'structure', 'members' => [ 'OrderableDBInstanceOptions' => [ 'shape' => 'OrderableDBInstanceOptionsList', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'Parameter' => [ 'type' => 'structure', 'members' => [ 'ParameterName' => [ 'shape' => 'String', ], 'ParameterValue' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'Source' => [ 'shape' => 'String', ], 'ApplyType' => [ 'shape' => 'String', ], 'DataType' => [ 'shape' => 'String', ], 'AllowedValues' => [ 'shape' => 'String', ], 'IsModifiable' => [ 'shape' => 'Boolean', ], 'MinimumEngineVersion' => [ 'shape' => 'String', ], 'ApplyMethod' => [ 'shape' => 'ApplyMethod', ], ], ], 'ParametersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parameter', 'locationName' => 'Parameter', ], ], 'PendingCloudwatchLogsExports' => [ 'type' => 'structure', 'members' => [ 'LogTypesToEnable' => [ 'shape' => 'LogTypeList', ], 'LogTypesToDisable' => [ 'shape' => 'LogTypeList', ], ], ], 'PendingMaintenanceAction' => [ 'type' => 'structure', 'members' => [ 'Action' => [ 'shape' => 'String', ], 'AutoAppliedAfterDate' => [ 'shape' => 'TStamp', ], 'ForcedApplyDate' => [ 'shape' => 'TStamp', ], 'OptInStatus' => [ 'shape' => 'String', ], 'CurrentApplyDate' => [ 'shape' => 'TStamp', ], 'Description' => [ 'shape' => 'String', ], ], ], 'PendingMaintenanceActionDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'PendingMaintenanceAction', 'locationName' => 'PendingMaintenanceAction', ], ], 'PendingMaintenanceActions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourcePendingMaintenanceActions', 'locationName' => 'ResourcePendingMaintenanceActions', ], ], 'PendingMaintenanceActionsMessage' => [ 'type' => 'structure', 'members' => [ 'PendingMaintenanceActions' => [ 'shape' => 'PendingMaintenanceActions', ], 'Marker' => [ 'shape' => 'String', ], ], ], 'PendingModifiedValues' => [ 'type' => 'structure', 'members' => [ 'DBInstanceClass' => [ 'shape' => 'String', ], 'AllocatedStorage' => [ 'shape' => 'IntegerOptional', ], 'MasterUserPassword' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'BackupRetentionPeriod' => [ 'shape' => 'IntegerOptional', ], 'MultiAZ' => [ 'shape' => 'BooleanOptional', ], 'EngineVersion' => [ 'shape' => 'String', ], 'LicenseModel' => [ 'shape' => 'String', ], 'Iops' => [ 'shape' => 'IntegerOptional', ], 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'StorageType' => [ 'shape' => 'String', ], 'CACertificateIdentifier' => [ 'shape' => 'String', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'PendingCloudwatchLogsExports' => [ 'shape' => 'PendingCloudwatchLogsExports', ], ], ], 'PromoteReadReplicaDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], ], ], 'PromoteReadReplicaDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'ProvisionedIopsNotAvailableInAZFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ProvisionedIopsNotAvailableInAZFault', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Range' => [ 'type' => 'structure', 'members' => [ 'From' => [ 'shape' => 'Integer', ], 'To' => [ 'shape' => 'Integer', ], 'Step' => [ 'shape' => 'IntegerOptional', ], ], ], 'RangeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Range', 'locationName' => 'Range', ], ], 'ReadReplicaDBClusterIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'ReadReplicaDBClusterIdentifier', ], ], 'ReadReplicaDBInstanceIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'ReadReplicaDBInstanceIdentifier', ], ], 'ReadReplicaIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'ReadReplicaIdentifier', ], ], 'ReadersArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'RebootDBInstanceMessage' => [ 'type' => 'structure', 'required' => [ 'DBInstanceIdentifier', ], 'members' => [ 'DBInstanceIdentifier' => [ 'shape' => 'String', ], 'ForceFailover' => [ 'shape' => 'BooleanOptional', ], ], ], 'RebootDBInstanceResult' => [ 'type' => 'structure', 'members' => [ 'DBInstance' => [ 'shape' => 'DBInstance', ], ], ], 'RemoveFromGlobalClusterMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalClusterIdentifier', 'DbClusterIdentifier', ], 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'GlobalClusterIdentifier', ], 'DbClusterIdentifier' => [ 'shape' => 'String', ], ], ], 'RemoveFromGlobalClusterResult' => [ 'type' => 'structure', 'members' => [ 'GlobalCluster' => [ 'shape' => 'GlobalCluster', ], ], ], 'RemoveRoleFromDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', 'RoleArn', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'RoleArn' => [ 'shape' => 'String', ], 'FeatureName' => [ 'shape' => 'String', ], ], ], 'RemoveSourceIdentifierFromSubscriptionMessage' => [ 'type' => 'structure', 'required' => [ 'SubscriptionName', 'SourceIdentifier', ], 'members' => [ 'SubscriptionName' => [ 'shape' => 'String', ], 'SourceIdentifier' => [ 'shape' => 'String', ], ], ], 'RemoveSourceIdentifierFromSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'EventSubscription' => [ 'shape' => 'EventSubscription', ], ], ], 'RemoveTagsFromResourceMessage' => [ 'type' => 'structure', 'required' => [ 'ResourceName', 'TagKeys', ], 'members' => [ 'ResourceName' => [ 'shape' => 'String', ], 'TagKeys' => [ 'shape' => 'KeyList', ], ], ], 'ResetDBClusterParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterParameterGroupName', ], 'members' => [ 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'ResetAllParameters' => [ 'shape' => 'Boolean', ], 'Parameters' => [ 'shape' => 'ParametersList', ], ], ], 'ResetDBParameterGroupMessage' => [ 'type' => 'structure', 'required' => [ 'DBParameterGroupName', ], 'members' => [ 'DBParameterGroupName' => [ 'shape' => 'String', ], 'ResetAllParameters' => [ 'shape' => 'Boolean', ], 'Parameters' => [ 'shape' => 'ParametersList', ], ], ], 'ResourceNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'ResourceNotFoundFault', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourcePendingMaintenanceActions' => [ 'type' => 'structure', 'members' => [ 'ResourceIdentifier' => [ 'shape' => 'String', ], 'PendingMaintenanceActionDetails' => [ 'shape' => 'PendingMaintenanceActionDetails', ], ], 'wrapper' => true, ], 'RestoreDBClusterFromSnapshotMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', 'SnapshotIdentifier', 'Engine', ], 'members' => [ 'AvailabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'SnapshotIdentifier' => [ 'shape' => 'String', ], 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'DatabaseName' => [ 'shape' => 'String', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'Tags' => [ 'shape' => 'TagList', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'EnableCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'CopyTagsToSnapshot' => [ 'shape' => 'BooleanOptional', ], 'ServerlessV2ScalingConfiguration' => [ 'shape' => 'ServerlessV2ScalingConfiguration', ], 'StorageType' => [ 'shape' => 'String', ], ], ], 'RestoreDBClusterFromSnapshotResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'RestoreDBClusterToPointInTimeMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', 'SourceDBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], 'RestoreType' => [ 'shape' => 'String', ], 'SourceDBClusterIdentifier' => [ 'shape' => 'String', ], 'RestoreToTime' => [ 'shape' => 'TStamp', ], 'UseLatestRestorableTime' => [ 'shape' => 'Boolean', ], 'Port' => [ 'shape' => 'IntegerOptional', ], 'DBSubnetGroupName' => [ 'shape' => 'String', ], 'OptionGroupName' => [ 'shape' => 'String', ], 'VpcSecurityGroupIds' => [ 'shape' => 'VpcSecurityGroupIdList', ], 'Tags' => [ 'shape' => 'TagList', ], 'KmsKeyId' => [ 'shape' => 'String', ], 'EnableIAMDatabaseAuthentication' => [ 'shape' => 'BooleanOptional', ], 'EnableCloudwatchLogsExports' => [ 'shape' => 'LogTypeList', ], 'DBClusterParameterGroupName' => [ 'shape' => 'String', ], 'DeletionProtection' => [ 'shape' => 'BooleanOptional', ], 'ServerlessV2ScalingConfiguration' => [ 'shape' => 'ServerlessV2ScalingConfiguration', ], 'StorageType' => [ 'shape' => 'String', ], ], ], 'RestoreDBClusterToPointInTimeResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'SNSInvalidTopicFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SNSInvalidTopic', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SNSNoAuthorizationFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SNSNoAuthorization', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SNSTopicArnNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SNSTopicArnNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'ServerlessV2ScalingConfiguration' => [ 'type' => 'structure', 'members' => [ 'MinCapacity' => [ 'shape' => 'DoubleOptional', ], 'MaxCapacity' => [ 'shape' => 'DoubleOptional', ], ], ], 'ServerlessV2ScalingConfigurationInfo' => [ 'type' => 'structure', 'members' => [ 'MinCapacity' => [ 'shape' => 'DoubleOptional', ], 'MaxCapacity' => [ 'shape' => 'DoubleOptional', ], ], ], 'SharedSnapshotQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SharedSnapshotQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SnapshotQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SnapshotQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SourceIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'SourceId', ], ], 'SourceNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SourceNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SourceType' => [ 'type' => 'string', 'enum' => [ 'db-instance', 'db-parameter-group', 'db-security-group', 'db-snapshot', 'db-cluster', 'db-cluster-snapshot', ], ], 'StartDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], ], ], 'StartDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'StopDBClusterMessage' => [ 'type' => 'structure', 'required' => [ 'DBClusterIdentifier', ], 'members' => [ 'DBClusterIdentifier' => [ 'shape' => 'String', ], ], ], 'StopDBClusterResult' => [ 'type' => 'structure', 'members' => [ 'DBCluster' => [ 'shape' => 'DBCluster', ], ], ], 'StorageQuotaExceededFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StorageQuotaExceeded', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'StorageTypeNotSupportedFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'StorageTypeNotSupported', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'StringList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Subnet' => [ 'type' => 'structure', 'members' => [ 'SubnetIdentifier' => [ 'shape' => 'String', ], 'SubnetAvailabilityZone' => [ 'shape' => 'AvailabilityZone', ], 'SubnetStatus' => [ 'shape' => 'String', ], ], ], 'SubnetAlreadyInUse' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SubnetAlreadyInUse', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SubnetIdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'SubnetIdentifier', ], ], 'SubnetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Subnet', 'locationName' => 'Subnet', ], ], 'SubscriptionAlreadyExistFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SubscriptionAlreadyExist', 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SubscriptionCategoryNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SubscriptionCategoryNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SubscriptionNotFoundFault' => [ 'type' => 'structure', 'members' => [], 'error' => [ 'code' => 'SubscriptionNotFound', 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SupportedCharacterSetsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CharacterSet', 'locationName' => 'CharacterSet', ], ], 'SupportedTimezonesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Timezone', 'locationName' => 'Timezone', ], ], 'SwitchoverGlobalClusterMessage' => [ 'type' => 'structure', 'required' => [ 'GlobalClusterIdentifier', 'TargetDbClusterIdentifier', ], 'members' => [ 'GlobalClusterIdentifier' => [ 'shape' => 'GlobalClusterIdentifier', ], 'TargetDbClusterIdentifier' => [ 'shape' => 'String', ], ], ], 'SwitchoverGlobalClusterResult' => [ 'type' => 'structure', 'members' => [ 'GlobalCluster' => [ 'shape' => 'GlobalCluster', ], ], ], 'TStamp' => [ 'type' => 'timestamp', ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'Key' => [ 'shape' => 'String', ], 'Value' => [ 'shape' => 'String', ], ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', 'locationName' => 'Tag', ], ], 'TagListMessage' => [ 'type' => 'structure', 'members' => [ 'TagList' => [ 'shape' => 'TagList', ], ], ], 'Timezone' => [ 'type' => 'structure', 'members' => [ 'TimezoneName' => [ 'shape' => 'String', ], ], ], 'UpgradeTarget' => [ 'type' => 'structure', 'members' => [ 'Engine' => [ 'shape' => 'String', ], 'EngineVersion' => [ 'shape' => 'String', ], 'Description' => [ 'shape' => 'String', ], 'AutoUpgrade' => [ 'shape' => 'Boolean', ], 'IsMajorVersionUpgrade' => [ 'shape' => 'Boolean', ], 'SupportsGlobalDatabases' => [ 'shape' => 'BooleanOptional', ], ], ], 'ValidDBInstanceModificationsMessage' => [ 'type' => 'structure', 'members' => [ 'Storage' => [ 'shape' => 'ValidStorageOptionsList', ], ], 'wrapper' => true, ], 'ValidStorageOptions' => [ 'type' => 'structure', 'members' => [ 'StorageType' => [ 'shape' => 'String', ], 'StorageSize' => [ 'shape' => 'RangeList', ], 'ProvisionedIops' => [ 'shape' => 'RangeList', ], 'IopsToStorageRatio' => [ 'shape' => 'DoubleRangeList', ], ], ], 'ValidStorageOptionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidStorageOptions', 'locationName' => 'ValidStorageOptions', ], ], 'ValidUpgradeTargetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'UpgradeTarget', 'locationName' => 'UpgradeTarget', ], ], 'VpcSecurityGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', 'locationName' => 'VpcSecurityGroupId', ], ], 'VpcSecurityGroupMembership' => [ 'type' => 'structure', 'members' => [ 'VpcSecurityGroupId' => [ 'shape' => 'String', ], 'Status' => [ 'shape' => 'String', ], ], ], 'VpcSecurityGroupMembershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcSecurityGroupMembership', 'locationName' => 'VpcSecurityGroupMembership', ], ], ],];
