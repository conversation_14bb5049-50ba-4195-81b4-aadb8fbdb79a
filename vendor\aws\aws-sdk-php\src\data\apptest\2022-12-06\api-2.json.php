<?php
// This file was auto-generated from sdk-root/src/data/apptest/2022-12-06/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-12-06', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'apptest', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Mainframe Modernization Application Testing', 'serviceId' => 'AppTest', 'signatureVersion' => 'v4', 'signingName' => 'apptest', 'uid' => 'apptest-2022-12-06', ], 'operations' => [ 'CreateTestCase' => [ 'name' => 'CreateTestCase', 'http' => [ 'method' => 'POST', 'requestUri' => '/testcase', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateTestCaseRequest', ], 'output' => [ 'shape' => 'CreateTestCaseResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateTestConfiguration' => [ 'name' => 'CreateTestConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/testconfiguration', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateTestConfigurationRequest', ], 'output' => [ 'shape' => 'CreateTestConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateTestSuite' => [ 'name' => 'CreateTestSuite', 'http' => [ 'method' => 'POST', 'requestUri' => '/testsuite', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateTestSuiteRequest', ], 'output' => [ 'shape' => 'CreateTestSuiteResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteTestCase' => [ 'name' => 'DeleteTestCase', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/testcases/{testCaseId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTestCaseRequest', ], 'output' => [ 'shape' => 'DeleteTestCaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteTestConfiguration' => [ 'name' => 'DeleteTestConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/testconfigurations/{testConfigurationId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTestConfigurationRequest', ], 'output' => [ 'shape' => 'DeleteTestConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteTestRun' => [ 'name' => 'DeleteTestRun', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/testruns/{testRunId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTestRunRequest', ], 'output' => [ 'shape' => 'DeleteTestRunResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteTestSuite' => [ 'name' => 'DeleteTestSuite', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/testsuites/{testSuiteId}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteTestSuiteRequest', ], 'output' => [ 'shape' => 'DeleteTestSuiteResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'GetTestCase' => [ 'name' => 'GetTestCase', 'http' => [ 'method' => 'GET', 'requestUri' => '/testcases/{testCaseId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTestCaseRequest', ], 'output' => [ 'shape' => 'GetTestCaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTestConfiguration' => [ 'name' => 'GetTestConfiguration', 'http' => [ 'method' => 'GET', 'requestUri' => '/testconfigurations/{testConfigurationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTestConfigurationRequest', ], 'output' => [ 'shape' => 'GetTestConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTestRunStep' => [ 'name' => 'GetTestRunStep', 'http' => [ 'method' => 'GET', 'requestUri' => '/testruns/{testRunId}/steps/{stepName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTestRunStepRequest', ], 'output' => [ 'shape' => 'GetTestRunStepResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTestSuite' => [ 'name' => 'GetTestSuite', 'http' => [ 'method' => 'GET', 'requestUri' => '/testsuites/{testSuiteId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTestSuiteRequest', ], 'output' => [ 'shape' => 'GetTestSuiteResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTestCases' => [ 'name' => 'ListTestCases', 'http' => [ 'method' => 'GET', 'requestUri' => '/testcases', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTestCasesRequest', ], 'output' => [ 'shape' => 'ListTestCasesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTestConfigurations' => [ 'name' => 'ListTestConfigurations', 'http' => [ 'method' => 'GET', 'requestUri' => '/testconfigurations', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTestConfigurationsRequest', ], 'output' => [ 'shape' => 'ListTestConfigurationsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTestRunSteps' => [ 'name' => 'ListTestRunSteps', 'http' => [ 'method' => 'GET', 'requestUri' => '/testruns/{testRunId}/steps', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTestRunStepsRequest', ], 'output' => [ 'shape' => 'ListTestRunStepsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTestRunTestCases' => [ 'name' => 'ListTestRunTestCases', 'http' => [ 'method' => 'GET', 'requestUri' => '/testruns/{testRunId}/testcases', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTestRunTestCasesRequest', ], 'output' => [ 'shape' => 'ListTestRunTestCasesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTestRuns' => [ 'name' => 'ListTestRuns', 'http' => [ 'method' => 'GET', 'requestUri' => '/testruns', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTestRunsRequest', ], 'output' => [ 'shape' => 'ListTestRunsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTestSuites' => [ 'name' => 'ListTestSuites', 'http' => [ 'method' => 'GET', 'requestUri' => '/testsuites', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTestSuitesRequest', ], 'output' => [ 'shape' => 'ListTestSuitesResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartTestRun' => [ 'name' => 'StartTestRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/testrun', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartTestRunRequest', ], 'output' => [ 'shape' => 'StartTestRunResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateTestCase' => [ 'name' => 'UpdateTestCase', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/testcases/{testCaseId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTestCaseRequest', ], 'output' => [ 'shape' => 'UpdateTestCaseResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateTestConfiguration' => [ 'name' => 'UpdateTestConfiguration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/testconfigurations/{testConfigurationId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTestConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateTestConfigurationResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateTestSuite' => [ 'name' => 'UpdateTestSuite', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/testsuites/{testSuiteId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateTestSuiteRequest', ], 'output' => [ 'shape' => 'UpdateTestSuiteResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Arn' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-cn|aws-iso|aws-iso-[a-z]{1}|aws-us-gov):[A-Za-z0-9][A-Za-z0-9_/.-]{0,62}:([a-z]{2}-((iso[a-z]{0,1}-)|(gov-)){0,1}[a-z]+-[0-9]):[0-9]{12}:[A-Za-z0-9/][A-Za-z0-9:_/+=,@.-]{0,1023}', ], 'Batch' => [ 'type' => 'structure', 'required' => [ 'batchJobName', ], 'members' => [ 'batchJobName' => [ 'shape' => 'Variable', ], 'batchJobParameters' => [ 'shape' => 'BatchJobParameters', ], 'exportDataSetNames' => [ 'shape' => 'ExportDataSetNames', ], ], ], 'BatchJobParameters' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'BatchStepInput' => [ 'type' => 'structure', 'required' => [ 'resource', 'batchJobName', ], 'members' => [ 'resource' => [ 'shape' => 'MainframeResourceSummary', ], 'batchJobName' => [ 'shape' => 'ResourceName', ], 'batchJobParameters' => [ 'shape' => 'BatchJobParameters', ], 'exportDataSetNames' => [ 'shape' => 'ExportDataSetNames', ], 'properties' => [ 'shape' => 'MainframeActionProperties', ], ], ], 'BatchStepOutput' => [ 'type' => 'structure', 'members' => [ 'dataSetExportLocation' => [ 'shape' => 'S3Uri', ], 'dmsOutputLocation' => [ 'shape' => 'S3Uri', ], 'dataSetDetails' => [ 'shape' => 'DataSetList', ], ], ], 'BatchSummary' => [ 'type' => 'structure', 'required' => [ 'stepInput', ], 'members' => [ 'stepInput' => [ 'shape' => 'BatchStepInput', ], 'stepOutput' => [ 'shape' => 'BatchStepOutput', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CaptureTool' => [ 'type' => 'string', 'enum' => [ 'Precisely', 'AWS DMS', ], ], 'CloudFormation' => [ 'type' => 'structure', 'required' => [ 'templateLocation', ], 'members' => [ 'templateLocation' => [ 'shape' => 'S3Uri', ], 'parameters' => [ 'shape' => 'Properties', ], ], ], 'CloudFormationAction' => [ 'type' => 'structure', 'required' => [ 'resource', ], 'members' => [ 'resource' => [ 'shape' => 'Variable', ], 'actionType' => [ 'shape' => 'CloudFormationActionType', ], ], ], 'CloudFormationActionType' => [ 'type' => 'string', 'enum' => [ 'Create', 'Delete', ], ], 'CloudFormationStepSummary' => [ 'type' => 'structure', 'members' => [ 'createCloudformation' => [ 'shape' => 'CreateCloudFormationSummary', ], 'deleteCloudformation' => [ 'shape' => 'DeleteCloudFormationSummary', ], ], 'union' => true, ], 'CompareAction' => [ 'type' => 'structure', 'required' => [ 'input', ], 'members' => [ 'input' => [ 'shape' => 'Input', ], 'output' => [ 'shape' => 'Output', ], ], ], 'CompareActionSummary' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'File', ], ], ], 'CompareDataSetsStepInput' => [ 'type' => 'structure', 'required' => [ 'sourceLocation', 'targetLocation', 'sourceDataSets', 'targetDataSets', ], 'members' => [ 'sourceLocation' => [ 'shape' => 'S3Uri', ], 'targetLocation' => [ 'shape' => 'S3Uri', ], 'sourceDataSets' => [ 'shape' => 'DataSetList', ], 'targetDataSets' => [ 'shape' => 'DataSetList', ], ], ], 'CompareDataSetsStepOutput' => [ 'type' => 'structure', 'required' => [ 'comparisonOutputLocation', 'comparisonStatus', ], 'members' => [ 'comparisonOutputLocation' => [ 'shape' => 'S3Uri', ], 'comparisonStatus' => [ 'shape' => 'ComparisonStatusEnum', ], ], ], 'CompareDataSetsSummary' => [ 'type' => 'structure', 'required' => [ 'stepInput', ], 'members' => [ 'stepInput' => [ 'shape' => 'CompareDataSetsStepInput', ], 'stepOutput' => [ 'shape' => 'CompareDataSetsStepOutput', ], ], ], 'CompareDatabaseCDCStepInput' => [ 'type' => 'structure', 'required' => [ 'sourceLocation', 'targetLocation', 'sourceMetadata', 'targetMetadata', ], 'members' => [ 'sourceLocation' => [ 'shape' => 'String', ], 'targetLocation' => [ 'shape' => 'String', ], 'outputLocation' => [ 'shape' => 'String', ], 'sourceMetadata' => [ 'shape' => 'SourceDatabaseMetadata', ], 'targetMetadata' => [ 'shape' => 'TargetDatabaseMetadata', ], ], ], 'CompareDatabaseCDCStepOutput' => [ 'type' => 'structure', 'required' => [ 'comparisonOutputLocation', 'comparisonStatus', ], 'members' => [ 'comparisonOutputLocation' => [ 'shape' => 'String', ], 'comparisonStatus' => [ 'shape' => 'ComparisonStatusEnum', ], ], ], 'CompareDatabaseCDCSummary' => [ 'type' => 'structure', 'required' => [ 'stepInput', ], 'members' => [ 'stepInput' => [ 'shape' => 'CompareDatabaseCDCStepInput', ], 'stepOutput' => [ 'shape' => 'CompareDatabaseCDCStepOutput', ], ], ], 'CompareFileType' => [ 'type' => 'structure', 'members' => [ 'datasets' => [ 'shape' => 'CompareDataSetsSummary', ], 'databaseCDC' => [ 'shape' => 'CompareDatabaseCDCSummary', ], ], 'union' => true, ], 'ComparisonStatusEnum' => [ 'type' => 'string', 'enum' => [ 'Different', 'Equivalent', 'Equal', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CreateCloudFormationStepInput' => [ 'type' => 'structure', 'required' => [ 'templateLocation', ], 'members' => [ 'templateLocation' => [ 'shape' => 'S3Uri', ], 'parameters' => [ 'shape' => 'Properties', ], ], ], 'CreateCloudFormationStepOutput' => [ 'type' => 'structure', 'required' => [ 'stackId', ], 'members' => [ 'stackId' => [ 'shape' => 'String', ], 'exports' => [ 'shape' => 'Properties', ], ], ], 'CreateCloudFormationSummary' => [ 'type' => 'structure', 'required' => [ 'stepInput', ], 'members' => [ 'stepInput' => [ 'shape' => 'CreateCloudFormationStepInput', ], 'stepOutput' => [ 'shape' => 'CreateCloudFormationStepOutput', ], ], ], 'CreateTestCaseRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'steps', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'steps' => [ 'shape' => 'StepList', ], 'clientToken' => [ 'shape' => 'IdempotencyTokenString', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateTestCaseResponse' => [ 'type' => 'structure', 'required' => [ 'testCaseId', 'testCaseVersion', ], 'members' => [ 'testCaseId' => [ 'shape' => 'Identifier', ], 'testCaseVersion' => [ 'shape' => 'Version', ], ], ], 'CreateTestConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'resources', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'resources' => [ 'shape' => 'ResourceList', ], 'properties' => [ 'shape' => 'Properties', ], 'clientToken' => [ 'shape' => 'IdempotencyTokenString', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], 'serviceSettings' => [ 'shape' => 'ServiceSettings', ], ], ], 'CreateTestConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'testConfigurationId', 'testConfigurationVersion', ], 'members' => [ 'testConfigurationId' => [ 'shape' => 'Identifier', ], 'testConfigurationVersion' => [ 'shape' => 'Version', ], ], ], 'CreateTestSuiteRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'testCases', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'beforeSteps' => [ 'shape' => 'StepList', ], 'afterSteps' => [ 'shape' => 'StepList', ], 'testCases' => [ 'shape' => 'TestCases', ], 'clientToken' => [ 'shape' => 'IdempotencyTokenString', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateTestSuiteResponse' => [ 'type' => 'structure', 'required' => [ 'testSuiteId', 'testSuiteVersion', ], 'members' => [ 'testSuiteId' => [ 'shape' => 'Identifier', ], 'testSuiteVersion' => [ 'shape' => 'Version', ], ], ], 'DataSet' => [ 'type' => 'structure', 'required' => [ 'type', 'name', 'ccsid', 'format', 'length', ], 'members' => [ 'type' => [ 'shape' => 'DataSetType', ], 'name' => [ 'shape' => 'String100', ], 'ccsid' => [ 'shape' => 'String50', ], 'format' => [ 'shape' => 'Format', ], 'length' => [ 'shape' => 'Integer', ], ], ], 'DataSetList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DataSet', ], ], 'DataSetType' => [ 'type' => 'string', 'enum' => [ 'PS', ], ], 'DatabaseCDC' => [ 'type' => 'structure', 'required' => [ 'sourceMetadata', 'targetMetadata', ], 'members' => [ 'sourceMetadata' => [ 'shape' => 'SourceDatabaseMetadata', ], 'targetMetadata' => [ 'shape' => 'TargetDatabaseMetadata', ], ], ], 'DeleteCloudFormationStepInput' => [ 'type' => 'structure', 'required' => [ 'stackId', ], 'members' => [ 'stackId' => [ 'shape' => 'String', ], ], ], 'DeleteCloudFormationStepOutput' => [ 'type' => 'structure', 'members' => [], ], 'DeleteCloudFormationSummary' => [ 'type' => 'structure', 'required' => [ 'stepInput', ], 'members' => [ 'stepInput' => [ 'shape' => 'DeleteCloudFormationStepInput', ], 'stepOutput' => [ 'shape' => 'DeleteCloudFormationStepOutput', ], ], ], 'DeleteTestCaseRequest' => [ 'type' => 'structure', 'required' => [ 'testCaseId', ], 'members' => [ 'testCaseId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'testCaseId', ], ], ], 'DeleteTestCaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTestConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'testConfigurationId', ], 'members' => [ 'testConfigurationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'testConfigurationId', ], ], ], 'DeleteTestConfigurationResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTestRunRequest' => [ 'type' => 'structure', 'required' => [ 'testRunId', ], 'members' => [ 'testRunId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'testRunId', ], ], ], 'DeleteTestRunResponse' => [ 'type' => 'structure', 'members' => [], ], 'DeleteTestSuiteRequest' => [ 'type' => 'structure', 'required' => [ 'testSuiteId', ], 'members' => [ 'testSuiteId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'testSuiteId', ], ], ], 'DeleteTestSuiteResponse' => [ 'type' => 'structure', 'members' => [], ], 'ExportDataSetNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'String100', ], ], 'File' => [ 'type' => 'structure', 'members' => [ 'fileType' => [ 'shape' => 'CompareFileType', ], ], 'union' => true, ], 'FileMetadata' => [ 'type' => 'structure', 'members' => [ 'dataSets' => [ 'shape' => 'DataSetList', ], 'databaseCDC' => [ 'shape' => 'DatabaseCDC', ], ], 'union' => true, ], 'Format' => [ 'type' => 'string', 'enum' => [ 'FIXED', 'VARIABLE', 'LINE_SEQUENTIAL', ], ], 'GetTestCaseRequest' => [ 'type' => 'structure', 'required' => [ 'testCaseId', ], 'members' => [ 'testCaseId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'testCaseId', ], 'testCaseVersion' => [ 'shape' => 'Version', 'location' => 'querystring', 'locationName' => 'testCaseVersion', ], ], ], 'GetTestCaseResponse' => [ 'type' => 'structure', 'required' => [ 'testCaseId', 'testCaseArn', 'name', 'latestVersion', 'testCaseVersion', 'status', 'creationTime', 'lastUpdateTime', 'steps', ], 'members' => [ 'testCaseId' => [ 'shape' => 'Identifier', ], 'testCaseArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'latestVersion' => [ 'shape' => 'TestCaseLatestVersion', ], 'testCaseVersion' => [ 'shape' => 'Version', ], 'status' => [ 'shape' => 'TestCaseLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdateTime' => [ 'shape' => 'Timestamp', ], 'steps' => [ 'shape' => 'StepList', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'GetTestConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'testConfigurationId', ], 'members' => [ 'testConfigurationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'testConfigurationId', ], 'testConfigurationVersion' => [ 'shape' => 'Version', 'location' => 'querystring', 'locationName' => 'testConfigurationVersion', ], ], ], 'GetTestConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'testConfigurationId', 'name', 'testConfigurationArn', 'latestVersion', 'testConfigurationVersion', 'status', 'creationTime', 'lastUpdateTime', 'resources', 'properties', ], 'members' => [ 'testConfigurationId' => [ 'shape' => 'Identifier', ], 'name' => [ 'shape' => 'ResourceName', ], 'testConfigurationArn' => [ 'shape' => 'Arn', ], 'latestVersion' => [ 'shape' => 'TestConfigurationLatestVersion', ], 'testConfigurationVersion' => [ 'shape' => 'Version', ], 'status' => [ 'shape' => 'TestConfigurationLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdateTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'resources' => [ 'shape' => 'ResourceList', ], 'properties' => [ 'shape' => 'Properties', ], 'tags' => [ 'shape' => 'TagMap', ], 'serviceSettings' => [ 'shape' => 'ServiceSettings', ], ], ], 'GetTestRunStepRequest' => [ 'type' => 'structure', 'required' => [ 'testRunId', 'stepName', ], 'members' => [ 'testRunId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'testRunId', ], 'stepName' => [ 'shape' => 'ResourceName', 'location' => 'uri', 'locationName' => 'stepName', ], 'testCaseId' => [ 'shape' => 'Identifier', 'location' => 'querystring', 'locationName' => 'testCaseId', ], 'testSuiteId' => [ 'shape' => 'Identifier', 'location' => 'querystring', 'locationName' => 'testSuiteId', ], ], ], 'GetTestRunStepResponse' => [ 'type' => 'structure', 'required' => [ 'stepName', 'testRunId', 'status', 'runStartTime', ], 'members' => [ 'stepName' => [ 'shape' => 'ResourceName', ], 'testRunId' => [ 'shape' => 'Identifier', ], 'testCaseId' => [ 'shape' => 'Identifier', ], 'testCaseVersion' => [ 'shape' => 'Version', ], 'testSuiteId' => [ 'shape' => 'Identifier', ], 'testSuiteVersion' => [ 'shape' => 'Version', ], 'beforeStep' => [ 'shape' => 'Boolean', ], 'afterStep' => [ 'shape' => 'Boolean', ], 'status' => [ 'shape' => 'StepRunStatus', ], 'statusReason' => [ 'shape' => 'String', ], 'runStartTime' => [ 'shape' => 'Timestamp', ], 'runEndTime' => [ 'shape' => 'Timestamp', ], 'stepRunSummary' => [ 'shape' => 'StepRunSummary', ], ], ], 'GetTestSuiteRequest' => [ 'type' => 'structure', 'required' => [ 'testSuiteId', ], 'members' => [ 'testSuiteId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'testSuiteId', ], 'testSuiteVersion' => [ 'shape' => 'Version', 'location' => 'querystring', 'locationName' => 'testSuiteVersion', ], ], ], 'GetTestSuiteResponse' => [ 'type' => 'structure', 'required' => [ 'testSuiteId', 'name', 'latestVersion', 'testSuiteVersion', 'testSuiteArn', 'creationTime', 'lastUpdateTime', 'beforeSteps', 'afterSteps', 'testCases', ], 'members' => [ 'testSuiteId' => [ 'shape' => 'Identifier', ], 'name' => [ 'shape' => 'ResourceName', ], 'latestVersion' => [ 'shape' => 'TestSuiteLatestVersion', ], 'testSuiteVersion' => [ 'shape' => 'Version', ], 'status' => [ 'shape' => 'TestSuiteLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], 'testSuiteArn' => [ 'shape' => 'Arn', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdateTime' => [ 'shape' => 'Timestamp', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'beforeSteps' => [ 'shape' => 'StepList', ], 'afterSteps' => [ 'shape' => 'StepList', ], 'testCases' => [ 'shape' => 'TestCases', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'IdempotencyTokenString' => [ 'type' => 'string', 'pattern' => '[A-Za-z0-9\\-]{1,64}', ], 'Identifier' => [ 'type' => 'string', 'pattern' => '[A-Za-z0-9:/\\-]{1,100}', ], 'Input' => [ 'type' => 'structure', 'members' => [ 'file' => [ 'shape' => 'InputFile', ], ], 'union' => true, ], 'InputFile' => [ 'type' => 'structure', 'required' => [ 'sourceLocation', 'targetLocation', 'fileMetadata', ], 'members' => [ 'sourceLocation' => [ 'shape' => 'Variable', ], 'targetLocation' => [ 'shape' => 'Variable', ], 'fileMetadata' => [ 'shape' => 'FileMetadata', ], ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'ListTestCasesRequest' => [ 'type' => 'structure', 'members' => [ 'testCaseIds' => [ 'shape' => 'TestCaseIdList', 'location' => 'querystring', 'locationName' => 'testCaseIds', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListTestCasesResponse' => [ 'type' => 'structure', 'required' => [ 'testCases', ], 'members' => [ 'testCases' => [ 'shape' => 'TestCaseSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTestConfigurationsRequest' => [ 'type' => 'structure', 'members' => [ 'testConfigurationIds' => [ 'shape' => 'TestConfigurationIdList', 'location' => 'querystring', 'locationName' => 'testConfigurationIds', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListTestConfigurationsResponse' => [ 'type' => 'structure', 'required' => [ 'testConfigurations', ], 'members' => [ 'testConfigurations' => [ 'shape' => 'TestConfigurationList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTestRunStepsRequest' => [ 'type' => 'structure', 'required' => [ 'testRunId', ], 'members' => [ 'testRunId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'testRunId', ], 'testCaseId' => [ 'shape' => 'Identifier', 'location' => 'querystring', 'locationName' => 'testCaseId', ], 'testSuiteId' => [ 'shape' => 'Identifier', 'location' => 'querystring', 'locationName' => 'testSuiteId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListTestRunStepsResponse' => [ 'type' => 'structure', 'required' => [ 'testRunSteps', ], 'members' => [ 'testRunSteps' => [ 'shape' => 'TestRunStepSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTestRunTestCasesRequest' => [ 'type' => 'structure', 'required' => [ 'testRunId', ], 'members' => [ 'testRunId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'testRunId', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListTestRunTestCasesResponse' => [ 'type' => 'structure', 'required' => [ 'testRunTestCases', ], 'members' => [ 'testRunTestCases' => [ 'shape' => 'TestCaseRunSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTestRunsRequest' => [ 'type' => 'structure', 'members' => [ 'testSuiteId' => [ 'shape' => 'Identifier', 'location' => 'querystring', 'locationName' => 'testSuiteId', ], 'testRunIds' => [ 'shape' => 'TestRunIdList', 'location' => 'querystring', 'locationName' => 'testrunIds', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListTestRunsResponse' => [ 'type' => 'structure', 'required' => [ 'testRuns', ], 'members' => [ 'testRuns' => [ 'shape' => 'TestRunSummaryList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTestSuitesRequest' => [ 'type' => 'structure', 'members' => [ 'testSuiteIds' => [ 'shape' => 'TestSuiteIdList', 'location' => 'querystring', 'locationName' => 'testSuiteIds', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListTestSuitesResponse' => [ 'type' => 'structure', 'required' => [ 'testSuites', ], 'members' => [ 'testSuites' => [ 'shape' => 'TestSuiteList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'M2ManagedActionProperties' => [ 'type' => 'structure', 'members' => [ 'forceStop' => [ 'shape' => 'Boolean', ], 'importDataSetLocation' => [ 'shape' => 'Variable', ], ], ], 'M2ManagedActionType' => [ 'type' => 'string', 'enum' => [ 'Configure', 'Deconfigure', ], ], 'M2ManagedApplication' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'runtime', ], 'members' => [ 'applicationId' => [ 'shape' => 'Variable', ], 'runtime' => [ 'shape' => 'M2ManagedRuntime', ], 'vpcEndpointServiceName' => [ 'shape' => 'Variable', ], 'listenerPort' => [ 'shape' => 'Variable', ], ], ], 'M2ManagedApplicationAction' => [ 'type' => 'structure', 'required' => [ 'resource', 'actionType', ], 'members' => [ 'resource' => [ 'shape' => 'Variable', ], 'actionType' => [ 'shape' => 'M2ManagedActionType', ], 'properties' => [ 'shape' => 'M2ManagedActionProperties', ], ], ], 'M2ManagedApplicationStepInput' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'runtime', 'actionType', ], 'members' => [ 'applicationId' => [ 'shape' => 'String', ], 'runtime' => [ 'shape' => 'String', ], 'vpcEndpointServiceName' => [ 'shape' => 'String', ], 'listenerPort' => [ 'shape' => 'Integer', ], 'actionType' => [ 'shape' => 'M2ManagedActionType', ], 'properties' => [ 'shape' => 'M2ManagedActionProperties', ], ], ], 'M2ManagedApplicationStepOutput' => [ 'type' => 'structure', 'members' => [ 'importDataSetSummary' => [ 'shape' => 'Properties', ], ], ], 'M2ManagedApplicationStepSummary' => [ 'type' => 'structure', 'required' => [ 'stepInput', ], 'members' => [ 'stepInput' => [ 'shape' => 'M2ManagedApplicationStepInput', ], 'stepOutput' => [ 'shape' => 'M2ManagedApplicationStepOutput', ], ], ], 'M2ManagedApplicationSummary' => [ 'type' => 'structure', 'required' => [ 'applicationId', 'runtime', ], 'members' => [ 'applicationId' => [ 'shape' => 'Identifier', ], 'runtime' => [ 'shape' => 'M2ManagedRuntime', ], 'listenerPort' => [ 'shape' => 'Integer', ], ], ], 'M2ManagedRuntime' => [ 'type' => 'string', 'enum' => [ 'MicroFocus', ], ], 'M2NonManagedActionType' => [ 'type' => 'string', 'enum' => [ 'Configure', 'Deconfigure', ], ], 'M2NonManagedApplication' => [ 'type' => 'structure', 'required' => [ 'vpcEndpointServiceName', 'listenerPort', 'runtime', ], 'members' => [ 'vpcEndpointServiceName' => [ 'shape' => 'Variable', ], 'listenerPort' => [ 'shape' => 'Variable', ], 'runtime' => [ 'shape' => 'M2NonManagedRuntime', ], 'webAppName' => [ 'shape' => 'Variable', ], ], ], 'M2NonManagedApplicationAction' => [ 'type' => 'structure', 'required' => [ 'resource', 'actionType', ], 'members' => [ 'resource' => [ 'shape' => 'Variable', ], 'actionType' => [ 'shape' => 'M2NonManagedActionType', ], ], ], 'M2NonManagedApplicationStepInput' => [ 'type' => 'structure', 'required' => [ 'vpcEndpointServiceName', 'listenerPort', 'runtime', 'actionType', ], 'members' => [ 'vpcEndpointServiceName' => [ 'shape' => 'String', ], 'listenerPort' => [ 'shape' => 'Integer', ], 'runtime' => [ 'shape' => 'M2NonManagedRuntime', ], 'webAppName' => [ 'shape' => 'String', ], 'actionType' => [ 'shape' => 'M2NonManagedActionType', ], ], ], 'M2NonManagedApplicationStepOutput' => [ 'type' => 'structure', 'members' => [], ], 'M2NonManagedApplicationStepSummary' => [ 'type' => 'structure', 'required' => [ 'stepInput', ], 'members' => [ 'stepInput' => [ 'shape' => 'M2NonManagedApplicationStepInput', ], 'stepOutput' => [ 'shape' => 'M2NonManagedApplicationStepOutput', ], ], ], 'M2NonManagedApplicationSummary' => [ 'type' => 'structure', 'required' => [ 'vpcEndpointServiceName', 'listenerPort', 'runtime', ], 'members' => [ 'vpcEndpointServiceName' => [ 'shape' => 'String', ], 'listenerPort' => [ 'shape' => 'Integer', ], 'runtime' => [ 'shape' => 'M2NonManagedRuntime', ], 'webAppName' => [ 'shape' => 'String', ], ], ], 'M2NonManagedRuntime' => [ 'type' => 'string', 'enum' => [ 'BluAge', ], ], 'MainframeAction' => [ 'type' => 'structure', 'required' => [ 'resource', 'actionType', ], 'members' => [ 'resource' => [ 'shape' => 'Variable', ], 'actionType' => [ 'shape' => 'MainframeActionType', ], 'properties' => [ 'shape' => 'MainframeActionProperties', ], ], ], 'MainframeActionProperties' => [ 'type' => 'structure', 'members' => [ 'dmsTaskArn' => [ 'shape' => 'Variable', ], ], ], 'MainframeActionSummary' => [ 'type' => 'structure', 'members' => [ 'batch' => [ 'shape' => 'BatchSummary', ], 'tn3270' => [ 'shape' => 'TN3270Summary', ], ], 'union' => true, ], 'MainframeActionType' => [ 'type' => 'structure', 'members' => [ 'batch' => [ 'shape' => 'Batch', ], 'tn3270' => [ 'shape' => 'TN3270', ], ], 'union' => true, ], 'MainframeResourceSummary' => [ 'type' => 'structure', 'members' => [ 'm2ManagedApplication' => [ 'shape' => 'M2ManagedApplicationSummary', ], 'm2NonManagedApplication' => [ 'shape' => 'M2NonManagedApplicationSummary', ], ], 'union' => true, ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'pattern' => '\\S{1,2000}', ], 'Output' => [ 'type' => 'structure', 'members' => [ 'file' => [ 'shape' => 'OutputFile', ], ], 'union' => true, ], 'OutputFile' => [ 'type' => 'structure', 'members' => [ 'fileLocation' => [ 'shape' => 'S3Uri', ], ], ], 'Properties' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'Resource' => [ 'type' => 'structure', 'required' => [ 'name', 'type', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'type' => [ 'shape' => 'ResourceType', ], ], ], 'ResourceAction' => [ 'type' => 'structure', 'members' => [ 'm2ManagedApplicationAction' => [ 'shape' => 'M2ManagedApplicationAction', ], 'm2NonManagedApplicationAction' => [ 'shape' => 'M2NonManagedApplicationAction', ], 'cloudFormationAction' => [ 'shape' => 'CloudFormationAction', ], ], 'union' => true, ], 'ResourceActionSummary' => [ 'type' => 'structure', 'members' => [ 'cloudFormation' => [ 'shape' => 'CloudFormationStepSummary', ], 'm2ManagedApplication' => [ 'shape' => 'M2ManagedApplicationStepSummary', ], 'm2NonManagedApplication' => [ 'shape' => 'M2NonManagedApplicationStepSummary', ], ], 'union' => true, ], 'ResourceDescription' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'ResourceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Resource', ], 'max' => 20, 'min' => 1, ], 'ResourceName' => [ 'type' => 'string', 'pattern' => '[A-Za-z][A-Za-z0-9_\\-]{1,59}', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ResourceType' => [ 'type' => 'structure', 'members' => [ 'cloudFormation' => [ 'shape' => 'CloudFormation', ], 'm2ManagedApplication' => [ 'shape' => 'M2ManagedApplication', ], 'm2NonManagedApplication' => [ 'shape' => 'M2NonManagedApplication', ], ], 'union' => true, ], 'S3Uri' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'Script' => [ 'type' => 'structure', 'required' => [ 'scriptLocation', 'type', ], 'members' => [ 'scriptLocation' => [ 'shape' => 'S3Uri', ], 'type' => [ 'shape' => 'ScriptType', ], ], ], 'ScriptSummary' => [ 'type' => 'structure', 'required' => [ 'scriptLocation', 'type', ], 'members' => [ 'scriptLocation' => [ 'shape' => 'S3Uri', ], 'type' => [ 'shape' => 'ScriptType', ], ], ], 'ScriptType' => [ 'type' => 'string', 'enum' => [ 'Selenium', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'ServiceSettings' => [ 'type' => 'structure', 'members' => [ 'kmsKeyId' => [ 'shape' => 'String', ], ], ], 'SourceDatabase' => [ 'type' => 'string', 'enum' => [ 'z/OS-DB2', ], ], 'SourceDatabaseMetadata' => [ 'type' => 'structure', 'required' => [ 'type', 'captureTool', ], 'members' => [ 'type' => [ 'shape' => 'SourceDatabase', ], 'captureTool' => [ 'shape' => 'CaptureTool', ], ], ], 'StartTestRunRequest' => [ 'type' => 'structure', 'required' => [ 'testSuiteId', ], 'members' => [ 'testSuiteId' => [ 'shape' => 'Identifier', ], 'testConfigurationId' => [ 'shape' => 'Identifier', ], 'clientToken' => [ 'shape' => 'IdempotencyTokenString', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'StartTestRunResponse' => [ 'type' => 'structure', 'required' => [ 'testRunId', 'testRunStatus', ], 'members' => [ 'testRunId' => [ 'shape' => 'Identifier', ], 'testRunStatus' => [ 'shape' => 'TestRunStatus', ], ], ], 'Step' => [ 'type' => 'structure', 'required' => [ 'name', 'action', ], 'members' => [ 'name' => [ 'shape' => 'ResourceName', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'action' => [ 'shape' => 'StepAction', ], ], ], 'StepAction' => [ 'type' => 'structure', 'members' => [ 'resourceAction' => [ 'shape' => 'ResourceAction', ], 'mainframeAction' => [ 'shape' => 'MainframeAction', ], 'compareAction' => [ 'shape' => 'CompareAction', ], ], 'union' => true, ], 'StepList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Step', ], 'max' => 20, 'min' => 1, ], 'StepRunStatus' => [ 'type' => 'string', 'enum' => [ 'Success', 'Failed', 'Running', ], ], 'StepRunSummary' => [ 'type' => 'structure', 'members' => [ 'mainframeAction' => [ 'shape' => 'MainframeActionSummary', ], 'compareAction' => [ 'shape' => 'CompareActionSummary', ], 'resourceAction' => [ 'shape' => 'ResourceActionSummary', ], ], 'union' => true, ], 'String' => [ 'type' => 'string', ], 'String100' => [ 'type' => 'string', 'pattern' => '\\S{1,100}', ], 'String50' => [ 'type' => 'string', 'pattern' => '\\S{1,50}', ], 'TN3270' => [ 'type' => 'structure', 'required' => [ 'script', ], 'members' => [ 'script' => [ 'shape' => 'Script', ], 'exportDataSetNames' => [ 'shape' => 'ExportDataSetNames', ], ], ], 'TN3270StepInput' => [ 'type' => 'structure', 'required' => [ 'resource', 'script', ], 'members' => [ 'resource' => [ 'shape' => 'MainframeResourceSummary', ], 'script' => [ 'shape' => 'ScriptSummary', ], 'exportDataSetNames' => [ 'shape' => 'ExportDataSetNames', ], 'properties' => [ 'shape' => 'MainframeActionProperties', ], ], ], 'TN3270StepOutput' => [ 'type' => 'structure', 'required' => [ 'scriptOutputLocation', ], 'members' => [ 'dataSetExportLocation' => [ 'shape' => 'S3Uri', ], 'dmsOutputLocation' => [ 'shape' => 'S3Uri', ], 'dataSetDetails' => [ 'shape' => 'DataSetList', ], 'scriptOutputLocation' => [ 'shape' => 'S3Uri', ], ], ], 'TN3270Summary' => [ 'type' => 'structure', 'required' => [ 'stepInput', ], 'members' => [ 'stepInput' => [ 'shape' => 'TN3270StepInput', ], 'stepOutput' => [ 'shape' => 'TN3270StepOutput', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '(?!aws:).+', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TargetDatabase' => [ 'type' => 'string', 'enum' => [ 'PostgreSQL', ], ], 'TargetDatabaseMetadata' => [ 'type' => 'structure', 'required' => [ 'type', 'captureTool', ], 'members' => [ 'type' => [ 'shape' => 'TargetDatabase', ], 'captureTool' => [ 'shape' => 'CaptureTool', ], ], ], 'TestCaseIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Identifier', ], ], 'TestCaseLatestVersion' => [ 'type' => 'structure', 'required' => [ 'version', 'status', ], 'members' => [ 'version' => [ 'shape' => 'Version', ], 'status' => [ 'shape' => 'TestCaseLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], ], ], 'TestCaseLifecycle' => [ 'type' => 'string', 'enum' => [ 'Active', 'Deleting', ], ], 'TestCaseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Identifier', ], ], 'TestCaseRunStatus' => [ 'type' => 'string', 'enum' => [ 'Success', 'Running', 'Failed', ], ], 'TestCaseRunSummary' => [ 'type' => 'structure', 'required' => [ 'testCaseId', 'testCaseVersion', 'testRunId', 'status', 'runStartTime', ], 'members' => [ 'testCaseId' => [ 'shape' => 'Identifier', ], 'testCaseVersion' => [ 'shape' => 'Version', ], 'testRunId' => [ 'shape' => 'Identifier', ], 'status' => [ 'shape' => 'TestCaseRunStatus', ], 'statusReason' => [ 'shape' => 'String', ], 'runStartTime' => [ 'shape' => 'Timestamp', ], 'runEndTime' => [ 'shape' => 'Timestamp', ], ], ], 'TestCaseRunSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestCaseRunSummary', ], ], 'TestCaseSummary' => [ 'type' => 'structure', 'required' => [ 'testCaseId', 'testCaseArn', 'name', 'latestVersion', 'status', 'creationTime', 'lastUpdateTime', ], 'members' => [ 'testCaseId' => [ 'shape' => 'Identifier', ], 'testCaseArn' => [ 'shape' => 'Arn', ], 'name' => [ 'shape' => 'ResourceName', ], 'statusReason' => [ 'shape' => 'String', ], 'latestVersion' => [ 'shape' => 'Version', ], 'status' => [ 'shape' => 'TestCaseLifecycle', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'TestCaseSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestCaseSummary', ], ], 'TestCases' => [ 'type' => 'structure', 'members' => [ 'sequential' => [ 'shape' => 'TestCaseList', ], ], 'union' => true, ], 'TestConfigurationIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Identifier', ], ], 'TestConfigurationLatestVersion' => [ 'type' => 'structure', 'required' => [ 'version', 'status', ], 'members' => [ 'version' => [ 'shape' => 'Version', ], 'status' => [ 'shape' => 'TestConfigurationLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], ], ], 'TestConfigurationLifecycle' => [ 'type' => 'string', 'enum' => [ 'Active', 'Deleting', ], ], 'TestConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestConfigurationSummary', ], ], 'TestConfigurationSummary' => [ 'type' => 'structure', 'required' => [ 'testConfigurationId', 'name', 'latestVersion', 'testConfigurationArn', 'status', 'creationTime', 'lastUpdateTime', ], 'members' => [ 'testConfigurationId' => [ 'shape' => 'Identifier', ], 'name' => [ 'shape' => 'ResourceName', ], 'statusReason' => [ 'shape' => 'String', ], 'latestVersion' => [ 'shape' => 'Version', ], 'testConfigurationArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'TestConfigurationLifecycle', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'TestRunIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Identifier', ], ], 'TestRunStatus' => [ 'type' => 'string', 'enum' => [ 'Success', 'Running', 'Failed', 'Deleting', ], ], 'TestRunStepSummary' => [ 'type' => 'structure', 'required' => [ 'stepName', 'testRunId', 'status', 'runStartTime', ], 'members' => [ 'stepName' => [ 'shape' => 'ResourceName', ], 'testRunId' => [ 'shape' => 'Identifier', ], 'testCaseId' => [ 'shape' => 'Identifier', ], 'testCaseVersion' => [ 'shape' => 'Version', ], 'testSuiteId' => [ 'shape' => 'Identifier', ], 'testSuiteVersion' => [ 'shape' => 'Version', ], 'beforeStep' => [ 'shape' => 'Boolean', ], 'afterStep' => [ 'shape' => 'Boolean', ], 'status' => [ 'shape' => 'StepRunStatus', ], 'statusReason' => [ 'shape' => 'String', ], 'runStartTime' => [ 'shape' => 'Timestamp', ], 'runEndTime' => [ 'shape' => 'Timestamp', ], ], ], 'TestRunStepSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestRunStepSummary', ], ], 'TestRunSummary' => [ 'type' => 'structure', 'required' => [ 'testRunId', 'testRunArn', 'testSuiteId', 'testSuiteVersion', 'status', 'runStartTime', ], 'members' => [ 'testRunId' => [ 'shape' => 'Identifier', ], 'testRunArn' => [ 'shape' => 'Arn', ], 'testSuiteId' => [ 'shape' => 'Identifier', ], 'testSuiteVersion' => [ 'shape' => 'Version', ], 'testConfigurationId' => [ 'shape' => 'Identifier', ], 'testConfigurationVersion' => [ 'shape' => 'Version', ], 'status' => [ 'shape' => 'TestRunStatus', ], 'statusReason' => [ 'shape' => 'String', ], 'runStartTime' => [ 'shape' => 'Timestamp', ], 'runEndTime' => [ 'shape' => 'Timestamp', ], ], ], 'TestRunSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestRunSummary', ], ], 'TestSuiteIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Identifier', ], ], 'TestSuiteLatestVersion' => [ 'type' => 'structure', 'required' => [ 'version', 'status', ], 'members' => [ 'version' => [ 'shape' => 'Version', ], 'status' => [ 'shape' => 'TestSuiteLifecycle', ], 'statusReason' => [ 'shape' => 'String', ], ], ], 'TestSuiteLifecycle' => [ 'type' => 'string', 'enum' => [ 'Creating', 'Updating', 'Active', 'Failed', 'Deleting', ], ], 'TestSuiteList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TestSuiteSummary', ], ], 'TestSuiteSummary' => [ 'type' => 'structure', 'required' => [ 'testSuiteId', 'name', 'latestVersion', 'testSuiteArn', 'status', 'creationTime', 'lastUpdateTime', ], 'members' => [ 'testSuiteId' => [ 'shape' => 'Identifier', ], 'name' => [ 'shape' => 'ResourceName', ], 'statusReason' => [ 'shape' => 'String', ], 'latestVersion' => [ 'shape' => 'Version', ], 'testSuiteArn' => [ 'shape' => 'Arn', ], 'status' => [ 'shape' => 'TestSuiteLifecycle', ], 'creationTime' => [ 'shape' => 'Timestamp', ], 'lastUpdateTime' => [ 'shape' => 'Timestamp', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateTestCaseRequest' => [ 'type' => 'structure', 'required' => [ 'testCaseId', ], 'members' => [ 'testCaseId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'testCaseId', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'steps' => [ 'shape' => 'StepList', ], ], ], 'UpdateTestCaseResponse' => [ 'type' => 'structure', 'required' => [ 'testCaseId', 'testCaseVersion', ], 'members' => [ 'testCaseId' => [ 'shape' => 'Identifier', ], 'testCaseVersion' => [ 'shape' => 'Version', ], ], ], 'UpdateTestConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'testConfigurationId', ], 'members' => [ 'testConfigurationId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'testConfigurationId', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'resources' => [ 'shape' => 'ResourceList', ], 'properties' => [ 'shape' => 'Properties', ], 'serviceSettings' => [ 'shape' => 'ServiceSettings', ], ], ], 'UpdateTestConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'testConfigurationId', 'testConfigurationVersion', ], 'members' => [ 'testConfigurationId' => [ 'shape' => 'Identifier', ], 'testConfigurationVersion' => [ 'shape' => 'Version', ], ], ], 'UpdateTestSuiteRequest' => [ 'type' => 'structure', 'required' => [ 'testSuiteId', ], 'members' => [ 'testSuiteId' => [ 'shape' => 'Identifier', 'location' => 'uri', 'locationName' => 'testSuiteId', ], 'description' => [ 'shape' => 'ResourceDescription', ], 'beforeSteps' => [ 'shape' => 'StepList', ], 'afterSteps' => [ 'shape' => 'StepList', ], 'testCases' => [ 'shape' => 'TestCases', ], ], ], 'UpdateTestSuiteResponse' => [ 'type' => 'structure', 'required' => [ 'testSuiteId', ], 'members' => [ 'testSuiteId' => [ 'shape' => 'Identifier', ], 'testSuiteVersion' => [ 'shape' => 'Version', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', ], ], 'Variable' => [ 'type' => 'string', 'pattern' => '\\S{1,1000}', ], 'Version' => [ 'type' => 'integer', 'box' => true, ], ],];
