let currentStep = 1;
const totalSteps = 6;
let selectedFiles = [];
const maxFileSize = 10 * 1024 * 1024; // 10MB
const allowedTypes = {
  "application/pdf": "pdf",
  "image/jpeg": "image",
  "image/jpg": "image",
  "image/png": "image",
  "audio/mpeg": "audio",
  "audio/mp3": "audio",
  "audio/wav": "audio",
  "video/mp4": "video",
  "application/msword": "document",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
    "document",
};

// File upload handling
const fileInput = document.getElementById('fileInput');
const fileUploadArea = document.getElementById('fileUploadArea');
const filePreviewContainer = document.getElementById('filePreviewContainer');
const filePreviewList = document.getElementById('filePreviewList');
const uploadProgressContainer = document.getElementById('uploadProgressContainer');
const uploadProgressFill = document.getElementById('uploadProgressFill');
const uploadProgressText = document.getElementById('uploadProgressText');
const form = document.getElementById('multiStepForm');

let uploadedFiles = [];

// Fungsi untuk menampilkan step tertentu
function showStep(step) {
  // Sembunyikan semua step
  document.querySelectorAll(".step").forEach((stepElement) => {
    stepElement.classList.remove("active");
  });

  // Tampilkan step yang dipilih
  const targetStep = document.querySelector(`[data-step="${step}"]`);
  if (targetStep) {
    targetStep.classList.add("active");
  }

  // Update progress bar
  updateProgressBar(step);

  // Update progress text
  document.getElementById(
    "progress-text"
  ).textContent = `Step ${step} of ${totalSteps}`;

  // Scroll ke atas
  window.scrollTo({ top: 0, behavior: "smooth" });
}

// Fungsi untuk update progress bar
function updateProgressBar(step) {
  const progressBar = document.getElementById("progress-bar");
  const percentage = (step / totalSteps) * 100;
  progressBar.style.width = `${percentage}%`;
}

// Fungsi untuk validasi step saat ini
function validateCurrentStep() {
  const currentStepElement = document.querySelector(
    `[data-step="${currentStep}"]`
  );
  const requiredFields = currentStepElement.querySelectorAll("[required]");

  for (let field of requiredFields) {
    if (!field.value.trim()) {
      field.focus();
      field.classList.add("border-red-500");
      showNotification("Mohon lengkapi semua field yang wajib diisi", "error");
      return false;
    } else {
      field.classList.remove("border-red-500");
    }

    // Validasi khusus untuk radio button
    if (field.type === "radio") {
      const radioGroup = currentStepElement.querySelectorAll(
        `[name="${field.name}"]`
      );
      const isChecked = Array.from(radioGroup).some((radio) => radio.checked);
      if (!isChecked) {
        showNotification("Mohon pilih salah satu opsi", "error");
        return false;
      }
    }
  }

  return true;
}

// Fungsi untuk ke step berikutnya
function nextStep() {
  if (validateCurrentStep()) {
    if (currentStep < totalSteps) {
      currentStep++;
      showStep(currentStep);
    }
  }
}

// Fungsi untuk ke step sebelumnya
function prevStep() {
  if (currentStep > 1) {
    currentStep--;
    showStep(currentStep);
  }
}

// Fungsi untuk toggle detail pengalaman
function toggleExperienceDetail(show) {
  const experienceDetail = document.getElementById("experience_detail");
  const textarea = experienceDetail.querySelector("textarea");

  if (show) {
    experienceDetail.style.display = "block";
    textarea.setAttribute("required", "required");
  } else {
    experienceDetail.style.display = "none";
    textarea.removeAttribute("required");
    textarea.value = "";
  }
}

// Fungsi untuk menampilkan notifikasi
function showNotification(message, type = "info") {
  // Hapus notifikasi yang ada
  const existingNotification = document.querySelector(".notification");
  if (existingNotification) {
    existingNotification.remove();
  }

  // Buat notifikasi baru
  const notification = document.createElement("div");
  notification.className = `notification fixed top-4 right-4 px-6 py-4 rounded-lg shadow-lg z-50 max-w-sm`;

  if (type === "error") {
    notification.className += " bg-red-500 text-white";
  } else if (type === "success") {
    notification.className += " bg-green-500 text-white";
  } else {
    notification.className += " bg-blue-500 text-white";
  }

  notification.textContent = message;
  document.body.appendChild(notification);

  // Hapus notifikasi setelah 3 detik
  setTimeout(() => {
    if (notification) {
      notification.remove();
    }
  }, 3000);
}

// Event listener untuk form submit
document.addEventListener("DOMContentLoaded", function () {
  const form = document.getElementById("multiStepForm");
  form.addEventListener("submit", handleFormSubmit);

  // Inisialisasi tampilan awal
  showStep(1);

  // Initialize file upload functionality
  initializeFileUpload();

  // Tambahkan event listener untuk input validation real-time
  const inputs = document.querySelectorAll("input, textarea, select");
  inputs.forEach((input) => {
    input.addEventListener("blur", function () {
      if (this.hasAttribute("required") && !this.value.trim()) {
        this.classList.add("border-red-500");
      } else {
        this.classList.remove("border-red-500");
      }
    });

    input.addEventListener("input", function () {
      if (this.classList.contains("border-red-500") && this.value.trim()) {
        this.classList.remove("border-red-500");
      }
    });
  });
});

// Fungsi untuk menangani submit form
function handleFormSubmit(event) {
  event.preventDefault();

  if (!validateCurrentStep()) {
    return;
  }

  // Simulasi pengiriman form dengan upload progress
  showNotification("Sedang mengirim aplikasi...", "info");

  // Show upload progress if files are selected
  //   if (selectedFiles.length > 0) {
  //     simulateUploadProgress();
  //   }

  // Simulasi delay pengiriman
  setTimeout(
    () => {
      // Sembunyikan form
      document.getElementById("multiStepForm").style.display = "none";

      // Tampilkan pesan sukses
      document.getElementById("successMessage").classList.remove("hidden");

      showNotification("Aplikasi berhasil dikirim!", "success");

      // Scroll ke pesan sukses
      document.getElementById("successMessage").scrollIntoView({
        behavior: "smooth",
      });
    },
    selectedFiles.length > 0 ? 3000 : 2000
  );
}

// Fungsi untuk navigasi dengan keyboard (optional)
document.addEventListener("keydown", function (event) {
  // Enter untuk lanjut (kecuali di textarea)
  if (event.key === "Enter" && event.target.tagName !== "TEXTAREA") {
    event.preventDefault();
    if (currentStep < totalSteps) {
      nextStep();
    }
  }

  // Escape untuk kembali
  if (event.key === "Escape") {
    if (currentStep > 1) {
      prevStep();
    }
  }
});

// Fungsi untuk menyimpan data form ke localStorage (optional)
function saveFormData() {
  const formData = new FormData(document.getElementById("multiStepForm"));
  const data = {};

  for (let [key, value] of formData.entries()) {
    data[key] = value;
  }

  localStorage.setItem("applicationFormData", JSON.stringify(data));
}

// Fungsi untuk memuat data form dari localStorage (optional)
function loadFormData() {
  const savedData = localStorage.getItem("applicationFormData");
  if (savedData) {
    const data = JSON.parse(savedData);
    const form = document.getElementById("multiStepForm");

    Object.keys(data).forEach((key) => {
      const field = form.querySelector(`[name="${key}"]`);
      if (field) {
        if (field.type === "radio") {
          const radioButton = form.querySelector(
            `[name="${key}"][value="${data[key]}"]`
          );
          if (radioButton) {
            radioButton.checked = true;
          }
        } else {
          field.value = data[key];
        }
      }
    });
  }
}

// Auto-save form data setiap kali user mengubah input
document.addEventListener("change", saveFormData);
document.addEventListener("input", saveFormData);

// Load saved data saat halaman dimuat
window.addEventListener("load", loadFormData);

// File Upload Functions
function initializeFileUpload() {
  // Handle drag and drop
  fileUploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    fileUploadArea.classList.add('drag-over');
  });

  fileUploadArea.addEventListener('dragleave', () => {
    fileUploadArea.classList.remove('drag-over');
  });

  fileUploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    fileUploadArea.classList.remove('drag-over');
    const files = e.dataTransfer.files;
    handleFiles(files);
  });

  // Handle file selection
  fileInput.addEventListener('change', (e) => {
    handleFiles(e.target.files);
  });

  // Handle click on upload area
  fileUploadArea.querySelector('.file-upload-button').addEventListener('click', () => {
    fileInput.click();
  });
}

async function handleFiles(files) {
  if (files.length === 0) return;

  // Validasi ukuran file (max 10MB)
  for (let file of files) {
    if (file.size > 10 * 1024 * 1024) {
      alert(`File ${file.name} terlalu besar. Maksimal 10MB per file.`);
      return;
    }
  }

  // Tampilkan preview dan mulai upload
  filePreviewContainer.style.display = 'block';
  uploadFiles(files);
}

async function uploadFiles(files) {
  uploadProgressContainer.style.display = 'block';
  let totalUploaded = 0;

  for (let file of files) {
    try {
      // Buat FormData untuk upload file
      const formData = new FormData();
      formData.append('file', file);
      formData.append('action', 'uploadFile');

      // Upload file melalui server
      const response = await fetch('config/r2.php', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Gagal upload file');
      }

      // Tambahkan file ke preview dan array uploadedFiles
      uploadedFiles.push({
        name: file.name,
        url: data.publicUrl
      });

      // Update preview
      const previewItem = document.createElement('div');
      previewItem.className = 'file-preview-item';
      previewItem.innerHTML = `
        <div class="file-preview-info">
          <span class="file-name">${file.name}</span>
          <span class="file-size">${formatFileSize(file.size)}</span>
        </div>
        <div class="file-preview-status success">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
      `;
      filePreviewList.appendChild(previewItem);

      // Update progress
      totalUploaded++;
      const progress = (totalUploaded / files.length) * 100;
      uploadProgressFill.style.width = `${progress}%`;
      uploadProgressText.textContent = `Uploading... ${Math.round(progress)}%`;

    } catch (error) {
      console.error('Upload error:', error);
      alert(`Gagal upload file ${file.name}: ${error.message}`);
    }
  }

  // Update form dengan URL file yang sudah diupload
  const portfolioFilesInput = document.createElement('input');
  portfolioFilesInput.type = 'hidden';
  portfolioFilesInput.name = 'portfolio_files';
  portfolioFilesInput.value = JSON.stringify(uploadedFiles);
  form.appendChild(portfolioFilesInput);

  // Reset progress setelah selesai
  setTimeout(() => {
    uploadProgressContainer.style.display = 'none';
    uploadProgressFill.style.width = '0%';
    uploadProgressText.textContent = 'Uploading...';
  }, 1000);
}

// Helper functions
function getFileIcon(type) {
  const icons = {
    'application/pdf': '<svg class="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg>',
    'image/jpeg': '<svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>',
    'image/png': '<svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>',
    'audio/mpeg': '<svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path></svg>',
    'audio/wav': '<svg class="w-6 h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"></path></svg>',
    'video/mp4': '<svg class="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path></svg>',
    'application/msword': '<svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg>',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '<svg class="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg>'
  };
  
  return icons[type] || '<svg class="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg>';
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Form submission
document.getElementById('multiStepForm').addEventListener('submit', async function(e) {
  e.preventDefault();
  
  try {
    const formData = new FormData(this);
    
    // Add uploaded files URLs
    if (uploadedFiles.length > 0) {
      formData.append('portfolio_files', JSON.stringify(uploadedFiles));
    }
    
    // Tampilkan loading state
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = 'Mengirim...';
    
    const response = await fetch('process_form.php', {
      method: 'POST',
      body: formData
    });
    
    const result = await response.json();
    
    if (result.success) {
      // Tampilkan pesan sukses
      document.getElementById('multiStepForm').style.display = 'none';
      document.getElementById('successMessage').classList.remove('hidden');
    } else {
      throw new Error(result.error || 'Terjadi kesalahan saat mengirim aplikasi');
    }
    
  } catch (error) {
    alert(error.message);
  } finally {
    // Reset button state
    submitBtn.disabled = false;
    submitBtn.innerHTML = originalText;
  }
});
