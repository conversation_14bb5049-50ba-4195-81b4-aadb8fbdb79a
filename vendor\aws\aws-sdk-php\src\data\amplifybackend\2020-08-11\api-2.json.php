<?php
// This file was auto-generated from sdk-root/src/data/amplifybackend/2020-08-11/api-2.json
return [ 'metadata' => [ 'apiVersion' => '2020-08-11', 'endpointPrefix' => 'amplifybackend', 'signingName' => 'amplifybackend', 'serviceFullName' => 'AmplifyBackend', 'serviceId' => 'AmplifyBackend', 'protocol' => 'rest-json', 'jsonVersion' => '1.1', 'uid' => 'amplifybackend-2020-08-11', 'signatureVersion' => 'v4', ], 'operations' => [ 'CloneBackend' => [ 'name' => 'CloneBackend', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/environments/{backendEnvironmentName}/clone', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CloneBackendRequest', ], 'output' => [ 'shape' => 'CloneBackendResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'CreateBackend' => [ 'name' => 'CreateBackend', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateBackendRequest', ], 'output' => [ 'shape' => 'CreateBackendResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'CreateBackendAPI' => [ 'name' => 'CreateBackendAPI', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/api', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateBackendAPIRequest', ], 'output' => [ 'shape' => 'CreateBackendAPIResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'CreateBackendAuth' => [ 'name' => 'CreateBackendAuth', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/auth', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateBackendAuthRequest', ], 'output' => [ 'shape' => 'CreateBackendAuthResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'CreateBackendConfig' => [ 'name' => 'CreateBackendConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateBackendConfigRequest', ], 'output' => [ 'shape' => 'CreateBackendConfigResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'CreateBackendStorage' => [ 'name' => 'CreateBackendStorage', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/storage', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateBackendStorageRequest', ], 'output' => [ 'shape' => 'CreateBackendStorageResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'CreateToken' => [ 'name' => 'CreateToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/challenge', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateTokenRequest', ], 'output' => [ 'shape' => 'CreateTokenResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'DeleteBackend' => [ 'name' => 'DeleteBackend', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/environments/{backendEnvironmentName}/remove', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteBackendRequest', ], 'output' => [ 'shape' => 'DeleteBackendResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'DeleteBackendAPI' => [ 'name' => 'DeleteBackendAPI', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/api/{backendEnvironmentName}/remove', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteBackendAPIRequest', ], 'output' => [ 'shape' => 'DeleteBackendAPIResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'DeleteBackendAuth' => [ 'name' => 'DeleteBackendAuth', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/auth/{backendEnvironmentName}/remove', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteBackendAuthRequest', ], 'output' => [ 'shape' => 'DeleteBackendAuthResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'DeleteBackendStorage' => [ 'name' => 'DeleteBackendStorage', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/storage/{backendEnvironmentName}/remove', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteBackendStorageRequest', ], 'output' => [ 'shape' => 'DeleteBackendStorageResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'DeleteToken' => [ 'name' => 'DeleteToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/challenge/{sessionId}/remove', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteTokenRequest', ], 'output' => [ 'shape' => 'DeleteTokenResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GenerateBackendAPIModels' => [ 'name' => 'GenerateBackendAPIModels', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/api/{backendEnvironmentName}/generateModels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GenerateBackendAPIModelsRequest', ], 'output' => [ 'shape' => 'GenerateBackendAPIModelsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBackend' => [ 'name' => 'GetBackend', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/details', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBackendRequest', ], 'output' => [ 'shape' => 'GetBackendResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBackendAPI' => [ 'name' => 'GetBackendAPI', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/api/{backendEnvironmentName}/details', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBackendAPIRequest', ], 'output' => [ 'shape' => 'GetBackendAPIResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBackendAPIModels' => [ 'name' => 'GetBackendAPIModels', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/api/{backendEnvironmentName}/getModels', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBackendAPIModelsRequest', ], 'output' => [ 'shape' => 'GetBackendAPIModelsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBackendAuth' => [ 'name' => 'GetBackendAuth', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/auth/{backendEnvironmentName}/details', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBackendAuthRequest', ], 'output' => [ 'shape' => 'GetBackendAuthResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBackendJob' => [ 'name' => 'GetBackendJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/backend/{appId}/job/{backendEnvironmentName}/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBackendJobRequest', ], 'output' => [ 'shape' => 'GetBackendJobResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetBackendStorage' => [ 'name' => 'GetBackendStorage', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/storage/{backendEnvironmentName}/details', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetBackendStorageRequest', ], 'output' => [ 'shape' => 'GetBackendStorageResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'GetToken' => [ 'name' => 'GetToken', 'http' => [ 'method' => 'GET', 'requestUri' => '/backend/{appId}/challenge/{sessionId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetTokenRequest', ], 'output' => [ 'shape' => 'GetTokenResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ImportBackendAuth' => [ 'name' => 'ImportBackendAuth', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/auth/{backendEnvironmentName}/import', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ImportBackendAuthRequest', ], 'output' => [ 'shape' => 'ImportBackendAuthResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ImportBackendStorage' => [ 'name' => 'ImportBackendStorage', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/storage/{backendEnvironmentName}/import', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ImportBackendStorageRequest', ], 'output' => [ 'shape' => 'ImportBackendStorageResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListBackendJobs' => [ 'name' => 'ListBackendJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/job/{backendEnvironmentName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListBackendJobsRequest', ], 'output' => [ 'shape' => 'ListBackendJobsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'ListS3Buckets' => [ 'name' => 'ListS3Buckets', 'http' => [ 'method' => 'POST', 'requestUri' => '/s3Buckets', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListS3BucketsRequest', ], 'output' => [ 'shape' => 'ListS3BucketsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'RemoveAllBackends' => [ 'name' => 'RemoveAllBackends', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/remove', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RemoveAllBackendsRequest', ], 'output' => [ 'shape' => 'RemoveAllBackendsResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'RemoveBackendConfig' => [ 'name' => 'RemoveBackendConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/config/remove', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RemoveBackendConfigRequest', ], 'output' => [ 'shape' => 'RemoveBackendConfigResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'UpdateBackendAPI' => [ 'name' => 'UpdateBackendAPI', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/api/{backendEnvironmentName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBackendAPIRequest', ], 'output' => [ 'shape' => 'UpdateBackendAPIResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'UpdateBackendAuth' => [ 'name' => 'UpdateBackendAuth', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/auth/{backendEnvironmentName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBackendAuthRequest', ], 'output' => [ 'shape' => 'UpdateBackendAuthResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'UpdateBackendConfig' => [ 'name' => 'UpdateBackendConfig', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/config/update', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBackendConfigRequest', ], 'output' => [ 'shape' => 'UpdateBackendConfigResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'UpdateBackendJob' => [ 'name' => 'UpdateBackendJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/job/{backendEnvironmentName}/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBackendJobRequest', ], 'output' => [ 'shape' => 'UpdateBackendJobResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], 'UpdateBackendStorage' => [ 'name' => 'UpdateBackendStorage', 'http' => [ 'method' => 'POST', 'requestUri' => '/backend/{appId}/storage/{backendEnvironmentName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateBackendStorageRequest', ], 'output' => [ 'shape' => 'UpdateBackendStorageResponse', ], 'errors' => [ [ 'shape' => 'NotFoundException', ], [ 'shape' => 'GatewayTimeoutException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'BadRequestException', ], ], ], ], 'shapes' => [ 'AuthResources' => [ 'type' => 'string', 'enum' => [ 'USER_POOL_ONLY', 'IDENTITY_POOL_AND_USER_POOL', ], ], 'BackendAPIAppSyncAuthSettings' => [ 'type' => 'structure', 'members' => [ 'CognitoUserPoolId' => [ 'shape' => '__string', 'locationName' => 'cognitoUserPoolId', ], 'Description' => [ 'shape' => '__string', 'locationName' => 'description', ], 'ExpirationTime' => [ 'shape' => '__double', 'locationName' => 'expirationTime', ], 'OpenIDAuthTTL' => [ 'shape' => '__string', 'locationName' => 'openIDAuthTTL', ], 'OpenIDClientId' => [ 'shape' => '__string', 'locationName' => 'openIDClientId', ], 'OpenIDIatTTL' => [ 'shape' => '__string', 'locationName' => 'openIDIatTTL', ], 'OpenIDIssueURL' => [ 'shape' => '__string', 'locationName' => 'openIDIssueURL', ], 'OpenIDProviderName' => [ 'shape' => '__string', 'locationName' => 'openIDProviderName', ], ], ], 'BackendAPIAuthType' => [ 'type' => 'structure', 'members' => [ 'Mode' => [ 'shape' => 'Mode', 'locationName' => 'mode', ], 'Settings' => [ 'shape' => 'BackendAPIAppSyncAuthSettings', 'locationName' => 'settings', ], ], ], 'BackendAPICodegenReqObj' => [ 'type' => 'structure', 'members' => [ 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'ResourceName', ], ], 'BackendAPICodegenRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', ], ], 'BackendAPIConflictResolution' => [ 'type' => 'structure', 'members' => [ 'ResolutionStrategy' => [ 'shape' => 'ResolutionStrategy', 'locationName' => 'resolutionStrategy', ], ], ], 'BackendAPIReqObj' => [ 'type' => 'structure', 'members' => [ 'ResourceConfig' => [ 'shape' => 'BackendAPIResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'ResourceName', ], ], 'BackendAPIResourceConfig' => [ 'type' => 'structure', 'members' => [ 'AdditionalAuthTypes' => [ 'shape' => 'ListOfBackendAPIAuthType', 'locationName' => 'additionalAuthTypes', ], 'ApiName' => [ 'shape' => '__string', 'locationName' => 'apiName', ], 'ConflictResolution' => [ 'shape' => 'BackendAPIConflictResolution', 'locationName' => 'conflictResolution', ], 'DefaultAuthType' => [ 'shape' => 'BackendAPIAuthType', 'locationName' => 'defaultAuthType', ], 'Service' => [ 'shape' => '__string', 'locationName' => 'service', ], 'TransformSchema' => [ 'shape' => '__string', 'locationName' => 'transformSchema', ], ], ], 'BackendAPIRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', ], ], 'BackendAuthAppleProviderConfig' => [ 'type' => 'structure', 'sensitive' => true, 'members' => [ 'ClientId' => [ 'shape' => '__string', 'locationName' => 'client_id', ], 'KeyId' => [ 'shape' => '__string', 'locationName' => 'key_id', ], 'PrivateKey' => [ 'shape' => '__string', 'locationName' => 'private_key', ], 'TeamId' => [ 'shape' => '__string', 'locationName' => 'team_id', ], ], ], 'BackendAuthRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', ], ], 'BackendAuthSocialProviderConfig' => [ 'type' => 'structure', 'sensitive' => true, 'members' => [ 'ClientId' => [ 'shape' => '__string', 'locationName' => 'client_id', ], 'ClientSecret' => [ 'shape' => '__string', 'locationName' => 'client_secret', ], ], ], 'BackendConfigRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendManagerAppId' => [ 'shape' => '__string', 'locationName' => 'backendManagerAppId', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'LoginAuthConfig' => [ 'shape' => 'LoginAuthConfigReqObj', 'locationName' => 'loginAuthConfig', ], ], ], 'BackendJobReqObj' => [ 'type' => 'structure', 'members' => [ 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'BackendJobRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'CreateTime' => [ 'shape' => '__string', 'locationName' => 'createTime', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], 'UpdateTime' => [ 'shape' => '__string', 'locationName' => 'updateTime', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', ], ], 'BackendStoragePermissions' => [ 'type' => 'structure', 'members' => [ 'Authenticated' => [ 'shape' => 'ListOfAuthenticatedElement', 'locationName' => 'authenticated', ], 'UnAuthenticated' => [ 'shape' => 'ListOfUnAuthenticatedElement', 'locationName' => 'unAuthenticated', ], ], 'required' => [ 'Authenticated', ], ], 'BackendStorageRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], 'required' => [ 'Status', 'AppId', 'BackendEnvironmentName', 'JobId', ], ], 'BadRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 400, ], ], 'CloneBackendReqObj' => [ 'type' => 'structure', 'members' => [ 'TargetEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'targetEnvironmentName', ], ], 'required' => [ 'TargetEnvironmentName', ], ], 'CloneBackendRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'TargetEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'targetEnvironmentName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'TargetEnvironmentName', ], ], 'CloneBackendRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', ], ], 'CloneBackendResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'CreateBackendAPIReqObj' => [ 'type' => 'structure', 'members' => [ 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'BackendAPIResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'ResourceName', 'BackendEnvironmentName', 'ResourceConfig', ], ], 'CreateBackendAPIRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'BackendAPIResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'ResourceName', 'BackendEnvironmentName', 'ResourceConfig', ], ], 'CreateBackendAPIResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'CreateBackendAuthForgotPasswordConfig' => [ 'type' => 'structure', 'members' => [ 'DeliveryMethod' => [ 'shape' => 'DeliveryMethod', 'locationName' => 'deliveryMethod', ], 'EmailSettings' => [ 'shape' => 'EmailSettings', 'locationName' => 'emailSettings', ], 'SmsSettings' => [ 'shape' => 'SmsSettings', 'locationName' => 'smsSettings', ], ], 'required' => [ 'DeliveryMethod', ], ], 'CreateBackendAuthIdentityPoolConfig' => [ 'type' => 'structure', 'members' => [ 'IdentityPoolName' => [ 'shape' => '__string', 'locationName' => 'identityPoolName', ], 'UnauthenticatedLogin' => [ 'shape' => '__boolean', 'locationName' => 'unauthenticatedLogin', ], ], 'required' => [ 'UnauthenticatedLogin', 'IdentityPoolName', ], ], 'CreateBackendAuthMFAConfig' => [ 'type' => 'structure', 'members' => [ 'MFAMode' => [ 'shape' => 'MFAMode', ], 'Settings' => [ 'shape' => 'Settings', 'locationName' => 'settings', ], ], 'required' => [ 'MFAMode', ], ], 'CreateBackendAuthOAuthConfig' => [ 'type' => 'structure', 'members' => [ 'DomainPrefix' => [ 'shape' => '__string', 'locationName' => 'domainPrefix', ], 'OAuthGrantType' => [ 'shape' => 'OAuthGrantType', 'locationName' => 'oAuthGrantType', ], 'OAuthScopes' => [ 'shape' => 'ListOfOAuthScopesElement', 'locationName' => 'oAuthScopes', ], 'RedirectSignInURIs' => [ 'shape' => 'ListOf__string', 'locationName' => 'redirectSignInURIs', ], 'RedirectSignOutURIs' => [ 'shape' => 'ListOf__string', 'locationName' => 'redirectSignOutURIs', ], 'SocialProviderSettings' => [ 'shape' => 'SocialProviderSettings', 'locationName' => 'socialProviderSettings', ], ], 'required' => [ 'RedirectSignOutURIs', 'RedirectSignInURIs', 'OAuthGrantType', 'OAuthScopes', ], ], 'CreateBackendAuthPasswordPolicyConfig' => [ 'type' => 'structure', 'members' => [ 'AdditionalConstraints' => [ 'shape' => 'ListOfAdditionalConstraintsElement', 'locationName' => 'additionalConstraints', ], 'MinimumLength' => [ 'shape' => '__double', 'locationName' => 'minimumLength', ], ], 'required' => [ 'MinimumLength', ], ], 'CreateBackendAuthReqObj' => [ 'type' => 'structure', 'members' => [ 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'CreateBackendAuthResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'ResourceName', 'BackendEnvironmentName', 'ResourceConfig', ], ], 'CreateBackendAuthRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'CreateBackendAuthResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'ResourceName', 'BackendEnvironmentName', 'ResourceConfig', ], ], 'CreateBackendAuthResourceConfig' => [ 'type' => 'structure', 'members' => [ 'AuthResources' => [ 'shape' => 'AuthResources', 'locationName' => 'authResources', ], 'IdentityPoolConfigs' => [ 'shape' => 'CreateBackendAuthIdentityPoolConfig', 'locationName' => 'identityPoolConfigs', ], 'Service' => [ 'shape' => 'Service', 'locationName' => 'service', ], 'UserPoolConfigs' => [ 'shape' => 'CreateBackendAuthUserPoolConfig', 'locationName' => 'userPoolConfigs', ], ], 'required' => [ 'AuthResources', 'UserPoolConfigs', 'Service', ], ], 'CreateBackendAuthResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'CreateBackendAuthUserPoolConfig' => [ 'type' => 'structure', 'members' => [ 'ForgotPassword' => [ 'shape' => 'CreateBackendAuthForgotPasswordConfig', 'locationName' => 'forgotPassword', ], 'Mfa' => [ 'shape' => 'CreateBackendAuthMFAConfig', 'locationName' => 'mfa', ], 'OAuth' => [ 'shape' => 'CreateBackendAuthOAuthConfig', 'locationName' => 'oAuth', ], 'PasswordPolicy' => [ 'shape' => 'CreateBackendAuthPasswordPolicyConfig', 'locationName' => 'passwordPolicy', ], 'RequiredSignUpAttributes' => [ 'shape' => 'ListOfRequiredSignUpAttributesElement', 'locationName' => 'requiredSignUpAttributes', ], 'SignInMethod' => [ 'shape' => 'SignInMethod', 'locationName' => 'signInMethod', ], 'UserPoolName' => [ 'shape' => '__string', 'locationName' => 'userPoolName', ], 'VerificationMessage' => [ 'shape' => 'CreateBackendAuthVerificationMessageConfig', 'locationName' => 'verificationMessage', ], ], 'required' => [ 'RequiredSignUpAttributes', 'SignInMethod', 'UserPoolName', ], ], 'CreateBackendAuthVerificationMessageConfig' => [ 'type' => 'structure', 'members' => [ 'DeliveryMethod' => [ 'shape' => 'DeliveryMethod', 'locationName' => 'deliveryMethod', ], 'EmailSettings' => [ 'shape' => 'EmailSettings', 'locationName' => 'emailSettings', ], 'SmsSettings' => [ 'shape' => 'SmsSettings', 'locationName' => 'smsSettings', ], ], 'required' => [ 'DeliveryMethod', ], ], 'CreateBackendConfigReqObj' => [ 'type' => 'structure', 'members' => [ 'BackendManagerAppId' => [ 'shape' => '__string', 'locationName' => 'backendManagerAppId', ], ], ], 'CreateBackendConfigRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendManagerAppId' => [ 'shape' => '__string', 'locationName' => 'backendManagerAppId', ], ], 'required' => [ 'AppId', ], ], 'CreateBackendConfigRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], 'required' => [ 'AppId', ], ], 'CreateBackendConfigResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'CreateBackendReqObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'AppName' => [ 'shape' => '__string', 'locationName' => 'appName', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'ResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'AppName', ], ], 'CreateBackendRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'AppName' => [ 'shape' => '__string', 'locationName' => 'appName', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'ResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'AppName', ], ], 'CreateBackendRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', ], ], 'CreateBackendResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'CreateBackendStorageReqObj' => [ 'type' => 'structure', 'members' => [ 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'CreateBackendStorageResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'ResourceName', 'BackendEnvironmentName', 'ResourceConfig', ], ], 'CreateBackendStorageRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'CreateBackendStorageResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'ResourceName', 'BackendEnvironmentName', 'ResourceConfig', ], ], 'CreateBackendStorageResourceConfig' => [ 'type' => 'structure', 'members' => [ 'BucketName' => [ 'shape' => '__string', 'locationName' => 'bucketName', ], 'Permissions' => [ 'shape' => 'BackendStoragePermissions', 'locationName' => 'permissions', ], 'ServiceName' => [ 'shape' => 'ServiceName', 'locationName' => 'serviceName', ], ], 'required' => [ 'ServiceName', 'Permissions', ], ], 'CreateBackendStorageResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'CreateTokenRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], ], 'required' => [ 'AppId', ], ], 'CreateTokenRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'ChallengeCode' => [ 'shape' => '__string', 'locationName' => 'challengeCode', ], 'SessionId' => [ 'shape' => '__string', 'locationName' => 'sessionId', ], 'Ttl' => [ 'shape' => '__string', 'locationName' => 'ttl', ], ], 'required' => [ 'AppId', 'Ttl', 'SessionId', 'ChallengeCode', ], ], 'CreateTokenResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'ChallengeCode' => [ 'shape' => '__string', 'locationName' => 'challengeCode', ], 'SessionId' => [ 'shape' => '__string', 'locationName' => 'sessionId', ], 'Ttl' => [ 'shape' => '__string', 'locationName' => 'ttl', ], ], ], 'DeleteBackendAPIRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'BackendAPIResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'ResourceName', ], ], 'DeleteBackendAPIResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'DeleteBackendAuthRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'ResourceName', ], ], 'DeleteBackendAuthResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'DeleteBackendRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', ], ], 'DeleteBackendRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', ], ], 'DeleteBackendResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'DeleteBackendStorageRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], 'ServiceName' => [ 'shape' => 'ServiceName', 'locationName' => 'serviceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'ServiceName', 'ResourceName', ], ], 'DeleteBackendStorageResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'DeleteTokenRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'SessionId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'sessionId', ], ], 'required' => [ 'SessionId', 'AppId', ], ], 'DeleteTokenRespObj' => [ 'type' => 'structure', 'members' => [ 'IsSuccess' => [ 'shape' => '__boolean', 'locationName' => 'isSuccess', ], ], 'required' => [ 'IsSuccess', ], ], 'DeleteTokenResponse' => [ 'type' => 'structure', 'members' => [ 'IsSuccess' => [ 'shape' => '__boolean', 'locationName' => 'isSuccess', ], ], ], 'DeliveryMethod' => [ 'type' => 'string', 'enum' => [ 'EMAIL', 'SMS', ], ], 'EmailSettings' => [ 'type' => 'structure', 'sensitive' => true, 'members' => [ 'EmailMessage' => [ 'shape' => '__string', 'locationName' => 'emailMessage', ], 'EmailSubject' => [ 'shape' => '__string', 'locationName' => 'emailSubject', ], ], ], 'GatewayTimeoutException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 504, ], ], 'GenerateBackendAPIModelsRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'ResourceName', ], ], 'GenerateBackendAPIModelsResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'GetBackendAPIModelsRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'ResourceName', ], ], 'GetBackendAPIModelsResponse' => [ 'type' => 'structure', 'members' => [ 'Models' => [ 'shape' => '__string', 'locationName' => 'models', ], 'Status' => [ 'shape' => 'Status', 'locationName' => 'status', ], 'ModelIntrospectionSchema' => [ 'shape' => '__string', 'locationName' => 'modelIntrospectionSchema', ], ], ], 'GetBackendAPIRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'BackendAPIResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'ResourceName', ], ], 'GetBackendAPIRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'ResourceConfig' => [ 'shape' => 'BackendAPIResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', ], ], 'GetBackendAPIResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'ResourceConfig' => [ 'shape' => 'BackendAPIResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], ], 'GetBackendAuthReqObj' => [ 'type' => 'structure', 'members' => [ 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'ResourceName', ], ], 'GetBackendAuthRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'ResourceName', ], ], 'GetBackendAuthRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'ResourceConfig' => [ 'shape' => 'CreateBackendAuthResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', ], ], 'GetBackendAuthResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'ResourceConfig' => [ 'shape' => 'CreateBackendAuthResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], ], 'GetBackendJobRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'JobId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'jobId', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'JobId', ], ], 'GetBackendJobResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'CreateTime' => [ 'shape' => '__string', 'locationName' => 'createTime', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], 'UpdateTime' => [ 'shape' => '__string', 'locationName' => 'updateTime', ], ], ], 'GetBackendReqObj' => [ 'type' => 'structure', 'members' => [ 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], ], ], 'GetBackendRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], ], 'required' => [ 'AppId', ], ], 'GetBackendRespObj' => [ 'type' => 'structure', 'members' => [ 'AmplifyFeatureFlags' => [ 'shape' => '__string', 'locationName' => 'amplifyFeatureFlags', ], 'AmplifyMetaConfig' => [ 'shape' => '__string', 'locationName' => 'amplifyMetaConfig', ], 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'AppName' => [ 'shape' => '__string', 'locationName' => 'appName', ], 'BackendEnvironmentList' => [ 'shape' => 'ListOf__string', 'locationName' => 'backendEnvironmentList', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], ], 'required' => [ 'AppId', ], ], 'GetBackendResponse' => [ 'type' => 'structure', 'members' => [ 'AmplifyFeatureFlags' => [ 'shape' => '__string', 'locationName' => 'amplifyFeatureFlags', ], 'AmplifyMetaConfig' => [ 'shape' => '__string', 'locationName' => 'amplifyMetaConfig', ], 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'AppName' => [ 'shape' => '__string', 'locationName' => 'appName', ], 'BackendEnvironmentList' => [ 'shape' => 'ListOf__string', 'locationName' => 'backendEnvironmentList', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], ], ], 'GetBackendStorageReqObj' => [ 'type' => 'structure', 'members' => [ 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'ResourceName', ], ], 'GetBackendStorageRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'ResourceName', ], ], 'GetBackendStorageResourceConfig' => [ 'type' => 'structure', 'members' => [ 'BucketName' => [ 'shape' => '__string', 'locationName' => 'bucketName', ], 'Imported' => [ 'shape' => '__boolean', 'locationName' => 'imported', ], 'Permissions' => [ 'shape' => 'BackendStoragePermissions', 'locationName' => 'permissions', ], 'ServiceName' => [ 'shape' => 'ServiceName', 'locationName' => 'serviceName', ], ], 'required' => [ 'ServiceName', 'Imported', ], ], 'GetBackendStorageRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'GetBackendStorageResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', ], ], 'GetBackendStorageResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'GetBackendStorageResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], ], 'GetTokenRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'SessionId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'sessionId', ], ], 'required' => [ 'SessionId', 'AppId', ], ], 'GetTokenRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'ChallengeCode' => [ 'shape' => '__string', 'locationName' => 'challengeCode', ], 'SessionId' => [ 'shape' => '__string', 'locationName' => 'sessionId', ], 'Ttl' => [ 'shape' => '__string', 'locationName' => 'ttl', ], ], 'required' => [ 'AppId', 'Ttl', 'SessionId', 'ChallengeCode', ], ], 'GetTokenResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'ChallengeCode' => [ 'shape' => '__string', 'locationName' => 'challengeCode', ], 'SessionId' => [ 'shape' => '__string', 'locationName' => 'sessionId', ], 'Ttl' => [ 'shape' => '__string', 'locationName' => 'ttl', ], ], ], 'ImportBackendAuthReqObj' => [ 'type' => 'structure', 'members' => [ 'IdentityPoolId' => [ 'shape' => '__string', 'locationName' => 'identityPoolId', ], 'NativeClientId' => [ 'shape' => '__string', 'locationName' => 'nativeClientId', ], 'UserPoolId' => [ 'shape' => '__string', 'locationName' => 'userPoolId', ], 'WebClientId' => [ 'shape' => '__string', 'locationName' => 'webClientId', ], ], 'required' => [ 'UserPoolId', 'NativeClientId', 'WebClientId', ], ], 'ImportBackendAuthRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'IdentityPoolId' => [ 'shape' => '__string', 'locationName' => 'identityPoolId', ], 'NativeClientId' => [ 'shape' => '__string', 'locationName' => 'nativeClientId', ], 'UserPoolId' => [ 'shape' => '__string', 'locationName' => 'userPoolId', ], 'WebClientId' => [ 'shape' => '__string', 'locationName' => 'webClientId', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'UserPoolId', 'NativeClientId', 'WebClientId', ], ], 'ImportBackendAuthResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'ImportBackendStorageReqObj' => [ 'type' => 'structure', 'members' => [ 'BucketName' => [ 'shape' => '__string', 'locationName' => 'bucketName', ], 'ServiceName' => [ 'shape' => 'ServiceName', 'locationName' => 'serviceName', ], ], 'required' => [ 'ServiceName', ], ], 'ImportBackendStorageRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'BucketName' => [ 'shape' => '__string', 'locationName' => 'bucketName', ], 'ServiceName' => [ 'shape' => 'ServiceName', 'locationName' => 'serviceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'ServiceName', ], ], 'ImportBackendStorageResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'InternalServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'LimitType' => [ 'shape' => '__string', 'locationName' => 'limitType', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], ], 'ListBackendJobReqObj' => [ 'type' => 'structure', 'members' => [ 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'MaxResults' => [ 'shape' => '__integerMin1Max25', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'ListBackendJobRespObj' => [ 'type' => 'structure', 'members' => [ 'Jobs' => [ 'shape' => 'ListOfBackendJobRespObj', 'locationName' => 'jobs', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListBackendJobsRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'MaxResults' => [ 'shape' => '__integerMin1Max25', 'locationName' => 'maxResults', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', ], ], 'ListBackendJobsResponse' => [ 'type' => 'structure', 'members' => [ 'Jobs' => [ 'shape' => 'ListOfBackendJobRespObj', 'locationName' => 'jobs', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListS3BucketsReqObj' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListS3BucketsRequest' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'ListS3BucketsRespObj' => [ 'type' => 'structure', 'members' => [ 'Buckets' => [ 'shape' => 'ListOfS3BucketInfo', 'locationName' => 'buckets', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], 'required' => [ 'Buckets', ], ], 'ListS3BucketsResponse' => [ 'type' => 'structure', 'members' => [ 'Buckets' => [ 'shape' => 'ListOfS3BucketInfo', 'locationName' => 'buckets', ], 'NextToken' => [ 'shape' => '__string', 'locationName' => 'nextToken', ], ], ], 'LoginAuthConfigReqObj' => [ 'type' => 'structure', 'members' => [ 'AwsCognitoIdentityPoolId' => [ 'shape' => '__string', 'locationName' => 'aws_cognito_identity_pool_id', ], 'AwsCognitoRegion' => [ 'shape' => '__string', 'locationName' => 'aws_cognito_region', ], 'AwsUserPoolsId' => [ 'shape' => '__string', 'locationName' => 'aws_user_pools_id', ], 'AwsUserPoolsWebClientId' => [ 'shape' => '__string', 'locationName' => 'aws_user_pools_web_client_id', ], ], ], 'MFAMode' => [ 'type' => 'string', 'enum' => [ 'ON', 'OFF', 'OPTIONAL', ], ], 'Mode' => [ 'type' => 'string', 'enum' => [ 'API_KEY', 'AWS_IAM', 'AMAZON_COGNITO_USER_POOLS', 'OPENID_CONNECT', ], ], 'NotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], 'ResourceType' => [ 'shape' => '__string', 'locationName' => 'resourceType', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 404, ], ], 'OAuthGrantType' => [ 'type' => 'string', 'enum' => [ 'CODE', 'IMPLICIT', ], ], 'RemoveAllBackendsReqObj' => [ 'type' => 'structure', 'members' => [ 'CleanAmplifyApp' => [ 'shape' => '__boolean', 'locationName' => 'cleanAmplifyApp', ], ], ], 'RemoveAllBackendsRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'CleanAmplifyApp' => [ 'shape' => '__boolean', 'locationName' => 'cleanAmplifyApp', ], ], 'required' => [ 'AppId', ], ], 'RemoveAllBackendsRespObj' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], 'required' => [ 'AppId', ], ], 'RemoveAllBackendsResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'RemoveBackendAuthReqObj' => [ 'type' => 'structure', 'members' => [ 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'ResourceName', ], ], 'RemoveBackendConfigRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], ], 'required' => [ 'AppId', ], ], 'RemoveBackendConfigRespObj' => [ 'type' => 'structure', 'members' => [ 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], ], ], 'RemoveBackendConfigResponse' => [ 'type' => 'structure', 'members' => [ 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], ], ], 'RemoveBackendStorageReqObj' => [ 'type' => 'structure', 'members' => [ 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], 'ServiceName' => [ 'shape' => 'ServiceName', 'locationName' => 'serviceName', ], ], 'required' => [ 'ServiceName', 'ResourceName', ], ], 'ResolutionStrategy' => [ 'type' => 'string', 'enum' => [ 'OPTIMISTIC_CONCURRENCY', 'LAMBDA', 'AUTOMERGE', 'NONE', ], ], 'ResourceConfig' => [ 'type' => 'structure', 'members' => [], ], 'S3BucketInfo' => [ 'type' => 'structure', 'members' => [ 'CreationDate' => [ 'shape' => '__string', 'locationName' => 'creationDate', ], 'Name' => [ 'shape' => '__string', 'locationName' => 'name', ], ], ], 'Service' => [ 'type' => 'string', 'enum' => [ 'COGNITO', ], ], 'ServiceName' => [ 'type' => 'string', 'enum' => [ 'S3', ], ], 'Settings' => [ 'type' => 'structure', 'members' => [ 'MfaTypes' => [ 'shape' => 'ListOfMfaTypesElement', 'locationName' => 'mfaTypes', ], 'SmsMessage' => [ 'shape' => '__string', 'locationName' => 'smsMessage', ], ], ], 'SignInMethod' => [ 'type' => 'string', 'enum' => [ 'EMAIL', 'EMAIL_AND_PHONE_NUMBER', 'PHONE_NUMBER', 'USERNAME', ], ], 'SmsSettings' => [ 'type' => 'structure', 'sensitive' => true, 'members' => [ 'SmsMessage' => [ 'shape' => '__string', 'locationName' => 'smsMessage', ], ], ], 'SocialProviderSettings' => [ 'type' => 'structure', 'members' => [ 'Facebook' => [ 'shape' => 'BackendAuthSocialProviderConfig', ], 'Google' => [ 'shape' => 'BackendAuthSocialProviderConfig', ], 'LoginWithAmazon' => [ 'shape' => 'BackendAuthSocialProviderConfig', ], 'SignInWithApple' => [ 'shape' => 'BackendAuthAppleProviderConfig', ], ], ], 'Status' => [ 'type' => 'string', 'enum' => [ 'LATEST', 'STALE', ], ], 'TooManyRequestsException' => [ 'type' => 'structure', 'members' => [ 'LimitType' => [ 'shape' => '__string', 'locationName' => 'limitType', ], 'Message' => [ 'shape' => '__string', 'locationName' => 'message', ], ], 'exception' => true, 'error' => [ 'httpStatusCode' => 429, ], ], 'UpdateBackendAPIRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'BackendAPIResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'ResourceName', ], ], 'UpdateBackendAPIResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'UpdateBackendAuthForgotPasswordConfig' => [ 'type' => 'structure', 'members' => [ 'DeliveryMethod' => [ 'shape' => 'DeliveryMethod', 'locationName' => 'deliveryMethod', ], 'EmailSettings' => [ 'shape' => 'EmailSettings', 'locationName' => 'emailSettings', ], 'SmsSettings' => [ 'shape' => 'SmsSettings', 'locationName' => 'smsSettings', ], ], ], 'UpdateBackendAuthIdentityPoolConfig' => [ 'type' => 'structure', 'members' => [ 'UnauthenticatedLogin' => [ 'shape' => '__boolean', 'locationName' => 'unauthenticatedLogin', ], ], ], 'UpdateBackendAuthMFAConfig' => [ 'type' => 'structure', 'members' => [ 'MFAMode' => [ 'shape' => 'MFAMode', ], 'Settings' => [ 'shape' => 'Settings', 'locationName' => 'settings', ], ], ], 'UpdateBackendAuthOAuthConfig' => [ 'type' => 'structure', 'members' => [ 'DomainPrefix' => [ 'shape' => '__string', 'locationName' => 'domainPrefix', ], 'OAuthGrantType' => [ 'shape' => 'OAuthGrantType', 'locationName' => 'oAuthGrantType', ], 'OAuthScopes' => [ 'shape' => 'ListOfOAuthScopesElement', 'locationName' => 'oAuthScopes', ], 'RedirectSignInURIs' => [ 'shape' => 'ListOf__string', 'locationName' => 'redirectSignInURIs', ], 'RedirectSignOutURIs' => [ 'shape' => 'ListOf__string', 'locationName' => 'redirectSignOutURIs', ], 'SocialProviderSettings' => [ 'shape' => 'SocialProviderSettings', 'locationName' => 'socialProviderSettings', ], ], ], 'UpdateBackendAuthPasswordPolicyConfig' => [ 'type' => 'structure', 'members' => [ 'AdditionalConstraints' => [ 'shape' => 'ListOfAdditionalConstraintsElement', 'locationName' => 'additionalConstraints', ], 'MinimumLength' => [ 'shape' => '__double', 'locationName' => 'minimumLength', ], ], ], 'UpdateBackendAuthReqObj' => [ 'type' => 'structure', 'members' => [ 'ResourceConfig' => [ 'shape' => 'UpdateBackendAuthResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'ResourceName', 'ResourceConfig', ], ], 'UpdateBackendAuthRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'UpdateBackendAuthResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'ResourceName', 'ResourceConfig', ], ], 'UpdateBackendAuthResourceConfig' => [ 'type' => 'structure', 'members' => [ 'AuthResources' => [ 'shape' => 'AuthResources', 'locationName' => 'authResources', ], 'IdentityPoolConfigs' => [ 'shape' => 'UpdateBackendAuthIdentityPoolConfig', 'locationName' => 'identityPoolConfigs', ], 'Service' => [ 'shape' => 'Service', 'locationName' => 'service', ], 'UserPoolConfigs' => [ 'shape' => 'UpdateBackendAuthUserPoolConfig', 'locationName' => 'userPoolConfigs', ], ], 'required' => [ 'AuthResources', 'UserPoolConfigs', 'Service', ], ], 'UpdateBackendAuthResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'UpdateBackendAuthUserPoolConfig' => [ 'type' => 'structure', 'members' => [ 'ForgotPassword' => [ 'shape' => 'UpdateBackendAuthForgotPasswordConfig', 'locationName' => 'forgotPassword', ], 'Mfa' => [ 'shape' => 'UpdateBackendAuthMFAConfig', 'locationName' => 'mfa', ], 'OAuth' => [ 'shape' => 'UpdateBackendAuthOAuthConfig', 'locationName' => 'oAuth', ], 'PasswordPolicy' => [ 'shape' => 'UpdateBackendAuthPasswordPolicyConfig', 'locationName' => 'passwordPolicy', ], 'VerificationMessage' => [ 'shape' => 'UpdateBackendAuthVerificationMessageConfig', 'locationName' => 'verificationMessage', ], ], ], 'UpdateBackendAuthVerificationMessageConfig' => [ 'type' => 'structure', 'members' => [ 'DeliveryMethod' => [ 'shape' => 'DeliveryMethod', 'locationName' => 'deliveryMethod', ], 'EmailSettings' => [ 'shape' => 'EmailSettings', 'locationName' => 'emailSettings', ], 'SmsSettings' => [ 'shape' => 'SmsSettings', 'locationName' => 'smsSettings', ], ], 'required' => [ 'DeliveryMethod', ], ], 'UpdateBackendConfigReqObj' => [ 'type' => 'structure', 'members' => [ 'LoginAuthConfig' => [ 'shape' => 'LoginAuthConfigReqObj', 'locationName' => 'loginAuthConfig', ], ], ], 'UpdateBackendConfigRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'LoginAuthConfig' => [ 'shape' => 'LoginAuthConfigReqObj', 'locationName' => 'loginAuthConfig', ], ], 'required' => [ 'AppId', ], ], 'UpdateBackendConfigResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendManagerAppId' => [ 'shape' => '__string', 'locationName' => 'backendManagerAppId', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'LoginAuthConfig' => [ 'shape' => 'LoginAuthConfigReqObj', 'locationName' => 'loginAuthConfig', ], ], ], 'UpdateBackendJobRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'JobId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'JobId', ], ], 'UpdateBackendJobResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'CreateTime' => [ 'shape' => '__string', 'locationName' => 'createTime', ], 'Error' => [ 'shape' => '__string', 'locationName' => 'error', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Operation' => [ 'shape' => '__string', 'locationName' => 'operation', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], 'UpdateTime' => [ 'shape' => '__string', 'locationName' => 'updateTime', ], ], ], 'UpdateBackendStorageReqObj' => [ 'type' => 'structure', 'members' => [ 'ResourceConfig' => [ 'shape' => 'UpdateBackendStorageResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'ResourceName', 'ResourceConfig', ], ], 'UpdateBackendStorageRequest' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'location' => 'uri', 'locationName' => 'backendEnvironmentName', ], 'ResourceConfig' => [ 'shape' => 'UpdateBackendStorageResourceConfig', 'locationName' => 'resourceConfig', ], 'ResourceName' => [ 'shape' => '__string', 'locationName' => 'resourceName', ], ], 'required' => [ 'AppId', 'BackendEnvironmentName', 'ResourceName', 'ResourceConfig', ], ], 'UpdateBackendStorageResourceConfig' => [ 'type' => 'structure', 'members' => [ 'Permissions' => [ 'shape' => 'BackendStoragePermissions', 'locationName' => 'permissions', ], 'ServiceName' => [ 'shape' => 'ServiceName', 'locationName' => 'serviceName', ], ], 'required' => [ 'ServiceName', 'Permissions', ], ], 'UpdateBackendStorageResponse' => [ 'type' => 'structure', 'members' => [ 'AppId' => [ 'shape' => '__string', 'locationName' => 'appId', ], 'BackendEnvironmentName' => [ 'shape' => '__string', 'locationName' => 'backendEnvironmentName', ], 'JobId' => [ 'shape' => '__string', 'locationName' => 'jobId', ], 'Status' => [ 'shape' => '__string', 'locationName' => 'status', ], ], ], 'AdditionalConstraintsElement' => [ 'type' => 'string', 'enum' => [ 'REQUIRE_DIGIT', 'REQUIRE_LOWERCASE', 'REQUIRE_SYMBOL', 'REQUIRE_UPPERCASE', ], ], 'AuthenticatedElement' => [ 'type' => 'string', 'enum' => [ 'READ', 'CREATE_AND_UPDATE', 'DELETE', ], ], 'MfaTypesElement' => [ 'type' => 'string', 'enum' => [ 'SMS', 'TOTP', ], ], 'OAuthScopesElement' => [ 'type' => 'string', 'enum' => [ 'PHONE', 'EMAIL', 'OPENID', 'PROFILE', 'AWS_COGNITO_SIGNIN_USER_ADMIN', ], ], 'RequiredSignUpAttributesElement' => [ 'type' => 'string', 'enum' => [ 'ADDRESS', 'BIRTHDATE', 'EMAIL', 'FAMILY_NAME', 'GENDER', 'GIVEN_NAME', 'LOCALE', 'MIDDLE_NAME', 'NAME', 'NICKNAME', 'PHONE_NUMBER', 'PICTURE', 'PREFERRED_USERNAME', 'PROFILE', 'UPDATED_AT', 'WEBSITE', 'ZONE_INFO', ], ], 'UnAuthenticatedElement' => [ 'type' => 'string', 'enum' => [ 'READ', 'CREATE_AND_UPDATE', 'DELETE', ], ], '__boolean' => [ 'type' => 'boolean', ], '__double' => [ 'type' => 'double', ], '__integer' => [ 'type' => 'integer', ], '__integerMin1Max25' => [ 'type' => 'integer', 'min' => 1, 'max' => 25, ], 'ListOfBackendAPIAuthType' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackendAPIAuthType', ], ], 'ListOfBackendJobRespObj' => [ 'type' => 'list', 'member' => [ 'shape' => 'BackendJobRespObj', ], ], 'ListOfS3BucketInfo' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3BucketInfo', ], ], 'ListOfAdditionalConstraintsElement' => [ 'type' => 'list', 'member' => [ 'shape' => 'AdditionalConstraintsElement', ], ], 'ListOfAuthenticatedElement' => [ 'type' => 'list', 'member' => [ 'shape' => 'AuthenticatedElement', ], ], 'ListOfMfaTypesElement' => [ 'type' => 'list', 'member' => [ 'shape' => 'MfaTypesElement', ], ], 'ListOfOAuthScopesElement' => [ 'type' => 'list', 'member' => [ 'shape' => 'OAuthScopesElement', ], ], 'ListOfRequiredSignUpAttributesElement' => [ 'type' => 'list', 'member' => [ 'shape' => 'RequiredSignUpAttributesElement', ], ], 'ListOfUnAuthenticatedElement' => [ 'type' => 'list', 'member' => [ 'shape' => 'UnAuthenticatedElement', ], ], 'ListOf__string' => [ 'type' => 'list', 'member' => [ 'shape' => '__string', ], ], '__long' => [ 'type' => 'long', ], '__string' => [ 'type' => 'string', ], '__timestampIso8601' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], '__timestampUnix' => [ 'type' => 'timestamp', 'timestampFormat' => 'unixTimestamp', ], ],];
