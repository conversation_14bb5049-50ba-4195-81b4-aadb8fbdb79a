<?php
// This file was auto-generated from sdk-root/src/data/cloudfront/2020-05-31/paginators-1.json
return [ 'pagination' => [ 'ListCloudFrontOriginAccessIdentities' => [ 'input_token' => 'Marker', 'output_token' => 'CloudFrontOriginAccessIdentityList.NextMarker', 'limit_key' => 'MaxItems', 'result_key' => 'CloudFrontOriginAccessIdentityList.Items', ], 'ListConnectionGroups' => [ 'input_token' => 'Marker', 'output_token' => 'NextMarker', 'limit_key' => 'MaxItems', 'result_key' => 'ConnectionGroups', ], 'ListDistributionTenants' => [ 'input_token' => 'Marker', 'output_token' => 'NextMarker', 'limit_key' => 'MaxItems', 'result_key' => 'DistributionTenantList', ], 'ListDistributionTenantsByCustomization' => [ 'input_token' => 'Marker', 'output_token' => 'NextMarker', 'limit_key' => 'MaxItems', 'result_key' => 'DistributionTenantList', ], 'ListDistributions' => [ 'input_token' => 'Marker', 'output_token' => 'DistributionList.NextMarker', 'limit_key' => 'MaxItems', 'result_key' => 'DistributionList.Items', ], 'ListDistributionsByConnectionMode' => [ 'input_token' => 'Marker', 'output_token' => 'DistributionList.NextMarker', 'limit_key' => 'MaxItems', 'result_key' => 'DistributionList.Items', ], 'ListDomainConflicts' => [ 'input_token' => 'Marker', 'output_token' => 'NextMarker', 'limit_key' => 'MaxItems', 'result_key' => 'DomainConflicts', ], 'ListInvalidations' => [ 'input_token' => 'Marker', 'output_token' => 'InvalidationList.NextMarker', 'limit_key' => 'MaxItems', 'result_key' => 'InvalidationList.Items', ], 'ListInvalidationsForDistributionTenant' => [ 'input_token' => 'Marker', 'output_token' => 'InvalidationList.NextMarker', 'limit_key' => 'MaxItems', 'result_key' => 'InvalidationList.Items', ], 'ListKeyValueStores' => [ 'input_token' => 'Marker', 'output_token' => 'KeyValueStoreList.NextMarker', 'limit_key' => 'MaxItems', 'result_key' => 'KeyValueStoreList.Items', ], 'ListPublicKeys' => [ 'input_token' => 'Marker', 'output_token' => 'PublicKeyList.NextMarker', 'limit_key' => 'MaxItems', 'result_key' => 'PublicKeyList.Items', ], 'ListStreamingDistributions' => [ 'input_token' => 'Marker', 'output_token' => 'StreamingDistributionList.NextMarker', 'limit_key' => 'MaxItems', 'result_key' => 'StreamingDistributionList.Items', ], ],];
