<?php
require_once __DIR__ . '/env.php';

$host = env('DB_HOST', 'localhost');
$dbname = env('DB_NAME', 'lowongan');
$username = env('DB_USER', 'root');
$password = env('DB_PASS', '');

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    echo "Koneksi database gagal: " . $e->getMessage();
    die();
}
?> 