<?php
// This file was auto-generated from sdk-root/src/data/cloudfront/2020-05-31/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2020-05-31', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'cloudfront', 'globalEndpoint' => 'cloudfront.amazonaws.com', 'protocol' => 'rest-xml', 'protocols' => [ 'rest-xml', ], 'serviceAbbreviation' => 'CloudFront', 'serviceFullName' => 'Amazon CloudFront', 'serviceId' => 'CloudFront', 'signatureVersion' => 'v4', 'signingName' => 'cloudfront', 'uid' => 'cloudfront-2020-05-31', ], 'operations' => [ 'AssociateAlias' => [ 'name' => 'AssociateAlias2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/distribution/{TargetDistributionId}/associate-alias', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateAliasRequest', ], 'errors' => [ [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'TooManyDistributionCNAMEs', ], ], ], 'AssociateDistributionTenantWebACL' => [ 'name' => 'AssociateDistributionTenantWebACL2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/distribution-tenant/{Id}/associate-web-acl', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateDistributionTenantWebACLRequest', 'locationName' => 'AssociateDistributionTenantWebACLRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'AssociateDistributionTenantWebACLResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'AssociateDistributionWebACL' => [ 'name' => 'AssociateDistributionWebACL2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/distribution/{Id}/associate-web-acl', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AssociateDistributionWebACLRequest', 'locationName' => 'AssociateDistributionWebACLRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'AssociateDistributionWebACLResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'CopyDistribution' => [ 'name' => 'CopyDistribution2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distribution/{PrimaryDistributionId}/copy', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CopyDistributionRequest', 'locationName' => 'CopyDistributionRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'CopyDistributionResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginAccessControl', ], [ 'shape' => 'InvalidDefaultRootObject', ], [ 'shape' => 'InvalidQueryStringParameters', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'TooManyCookieNamesInWhiteList', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], [ 'shape' => 'InvalidErrorCode', ], [ 'shape' => 'InvalidProtocolSettings', ], [ 'shape' => 'TooManyFunctionAssociations', ], [ 'shape' => 'TooManyOriginCustomHeaders', ], [ 'shape' => 'InvalidOrigin', ], [ 'shape' => 'InvalidForwardCookies', ], [ 'shape' => 'InvalidMinimumProtocolVersion', ], [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'TooManyKeyGroupsAssociatedToDistribution', ], [ 'shape' => 'TooManyDistributionsAssociatedToCachePolicy', ], [ 'shape' => 'InvalidRequiredProtocol', ], [ 'shape' => 'TooManyDistributionsWithFunctionAssociations', ], [ 'shape' => 'TooManyOriginGroupsPerDistribution', ], [ 'shape' => 'TooManyDistributions', ], [ 'shape' => 'InvalidTTLOrder', ], [ 'shape' => 'IllegalFieldLevelEncryptionConfigAssociationWithCacheBehavior', ], [ 'shape' => 'InvalidOriginKeepaliveTimeout', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidOriginReadTimeout', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'InvalidHeadersForS3Origin', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'InvalidWebACLId', ], [ 'shape' => 'TooManyDistributionsWithSingleFunctionARN', ], [ 'shape' => 'InvalidRelativePath', ], [ 'shape' => 'TooManyLambdaFunctionAssociations', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToFieldLevelEncryptionConfig', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidLocationCode', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'TooManyDistributionCNAMEs', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginRequestPolicy', ], [ 'shape' => 'TooManyQueryStringParameters', ], [ 'shape' => 'RealtimeLogConfigOwnerMismatch', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyHeadersInForwardedValues', ], [ 'shape' => 'InvalidLambdaFunctionAssociation', ], [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'TooManyCertificates', ], [ 'shape' => 'TrustedKeyGroupDoesNotExist', ], [ 'shape' => 'TooManyDistributionsAssociatedToResponseHeadersPolicy', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], [ 'shape' => 'InvalidResponseCode', ], [ 'shape' => 'InvalidGeoRestrictionParameter', ], [ 'shape' => 'TooManyOrigins', ], [ 'shape' => 'InvalidViewerCertificate', ], [ 'shape' => 'InvalidFunctionAssociation', ], [ 'shape' => 'TooManyDistributionsWithLambdaAssociations', ], [ 'shape' => 'TooManyDistributionsAssociatedToKeyGroup', ], [ 'shape' => 'DistributionAlreadyExists', ], [ 'shape' => 'NoSuchOrigin', ], [ 'shape' => 'TooManyCacheBehaviors', ], ], ], 'CreateAnycastIpList' => [ 'name' => 'CreateAnycastIpList2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/anycast-ip-list', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateAnycastIpListRequest', 'locationName' => 'CreateAnycastIpListRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'CreateAnycastIpListResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'EntityAlreadyExists', ], [ 'shape' => 'InvalidTagging', ], [ 'shape' => 'EntityLimitExceeded', ], [ 'shape' => 'InvalidArgument', ], ], ], 'CreateCachePolicy' => [ 'name' => 'CreateCachePolicy2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/cache-policy', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateCachePolicyRequest', ], 'output' => [ 'shape' => 'CreateCachePolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyHeadersInCachePolicy', ], [ 'shape' => 'CachePolicyAlreadyExists', ], [ 'shape' => 'TooManyCookiesInCachePolicy', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'TooManyCachePolicies', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'TooManyQueryStringsInCachePolicy', ], ], ], 'CreateCloudFrontOriginAccessIdentity' => [ 'name' => 'CreateCloudFrontOriginAccessIdentity2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/origin-access-identity/cloudfront', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateCloudFrontOriginAccessIdentityRequest', ], 'output' => [ 'shape' => 'CreateCloudFrontOriginAccessIdentityResult', ], 'errors' => [ [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyCloudFrontOriginAccessIdentities', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'CloudFrontOriginAccessIdentityAlreadyExists', ], [ 'shape' => 'InvalidArgument', ], ], ], 'CreateConnectionGroup' => [ 'name' => 'CreateConnectionGroup2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/connection-group', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateConnectionGroupRequest', 'locationName' => 'CreateConnectionGroupRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'CreateConnectionGroupResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'EntityAlreadyExists', ], [ 'shape' => 'InvalidTagging', ], [ 'shape' => 'EntityLimitExceeded', ], [ 'shape' => 'InvalidArgument', ], ], ], 'CreateContinuousDeploymentPolicy' => [ 'name' => 'CreateContinuousDeploymentPolicy2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/continuous-deployment-policy', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateContinuousDeploymentPolicyRequest', ], 'output' => [ 'shape' => 'CreateContinuousDeploymentPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyContinuousDeploymentPolicies', ], [ 'shape' => 'StagingDistributionInUse', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'ContinuousDeploymentPolicyAlreadyExists', ], [ 'shape' => 'InvalidArgument', ], ], ], 'CreateDistribution' => [ 'name' => 'CreateDistribution2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distribution', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDistributionRequest', ], 'output' => [ 'shape' => 'CreateDistributionResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginAccessControl', ], [ 'shape' => 'InvalidDefaultRootObject', ], [ 'shape' => 'InvalidDomainNameForOriginAccessControl', ], [ 'shape' => 'InvalidQueryStringParameters', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'TooManyCookieNamesInWhiteList', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], [ 'shape' => 'InvalidErrorCode', ], [ 'shape' => 'IllegalOriginAccessConfiguration', ], [ 'shape' => 'InvalidProtocolSettings', ], [ 'shape' => 'TooManyFunctionAssociations', ], [ 'shape' => 'TooManyOriginCustomHeaders', ], [ 'shape' => 'InvalidOrigin', ], [ 'shape' => 'InvalidForwardCookies', ], [ 'shape' => 'InvalidMinimumProtocolVersion', ], [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'TooManyKeyGroupsAssociatedToDistribution', ], [ 'shape' => 'TooManyDistributionsAssociatedToCachePolicy', ], [ 'shape' => 'InvalidRequiredProtocol', ], [ 'shape' => 'TooManyDistributionsWithFunctionAssociations', ], [ 'shape' => 'TooManyOriginGroupsPerDistribution', ], [ 'shape' => 'TooManyDistributions', ], [ 'shape' => 'InvalidTTLOrder', ], [ 'shape' => 'IllegalFieldLevelEncryptionConfigAssociationWithCacheBehavior', ], [ 'shape' => 'InvalidOriginKeepaliveTimeout', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidOriginReadTimeout', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'InvalidHeadersForS3Origin', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'InvalidWebACLId', ], [ 'shape' => 'TooManyDistributionsWithSingleFunctionARN', ], [ 'shape' => 'InvalidRelativePath', ], [ 'shape' => 'TooManyLambdaFunctionAssociations', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToFieldLevelEncryptionConfig', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidLocationCode', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'TooManyDistributionCNAMEs', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginRequestPolicy', ], [ 'shape' => 'TooManyQueryStringParameters', ], [ 'shape' => 'RealtimeLogConfigOwnerMismatch', ], [ 'shape' => 'ContinuousDeploymentPolicyInUse', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyHeadersInForwardedValues', ], [ 'shape' => 'InvalidLambdaFunctionAssociation', ], [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'TooManyCertificates', ], [ 'shape' => 'TrustedKeyGroupDoesNotExist', ], [ 'shape' => 'TooManyDistributionsAssociatedToResponseHeadersPolicy', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], [ 'shape' => 'InvalidResponseCode', ], [ 'shape' => 'InvalidGeoRestrictionParameter', ], [ 'shape' => 'TooManyOrigins', ], [ 'shape' => 'InvalidViewerCertificate', ], [ 'shape' => 'InvalidFunctionAssociation', ], [ 'shape' => 'TooManyDistributionsWithLambdaAssociations', ], [ 'shape' => 'TooManyDistributionsAssociatedToKeyGroup', ], [ 'shape' => 'EntityLimitExceeded', ], [ 'shape' => 'DistributionAlreadyExists', ], [ 'shape' => 'NoSuchOrigin', ], [ 'shape' => 'TooManyCacheBehaviors', ], ], ], 'CreateDistributionTenant' => [ 'name' => 'CreateDistributionTenant2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distribution-tenant', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDistributionTenantRequest', 'locationName' => 'CreateDistributionTenantRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'CreateDistributionTenantResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'EntityAlreadyExists', ], [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'InvalidTagging', ], [ 'shape' => 'InvalidAssociation', ], [ 'shape' => 'EntityLimitExceeded', ], [ 'shape' => 'InvalidArgument', ], ], ], 'CreateDistributionWithTags' => [ 'name' => 'CreateDistributionWithTags2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distribution?WithTags', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateDistributionWithTagsRequest', ], 'output' => [ 'shape' => 'CreateDistributionWithTagsResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginAccessControl', ], [ 'shape' => 'InvalidDefaultRootObject', ], [ 'shape' => 'InvalidDomainNameForOriginAccessControl', ], [ 'shape' => 'InvalidQueryStringParameters', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'TooManyCookieNamesInWhiteList', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], [ 'shape' => 'InvalidErrorCode', ], [ 'shape' => 'IllegalOriginAccessConfiguration', ], [ 'shape' => 'InvalidProtocolSettings', ], [ 'shape' => 'TooManyFunctionAssociations', ], [ 'shape' => 'TooManyOriginCustomHeaders', ], [ 'shape' => 'InvalidOrigin', ], [ 'shape' => 'InvalidForwardCookies', ], [ 'shape' => 'InvalidMinimumProtocolVersion', ], [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'TooManyKeyGroupsAssociatedToDistribution', ], [ 'shape' => 'TooManyDistributionsAssociatedToCachePolicy', ], [ 'shape' => 'InvalidRequiredProtocol', ], [ 'shape' => 'TooManyDistributionsWithFunctionAssociations', ], [ 'shape' => 'TooManyOriginGroupsPerDistribution', ], [ 'shape' => 'TooManyDistributions', ], [ 'shape' => 'InvalidTTLOrder', ], [ 'shape' => 'IllegalFieldLevelEncryptionConfigAssociationWithCacheBehavior', ], [ 'shape' => 'InvalidOriginKeepaliveTimeout', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidOriginReadTimeout', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'InvalidHeadersForS3Origin', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'InvalidWebACLId', ], [ 'shape' => 'TooManyDistributionsWithSingleFunctionARN', ], [ 'shape' => 'InvalidRelativePath', ], [ 'shape' => 'TooManyLambdaFunctionAssociations', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToFieldLevelEncryptionConfig', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidLocationCode', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'InvalidTagging', ], [ 'shape' => 'TooManyDistributionCNAMEs', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginRequestPolicy', ], [ 'shape' => 'TooManyQueryStringParameters', ], [ 'shape' => 'RealtimeLogConfigOwnerMismatch', ], [ 'shape' => 'ContinuousDeploymentPolicyInUse', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyHeadersInForwardedValues', ], [ 'shape' => 'InvalidLambdaFunctionAssociation', ], [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'TooManyCertificates', ], [ 'shape' => 'TrustedKeyGroupDoesNotExist', ], [ 'shape' => 'TooManyDistributionsAssociatedToResponseHeadersPolicy', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], [ 'shape' => 'InvalidResponseCode', ], [ 'shape' => 'InvalidGeoRestrictionParameter', ], [ 'shape' => 'TooManyOrigins', ], [ 'shape' => 'InvalidViewerCertificate', ], [ 'shape' => 'InvalidFunctionAssociation', ], [ 'shape' => 'TooManyDistributionsWithLambdaAssociations', ], [ 'shape' => 'TooManyDistributionsAssociatedToKeyGroup', ], [ 'shape' => 'DistributionAlreadyExists', ], [ 'shape' => 'NoSuchOrigin', ], [ 'shape' => 'TooManyCacheBehaviors', ], ], ], 'CreateFieldLevelEncryptionConfig' => [ 'name' => 'CreateFieldLevelEncryptionConfig2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/field-level-encryption', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFieldLevelEncryptionConfigRequest', ], 'output' => [ 'shape' => 'CreateFieldLevelEncryptionConfigResult', ], 'errors' => [ [ 'shape' => 'QueryArgProfileEmpty', ], [ 'shape' => 'TooManyFieldLevelEncryptionContentTypeProfiles', ], [ 'shape' => 'TooManyFieldLevelEncryptionQueryArgProfiles', ], [ 'shape' => 'FieldLevelEncryptionConfigAlreadyExists', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'TooManyFieldLevelEncryptionConfigs', ], [ 'shape' => 'NoSuchFieldLevelEncryptionProfile', ], [ 'shape' => 'InvalidArgument', ], ], ], 'CreateFieldLevelEncryptionProfile' => [ 'name' => 'CreateFieldLevelEncryptionProfile2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/field-level-encryption-profile', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFieldLevelEncryptionProfileRequest', ], 'output' => [ 'shape' => 'CreateFieldLevelEncryptionProfileResult', ], 'errors' => [ [ 'shape' => 'TooManyFieldLevelEncryptionFieldPatterns', ], [ 'shape' => 'FieldLevelEncryptionProfileAlreadyExists', ], [ 'shape' => 'NoSuchPublicKey', ], [ 'shape' => 'FieldLevelEncryptionProfileSizeExceeded', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'TooManyFieldLevelEncryptionProfiles', ], [ 'shape' => 'TooManyFieldLevelEncryptionEncryptionEntities', ], [ 'shape' => 'InvalidArgument', ], ], ], 'CreateFunction' => [ 'name' => 'CreateFunction2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/function', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateFunctionRequest', 'locationName' => 'CreateFunctionRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'CreateFunctionResult', ], 'errors' => [ [ 'shape' => 'FunctionAlreadyExists', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'FunctionSizeLimitExceeded', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'TooManyFunctions', ], ], ], 'CreateInvalidation' => [ 'name' => 'CreateInvalidation2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distribution/{DistributionId}/invalidation', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateInvalidationRequest', ], 'output' => [ 'shape' => 'CreateInvalidationResult', ], 'errors' => [ [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyInvalidationsInProgress', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'BatchTooLarge', ], [ 'shape' => 'InvalidArgument', ], ], ], 'CreateInvalidationForDistributionTenant' => [ 'name' => 'CreateInvalidationForDistributionTenant2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distribution-tenant/{Id}/invalidation', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateInvalidationForDistributionTenantRequest', ], 'output' => [ 'shape' => 'CreateInvalidationForDistributionTenantResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'TooManyInvalidationsInProgress', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'BatchTooLarge', ], [ 'shape' => 'InvalidArgument', ], ], ], 'CreateKeyGroup' => [ 'name' => 'CreateKeyGroup2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/key-group', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateKeyGroupRequest', ], 'output' => [ 'shape' => 'CreateKeyGroupResult', ], 'errors' => [ [ 'shape' => 'TooManyPublicKeysInKeyGroup', ], [ 'shape' => 'TooManyKeyGroups', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'KeyGroupAlreadyExists', ], ], ], 'CreateKeyValueStore' => [ 'name' => 'CreateKeyValueStore2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/key-value-store', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateKeyValueStoreRequest', 'locationName' => 'CreateKeyValueStoreRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'CreateKeyValueStoreResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'EntityAlreadyExists', ], [ 'shape' => 'EntityLimitExceeded', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'EntitySizeLimitExceeded', ], ], ], 'CreateMonitoringSubscription' => [ 'name' => 'CreateMonitoringSubscription2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distributions/{DistributionId}/monitoring-subscription', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMonitoringSubscriptionRequest', ], 'output' => [ 'shape' => 'CreateMonitoringSubscriptionResult', ], 'errors' => [ [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'MonitoringSubscriptionAlreadyExists', ], [ 'shape' => 'UnsupportedOperation', ], ], ], 'CreateOriginAccessControl' => [ 'name' => 'CreateOriginAccessControl2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/origin-access-control', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateOriginAccessControlRequest', ], 'output' => [ 'shape' => 'CreateOriginAccessControlResult', ], 'errors' => [ [ 'shape' => 'OriginAccessControlAlreadyExists', ], [ 'shape' => 'TooManyOriginAccessControls', ], [ 'shape' => 'InvalidArgument', ], ], ], 'CreateOriginRequestPolicy' => [ 'name' => 'CreateOriginRequestPolicy2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/origin-request-policy', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateOriginRequestPolicyRequest', ], 'output' => [ 'shape' => 'CreateOriginRequestPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyHeadersInOriginRequestPolicy', ], [ 'shape' => 'TooManyCookiesInOriginRequestPolicy', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'OriginRequestPolicyAlreadyExists', ], [ 'shape' => 'TooManyQueryStringsInOriginRequestPolicy', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'TooManyOriginRequestPolicies', ], ], ], 'CreatePublicKey' => [ 'name' => 'CreatePublicKey2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/public-key', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreatePublicKeyRequest', ], 'output' => [ 'shape' => 'CreatePublicKeyResult', ], 'errors' => [ [ 'shape' => 'TooManyPublicKeys', ], [ 'shape' => 'PublicKeyAlreadyExists', ], [ 'shape' => 'InvalidArgument', ], ], ], 'CreateRealtimeLogConfig' => [ 'name' => 'CreateRealtimeLogConfig2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/realtime-log-config', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateRealtimeLogConfigRequest', 'locationName' => 'CreateRealtimeLogConfigRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'CreateRealtimeLogConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'RealtimeLogConfigAlreadyExists', ], [ 'shape' => 'TooManyRealtimeLogConfigs', ], [ 'shape' => 'InvalidArgument', ], ], ], 'CreateResponseHeadersPolicy' => [ 'name' => 'CreateResponseHeadersPolicy2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/response-headers-policy', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateResponseHeadersPolicyRequest', ], 'output' => [ 'shape' => 'CreateResponseHeadersPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyCustomHeadersInResponseHeadersPolicy', ], [ 'shape' => 'ResponseHeadersPolicyAlreadyExists', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'TooLongCSPInResponseHeadersPolicy', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'TooManyRemoveHeadersInResponseHeadersPolicy', ], [ 'shape' => 'TooManyResponseHeadersPolicies', ], ], ], 'CreateStreamingDistribution' => [ 'name' => 'CreateStreamingDistribution2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/streaming-distribution', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateStreamingDistributionRequest', ], 'output' => [ 'shape' => 'CreateStreamingDistributionResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'StreamingDistributionAlreadyExists', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'TooManyStreamingDistributions', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyStreamingDistributionCNAMEs', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'InvalidOrigin', ], ], ], 'CreateStreamingDistributionWithTags' => [ 'name' => 'CreateStreamingDistributionWithTags2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/streaming-distribution?WithTags', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateStreamingDistributionWithTagsRequest', ], 'output' => [ 'shape' => 'CreateStreamingDistributionWithTagsResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'StreamingDistributionAlreadyExists', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'InvalidTagging', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'TooManyStreamingDistributions', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyStreamingDistributionCNAMEs', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'InvalidOrigin', ], ], ], 'CreateVpcOrigin' => [ 'name' => 'CreateVpcOrigin2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/vpc-origin', 'responseCode' => 202, ], 'input' => [ 'shape' => 'CreateVpcOriginRequest', 'locationName' => 'CreateVpcOriginRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'CreateVpcOriginResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'EntityAlreadyExists', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidTagging', ], [ 'shape' => 'EntityLimitExceeded', ], [ 'shape' => 'InvalidArgument', ], ], ], 'DeleteAnycastIpList' => [ 'name' => 'DeleteAnycastIpList2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/anycast-ip-list/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteAnycastIpListRequest', ], 'errors' => [ [ 'shape' => 'CannotDeleteEntityWhileInUse', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'IllegalDelete', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DeleteCachePolicy' => [ 'name' => 'DeleteCachePolicy2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/cache-policy/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteCachePolicyRequest', ], 'errors' => [ [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'IllegalDelete', ], [ 'shape' => 'CachePolicyInUse', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DeleteCloudFrontOriginAccessIdentity' => [ 'name' => 'DeleteCloudFrontOriginAccessIdentity2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/origin-access-identity/cloudfront/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteCloudFrontOriginAccessIdentityRequest', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'CloudFrontOriginAccessIdentityInUse', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchCloudFrontOriginAccessIdentity', ], ], ], 'DeleteConnectionGroup' => [ 'name' => 'DeleteConnectionGroup2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/connection-group/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteConnectionGroupRequest', ], 'errors' => [ [ 'shape' => 'CannotDeleteEntityWhileInUse', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'ResourceNotDisabled', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DeleteContinuousDeploymentPolicy' => [ 'name' => 'DeleteContinuousDeploymentPolicy2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/continuous-deployment-policy/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteContinuousDeploymentPolicyRequest', ], 'errors' => [ [ 'shape' => 'ContinuousDeploymentPolicyInUse', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DeleteDistribution' => [ 'name' => 'DeleteDistribution2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/distribution/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteDistributionRequest', ], 'errors' => [ [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'DistributionNotDisabled', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DeleteDistributionTenant' => [ 'name' => 'DeleteDistributionTenant2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/distribution-tenant/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteDistributionTenantRequest', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'ResourceNotDisabled', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DeleteFieldLevelEncryptionConfig' => [ 'name' => 'DeleteFieldLevelEncryptionConfig2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/field-level-encryption/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFieldLevelEncryptionConfigRequest', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'FieldLevelEncryptionConfigInUse', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DeleteFieldLevelEncryptionProfile' => [ 'name' => 'DeleteFieldLevelEncryptionProfile2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/field-level-encryption-profile/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFieldLevelEncryptionProfileRequest', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchFieldLevelEncryptionProfile', ], [ 'shape' => 'FieldLevelEncryptionProfileInUse', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DeleteFunction' => [ 'name' => 'DeleteFunction2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/function/{Name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteFunctionRequest', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'FunctionInUse', ], [ 'shape' => 'NoSuchFunctionExists', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DeleteKeyGroup' => [ 'name' => 'DeleteKeyGroup2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/key-group/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteKeyGroupRequest', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'NoSuchResource', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DeleteKeyValueStore' => [ 'name' => 'DeleteKeyValueStore2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/key-value-store/{Name}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteKeyValueStoreRequest', ], 'errors' => [ [ 'shape' => 'CannotDeleteEntityWhileInUse', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], 'idempotent' => true, ], 'DeleteMonitoringSubscription' => [ 'name' => 'DeleteMonitoringSubscription2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/distributions/{DistributionId}/monitoring-subscription', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMonitoringSubscriptionRequest', ], 'output' => [ 'shape' => 'DeleteMonitoringSubscriptionResult', ], 'errors' => [ [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'NoSuchMonitoringSubscription', ], ], ], 'DeleteOriginAccessControl' => [ 'name' => 'DeleteOriginAccessControl2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/origin-access-control/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteOriginAccessControlRequest', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'OriginAccessControlInUse', ], [ 'shape' => 'NoSuchOriginAccessControl', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DeleteOriginRequestPolicy' => [ 'name' => 'DeleteOriginRequestPolicy2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/origin-request-policy/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteOriginRequestPolicyRequest', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'IllegalDelete', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'OriginRequestPolicyInUse', ], ], ], 'DeletePublicKey' => [ 'name' => 'DeletePublicKey2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/public-key/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeletePublicKeyRequest', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchPublicKey', ], [ 'shape' => 'PublicKeyInUse', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DeleteRealtimeLogConfig' => [ 'name' => 'DeleteRealtimeLogConfig2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/delete-realtime-log-config', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteRealtimeLogConfigRequest', 'locationName' => 'DeleteRealtimeLogConfigRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], [ 'shape' => 'RealtimeLogConfigInUse', ], ], ], 'DeleteResponseHeadersPolicy' => [ 'name' => 'DeleteResponseHeadersPolicy2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/response-headers-policy/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteResponseHeadersPolicyRequest', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'ResponseHeadersPolicyInUse', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'IllegalDelete', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DeleteStreamingDistribution' => [ 'name' => 'DeleteStreamingDistribution2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/streaming-distribution/{Id}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'DeleteStreamingDistributionRequest', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchStreamingDistribution', ], [ 'shape' => 'StreamingDistributionNotDisabled', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DeleteVpcOrigin' => [ 'name' => 'DeleteVpcOrigin2020_05_31', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/2020-05-31/vpc-origin/{Id}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'DeleteVpcOriginRequest', ], 'output' => [ 'shape' => 'DeleteVpcOriginResult', ], 'errors' => [ [ 'shape' => 'CannotDeleteEntityWhileInUse', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'IllegalDelete', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DescribeFunction' => [ 'name' => 'DescribeFunction2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/function/{Name}/describe', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeFunctionRequest', ], 'output' => [ 'shape' => 'DescribeFunctionResult', ], 'errors' => [ [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'NoSuchFunctionExists', ], ], ], 'DescribeKeyValueStore' => [ 'name' => 'DescribeKeyValueStore2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/key-value-store/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DescribeKeyValueStoreRequest', ], 'output' => [ 'shape' => 'DescribeKeyValueStoreResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidArgument', ], ], ], 'DisassociateDistributionTenantWebACL' => [ 'name' => 'DisassociateDistributionTenantWebACL2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/distribution-tenant/{Id}/disassociate-web-acl', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateDistributionTenantWebACLRequest', ], 'output' => [ 'shape' => 'DisassociateDistributionTenantWebACLResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'DisassociateDistributionWebACL' => [ 'name' => 'DisassociateDistributionWebACL2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/distribution/{Id}/disassociate-web-acl', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisassociateDistributionWebACLRequest', ], 'output' => [ 'shape' => 'DisassociateDistributionWebACLResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'GetAnycastIpList' => [ 'name' => 'GetAnycastIpList2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/anycast-ip-list/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAnycastIpListRequest', ], 'output' => [ 'shape' => 'GetAnycastIpListResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidArgument', ], ], ], 'GetCachePolicy' => [ 'name' => 'GetCachePolicy2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/cache-policy/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCachePolicyRequest', ], 'output' => [ 'shape' => 'GetCachePolicyResult', ], 'errors' => [ [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'AccessDenied', ], ], ], 'GetCachePolicyConfig' => [ 'name' => 'GetCachePolicyConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/cache-policy/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCachePolicyConfigRequest', ], 'output' => [ 'shape' => 'GetCachePolicyConfigResult', ], 'errors' => [ [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'AccessDenied', ], ], ], 'GetCloudFrontOriginAccessIdentity' => [ 'name' => 'GetCloudFrontOriginAccessIdentity2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-access-identity/cloudfront/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCloudFrontOriginAccessIdentityRequest', ], 'output' => [ 'shape' => 'GetCloudFrontOriginAccessIdentityResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchCloudFrontOriginAccessIdentity', ], ], ], 'GetCloudFrontOriginAccessIdentityConfig' => [ 'name' => 'GetCloudFrontOriginAccessIdentityConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-access-identity/cloudfront/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCloudFrontOriginAccessIdentityConfigRequest', ], 'output' => [ 'shape' => 'GetCloudFrontOriginAccessIdentityConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchCloudFrontOriginAccessIdentity', ], ], ], 'GetConnectionGroup' => [ 'name' => 'GetConnectionGroup2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/connection-group/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConnectionGroupRequest', ], 'output' => [ 'shape' => 'GetConnectionGroupResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], ], ], 'GetConnectionGroupByRoutingEndpoint' => [ 'name' => 'GetConnectionGroupByRoutingEndpoint2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/connection-group', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetConnectionGroupByRoutingEndpointRequest', ], 'output' => [ 'shape' => 'GetConnectionGroupByRoutingEndpointResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], ], ], 'GetContinuousDeploymentPolicy' => [ 'name' => 'GetContinuousDeploymentPolicy2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/continuous-deployment-policy/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetContinuousDeploymentPolicyRequest', ], 'output' => [ 'shape' => 'GetContinuousDeploymentPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], ], ], 'GetContinuousDeploymentPolicyConfig' => [ 'name' => 'GetContinuousDeploymentPolicyConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/continuous-deployment-policy/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetContinuousDeploymentPolicyConfigRequest', ], 'output' => [ 'shape' => 'GetContinuousDeploymentPolicyConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], ], ], 'GetDistribution' => [ 'name' => 'GetDistribution2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distribution/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDistributionRequest', ], 'output' => [ 'shape' => 'GetDistributionResult', ], 'errors' => [ [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'AccessDenied', ], ], ], 'GetDistributionConfig' => [ 'name' => 'GetDistributionConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distribution/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDistributionConfigRequest', ], 'output' => [ 'shape' => 'GetDistributionConfigResult', ], 'errors' => [ [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'AccessDenied', ], ], ], 'GetDistributionTenant' => [ 'name' => 'GetDistributionTenant2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distribution-tenant/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDistributionTenantRequest', ], 'output' => [ 'shape' => 'GetDistributionTenantResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], ], ], 'GetDistributionTenantByDomain' => [ 'name' => 'GetDistributionTenantByDomain2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distribution-tenant', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetDistributionTenantByDomainRequest', ], 'output' => [ 'shape' => 'GetDistributionTenantByDomainResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], ], ], 'GetFieldLevelEncryption' => [ 'name' => 'GetFieldLevelEncryption2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/field-level-encryption/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFieldLevelEncryptionRequest', ], 'output' => [ 'shape' => 'GetFieldLevelEncryptionResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], ], ], 'GetFieldLevelEncryptionConfig' => [ 'name' => 'GetFieldLevelEncryptionConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/field-level-encryption/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFieldLevelEncryptionConfigRequest', ], 'output' => [ 'shape' => 'GetFieldLevelEncryptionConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], ], ], 'GetFieldLevelEncryptionProfile' => [ 'name' => 'GetFieldLevelEncryptionProfile2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/field-level-encryption-profile/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFieldLevelEncryptionProfileRequest', ], 'output' => [ 'shape' => 'GetFieldLevelEncryptionProfileResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchFieldLevelEncryptionProfile', ], ], ], 'GetFieldLevelEncryptionProfileConfig' => [ 'name' => 'GetFieldLevelEncryptionProfileConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/field-level-encryption-profile/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFieldLevelEncryptionProfileConfigRequest', ], 'output' => [ 'shape' => 'GetFieldLevelEncryptionProfileConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchFieldLevelEncryptionProfile', ], ], ], 'GetFunction' => [ 'name' => 'GetFunction2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/function/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFunctionRequest', ], 'output' => [ 'shape' => 'GetFunctionResult', ], 'errors' => [ [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'NoSuchFunctionExists', ], ], ], 'GetInvalidation' => [ 'name' => 'GetInvalidation2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distribution/{DistributionId}/invalidation/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetInvalidationRequest', ], 'output' => [ 'shape' => 'GetInvalidationResult', ], 'errors' => [ [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchInvalidation', ], ], ], 'GetInvalidationForDistributionTenant' => [ 'name' => 'GetInvalidationForDistributionTenant2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distribution-tenant/{DistributionTenantId}/invalidation/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetInvalidationForDistributionTenantRequest', ], 'output' => [ 'shape' => 'GetInvalidationForDistributionTenantResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'NoSuchInvalidation', ], ], ], 'GetKeyGroup' => [ 'name' => 'GetKeyGroup2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/key-group/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetKeyGroupRequest', ], 'output' => [ 'shape' => 'GetKeyGroupResult', ], 'errors' => [ [ 'shape' => 'NoSuchResource', ], ], ], 'GetKeyGroupConfig' => [ 'name' => 'GetKeyGroupConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/key-group/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetKeyGroupConfigRequest', ], 'output' => [ 'shape' => 'GetKeyGroupConfigResult', ], 'errors' => [ [ 'shape' => 'NoSuchResource', ], ], ], 'GetManagedCertificateDetails' => [ 'name' => 'GetManagedCertificateDetails2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/managed-certificate/{Identifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetManagedCertificateDetailsRequest', ], 'output' => [ 'shape' => 'GetManagedCertificateDetailsResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], ], ], 'GetMonitoringSubscription' => [ 'name' => 'GetMonitoringSubscription2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributions/{DistributionId}/monitoring-subscription', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMonitoringSubscriptionRequest', ], 'output' => [ 'shape' => 'GetMonitoringSubscriptionResult', ], 'errors' => [ [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'NoSuchMonitoringSubscription', ], ], ], 'GetOriginAccessControl' => [ 'name' => 'GetOriginAccessControl2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-access-control/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetOriginAccessControlRequest', ], 'output' => [ 'shape' => 'GetOriginAccessControlResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchOriginAccessControl', ], ], ], 'GetOriginAccessControlConfig' => [ 'name' => 'GetOriginAccessControlConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-access-control/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetOriginAccessControlConfigRequest', ], 'output' => [ 'shape' => 'GetOriginAccessControlConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchOriginAccessControl', ], ], ], 'GetOriginRequestPolicy' => [ 'name' => 'GetOriginRequestPolicy2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-request-policy/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetOriginRequestPolicyRequest', ], 'output' => [ 'shape' => 'GetOriginRequestPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], ], ], 'GetOriginRequestPolicyConfig' => [ 'name' => 'GetOriginRequestPolicyConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-request-policy/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetOriginRequestPolicyConfigRequest', ], 'output' => [ 'shape' => 'GetOriginRequestPolicyConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], ], ], 'GetPublicKey' => [ 'name' => 'GetPublicKey2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/public-key/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPublicKeyRequest', ], 'output' => [ 'shape' => 'GetPublicKeyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchPublicKey', ], ], ], 'GetPublicKeyConfig' => [ 'name' => 'GetPublicKeyConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/public-key/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPublicKeyConfigRequest', ], 'output' => [ 'shape' => 'GetPublicKeyConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchPublicKey', ], ], ], 'GetRealtimeLogConfig' => [ 'name' => 'GetRealtimeLogConfig2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/get-realtime-log-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRealtimeLogConfigRequest', 'locationName' => 'GetRealtimeLogConfigRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'GetRealtimeLogConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], ], ], 'GetResponseHeadersPolicy' => [ 'name' => 'GetResponseHeadersPolicy2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/response-headers-policy/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetResponseHeadersPolicyRequest', ], 'output' => [ 'shape' => 'GetResponseHeadersPolicyResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], ], ], 'GetResponseHeadersPolicyConfig' => [ 'name' => 'GetResponseHeadersPolicyConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/response-headers-policy/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetResponseHeadersPolicyConfigRequest', ], 'output' => [ 'shape' => 'GetResponseHeadersPolicyConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], ], ], 'GetStreamingDistribution' => [ 'name' => 'GetStreamingDistribution2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/streaming-distribution/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStreamingDistributionRequest', ], 'output' => [ 'shape' => 'GetStreamingDistributionResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchStreamingDistribution', ], ], ], 'GetStreamingDistributionConfig' => [ 'name' => 'GetStreamingDistributionConfig2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/streaming-distribution/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetStreamingDistributionConfigRequest', ], 'output' => [ 'shape' => 'GetStreamingDistributionConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchStreamingDistribution', ], ], ], 'GetVpcOrigin' => [ 'name' => 'GetVpcOrigin2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/vpc-origin/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetVpcOriginRequest', ], 'output' => [ 'shape' => 'GetVpcOriginResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListAnycastIpLists' => [ 'name' => 'ListAnycastIpLists2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/anycast-ip-list', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAnycastIpListsRequest', ], 'output' => [ 'shape' => 'ListAnycastIpListsResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListCachePolicies' => [ 'name' => 'ListCachePolicies2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/cache-policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCachePoliciesRequest', ], 'output' => [ 'shape' => 'ListCachePoliciesResult', ], 'errors' => [ [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListCloudFrontOriginAccessIdentities' => [ 'name' => 'ListCloudFrontOriginAccessIdentities2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-access-identity/cloudfront', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCloudFrontOriginAccessIdentitiesRequest', ], 'output' => [ 'shape' => 'ListCloudFrontOriginAccessIdentitiesResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListConflictingAliases' => [ 'name' => 'ListConflictingAliases2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/conflicting-alias', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConflictingAliasesRequest', ], 'output' => [ 'shape' => 'ListConflictingAliasesResult', ], 'errors' => [ [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListConnectionGroups' => [ 'name' => 'ListConnectionGroups2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/connection-groups', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListConnectionGroupsRequest', 'locationName' => 'ListConnectionGroupsRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'ListConnectionGroupsResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListContinuousDeploymentPolicies' => [ 'name' => 'ListContinuousDeploymentPolicies2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/continuous-deployment-policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListContinuousDeploymentPoliciesRequest', ], 'output' => [ 'shape' => 'ListContinuousDeploymentPoliciesResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], ], ], 'ListDistributionTenants' => [ 'name' => 'ListDistributionTenants2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distribution-tenants', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDistributionTenantsRequest', 'locationName' => 'ListDistributionTenantsRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'ListDistributionTenantsResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListDistributionTenantsByCustomization' => [ 'name' => 'ListDistributionTenantsByCustomization2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distribution-tenants-by-customization', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDistributionTenantsByCustomizationRequest', 'locationName' => 'ListDistributionTenantsByCustomizationRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'ListDistributionTenantsByCustomizationResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListDistributions' => [ 'name' => 'ListDistributions2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distribution', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDistributionsRequest', ], 'output' => [ 'shape' => 'ListDistributionsResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListDistributionsByAnycastIpListId' => [ 'name' => 'ListDistributionsByAnycastIpListId2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributionsByAnycastIpListId/{AnycastIpListId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDistributionsByAnycastIpListIdRequest', ], 'output' => [ 'shape' => 'ListDistributionsByAnycastIpListIdResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListDistributionsByCachePolicyId' => [ 'name' => 'ListDistributionsByCachePolicyId2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributionsByCachePolicyId/{CachePolicyId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDistributionsByCachePolicyIdRequest', ], 'output' => [ 'shape' => 'ListDistributionsByCachePolicyIdResult', ], 'errors' => [ [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListDistributionsByConnectionMode' => [ 'name' => 'ListDistributionsByConnectionMode2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributionsByConnectionMode/{ConnectionMode}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDistributionsByConnectionModeRequest', ], 'output' => [ 'shape' => 'ListDistributionsByConnectionModeResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListDistributionsByKeyGroup' => [ 'name' => 'ListDistributionsByKeyGroup2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributionsByKeyGroupId/{KeyGroupId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDistributionsByKeyGroupRequest', ], 'output' => [ 'shape' => 'ListDistributionsByKeyGroupResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchResource', ], ], ], 'ListDistributionsByOriginRequestPolicyId' => [ 'name' => 'ListDistributionsByOriginRequestPolicyId2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributionsByOriginRequestPolicyId/{OriginRequestPolicyId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDistributionsByOriginRequestPolicyIdRequest', ], 'output' => [ 'shape' => 'ListDistributionsByOriginRequestPolicyIdResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListDistributionsByRealtimeLogConfig' => [ 'name' => 'ListDistributionsByRealtimeLogConfig2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/distributionsByRealtimeLogConfig', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDistributionsByRealtimeLogConfigRequest', 'locationName' => 'ListDistributionsByRealtimeLogConfigRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'ListDistributionsByRealtimeLogConfigResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListDistributionsByResponseHeadersPolicyId' => [ 'name' => 'ListDistributionsByResponseHeadersPolicyId2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributionsByResponseHeadersPolicyId/{ResponseHeadersPolicyId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDistributionsByResponseHeadersPolicyIdRequest', ], 'output' => [ 'shape' => 'ListDistributionsByResponseHeadersPolicyIdResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListDistributionsByVpcOriginId' => [ 'name' => 'ListDistributionsByVpcOriginId2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributionsByVpcOriginId/{VpcOriginId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDistributionsByVpcOriginIdRequest', ], 'output' => [ 'shape' => 'ListDistributionsByVpcOriginIdResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListDistributionsByWebACLId' => [ 'name' => 'ListDistributionsByWebACLId2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distributionsByWebACLId/{WebACLId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDistributionsByWebACLIdRequest', ], 'output' => [ 'shape' => 'ListDistributionsByWebACLIdResult', ], 'errors' => [ [ 'shape' => 'InvalidWebACLId', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListDomainConflicts' => [ 'name' => 'ListDomainConflicts2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/domain-conflicts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListDomainConflictsRequest', 'locationName' => 'ListDomainConflictsRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'ListDomainConflictsResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListFieldLevelEncryptionConfigs' => [ 'name' => 'ListFieldLevelEncryptionConfigs2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/field-level-encryption', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFieldLevelEncryptionConfigsRequest', ], 'output' => [ 'shape' => 'ListFieldLevelEncryptionConfigsResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListFieldLevelEncryptionProfiles' => [ 'name' => 'ListFieldLevelEncryptionProfiles2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/field-level-encryption-profile', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFieldLevelEncryptionProfilesRequest', ], 'output' => [ 'shape' => 'ListFieldLevelEncryptionProfilesResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListFunctions' => [ 'name' => 'ListFunctions2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/function', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFunctionsRequest', ], 'output' => [ 'shape' => 'ListFunctionsResult', ], 'errors' => [ [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListInvalidations' => [ 'name' => 'ListInvalidations2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distribution/{DistributionId}/invalidation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListInvalidationsRequest', ], 'output' => [ 'shape' => 'ListInvalidationsResult', ], 'errors' => [ [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListInvalidationsForDistributionTenant' => [ 'name' => 'ListInvalidationsForDistributionTenant2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/distribution-tenant/{Id}/invalidation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListInvalidationsForDistributionTenantRequest', ], 'output' => [ 'shape' => 'ListInvalidationsForDistributionTenantResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListKeyGroups' => [ 'name' => 'ListKeyGroups2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/key-group', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListKeyGroupsRequest', ], 'output' => [ 'shape' => 'ListKeyGroupsResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListKeyValueStores' => [ 'name' => 'ListKeyValueStores2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/key-value-store', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListKeyValueStoresRequest', ], 'output' => [ 'shape' => 'ListKeyValueStoresResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListOriginAccessControls' => [ 'name' => 'ListOriginAccessControls2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-access-control', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListOriginAccessControlsRequest', ], 'output' => [ 'shape' => 'ListOriginAccessControlsResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListOriginRequestPolicies' => [ 'name' => 'ListOriginRequestPolicies2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/origin-request-policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListOriginRequestPoliciesRequest', ], 'output' => [ 'shape' => 'ListOriginRequestPoliciesResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListPublicKeys' => [ 'name' => 'ListPublicKeys2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/public-key', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListPublicKeysRequest', ], 'output' => [ 'shape' => 'ListPublicKeysResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListRealtimeLogConfigs' => [ 'name' => 'ListRealtimeLogConfigs2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/realtime-log-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRealtimeLogConfigsRequest', ], 'output' => [ 'shape' => 'ListRealtimeLogConfigsResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], ], ], 'ListResponseHeadersPolicies' => [ 'name' => 'ListResponseHeadersPolicies2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/response-headers-policy', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListResponseHeadersPoliciesRequest', ], 'output' => [ 'shape' => 'ListResponseHeadersPoliciesResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'InvalidArgument', ], ], ], 'ListStreamingDistributions' => [ 'name' => 'ListStreamingDistributions2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/streaming-distribution', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListStreamingDistributionsRequest', ], 'output' => [ 'shape' => 'ListStreamingDistributionsResult', ], 'errors' => [ [ 'shape' => 'InvalidArgument', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/tagging', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidTagging', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchResource', ], ], ], 'ListVpcOrigins' => [ 'name' => 'ListVpcOrigins2020_05_31', 'http' => [ 'method' => 'GET', 'requestUri' => '/2020-05-31/vpc-origin', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListVpcOriginsRequest', ], 'output' => [ 'shape' => 'ListVpcOriginsResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidArgument', ], ], ], 'PublishFunction' => [ 'name' => 'PublishFunction2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/function/{Name}/publish', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PublishFunctionRequest', ], 'output' => [ 'shape' => 'PublishFunctionResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchFunctionExists', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'TagResource' => [ 'name' => 'TagResource2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/tagging?Operation=Tag', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidTagging', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchResource', ], ], ], 'TestFunction' => [ 'name' => 'TestFunction2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/function/{Name}/test', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TestFunctionRequest', 'locationName' => 'TestFunctionRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'TestFunctionResult', ], 'errors' => [ [ 'shape' => 'TestFunctionFailed', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchFunctionExists', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/tagging?Operation=Untag', 'responseCode' => 204, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidTagging', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchResource', ], ], ], 'UpdateCachePolicy' => [ 'name' => 'UpdateCachePolicy2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/cache-policy/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCachePolicyRequest', ], 'output' => [ 'shape' => 'UpdateCachePolicyResult', ], 'errors' => [ [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyHeadersInCachePolicy', ], [ 'shape' => 'CachePolicyAlreadyExists', ], [ 'shape' => 'TooManyCookiesInCachePolicy', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'TooManyQueryStringsInCachePolicy', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'UpdateCloudFrontOriginAccessIdentity' => [ 'name' => 'UpdateCloudFrontOriginAccessIdentity2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/origin-access-identity/cloudfront/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCloudFrontOriginAccessIdentityRequest', ], 'output' => [ 'shape' => 'UpdateCloudFrontOriginAccessIdentityResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'NoSuchCloudFrontOriginAccessIdentity', ], ], ], 'UpdateConnectionGroup' => [ 'name' => 'UpdateConnectionGroup2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/connection-group/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateConnectionGroupRequest', 'locationName' => 'UpdateConnectionGroupRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'UpdateConnectionGroupResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'ResourceInUse', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'EntityAlreadyExists', ], [ 'shape' => 'EntityLimitExceeded', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'UpdateContinuousDeploymentPolicy' => [ 'name' => 'UpdateContinuousDeploymentPolicy2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/continuous-deployment-policy/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateContinuousDeploymentPolicyRequest', ], 'output' => [ 'shape' => 'UpdateContinuousDeploymentPolicyResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'StagingDistributionInUse', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'UpdateDistribution' => [ 'name' => 'UpdateDistribution2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/distribution/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDistributionRequest', ], 'output' => [ 'shape' => 'UpdateDistributionResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginAccessControl', ], [ 'shape' => 'InvalidDefaultRootObject', ], [ 'shape' => 'InvalidDomainNameForOriginAccessControl', ], [ 'shape' => 'InvalidQueryStringParameters', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'TooManyCookieNamesInWhiteList', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], [ 'shape' => 'InvalidErrorCode', ], [ 'shape' => 'IllegalOriginAccessConfiguration', ], [ 'shape' => 'TooManyFunctionAssociations', ], [ 'shape' => 'TooManyOriginCustomHeaders', ], [ 'shape' => 'InvalidForwardCookies', ], [ 'shape' => 'InvalidMinimumProtocolVersion', ], [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'TooManyKeyGroupsAssociatedToDistribution', ], [ 'shape' => 'TooManyDistributionsAssociatedToCachePolicy', ], [ 'shape' => 'InvalidRequiredProtocol', ], [ 'shape' => 'TooManyDistributionsWithFunctionAssociations', ], [ 'shape' => 'TooManyOriginGroupsPerDistribution', ], [ 'shape' => 'InvalidTTLOrder', ], [ 'shape' => 'IllegalFieldLevelEncryptionConfigAssociationWithCacheBehavior', ], [ 'shape' => 'InvalidOriginKeepaliveTimeout', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidOriginReadTimeout', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'StagingDistributionInUse', ], [ 'shape' => 'InvalidHeadersForS3Origin', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'InvalidWebACLId', ], [ 'shape' => 'TooManyDistributionsWithSingleFunctionARN', ], [ 'shape' => 'InvalidRelativePath', ], [ 'shape' => 'TooManyLambdaFunctionAssociations', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToFieldLevelEncryptionConfig', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidLocationCode', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'TooManyDistributionCNAMEs', ], [ 'shape' => 'NoSuchContinuousDeploymentPolicy', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginRequestPolicy', ], [ 'shape' => 'TooManyQueryStringParameters', ], [ 'shape' => 'RealtimeLogConfigOwnerMismatch', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'ContinuousDeploymentPolicyInUse', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyHeadersInForwardedValues', ], [ 'shape' => 'InvalidLambdaFunctionAssociation', ], [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'TooManyCertificates', ], [ 'shape' => 'TrustedKeyGroupDoesNotExist', ], [ 'shape' => 'TooManyDistributionsAssociatedToResponseHeadersPolicy', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], [ 'shape' => 'InvalidResponseCode', ], [ 'shape' => 'InvalidGeoRestrictionParameter', ], [ 'shape' => 'TooManyOrigins', ], [ 'shape' => 'InvalidViewerCertificate', ], [ 'shape' => 'InvalidFunctionAssociation', ], [ 'shape' => 'TooManyDistributionsWithLambdaAssociations', ], [ 'shape' => 'TooManyDistributionsAssociatedToKeyGroup', ], [ 'shape' => 'NoSuchOrigin', ], [ 'shape' => 'TooManyCacheBehaviors', ], ], ], 'UpdateDistributionTenant' => [ 'name' => 'UpdateDistributionTenant2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/distribution-tenant/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDistributionTenantRequest', 'locationName' => 'UpdateDistributionTenantRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'UpdateDistributionTenantResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'EntityAlreadyExists', ], [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'InvalidAssociation', ], [ 'shape' => 'EntityLimitExceeded', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'UpdateDistributionWithStagingConfig' => [ 'name' => 'UpdateDistributionWithStagingConfig2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/distribution/{Id}/promote-staging-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDistributionWithStagingConfigRequest', ], 'output' => [ 'shape' => 'UpdateDistributionWithStagingConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginAccessControl', ], [ 'shape' => 'InvalidDefaultRootObject', ], [ 'shape' => 'InvalidQueryStringParameters', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'TooManyCookieNamesInWhiteList', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], [ 'shape' => 'InvalidErrorCode', ], [ 'shape' => 'TooManyFunctionAssociations', ], [ 'shape' => 'TooManyOriginCustomHeaders', ], [ 'shape' => 'InvalidForwardCookies', ], [ 'shape' => 'InvalidMinimumProtocolVersion', ], [ 'shape' => 'NoSuchCachePolicy', ], [ 'shape' => 'TooManyKeyGroupsAssociatedToDistribution', ], [ 'shape' => 'TooManyDistributionsAssociatedToCachePolicy', ], [ 'shape' => 'InvalidRequiredProtocol', ], [ 'shape' => 'TooManyDistributionsWithFunctionAssociations', ], [ 'shape' => 'TooManyOriginGroupsPerDistribution', ], [ 'shape' => 'InvalidTTLOrder', ], [ 'shape' => 'IllegalFieldLevelEncryptionConfigAssociationWithCacheBehavior', ], [ 'shape' => 'InvalidOriginKeepaliveTimeout', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidOriginReadTimeout', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'InvalidHeadersForS3Origin', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'InvalidWebACLId', ], [ 'shape' => 'TooManyDistributionsWithSingleFunctionARN', ], [ 'shape' => 'InvalidRelativePath', ], [ 'shape' => 'TooManyLambdaFunctionAssociations', ], [ 'shape' => 'NoSuchDistribution', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'TooManyDistributionsAssociatedToFieldLevelEncryptionConfig', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidLocationCode', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'TooManyDistributionCNAMEs', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'TooManyDistributionsAssociatedToOriginRequestPolicy', ], [ 'shape' => 'TooManyQueryStringParameters', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'RealtimeLogConfigOwnerMismatch', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyHeadersInForwardedValues', ], [ 'shape' => 'InvalidLambdaFunctionAssociation', ], [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'TooManyCertificates', ], [ 'shape' => 'TooManyDistributionsAssociatedToResponseHeadersPolicy', ], [ 'shape' => 'TrustedKeyGroupDoesNotExist', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'InvalidResponseCode', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], [ 'shape' => 'InvalidGeoRestrictionParameter', ], [ 'shape' => 'InvalidViewerCertificate', ], [ 'shape' => 'TooManyOrigins', ], [ 'shape' => 'InvalidFunctionAssociation', ], [ 'shape' => 'TooManyDistributionsWithLambdaAssociations', ], [ 'shape' => 'TooManyDistributionsAssociatedToKeyGroup', ], [ 'shape' => 'NoSuchOrigin', ], [ 'shape' => 'TooManyCacheBehaviors', ], ], ], 'UpdateDomainAssociation' => [ 'name' => 'UpdateDomainAssociation2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/domain-association', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateDomainAssociationRequest', 'locationName' => 'UpdateDomainAssociationRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'UpdateDomainAssociationResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'UpdateFieldLevelEncryptionConfig' => [ 'name' => 'UpdateFieldLevelEncryptionConfig2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/field-level-encryption/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFieldLevelEncryptionConfigRequest', ], 'output' => [ 'shape' => 'UpdateFieldLevelEncryptionConfigResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'QueryArgProfileEmpty', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchFieldLevelEncryptionConfig', ], [ 'shape' => 'TooManyFieldLevelEncryptionContentTypeProfiles', ], [ 'shape' => 'TooManyFieldLevelEncryptionQueryArgProfiles', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'NoSuchFieldLevelEncryptionProfile', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'UpdateFieldLevelEncryptionProfile' => [ 'name' => 'UpdateFieldLevelEncryptionProfile2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/field-level-encryption-profile/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFieldLevelEncryptionProfileRequest', ], 'output' => [ 'shape' => 'UpdateFieldLevelEncryptionProfileResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'TooManyFieldLevelEncryptionFieldPatterns', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'FieldLevelEncryptionProfileAlreadyExists', ], [ 'shape' => 'NoSuchPublicKey', ], [ 'shape' => 'FieldLevelEncryptionProfileSizeExceeded', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'NoSuchFieldLevelEncryptionProfile', ], [ 'shape' => 'TooManyFieldLevelEncryptionEncryptionEntities', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'UpdateFunction' => [ 'name' => 'UpdateFunction2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/function/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFunctionRequest', 'locationName' => 'UpdateFunctionRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'UpdateFunctionResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'FunctionSizeLimitExceeded', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchFunctionExists', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'UpdateKeyGroup' => [ 'name' => 'UpdateKeyGroup2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/key-group/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateKeyGroupRequest', ], 'output' => [ 'shape' => 'UpdateKeyGroupResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'TooManyPublicKeysInKeyGroup', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchResource', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'KeyGroupAlreadyExists', ], ], ], 'UpdateKeyValueStore' => [ 'name' => 'UpdateKeyValueStore2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/key-value-store/{Name}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateKeyValueStoreRequest', 'locationName' => 'UpdateKeyValueStoreRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'UpdateKeyValueStoreResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], 'idempotent' => true, ], 'UpdateOriginAccessControl' => [ 'name' => 'UpdateOriginAccessControl2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/origin-access-control/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateOriginAccessControlRequest', ], 'output' => [ 'shape' => 'UpdateOriginAccessControlResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'OriginAccessControlAlreadyExists', ], [ 'shape' => 'NoSuchOriginAccessControl', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'UpdateOriginRequestPolicy' => [ 'name' => 'UpdateOriginRequestPolicy2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/origin-request-policy/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateOriginRequestPolicyRequest', ], 'output' => [ 'shape' => 'UpdateOriginRequestPolicyResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyHeadersInOriginRequestPolicy', ], [ 'shape' => 'NoSuchOriginRequestPolicy', ], [ 'shape' => 'TooManyCookiesInOriginRequestPolicy', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'OriginRequestPolicyAlreadyExists', ], [ 'shape' => 'TooManyQueryStringsInOriginRequestPolicy', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'UpdatePublicKey' => [ 'name' => 'UpdatePublicKey2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/public-key/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePublicKeyRequest', ], 'output' => [ 'shape' => 'UpdatePublicKeyResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'NoSuchPublicKey', ], [ 'shape' => 'CannotChangeImmutablePublicKeyFields', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'UpdateRealtimeLogConfig' => [ 'name' => 'UpdateRealtimeLogConfig2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/realtime-log-config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateRealtimeLogConfigRequest', 'locationName' => 'UpdateRealtimeLogConfigRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'UpdateRealtimeLogConfigResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'NoSuchRealtimeLogConfig', ], ], ], 'UpdateResponseHeadersPolicy' => [ 'name' => 'UpdateResponseHeadersPolicy2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/response-headers-policy/{Id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateResponseHeadersPolicyRequest', ], 'output' => [ 'shape' => 'UpdateResponseHeadersPolicyResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'TooManyCustomHeadersInResponseHeadersPolicy', ], [ 'shape' => 'ResponseHeadersPolicyAlreadyExists', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'NoSuchResponseHeadersPolicy', ], [ 'shape' => 'TooLongCSPInResponseHeadersPolicy', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'TooManyRemoveHeadersInResponseHeadersPolicy', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'UpdateStreamingDistribution' => [ 'name' => 'UpdateStreamingDistribution2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/streaming-distribution/{Id}/config', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateStreamingDistributionRequest', ], 'output' => [ 'shape' => 'UpdateStreamingDistributionResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'InvalidOriginAccessIdentity', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'TooManyTrustedSigners', ], [ 'shape' => 'InvalidOriginAccessControl', ], [ 'shape' => 'InvalidIfMatchVersion', ], [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'MissingBody', ], [ 'shape' => 'TooManyStreamingDistributionCNAMEs', ], [ 'shape' => 'TrustedSignerDoesNotExist', ], [ 'shape' => 'CNAMEAlreadyExists', ], [ 'shape' => 'NoSuchStreamingDistribution', ], ], ], 'UpdateVpcOrigin' => [ 'name' => 'UpdateVpcOrigin2020_05_31', 'http' => [ 'method' => 'PUT', 'requestUri' => '/2020-05-31/vpc-origin/{Id}', 'responseCode' => 202, ], 'input' => [ 'shape' => 'UpdateVpcOriginRequest', ], 'output' => [ 'shape' => 'UpdateVpcOriginResult', ], 'errors' => [ [ 'shape' => 'PreconditionFailed', ], [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'UnsupportedOperation', ], [ 'shape' => 'EntityAlreadyExists', ], [ 'shape' => 'InconsistentQuantities', ], [ 'shape' => 'CannotUpdateEntityWhileInUse', ], [ 'shape' => 'EntityLimitExceeded', ], [ 'shape' => 'IllegalUpdate', ], [ 'shape' => 'InvalidArgument', ], [ 'shape' => 'InvalidIfMatchVersion', ], ], ], 'VerifyDnsConfiguration' => [ 'name' => 'VerifyDnsConfiguration2020_05_31', 'http' => [ 'method' => 'POST', 'requestUri' => '/2020-05-31/verify-dns-configuration', 'responseCode' => 200, ], 'input' => [ 'shape' => 'VerifyDnsConfigurationRequest', 'locationName' => 'VerifyDnsConfigurationRequest', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'output' => [ 'shape' => 'VerifyDnsConfigurationResult', ], 'errors' => [ [ 'shape' => 'AccessDenied', ], [ 'shape' => 'EntityNotFound', ], [ 'shape' => 'InvalidArgument', ], ], ], ], 'shapes' => [ 'AccessControlAllowHeadersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Header', ], ], 'AccessControlAllowMethodsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseHeadersPolicyAccessControlAllowMethodsValues', 'locationName' => 'Method', ], ], 'AccessControlAllowOriginsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Origin', ], ], 'AccessControlExposeHeadersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Header', ], ], 'AccessDenied' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'ActiveTrustedKeyGroups' => [ 'type' => 'structure', 'required' => [ 'Enabled', 'Quantity', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'KGKeyPairIdsList', ], ], ], 'ActiveTrustedSigners' => [ 'type' => 'structure', 'required' => [ 'Enabled', 'Quantity', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'SignerList', ], ], ], 'AliasICPRecordal' => [ 'type' => 'structure', 'members' => [ 'CNAME' => [ 'shape' => 'string', ], 'ICPRecordalStatus' => [ 'shape' => 'ICPRecordalStatus', ], ], ], 'AliasICPRecordals' => [ 'type' => 'list', 'member' => [ 'shape' => 'AliasICPRecordal', 'locationName' => 'AliasICPRecordal', ], ], 'AliasList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'CNAME', ], ], 'Aliases' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'AliasList', ], ], ], 'AllowedMethods' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'MethodsList', ], 'CachedMethods' => [ 'shape' => 'CachedMethods', ], ], ], 'AnycastIpList' => [ 'type' => 'structure', 'required' => [ 'Id', 'Name', 'Status', 'Arn', 'AnycastIps', 'IpCount', 'LastModifiedTime', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'AnycastIpListName', ], 'Status' => [ 'shape' => 'string', ], 'Arn' => [ 'shape' => 'string', ], 'AnycastIps' => [ 'shape' => 'AnycastIps', ], 'IpCount' => [ 'shape' => 'integer', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], ], ], 'AnycastIpListCollection' => [ 'type' => 'structure', 'required' => [ 'Marker', 'MaxItems', 'IsTruncated', 'Quantity', ], 'members' => [ 'Items' => [ 'shape' => 'AnycastIpListSummaries', ], 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], ], ], 'AnycastIpListName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_]{1,64}', ], 'AnycastIpListSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AnycastIpListSummary', 'locationName' => 'AnycastIpListSummary', ], ], 'AnycastIpListSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'Name', 'Status', 'Arn', 'IpCount', 'LastModifiedTime', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'AnycastIpListName', ], 'Status' => [ 'shape' => 'string', ], 'Arn' => [ 'shape' => 'string', ], 'IpCount' => [ 'shape' => 'integer', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], ], ], 'AnycastIps' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'AnycastIp', ], ], 'AssociateAliasRequest' => [ 'type' => 'structure', 'required' => [ 'TargetDistributionId', 'Alias', ], 'members' => [ 'TargetDistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'TargetDistributionId', ], 'Alias' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Alias', ], ], ], 'AssociateDistributionTenantWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'WebACLArn', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'WebACLArn' => [ 'shape' => 'string', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'AssociateDistributionTenantWebACLResult' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'string', ], 'WebACLArn' => [ 'shape' => 'string', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], ], 'AssociateDistributionWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'WebACLArn', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'WebACLArn' => [ 'shape' => 'string', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'AssociateDistributionWebACLResult' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'string', ], 'WebACLArn' => [ 'shape' => 'string', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], ], 'AwsAccountNumberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'AwsAccountNumber', ], ], 'BatchTooLarge' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 413, 'senderFault' => true, ], 'exception' => true, ], 'CNAMEAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CacheBehavior' => [ 'type' => 'structure', 'required' => [ 'PathPattern', 'TargetOriginId', 'ViewerProtocolPolicy', ], 'members' => [ 'PathPattern' => [ 'shape' => 'string', ], 'TargetOriginId' => [ 'shape' => 'string', ], 'TrustedSigners' => [ 'shape' => 'TrustedSigners', ], 'TrustedKeyGroups' => [ 'shape' => 'TrustedKeyGroups', ], 'ViewerProtocolPolicy' => [ 'shape' => 'ViewerProtocolPolicy', ], 'AllowedMethods' => [ 'shape' => 'AllowedMethods', ], 'SmoothStreaming' => [ 'shape' => 'boolean', ], 'Compress' => [ 'shape' => 'boolean', ], 'LambdaFunctionAssociations' => [ 'shape' => 'LambdaFunctionAssociations', ], 'FunctionAssociations' => [ 'shape' => 'FunctionAssociations', ], 'FieldLevelEncryptionId' => [ 'shape' => 'string', ], 'RealtimeLogConfigArn' => [ 'shape' => 'string', ], 'CachePolicyId' => [ 'shape' => 'string', ], 'OriginRequestPolicyId' => [ 'shape' => 'string', ], 'ResponseHeadersPolicyId' => [ 'shape' => 'string', ], 'GrpcConfig' => [ 'shape' => 'GrpcConfig', ], 'ForwardedValues' => [ 'shape' => 'ForwardedValues', 'deprecated' => true, ], 'MinTTL' => [ 'shape' => 'long', 'deprecated' => true, ], 'DefaultTTL' => [ 'shape' => 'long', 'deprecated' => true, ], 'MaxTTL' => [ 'shape' => 'long', 'deprecated' => true, ], ], ], 'CacheBehaviorList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CacheBehavior', 'locationName' => 'CacheBehavior', ], ], 'CacheBehaviors' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'CacheBehaviorList', ], ], ], 'CachePolicy' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'CachePolicyConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'CachePolicyConfig' => [ 'shape' => 'CachePolicyConfig', ], ], ], 'CachePolicyAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CachePolicyConfig' => [ 'type' => 'structure', 'required' => [ 'Name', 'MinTTL', ], 'members' => [ 'Comment' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'DefaultTTL' => [ 'shape' => 'long', ], 'MaxTTL' => [ 'shape' => 'long', ], 'MinTTL' => [ 'shape' => 'long', ], 'ParametersInCacheKeyAndForwardedToOrigin' => [ 'shape' => 'ParametersInCacheKeyAndForwardedToOrigin', ], ], ], 'CachePolicyCookieBehavior' => [ 'type' => 'string', 'enum' => [ 'none', 'whitelist', 'allExcept', 'all', ], ], 'CachePolicyCookiesConfig' => [ 'type' => 'structure', 'required' => [ 'CookieBehavior', ], 'members' => [ 'CookieBehavior' => [ 'shape' => 'CachePolicyCookieBehavior', ], 'Cookies' => [ 'shape' => 'CookieNames', ], ], ], 'CachePolicyHeaderBehavior' => [ 'type' => 'string', 'enum' => [ 'none', 'whitelist', ], ], 'CachePolicyHeadersConfig' => [ 'type' => 'structure', 'required' => [ 'HeaderBehavior', ], 'members' => [ 'HeaderBehavior' => [ 'shape' => 'CachePolicyHeaderBehavior', ], 'Headers' => [ 'shape' => 'Headers', ], ], ], 'CachePolicyInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CachePolicyList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'CachePolicySummaryList', ], ], ], 'CachePolicyQueryStringBehavior' => [ 'type' => 'string', 'enum' => [ 'none', 'whitelist', 'allExcept', 'all', ], ], 'CachePolicyQueryStringsConfig' => [ 'type' => 'structure', 'required' => [ 'QueryStringBehavior', ], 'members' => [ 'QueryStringBehavior' => [ 'shape' => 'CachePolicyQueryStringBehavior', ], 'QueryStrings' => [ 'shape' => 'QueryStringNames', ], ], ], 'CachePolicySummary' => [ 'type' => 'structure', 'required' => [ 'Type', 'CachePolicy', ], 'members' => [ 'Type' => [ 'shape' => 'CachePolicyType', ], 'CachePolicy' => [ 'shape' => 'CachePolicy', ], ], ], 'CachePolicySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CachePolicySummary', 'locationName' => 'CachePolicySummary', ], ], 'CachePolicyType' => [ 'type' => 'string', 'enum' => [ 'managed', 'custom', ], ], 'CachedMethods' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'MethodsList', ], ], ], 'CannotChangeImmutablePublicKeyFields' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CannotDeleteEntityWhileInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CannotUpdateEntityWhileInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'Certificate' => [ 'type' => 'structure', 'required' => [ 'Arn', ], 'members' => [ 'Arn' => [ 'shape' => 'string', ], ], ], 'CertificateSource' => [ 'type' => 'string', 'enum' => [ 'cloudfront', 'iam', 'acm', ], ], 'CertificateTransparencyLoggingPreference' => [ 'type' => 'string', 'enum' => [ 'enabled', 'disabled', ], ], 'CloudFrontOriginAccessIdentity' => [ 'type' => 'structure', 'required' => [ 'Id', 'S3CanonicalUserId', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'S3CanonicalUserId' => [ 'shape' => 'string', ], 'CloudFrontOriginAccessIdentityConfig' => [ 'shape' => 'CloudFrontOriginAccessIdentityConfig', ], ], ], 'CloudFrontOriginAccessIdentityAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CloudFrontOriginAccessIdentityConfig' => [ 'type' => 'structure', 'required' => [ 'CallerReference', 'Comment', ], 'members' => [ 'CallerReference' => [ 'shape' => 'string', ], 'Comment' => [ 'shape' => 'string', ], ], ], 'CloudFrontOriginAccessIdentityInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'CloudFrontOriginAccessIdentityList' => [ 'type' => 'structure', 'required' => [ 'Marker', 'MaxItems', 'IsTruncated', 'Quantity', ], 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'CloudFrontOriginAccessIdentitySummaryList', ], ], ], 'CloudFrontOriginAccessIdentitySummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'S3CanonicalUserId', 'Comment', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'S3CanonicalUserId' => [ 'shape' => 'string', ], 'Comment' => [ 'shape' => 'string', ], ], ], 'CloudFrontOriginAccessIdentitySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CloudFrontOriginAccessIdentitySummary', 'locationName' => 'CloudFrontOriginAccessIdentitySummary', ], ], 'CommentType' => [ 'type' => 'string', 'sensitive' => true, ], 'ConflictingAlias' => [ 'type' => 'structure', 'members' => [ 'Alias' => [ 'shape' => 'string', ], 'DistributionId' => [ 'shape' => 'string', ], 'AccountId' => [ 'shape' => 'string', ], ], ], 'ConflictingAliases' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConflictingAlias', 'locationName' => 'ConflictingAlias', ], ], 'ConflictingAliasesList' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'ConflictingAliases', ], ], ], 'ConnectionGroup' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'Arn' => [ 'shape' => 'string', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'Tags' => [ 'shape' => 'Tags', ], 'Ipv6Enabled' => [ 'shape' => 'boolean', ], 'RoutingEndpoint' => [ 'shape' => 'string', ], 'AnycastIpListId' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'Enabled' => [ 'shape' => 'boolean', ], 'IsDefault' => [ 'shape' => 'boolean', ], ], ], 'ConnectionGroupAssociationFilter' => [ 'type' => 'structure', 'members' => [ 'AnycastIpListId' => [ 'shape' => 'string', ], ], ], 'ConnectionGroupSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'Name', 'Arn', 'RoutingEndpoint', 'CreatedTime', 'LastModifiedTime', 'ETag', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'Arn' => [ 'shape' => 'string', ], 'RoutingEndpoint' => [ 'shape' => 'string', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'ETag' => [ 'shape' => 'string', ], 'AnycastIpListId' => [ 'shape' => 'string', ], 'Enabled' => [ 'shape' => 'boolean', ], 'Status' => [ 'shape' => 'string', ], 'IsDefault' => [ 'shape' => 'boolean', ], ], ], 'ConnectionGroupSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ConnectionGroupSummary', 'locationName' => 'ConnectionGroupSummary', ], ], 'ConnectionMode' => [ 'type' => 'string', 'enum' => [ 'direct', 'tenant-only', ], ], 'ContentTypeProfile' => [ 'type' => 'structure', 'required' => [ 'Format', 'ContentType', ], 'members' => [ 'Format' => [ 'shape' => 'Format', ], 'ProfileId' => [ 'shape' => 'string', ], 'ContentType' => [ 'shape' => 'string', ], ], ], 'ContentTypeProfileConfig' => [ 'type' => 'structure', 'required' => [ 'ForwardWhenContentTypeIsUnknown', ], 'members' => [ 'ForwardWhenContentTypeIsUnknown' => [ 'shape' => 'boolean', ], 'ContentTypeProfiles' => [ 'shape' => 'ContentTypeProfiles', ], ], ], 'ContentTypeProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContentTypeProfile', 'locationName' => 'ContentTypeProfile', ], ], 'ContentTypeProfiles' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'ContentTypeProfileList', ], ], ], 'ContinuousDeploymentPolicy' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'ContinuousDeploymentPolicyConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'ContinuousDeploymentPolicyConfig' => [ 'shape' => 'ContinuousDeploymentPolicyConfig', ], ], ], 'ContinuousDeploymentPolicyAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ContinuousDeploymentPolicyConfig' => [ 'type' => 'structure', 'required' => [ 'StagingDistributionDnsNames', 'Enabled', ], 'members' => [ 'StagingDistributionDnsNames' => [ 'shape' => 'StagingDistributionDnsNames', ], 'Enabled' => [ 'shape' => 'boolean', ], 'TrafficConfig' => [ 'shape' => 'TrafficConfig', ], ], ], 'ContinuousDeploymentPolicyInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ContinuousDeploymentPolicyList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'ContinuousDeploymentPolicySummaryList', ], ], ], 'ContinuousDeploymentPolicySummary' => [ 'type' => 'structure', 'required' => [ 'ContinuousDeploymentPolicy', ], 'members' => [ 'ContinuousDeploymentPolicy' => [ 'shape' => 'ContinuousDeploymentPolicy', ], ], ], 'ContinuousDeploymentPolicySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContinuousDeploymentPolicySummary', 'locationName' => 'ContinuousDeploymentPolicySummary', ], ], 'ContinuousDeploymentPolicyType' => [ 'type' => 'string', 'enum' => [ 'SingleWeight', 'SingleHeader', ], ], 'ContinuousDeploymentSingleHeaderConfig' => [ 'type' => 'structure', 'required' => [ 'Header', 'Value', ], 'members' => [ 'Header' => [ 'shape' => 'string', ], 'Value' => [ 'shape' => 'string', ], ], ], 'ContinuousDeploymentSingleWeightConfig' => [ 'type' => 'structure', 'required' => [ 'Weight', ], 'members' => [ 'Weight' => [ 'shape' => 'float', ], 'SessionStickinessConfig' => [ 'shape' => 'SessionStickinessConfig', ], ], ], 'CookieNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Name', ], ], 'CookieNames' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'CookieNameList', ], ], ], 'CookiePreference' => [ 'type' => 'structure', 'required' => [ 'Forward', ], 'members' => [ 'Forward' => [ 'shape' => 'ItemSelection', ], 'WhitelistedNames' => [ 'shape' => 'CookieNames', ], ], ], 'CopyDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'PrimaryDistributionId', 'CallerReference', ], 'members' => [ 'PrimaryDistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'PrimaryDistributionId', ], 'Staging' => [ 'shape' => 'boolean', 'location' => 'header', 'locationName' => 'Staging', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], 'CallerReference' => [ 'shape' => 'string', ], 'Enabled' => [ 'shape' => 'boolean', ], ], ], 'CopyDistributionResult' => [ 'type' => 'structure', 'members' => [ 'Distribution' => [ 'shape' => 'Distribution', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'Distribution', ], 'CreateAnycastIpListRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'IpCount', ], 'members' => [ 'Name' => [ 'shape' => 'AnycastIpListName', ], 'IpCount' => [ 'shape' => 'integer', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateAnycastIpListResult' => [ 'type' => 'structure', 'members' => [ 'AnycastIpList' => [ 'shape' => 'AnycastIpList', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'AnycastIpList', ], 'CreateCachePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'CachePolicyConfig', ], 'members' => [ 'CachePolicyConfig' => [ 'shape' => 'CachePolicyConfig', 'locationName' => 'CachePolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'CachePolicyConfig', ], 'CreateCachePolicyResult' => [ 'type' => 'structure', 'members' => [ 'CachePolicy' => [ 'shape' => 'CachePolicy', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CachePolicy', ], 'CreateCloudFrontOriginAccessIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'CloudFrontOriginAccessIdentityConfig', ], 'members' => [ 'CloudFrontOriginAccessIdentityConfig' => [ 'shape' => 'CloudFrontOriginAccessIdentityConfig', 'locationName' => 'CloudFrontOriginAccessIdentityConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'CloudFrontOriginAccessIdentityConfig', ], 'CreateCloudFrontOriginAccessIdentityResult' => [ 'type' => 'structure', 'members' => [ 'CloudFrontOriginAccessIdentity' => [ 'shape' => 'CloudFrontOriginAccessIdentity', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CloudFrontOriginAccessIdentity', ], 'CreateConnectionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'string', ], 'Ipv6Enabled' => [ 'shape' => 'boolean', ], 'Tags' => [ 'shape' => 'Tags', ], 'AnycastIpListId' => [ 'shape' => 'string', ], 'Enabled' => [ 'shape' => 'boolean', ], ], ], 'CreateConnectionGroupResult' => [ 'type' => 'structure', 'members' => [ 'ConnectionGroup' => [ 'shape' => 'ConnectionGroup', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ConnectionGroup', ], 'CreateContinuousDeploymentPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ContinuousDeploymentPolicyConfig', ], 'members' => [ 'ContinuousDeploymentPolicyConfig' => [ 'shape' => 'ContinuousDeploymentPolicyConfig', 'locationName' => 'ContinuousDeploymentPolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'ContinuousDeploymentPolicyConfig', ], 'CreateContinuousDeploymentPolicyResult' => [ 'type' => 'structure', 'members' => [ 'ContinuousDeploymentPolicy' => [ 'shape' => 'ContinuousDeploymentPolicy', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ContinuousDeploymentPolicy', ], 'CreateDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionConfig', ], 'members' => [ 'DistributionConfig' => [ 'shape' => 'DistributionConfig', 'locationName' => 'DistributionConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'DistributionConfig', ], 'CreateDistributionResult' => [ 'type' => 'structure', 'members' => [ 'Distribution' => [ 'shape' => 'Distribution', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'Distribution', ], 'CreateDistributionTenantRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionId', 'Name', 'Domains', ], 'members' => [ 'DistributionId' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'CreateDistributionTenantRequestNameString', ], 'Domains' => [ 'shape' => 'DomainList', ], 'Tags' => [ 'shape' => 'Tags', ], 'Customizations' => [ 'shape' => 'Customizations', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'ConnectionGroupId' => [ 'shape' => 'string', ], 'ManagedCertificateRequest' => [ 'shape' => 'ManagedCertificateRequest', ], 'Enabled' => [ 'shape' => 'boolean', ], ], ], 'CreateDistributionTenantRequestNameString' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9][a-zA-Z0-9-.]{1,126}[a-zA-Z0-9]', ], 'CreateDistributionTenantResult' => [ 'type' => 'structure', 'members' => [ 'DistributionTenant' => [ 'shape' => 'DistributionTenant', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'DistributionTenant', ], 'CreateDistributionWithTagsRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionConfigWithTags', ], 'members' => [ 'DistributionConfigWithTags' => [ 'shape' => 'DistributionConfigWithTags', 'locationName' => 'DistributionConfigWithTags', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'DistributionConfigWithTags', ], 'CreateDistributionWithTagsResult' => [ 'type' => 'structure', 'members' => [ 'Distribution' => [ 'shape' => 'Distribution', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'Distribution', ], 'CreateFieldLevelEncryptionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FieldLevelEncryptionConfig', ], 'members' => [ 'FieldLevelEncryptionConfig' => [ 'shape' => 'FieldLevelEncryptionConfig', 'locationName' => 'FieldLevelEncryptionConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'FieldLevelEncryptionConfig', ], 'CreateFieldLevelEncryptionConfigResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryption' => [ 'shape' => 'FieldLevelEncryption', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryption', ], 'CreateFieldLevelEncryptionProfileRequest' => [ 'type' => 'structure', 'required' => [ 'FieldLevelEncryptionProfileConfig', ], 'members' => [ 'FieldLevelEncryptionProfileConfig' => [ 'shape' => 'FieldLevelEncryptionProfileConfig', 'locationName' => 'FieldLevelEncryptionProfileConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'FieldLevelEncryptionProfileConfig', ], 'CreateFieldLevelEncryptionProfileResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryptionProfile' => [ 'shape' => 'FieldLevelEncryptionProfile', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryptionProfile', ], 'CreateFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'FunctionConfig', 'FunctionCode', ], 'members' => [ 'Name' => [ 'shape' => 'FunctionName', ], 'FunctionConfig' => [ 'shape' => 'FunctionConfig', ], 'FunctionCode' => [ 'shape' => 'FunctionBlob', ], ], ], 'CreateFunctionResult' => [ 'type' => 'structure', 'members' => [ 'FunctionSummary' => [ 'shape' => 'FunctionSummary', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FunctionSummary', ], 'CreateInvalidationForDistributionTenantRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'InvalidationBatch', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'InvalidationBatch' => [ 'shape' => 'InvalidationBatch', 'locationName' => 'InvalidationBatch', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'InvalidationBatch', ], 'CreateInvalidationForDistributionTenantResult' => [ 'type' => 'structure', 'members' => [ 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'Invalidation' => [ 'shape' => 'Invalidation', ], ], 'payload' => 'Invalidation', ], 'CreateInvalidationRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionId', 'InvalidationBatch', ], 'members' => [ 'DistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DistributionId', ], 'InvalidationBatch' => [ 'shape' => 'InvalidationBatch', 'locationName' => 'InvalidationBatch', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'InvalidationBatch', ], 'CreateInvalidationResult' => [ 'type' => 'structure', 'members' => [ 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'Invalidation' => [ 'shape' => 'Invalidation', ], ], 'payload' => 'Invalidation', ], 'CreateKeyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'KeyGroupConfig', ], 'members' => [ 'KeyGroupConfig' => [ 'shape' => 'KeyGroupConfig', 'locationName' => 'KeyGroupConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'KeyGroupConfig', ], 'CreateKeyGroupResult' => [ 'type' => 'structure', 'members' => [ 'KeyGroup' => [ 'shape' => 'KeyGroup', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'KeyGroup', ], 'CreateKeyValueStoreRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'KeyValueStoreName', ], 'Comment' => [ 'shape' => 'KeyValueStoreComment', ], 'ImportSource' => [ 'shape' => 'ImportSource', ], ], ], 'CreateKeyValueStoreResult' => [ 'type' => 'structure', 'members' => [ 'KeyValueStore' => [ 'shape' => 'KeyValueStore', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], ], 'payload' => 'KeyValueStore', ], 'CreateMonitoringSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionId', 'MonitoringSubscription', ], 'members' => [ 'DistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DistributionId', ], 'MonitoringSubscription' => [ 'shape' => 'MonitoringSubscription', 'locationName' => 'MonitoringSubscription', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'MonitoringSubscription', ], 'CreateMonitoringSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'MonitoringSubscription' => [ 'shape' => 'MonitoringSubscription', ], ], 'payload' => 'MonitoringSubscription', ], 'CreateOriginAccessControlRequest' => [ 'type' => 'structure', 'required' => [ 'OriginAccessControlConfig', ], 'members' => [ 'OriginAccessControlConfig' => [ 'shape' => 'OriginAccessControlConfig', 'locationName' => 'OriginAccessControlConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'OriginAccessControlConfig', ], 'CreateOriginAccessControlResult' => [ 'type' => 'structure', 'members' => [ 'OriginAccessControl' => [ 'shape' => 'OriginAccessControl', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginAccessControl', ], 'CreateOriginRequestPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'OriginRequestPolicyConfig', ], 'members' => [ 'OriginRequestPolicyConfig' => [ 'shape' => 'OriginRequestPolicyConfig', 'locationName' => 'OriginRequestPolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'OriginRequestPolicyConfig', ], 'CreateOriginRequestPolicyResult' => [ 'type' => 'structure', 'members' => [ 'OriginRequestPolicy' => [ 'shape' => 'OriginRequestPolicy', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginRequestPolicy', ], 'CreatePublicKeyRequest' => [ 'type' => 'structure', 'required' => [ 'PublicKeyConfig', ], 'members' => [ 'PublicKeyConfig' => [ 'shape' => 'PublicKeyConfig', 'locationName' => 'PublicKeyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'PublicKeyConfig', ], 'CreatePublicKeyResult' => [ 'type' => 'structure', 'members' => [ 'PublicKey' => [ 'shape' => 'PublicKey', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'PublicKey', ], 'CreateRealtimeLogConfigRequest' => [ 'type' => 'structure', 'required' => [ 'EndPoints', 'Fields', 'Name', 'SamplingRate', ], 'members' => [ 'EndPoints' => [ 'shape' => 'EndPointList', ], 'Fields' => [ 'shape' => 'FieldList', ], 'Name' => [ 'shape' => 'string', ], 'SamplingRate' => [ 'shape' => 'long', ], ], ], 'CreateRealtimeLogConfigResult' => [ 'type' => 'structure', 'members' => [ 'RealtimeLogConfig' => [ 'shape' => 'RealtimeLogConfig', ], ], ], 'CreateResponseHeadersPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResponseHeadersPolicyConfig', ], 'members' => [ 'ResponseHeadersPolicyConfig' => [ 'shape' => 'ResponseHeadersPolicyConfig', 'locationName' => 'ResponseHeadersPolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'ResponseHeadersPolicyConfig', ], 'CreateResponseHeadersPolicyResult' => [ 'type' => 'structure', 'members' => [ 'ResponseHeadersPolicy' => [ 'shape' => 'ResponseHeadersPolicy', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ResponseHeadersPolicy', ], 'CreateStreamingDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'StreamingDistributionConfig', ], 'members' => [ 'StreamingDistributionConfig' => [ 'shape' => 'StreamingDistributionConfig', 'locationName' => 'StreamingDistributionConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'StreamingDistributionConfig', ], 'CreateStreamingDistributionResult' => [ 'type' => 'structure', 'members' => [ 'StreamingDistribution' => [ 'shape' => 'StreamingDistribution', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'StreamingDistribution', ], 'CreateStreamingDistributionWithTagsRequest' => [ 'type' => 'structure', 'required' => [ 'StreamingDistributionConfigWithTags', ], 'members' => [ 'StreamingDistributionConfigWithTags' => [ 'shape' => 'StreamingDistributionConfigWithTags', 'locationName' => 'StreamingDistributionConfigWithTags', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'StreamingDistributionConfigWithTags', ], 'CreateStreamingDistributionWithTagsResult' => [ 'type' => 'structure', 'members' => [ 'StreamingDistribution' => [ 'shape' => 'StreamingDistribution', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'StreamingDistribution', ], 'CreateVpcOriginRequest' => [ 'type' => 'structure', 'required' => [ 'VpcOriginEndpointConfig', ], 'members' => [ 'VpcOriginEndpointConfig' => [ 'shape' => 'VpcOriginEndpointConfig', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'CreateVpcOriginResult' => [ 'type' => 'structure', 'members' => [ 'VpcOrigin' => [ 'shape' => 'VpcOrigin', ], 'Location' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Location', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'VpcOrigin', ], 'CustomErrorResponse' => [ 'type' => 'structure', 'required' => [ 'ErrorCode', ], 'members' => [ 'ErrorCode' => [ 'shape' => 'integer', ], 'ResponsePagePath' => [ 'shape' => 'string', ], 'ResponseCode' => [ 'shape' => 'string', ], 'ErrorCachingMinTTL' => [ 'shape' => 'long', ], ], ], 'CustomErrorResponseList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CustomErrorResponse', 'locationName' => 'CustomErrorResponse', ], ], 'CustomErrorResponses' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'CustomErrorResponseList', ], ], ], 'CustomHeaders' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'OriginCustomHeadersList', ], ], ], 'CustomOriginConfig' => [ 'type' => 'structure', 'required' => [ 'HTTPPort', 'HTTPSPort', 'OriginProtocolPolicy', ], 'members' => [ 'HTTPPort' => [ 'shape' => 'integer', ], 'HTTPSPort' => [ 'shape' => 'integer', ], 'OriginProtocolPolicy' => [ 'shape' => 'OriginProtocolPolicy', ], 'OriginSslProtocols' => [ 'shape' => 'OriginSslProtocols', ], 'OriginReadTimeout' => [ 'shape' => 'integer', ], 'OriginKeepaliveTimeout' => [ 'shape' => 'integer', ], ], ], 'CustomizationActionType' => [ 'type' => 'string', 'enum' => [ 'override', 'disable', ], ], 'Customizations' => [ 'type' => 'structure', 'members' => [ 'WebAcl' => [ 'shape' => 'WebAclCustomization', ], 'Certificate' => [ 'shape' => 'Certificate', ], 'GeoRestrictions' => [ 'shape' => 'GeoRestrictionCustomization', ], ], ], 'DefaultCacheBehavior' => [ 'type' => 'structure', 'required' => [ 'TargetOriginId', 'ViewerProtocolPolicy', ], 'members' => [ 'TargetOriginId' => [ 'shape' => 'string', ], 'TrustedSigners' => [ 'shape' => 'TrustedSigners', ], 'TrustedKeyGroups' => [ 'shape' => 'TrustedKeyGroups', ], 'ViewerProtocolPolicy' => [ 'shape' => 'ViewerProtocolPolicy', ], 'AllowedMethods' => [ 'shape' => 'AllowedMethods', ], 'SmoothStreaming' => [ 'shape' => 'boolean', ], 'Compress' => [ 'shape' => 'boolean', ], 'LambdaFunctionAssociations' => [ 'shape' => 'LambdaFunctionAssociations', ], 'FunctionAssociations' => [ 'shape' => 'FunctionAssociations', ], 'FieldLevelEncryptionId' => [ 'shape' => 'string', ], 'RealtimeLogConfigArn' => [ 'shape' => 'string', ], 'CachePolicyId' => [ 'shape' => 'string', ], 'OriginRequestPolicyId' => [ 'shape' => 'string', ], 'ResponseHeadersPolicyId' => [ 'shape' => 'string', ], 'GrpcConfig' => [ 'shape' => 'GrpcConfig', ], 'ForwardedValues' => [ 'shape' => 'ForwardedValues', 'deprecated' => true, ], 'MinTTL' => [ 'shape' => 'long', 'deprecated' => true, ], 'DefaultTTL' => [ 'shape' => 'long', 'deprecated' => true, ], 'MaxTTL' => [ 'shape' => 'long', 'deprecated' => true, ], ], ], 'DeleteAnycastIpListRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IfMatch', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteCachePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteCloudFrontOriginAccessIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteConnectionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IfMatch', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteContinuousDeploymentPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteDistributionTenantRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IfMatch', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteFieldLevelEncryptionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteFieldLevelEncryptionProfileRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'IfMatch', ], 'members' => [ 'Name' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Name', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteKeyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteKeyValueStoreRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'IfMatch', ], 'members' => [ 'Name' => [ 'shape' => 'KeyValueStoreName', 'location' => 'uri', 'locationName' => 'Name', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteMonitoringSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionId', ], 'members' => [ 'DistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DistributionId', ], ], ], 'DeleteMonitoringSubscriptionResult' => [ 'type' => 'structure', 'members' => [], ], 'DeleteOriginAccessControlRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteOriginRequestPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeletePublicKeyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteRealtimeLogConfigRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], ], ], 'DeleteResponseHeadersPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteStreamingDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteVpcOriginRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IfMatch', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DeleteVpcOriginResult' => [ 'type' => 'structure', 'members' => [ 'VpcOrigin' => [ 'shape' => 'VpcOrigin', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'VpcOrigin', ], 'DescribeFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Name', ], 'Stage' => [ 'shape' => 'FunctionStage', 'location' => 'querystring', 'locationName' => 'Stage', ], ], ], 'DescribeFunctionResult' => [ 'type' => 'structure', 'members' => [ 'FunctionSummary' => [ 'shape' => 'FunctionSummary', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FunctionSummary', ], 'DescribeKeyValueStoreRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'KeyValueStoreName', 'location' => 'uri', 'locationName' => 'Name', ], ], ], 'DescribeKeyValueStoreResult' => [ 'type' => 'structure', 'members' => [ 'KeyValueStore' => [ 'shape' => 'KeyValueStore', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'KeyValueStore', ], 'DisassociateDistributionTenantWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DisassociateDistributionTenantWebACLResult' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'string', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], ], 'DisassociateDistributionWebACLRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'DisassociateDistributionWebACLResult' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'string', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], ], 'Distribution' => [ 'type' => 'structure', 'required' => [ 'Id', 'ARN', 'Status', 'LastModifiedTime', 'InProgressInvalidationBatches', 'DomainName', 'DistributionConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'InProgressInvalidationBatches' => [ 'shape' => 'integer', ], 'DomainName' => [ 'shape' => 'string', ], 'ActiveTrustedSigners' => [ 'shape' => 'ActiveTrustedSigners', ], 'ActiveTrustedKeyGroups' => [ 'shape' => 'ActiveTrustedKeyGroups', ], 'DistributionConfig' => [ 'shape' => 'DistributionConfig', ], 'AliasICPRecordals' => [ 'shape' => 'AliasICPRecordals', ], ], ], 'DistributionAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'DistributionConfig' => [ 'type' => 'structure', 'required' => [ 'CallerReference', 'Origins', 'DefaultCacheBehavior', 'Comment', 'Enabled', ], 'members' => [ 'CallerReference' => [ 'shape' => 'string', ], 'Aliases' => [ 'shape' => 'Aliases', ], 'DefaultRootObject' => [ 'shape' => 'string', ], 'Origins' => [ 'shape' => 'Origins', ], 'OriginGroups' => [ 'shape' => 'OriginGroups', ], 'DefaultCacheBehavior' => [ 'shape' => 'DefaultCacheBehavior', ], 'CacheBehaviors' => [ 'shape' => 'CacheBehaviors', ], 'CustomErrorResponses' => [ 'shape' => 'CustomErrorResponses', ], 'Comment' => [ 'shape' => 'CommentType', ], 'Logging' => [ 'shape' => 'LoggingConfig', ], 'PriceClass' => [ 'shape' => 'PriceClass', ], 'Enabled' => [ 'shape' => 'boolean', ], 'ViewerCertificate' => [ 'shape' => 'ViewerCertificate', ], 'Restrictions' => [ 'shape' => 'Restrictions', ], 'WebACLId' => [ 'shape' => 'string', ], 'HttpVersion' => [ 'shape' => 'HttpVersion', ], 'IsIPV6Enabled' => [ 'shape' => 'boolean', ], 'ContinuousDeploymentPolicyId' => [ 'shape' => 'string', ], 'Staging' => [ 'shape' => 'boolean', ], 'AnycastIpListId' => [ 'shape' => 'string', ], 'TenantConfig' => [ 'shape' => 'TenantConfig', ], 'ConnectionMode' => [ 'shape' => 'ConnectionMode', ], ], ], 'DistributionConfigWithTags' => [ 'type' => 'structure', 'required' => [ 'DistributionConfig', 'Tags', ], 'members' => [ 'DistributionConfig' => [ 'shape' => 'DistributionConfig', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'DistributionIdList' => [ 'type' => 'structure', 'required' => [ 'Marker', 'MaxItems', 'IsTruncated', 'Quantity', ], 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'DistributionIdListSummary', ], ], ], 'DistributionIdListSummary' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'DistributionId', ], ], 'DistributionList' => [ 'type' => 'structure', 'required' => [ 'Marker', 'MaxItems', 'IsTruncated', 'Quantity', ], 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'DistributionSummaryList', ], ], ], 'DistributionNotDisabled' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'DistributionResourceId' => [ 'type' => 'structure', 'members' => [ 'DistributionId' => [ 'shape' => 'string', ], 'DistributionTenantId' => [ 'shape' => 'string', ], ], ], 'DistributionResourceType' => [ 'type' => 'string', 'enum' => [ 'distribution', 'distribution-tenant', ], ], 'DistributionSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'ARN', 'Status', 'LastModifiedTime', 'DomainName', 'Aliases', 'Origins', 'DefaultCacheBehavior', 'CacheBehaviors', 'CustomErrorResponses', 'Comment', 'PriceClass', 'Enabled', 'ViewerCertificate', 'Restrictions', 'WebACLId', 'HttpVersion', 'IsIPV6Enabled', 'Staging', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], 'ETag' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'DomainName' => [ 'shape' => 'string', ], 'Aliases' => [ 'shape' => 'Aliases', ], 'Origins' => [ 'shape' => 'Origins', ], 'OriginGroups' => [ 'shape' => 'OriginGroups', ], 'DefaultCacheBehavior' => [ 'shape' => 'DefaultCacheBehavior', ], 'CacheBehaviors' => [ 'shape' => 'CacheBehaviors', ], 'CustomErrorResponses' => [ 'shape' => 'CustomErrorResponses', ], 'Comment' => [ 'shape' => 'string', ], 'PriceClass' => [ 'shape' => 'PriceClass', ], 'Enabled' => [ 'shape' => 'boolean', ], 'ViewerCertificate' => [ 'shape' => 'ViewerCertificate', ], 'Restrictions' => [ 'shape' => 'Restrictions', ], 'WebACLId' => [ 'shape' => 'string', ], 'HttpVersion' => [ 'shape' => 'HttpVersion', ], 'IsIPV6Enabled' => [ 'shape' => 'boolean', ], 'AliasICPRecordals' => [ 'shape' => 'AliasICPRecordals', ], 'Staging' => [ 'shape' => 'boolean', ], 'ConnectionMode' => [ 'shape' => 'ConnectionMode', ], 'AnycastIpListId' => [ 'shape' => 'string', ], ], ], 'DistributionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DistributionSummary', 'locationName' => 'DistributionSummary', ], ], 'DistributionTenant' => [ 'type' => 'structure', 'members' => [ 'Id' => [ 'shape' => 'string', ], 'DistributionId' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'Arn' => [ 'shape' => 'string', ], 'Domains' => [ 'shape' => 'DomainResultList', ], 'Tags' => [ 'shape' => 'Tags', ], 'Customizations' => [ 'shape' => 'Customizations', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'ConnectionGroupId' => [ 'shape' => 'string', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'Enabled' => [ 'shape' => 'boolean', ], 'Status' => [ 'shape' => 'string', ], ], ], 'DistributionTenantAssociationFilter' => [ 'type' => 'structure', 'members' => [ 'DistributionId' => [ 'shape' => 'string', ], 'ConnectionGroupId' => [ 'shape' => 'string', ], ], ], 'DistributionTenantList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DistributionTenantSummary', 'locationName' => 'DistributionTenantSummary', ], ], 'DistributionTenantSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'DistributionId', 'Name', 'Arn', 'Domains', 'CreatedTime', 'LastModifiedTime', 'ETag', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'DistributionId' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'Arn' => [ 'shape' => 'string', ], 'Domains' => [ 'shape' => 'DomainResultList', ], 'ConnectionGroupId' => [ 'shape' => 'string', ], 'Customizations' => [ 'shape' => 'Customizations', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'ETag' => [ 'shape' => 'string', ], 'Enabled' => [ 'shape' => 'boolean', ], 'Status' => [ 'shape' => 'string', ], ], ], 'DnsConfiguration' => [ 'type' => 'structure', 'required' => [ 'Domain', 'Status', ], 'members' => [ 'Domain' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'DnsConfigurationStatus', ], 'Reason' => [ 'shape' => 'string', ], ], ], 'DnsConfigurationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DnsConfiguration', 'locationName' => 'DnsConfiguration', ], ], 'DnsConfigurationStatus' => [ 'type' => 'string', 'enum' => [ 'valid-configuration', 'invalid-configuration', 'unknown-configuration', ], ], 'DomainConflict' => [ 'type' => 'structure', 'required' => [ 'Domain', 'ResourceType', 'ResourceId', 'AccountId', ], 'members' => [ 'Domain' => [ 'shape' => 'string', ], 'ResourceType' => [ 'shape' => 'DistributionResourceType', ], 'ResourceId' => [ 'shape' => 'string', ], 'AccountId' => [ 'shape' => 'string', ], ], ], 'DomainConflictsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainConflict', 'locationName' => 'DomainConflicts', ], ], 'DomainItem' => [ 'type' => 'structure', 'required' => [ 'Domain', ], 'members' => [ 'Domain' => [ 'shape' => 'string', ], ], ], 'DomainList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainItem', ], ], 'DomainResult' => [ 'type' => 'structure', 'required' => [ 'Domain', ], 'members' => [ 'Domain' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'DomainStatus', ], ], ], 'DomainResultList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DomainResult', ], ], 'DomainStatus' => [ 'type' => 'string', 'enum' => [ 'active', 'inactive', ], ], 'EncryptionEntities' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'EncryptionEntityList', ], ], ], 'EncryptionEntity' => [ 'type' => 'structure', 'required' => [ 'PublicKeyId', 'ProviderId', 'FieldPatterns', ], 'members' => [ 'PublicKeyId' => [ 'shape' => 'string', ], 'ProviderId' => [ 'shape' => 'string', ], 'FieldPatterns' => [ 'shape' => 'FieldPatterns', ], ], ], 'EncryptionEntityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EncryptionEntity', 'locationName' => 'EncryptionEntity', ], ], 'EndPoint' => [ 'type' => 'structure', 'required' => [ 'StreamType', ], 'members' => [ 'StreamType' => [ 'shape' => 'string', ], 'KinesisStreamConfig' => [ 'shape' => 'KinesisStreamConfig', ], ], ], 'EndPointList' => [ 'type' => 'list', 'member' => [ 'shape' => 'EndPoint', ], ], 'EntityAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'EntityLimitExceeded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'EntityNotFound' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'EntitySizeLimitExceeded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 413, 'senderFault' => true, ], 'exception' => true, ], 'EventType' => [ 'type' => 'string', 'enum' => [ 'viewer-request', 'viewer-response', 'origin-request', 'origin-response', ], ], 'FieldLevelEncryption' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'FieldLevelEncryptionConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'FieldLevelEncryptionConfig' => [ 'shape' => 'FieldLevelEncryptionConfig', ], ], ], 'FieldLevelEncryptionConfig' => [ 'type' => 'structure', 'required' => [ 'CallerReference', ], 'members' => [ 'CallerReference' => [ 'shape' => 'string', ], 'Comment' => [ 'shape' => 'string', ], 'QueryArgProfileConfig' => [ 'shape' => 'QueryArgProfileConfig', ], 'ContentTypeProfileConfig' => [ 'shape' => 'ContentTypeProfileConfig', ], ], ], 'FieldLevelEncryptionConfigAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'FieldLevelEncryptionConfigInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'FieldLevelEncryptionList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'FieldLevelEncryptionSummaryList', ], ], ], 'FieldLevelEncryptionProfile' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'FieldLevelEncryptionProfileConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'FieldLevelEncryptionProfileConfig' => [ 'shape' => 'FieldLevelEncryptionProfileConfig', ], ], ], 'FieldLevelEncryptionProfileAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'FieldLevelEncryptionProfileConfig' => [ 'type' => 'structure', 'required' => [ 'Name', 'CallerReference', 'EncryptionEntities', ], 'members' => [ 'Name' => [ 'shape' => 'string', ], 'CallerReference' => [ 'shape' => 'string', ], 'Comment' => [ 'shape' => 'string', ], 'EncryptionEntities' => [ 'shape' => 'EncryptionEntities', ], ], ], 'FieldLevelEncryptionProfileInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'FieldLevelEncryptionProfileList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'FieldLevelEncryptionProfileSummaryList', ], ], ], 'FieldLevelEncryptionProfileSizeExceeded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'FieldLevelEncryptionProfileSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'Name', 'EncryptionEntities', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'Name' => [ 'shape' => 'string', ], 'EncryptionEntities' => [ 'shape' => 'EncryptionEntities', ], 'Comment' => [ 'shape' => 'string', ], ], ], 'FieldLevelEncryptionProfileSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldLevelEncryptionProfileSummary', 'locationName' => 'FieldLevelEncryptionProfileSummary', ], ], 'FieldLevelEncryptionSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'Comment' => [ 'shape' => 'string', ], 'QueryArgProfileConfig' => [ 'shape' => 'QueryArgProfileConfig', ], 'ContentTypeProfileConfig' => [ 'shape' => 'ContentTypeProfileConfig', ], ], ], 'FieldLevelEncryptionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldLevelEncryptionSummary', 'locationName' => 'FieldLevelEncryptionSummary', ], ], 'FieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Field', ], ], 'FieldPatternList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'FieldPattern', ], ], 'FieldPatterns' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'FieldPatternList', ], ], ], 'Format' => [ 'type' => 'string', 'enum' => [ 'URLEncoded', ], ], 'ForwardedValues' => [ 'type' => 'structure', 'required' => [ 'QueryString', 'Cookies', ], 'members' => [ 'QueryString' => [ 'shape' => 'boolean', ], 'Cookies' => [ 'shape' => 'CookiePreference', ], 'Headers' => [ 'shape' => 'Headers', ], 'QueryStringCacheKeys' => [ 'shape' => 'QueryStringCacheKeys', ], ], ], 'FrameOptionsList' => [ 'type' => 'string', 'enum' => [ 'DENY', 'SAMEORIGIN', ], ], 'FunctionARN' => [ 'type' => 'string', 'max' => 108, 'min' => 0, 'pattern' => 'arn:aws:cloudfront::[0-9]{12}:function\\/[a-zA-Z0-9-_]{1,64}', ], 'FunctionAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'FunctionAssociation' => [ 'type' => 'structure', 'required' => [ 'FunctionARN', 'EventType', ], 'members' => [ 'FunctionARN' => [ 'shape' => 'FunctionARN', ], 'EventType' => [ 'shape' => 'EventType', ], ], ], 'FunctionAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionAssociation', 'locationName' => 'FunctionAssociation', ], ], 'FunctionAssociations' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'FunctionAssociationList', ], ], ], 'FunctionBlob' => [ 'type' => 'blob', 'max' => 40960, 'min' => 1, 'sensitive' => true, ], 'FunctionConfig' => [ 'type' => 'structure', 'required' => [ 'Comment', 'Runtime', ], 'members' => [ 'Comment' => [ 'shape' => 'string', ], 'Runtime' => [ 'shape' => 'FunctionRuntime', ], 'KeyValueStoreAssociations' => [ 'shape' => 'KeyValueStoreAssociations', ], ], ], 'FunctionEventObject' => [ 'type' => 'blob', 'max' => 40960, 'min' => 0, 'sensitive' => true, ], 'FunctionExecutionLogList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', ], 'sensitive' => true, ], 'FunctionInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'FunctionList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'FunctionSummaryList', ], ], ], 'FunctionMetadata' => [ 'type' => 'structure', 'required' => [ 'FunctionARN', 'LastModifiedTime', ], 'members' => [ 'FunctionARN' => [ 'shape' => 'string', ], 'Stage' => [ 'shape' => 'FunctionStage', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], ], ], 'FunctionName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_]{1,64}', ], 'FunctionRuntime' => [ 'type' => 'string', 'enum' => [ 'cloudfront-js-1.0', 'cloudfront-js-2.0', ], ], 'FunctionSizeLimitExceeded' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 413, 'senderFault' => true, ], 'exception' => true, ], 'FunctionStage' => [ 'type' => 'string', 'enum' => [ 'DEVELOPMENT', 'LIVE', ], ], 'FunctionSummary' => [ 'type' => 'structure', 'required' => [ 'Name', 'FunctionConfig', 'FunctionMetadata', ], 'members' => [ 'Name' => [ 'shape' => 'FunctionName', ], 'Status' => [ 'shape' => 'string', ], 'FunctionConfig' => [ 'shape' => 'FunctionConfig', ], 'FunctionMetadata' => [ 'shape' => 'FunctionMetadata', ], ], ], 'FunctionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionSummary', 'locationName' => 'FunctionSummary', ], ], 'GeoRestriction' => [ 'type' => 'structure', 'required' => [ 'RestrictionType', 'Quantity', ], 'members' => [ 'RestrictionType' => [ 'shape' => 'GeoRestrictionType', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'LocationList', ], ], ], 'GeoRestrictionCustomization' => [ 'type' => 'structure', 'required' => [ 'RestrictionType', ], 'members' => [ 'RestrictionType' => [ 'shape' => 'GeoRestrictionType', ], 'Locations' => [ 'shape' => 'LocationList', ], ], ], 'GeoRestrictionType' => [ 'type' => 'string', 'enum' => [ 'blacklist', 'whitelist', 'none', ], ], 'GetAnycastIpListRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetAnycastIpListResult' => [ 'type' => 'structure', 'members' => [ 'AnycastIpList' => [ 'shape' => 'AnycastIpList', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'AnycastIpList', ], 'GetCachePolicyConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetCachePolicyConfigResult' => [ 'type' => 'structure', 'members' => [ 'CachePolicyConfig' => [ 'shape' => 'CachePolicyConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CachePolicyConfig', ], 'GetCachePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetCachePolicyResult' => [ 'type' => 'structure', 'members' => [ 'CachePolicy' => [ 'shape' => 'CachePolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CachePolicy', ], 'GetCloudFrontOriginAccessIdentityConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetCloudFrontOriginAccessIdentityConfigResult' => [ 'type' => 'structure', 'members' => [ 'CloudFrontOriginAccessIdentityConfig' => [ 'shape' => 'CloudFrontOriginAccessIdentityConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CloudFrontOriginAccessIdentityConfig', ], 'GetCloudFrontOriginAccessIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetCloudFrontOriginAccessIdentityResult' => [ 'type' => 'structure', 'members' => [ 'CloudFrontOriginAccessIdentity' => [ 'shape' => 'CloudFrontOriginAccessIdentity', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CloudFrontOriginAccessIdentity', ], 'GetConnectionGroupByRoutingEndpointRequest' => [ 'type' => 'structure', 'required' => [ 'RoutingEndpoint', ], 'members' => [ 'RoutingEndpoint' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'RoutingEndpoint', ], ], ], 'GetConnectionGroupByRoutingEndpointResult' => [ 'type' => 'structure', 'members' => [ 'ConnectionGroup' => [ 'shape' => 'ConnectionGroup', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ConnectionGroup', ], 'GetConnectionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetConnectionGroupResult' => [ 'type' => 'structure', 'members' => [ 'ConnectionGroup' => [ 'shape' => 'ConnectionGroup', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ConnectionGroup', ], 'GetContinuousDeploymentPolicyConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetContinuousDeploymentPolicyConfigResult' => [ 'type' => 'structure', 'members' => [ 'ContinuousDeploymentPolicyConfig' => [ 'shape' => 'ContinuousDeploymentPolicyConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ContinuousDeploymentPolicyConfig', ], 'GetContinuousDeploymentPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetContinuousDeploymentPolicyResult' => [ 'type' => 'structure', 'members' => [ 'ContinuousDeploymentPolicy' => [ 'shape' => 'ContinuousDeploymentPolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ContinuousDeploymentPolicy', ], 'GetDistributionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetDistributionConfigResult' => [ 'type' => 'structure', 'members' => [ 'DistributionConfig' => [ 'shape' => 'DistributionConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'DistributionConfig', ], 'GetDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetDistributionResult' => [ 'type' => 'structure', 'members' => [ 'Distribution' => [ 'shape' => 'Distribution', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'Distribution', ], 'GetDistributionTenantByDomainRequest' => [ 'type' => 'structure', 'required' => [ 'Domain', ], 'members' => [ 'Domain' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'domain', ], ], ], 'GetDistributionTenantByDomainResult' => [ 'type' => 'structure', 'members' => [ 'DistributionTenant' => [ 'shape' => 'DistributionTenant', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'DistributionTenant', ], 'GetDistributionTenantRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetDistributionTenantResult' => [ 'type' => 'structure', 'members' => [ 'DistributionTenant' => [ 'shape' => 'DistributionTenant', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'DistributionTenant', ], 'GetFieldLevelEncryptionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetFieldLevelEncryptionConfigResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryptionConfig' => [ 'shape' => 'FieldLevelEncryptionConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryptionConfig', ], 'GetFieldLevelEncryptionProfileConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetFieldLevelEncryptionProfileConfigResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryptionProfileConfig' => [ 'shape' => 'FieldLevelEncryptionProfileConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryptionProfileConfig', ], 'GetFieldLevelEncryptionProfileRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetFieldLevelEncryptionProfileResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryptionProfile' => [ 'shape' => 'FieldLevelEncryptionProfile', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryptionProfile', ], 'GetFieldLevelEncryptionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetFieldLevelEncryptionResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryption' => [ 'shape' => 'FieldLevelEncryption', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryption', ], 'GetFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Name' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Name', ], 'Stage' => [ 'shape' => 'FunctionStage', 'location' => 'querystring', 'locationName' => 'Stage', ], ], ], 'GetFunctionResult' => [ 'type' => 'structure', 'members' => [ 'FunctionCode' => [ 'shape' => 'FunctionBlob', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], 'ContentType' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'Content-Type', ], ], 'payload' => 'FunctionCode', ], 'GetInvalidationForDistributionTenantRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionTenantId', 'Id', ], 'members' => [ 'DistributionTenantId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DistributionTenantId', ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetInvalidationForDistributionTenantResult' => [ 'type' => 'structure', 'members' => [ 'Invalidation' => [ 'shape' => 'Invalidation', ], ], 'payload' => 'Invalidation', ], 'GetInvalidationRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionId', 'Id', ], 'members' => [ 'DistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DistributionId', ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetInvalidationResult' => [ 'type' => 'structure', 'members' => [ 'Invalidation' => [ 'shape' => 'Invalidation', ], ], 'payload' => 'Invalidation', ], 'GetKeyGroupConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetKeyGroupConfigResult' => [ 'type' => 'structure', 'members' => [ 'KeyGroupConfig' => [ 'shape' => 'KeyGroupConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'KeyGroupConfig', ], 'GetKeyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetKeyGroupResult' => [ 'type' => 'structure', 'members' => [ 'KeyGroup' => [ 'shape' => 'KeyGroup', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'KeyGroup', ], 'GetManagedCertificateDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Identifier' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Identifier', ], ], ], 'GetManagedCertificateDetailsResult' => [ 'type' => 'structure', 'members' => [ 'ManagedCertificateDetails' => [ 'shape' => 'ManagedCertificateDetails', ], ], 'payload' => 'ManagedCertificateDetails', ], 'GetMonitoringSubscriptionRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionId', ], 'members' => [ 'DistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DistributionId', ], ], ], 'GetMonitoringSubscriptionResult' => [ 'type' => 'structure', 'members' => [ 'MonitoringSubscription' => [ 'shape' => 'MonitoringSubscription', ], ], 'payload' => 'MonitoringSubscription', ], 'GetOriginAccessControlConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetOriginAccessControlConfigResult' => [ 'type' => 'structure', 'members' => [ 'OriginAccessControlConfig' => [ 'shape' => 'OriginAccessControlConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginAccessControlConfig', ], 'GetOriginAccessControlRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetOriginAccessControlResult' => [ 'type' => 'structure', 'members' => [ 'OriginAccessControl' => [ 'shape' => 'OriginAccessControl', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginAccessControl', ], 'GetOriginRequestPolicyConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetOriginRequestPolicyConfigResult' => [ 'type' => 'structure', 'members' => [ 'OriginRequestPolicyConfig' => [ 'shape' => 'OriginRequestPolicyConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginRequestPolicyConfig', ], 'GetOriginRequestPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetOriginRequestPolicyResult' => [ 'type' => 'structure', 'members' => [ 'OriginRequestPolicy' => [ 'shape' => 'OriginRequestPolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginRequestPolicy', ], 'GetPublicKeyConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetPublicKeyConfigResult' => [ 'type' => 'structure', 'members' => [ 'PublicKeyConfig' => [ 'shape' => 'PublicKeyConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'PublicKeyConfig', ], 'GetPublicKeyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetPublicKeyResult' => [ 'type' => 'structure', 'members' => [ 'PublicKey' => [ 'shape' => 'PublicKey', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'PublicKey', ], 'GetRealtimeLogConfigRequest' => [ 'type' => 'structure', 'members' => [ 'Name' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], ], ], 'GetRealtimeLogConfigResult' => [ 'type' => 'structure', 'members' => [ 'RealtimeLogConfig' => [ 'shape' => 'RealtimeLogConfig', ], ], ], 'GetResponseHeadersPolicyConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetResponseHeadersPolicyConfigResult' => [ 'type' => 'structure', 'members' => [ 'ResponseHeadersPolicyConfig' => [ 'shape' => 'ResponseHeadersPolicyConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ResponseHeadersPolicyConfig', ], 'GetResponseHeadersPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetResponseHeadersPolicyResult' => [ 'type' => 'structure', 'members' => [ 'ResponseHeadersPolicy' => [ 'shape' => 'ResponseHeadersPolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ResponseHeadersPolicy', ], 'GetStreamingDistributionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetStreamingDistributionConfigResult' => [ 'type' => 'structure', 'members' => [ 'StreamingDistributionConfig' => [ 'shape' => 'StreamingDistributionConfig', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'StreamingDistributionConfig', ], 'GetStreamingDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetStreamingDistributionResult' => [ 'type' => 'structure', 'members' => [ 'StreamingDistribution' => [ 'shape' => 'StreamingDistribution', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'StreamingDistribution', ], 'GetVpcOriginRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], ], ], 'GetVpcOriginResult' => [ 'type' => 'structure', 'members' => [ 'VpcOrigin' => [ 'shape' => 'VpcOrigin', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'VpcOrigin', ], 'GrpcConfig' => [ 'type' => 'structure', 'required' => [ 'Enabled', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], ], ], 'HeaderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Name', ], ], 'Headers' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'HeaderList', ], ], ], 'HttpVersion' => [ 'type' => 'string', 'enum' => [ 'http1.1', 'http2', 'http3', 'http2and3', ], ], 'ICPRecordalStatus' => [ 'type' => 'string', 'enum' => [ 'APPROVED', 'SUSPENDED', 'PENDING', ], ], 'IllegalDelete' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IllegalFieldLevelEncryptionConfigAssociationWithCacheBehavior' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IllegalOriginAccessConfiguration' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'IllegalUpdate' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ImportSource' => [ 'type' => 'structure', 'required' => [ 'SourceType', 'SourceARN', ], 'members' => [ 'SourceType' => [ 'shape' => 'ImportSourceType', ], 'SourceARN' => [ 'shape' => 'string', ], ], ], 'ImportSourceType' => [ 'type' => 'string', 'enum' => [ 'S3', ], ], 'InconsistentQuantities' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidArgument' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidAssociation' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDefaultRootObject' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidDomainNameForOriginAccessControl' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidErrorCode' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidForwardCookies' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidFunctionAssociation' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidGeoRestrictionParameter' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidHeadersForS3Origin' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidIfMatchVersion' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidLambdaFunctionAssociation' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidLocationCode' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidMinimumProtocolVersion' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidOrigin' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidOriginAccessControl' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidOriginAccessIdentity' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidOriginKeepaliveTimeout' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidOriginReadTimeout' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidProtocolSettings' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidQueryStringParameters' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidRelativePath' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidRequiredProtocol' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidResponseCode' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidTTLOrder' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidTagging' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidViewerCertificate' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'InvalidWebACLId' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'Invalidation' => [ 'type' => 'structure', 'required' => [ 'Id', 'Status', 'CreateTime', 'InvalidationBatch', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'CreateTime' => [ 'shape' => 'timestamp', ], 'InvalidationBatch' => [ 'shape' => 'InvalidationBatch', ], ], ], 'InvalidationBatch' => [ 'type' => 'structure', 'required' => [ 'Paths', 'CallerReference', ], 'members' => [ 'Paths' => [ 'shape' => 'Paths', ], 'CallerReference' => [ 'shape' => 'string', ], ], ], 'InvalidationList' => [ 'type' => 'structure', 'required' => [ 'Marker', 'MaxItems', 'IsTruncated', 'Quantity', ], 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'InvalidationSummaryList', ], ], ], 'InvalidationSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'CreateTime', 'Status', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'CreateTime' => [ 'shape' => 'timestamp', ], 'Status' => [ 'shape' => 'string', ], ], ], 'InvalidationSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'InvalidationSummary', 'locationName' => 'InvalidationSummary', ], ], 'ItemSelection' => [ 'type' => 'string', 'enum' => [ 'none', 'whitelist', 'all', ], ], 'KGKeyPairIds' => [ 'type' => 'structure', 'members' => [ 'KeyGroupId' => [ 'shape' => 'string', ], 'KeyPairIds' => [ 'shape' => 'KeyPairIds', ], ], ], 'KGKeyPairIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KGKeyPairIds', 'locationName' => 'KeyGroup', ], ], 'KeyGroup' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'KeyGroupConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'KeyGroupConfig' => [ 'shape' => 'KeyGroupConfig', ], ], ], 'KeyGroupAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'KeyGroupConfig' => [ 'type' => 'structure', 'required' => [ 'Name', 'Items', ], 'members' => [ 'Name' => [ 'shape' => 'string', ], 'Items' => [ 'shape' => 'PublicKeyIdList', ], 'Comment' => [ 'shape' => 'string', ], ], ], 'KeyGroupList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'KeyGroupSummaryList', ], ], ], 'KeyGroupSummary' => [ 'type' => 'structure', 'required' => [ 'KeyGroup', ], 'members' => [ 'KeyGroup' => [ 'shape' => 'KeyGroup', ], ], ], 'KeyGroupSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyGroupSummary', 'locationName' => 'KeyGroupSummary', ], ], 'KeyPairIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'KeyPairId', ], ], 'KeyPairIds' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'KeyPairIdList', ], ], ], 'KeyValueStore' => [ 'type' => 'structure', 'required' => [ 'Name', 'Id', 'Comment', 'ARN', 'LastModifiedTime', ], 'members' => [ 'Name' => [ 'shape' => 'string', ], 'Id' => [ 'shape' => 'string', ], 'Comment' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], ], ], 'KeyValueStoreARN' => [ 'type' => 'string', 'max' => 85, 'min' => 0, 'pattern' => 'arn:aws:cloudfront::[0-9]{12}:key-value-store\\/[0-9a-fA-F-]{36}', ], 'KeyValueStoreAssociation' => [ 'type' => 'structure', 'required' => [ 'KeyValueStoreARN', ], 'members' => [ 'KeyValueStoreARN' => [ 'shape' => 'KeyValueStoreARN', ], ], ], 'KeyValueStoreAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyValueStoreAssociation', 'locationName' => 'KeyValueStoreAssociation', ], ], 'KeyValueStoreAssociations' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'KeyValueStoreAssociationList', ], ], ], 'KeyValueStoreComment' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'KeyValueStoreList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'KeyValueStoreSummaryList', ], ], ], 'KeyValueStoreName' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_]{1,64}', ], 'KeyValueStoreSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'KeyValueStore', 'locationName' => 'KeyValueStore', ], ], 'KinesisStreamConfig' => [ 'type' => 'structure', 'required' => [ 'RoleARN', 'StreamARN', ], 'members' => [ 'RoleARN' => [ 'shape' => 'string', ], 'StreamARN' => [ 'shape' => 'string', ], ], ], 'LambdaFunctionARN' => [ 'type' => 'string', ], 'LambdaFunctionAssociation' => [ 'type' => 'structure', 'required' => [ 'LambdaFunctionARN', 'EventType', ], 'members' => [ 'LambdaFunctionARN' => [ 'shape' => 'LambdaFunctionARN', ], 'EventType' => [ 'shape' => 'EventType', ], 'IncludeBody' => [ 'shape' => 'boolean', ], ], ], 'LambdaFunctionAssociationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'LambdaFunctionAssociation', 'locationName' => 'LambdaFunctionAssociation', ], ], 'LambdaFunctionAssociations' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'LambdaFunctionAssociationList', ], ], ], 'ListAnycastIpListsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'integer', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListAnycastIpListsResult' => [ 'type' => 'structure', 'members' => [ 'AnycastIpLists' => [ 'shape' => 'AnycastIpListCollection', 'locationName' => 'AnycastIpListCollection', ], ], 'payload' => 'AnycastIpLists', ], 'ListCachePoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'CachePolicyType', 'location' => 'querystring', 'locationName' => 'Type', ], 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListCachePoliciesResult' => [ 'type' => 'structure', 'members' => [ 'CachePolicyList' => [ 'shape' => 'CachePolicyList', ], ], 'payload' => 'CachePolicyList', ], 'ListCloudFrontOriginAccessIdentitiesRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListCloudFrontOriginAccessIdentitiesResult' => [ 'type' => 'structure', 'members' => [ 'CloudFrontOriginAccessIdentityList' => [ 'shape' => 'CloudFrontOriginAccessIdentityList', ], ], 'payload' => 'CloudFrontOriginAccessIdentityList', ], 'ListConflictingAliasesRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionId', 'Alias', ], 'members' => [ 'DistributionId' => [ 'shape' => 'distributionIdString', 'location' => 'querystring', 'locationName' => 'DistributionId', ], 'Alias' => [ 'shape' => 'aliasString', 'location' => 'querystring', 'locationName' => 'Alias', ], 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'listConflictingAliasesMaxItemsInteger', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListConflictingAliasesResult' => [ 'type' => 'structure', 'members' => [ 'ConflictingAliasesList' => [ 'shape' => 'ConflictingAliasesList', ], ], 'payload' => 'ConflictingAliasesList', ], 'ListConnectionGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'AssociationFilter' => [ 'shape' => 'ConnectionGroupAssociationFilter', ], 'Marker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], ], ], 'ListConnectionGroupsResult' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'ConnectionGroups' => [ 'shape' => 'ConnectionGroupSummaryList', ], ], ], 'ListContinuousDeploymentPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListContinuousDeploymentPoliciesResult' => [ 'type' => 'structure', 'members' => [ 'ContinuousDeploymentPolicyList' => [ 'shape' => 'ContinuousDeploymentPolicyList', ], ], 'payload' => 'ContinuousDeploymentPolicyList', ], 'ListDistributionTenantsByCustomizationRequest' => [ 'type' => 'structure', 'members' => [ 'WebACLArn' => [ 'shape' => 'string', ], 'CertificateArn' => [ 'shape' => 'string', ], 'Marker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], ], ], 'ListDistributionTenantsByCustomizationResult' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'DistributionTenantList' => [ 'shape' => 'DistributionTenantList', ], ], ], 'ListDistributionTenantsRequest' => [ 'type' => 'structure', 'members' => [ 'AssociationFilter' => [ 'shape' => 'DistributionTenantAssociationFilter', ], 'Marker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], ], ], 'ListDistributionTenantsResult' => [ 'type' => 'structure', 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'DistributionTenantList' => [ 'shape' => 'DistributionTenantList', ], ], ], 'ListDistributionsByAnycastIpListIdRequest' => [ 'type' => 'structure', 'required' => [ 'AnycastIpListId', ], 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'AnycastIpListId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'AnycastIpListId', ], ], ], 'ListDistributionsByAnycastIpListIdResult' => [ 'type' => 'structure', 'members' => [ 'DistributionList' => [ 'shape' => 'DistributionList', ], ], 'payload' => 'DistributionList', ], 'ListDistributionsByCachePolicyIdRequest' => [ 'type' => 'structure', 'required' => [ 'CachePolicyId', ], 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'CachePolicyId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'CachePolicyId', ], ], ], 'ListDistributionsByCachePolicyIdResult' => [ 'type' => 'structure', 'members' => [ 'DistributionIdList' => [ 'shape' => 'DistributionIdList', ], ], 'payload' => 'DistributionIdList', ], 'ListDistributionsByConnectionModeRequest' => [ 'type' => 'structure', 'required' => [ 'ConnectionMode', ], 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'integer', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'ConnectionMode' => [ 'shape' => 'ConnectionMode', 'location' => 'uri', 'locationName' => 'ConnectionMode', ], ], ], 'ListDistributionsByConnectionModeResult' => [ 'type' => 'structure', 'members' => [ 'DistributionList' => [ 'shape' => 'DistributionList', ], ], 'payload' => 'DistributionList', ], 'ListDistributionsByKeyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'KeyGroupId', ], 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'KeyGroupId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'KeyGroupId', ], ], ], 'ListDistributionsByKeyGroupResult' => [ 'type' => 'structure', 'members' => [ 'DistributionIdList' => [ 'shape' => 'DistributionIdList', ], ], 'payload' => 'DistributionIdList', ], 'ListDistributionsByOriginRequestPolicyIdRequest' => [ 'type' => 'structure', 'required' => [ 'OriginRequestPolicyId', ], 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'OriginRequestPolicyId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'OriginRequestPolicyId', ], ], ], 'ListDistributionsByOriginRequestPolicyIdResult' => [ 'type' => 'structure', 'members' => [ 'DistributionIdList' => [ 'shape' => 'DistributionIdList', ], ], 'payload' => 'DistributionIdList', ], 'ListDistributionsByRealtimeLogConfigRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'string', ], 'RealtimeLogConfigName' => [ 'shape' => 'string', ], 'RealtimeLogConfigArn' => [ 'shape' => 'string', ], ], ], 'ListDistributionsByRealtimeLogConfigResult' => [ 'type' => 'structure', 'members' => [ 'DistributionList' => [ 'shape' => 'DistributionList', ], ], 'payload' => 'DistributionList', ], 'ListDistributionsByResponseHeadersPolicyIdRequest' => [ 'type' => 'structure', 'required' => [ 'ResponseHeadersPolicyId', ], 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'ResponseHeadersPolicyId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'ResponseHeadersPolicyId', ], ], ], 'ListDistributionsByResponseHeadersPolicyIdResult' => [ 'type' => 'structure', 'members' => [ 'DistributionIdList' => [ 'shape' => 'DistributionIdList', ], ], 'payload' => 'DistributionIdList', ], 'ListDistributionsByVpcOriginIdRequest' => [ 'type' => 'structure', 'required' => [ 'VpcOriginId', ], 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'VpcOriginId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'VpcOriginId', ], ], ], 'ListDistributionsByVpcOriginIdResult' => [ 'type' => 'structure', 'members' => [ 'DistributionIdList' => [ 'shape' => 'DistributionIdList', ], ], 'payload' => 'DistributionIdList', ], 'ListDistributionsByWebACLIdRequest' => [ 'type' => 'structure', 'required' => [ 'WebACLId', ], 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'WebACLId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'WebACLId', ], ], ], 'ListDistributionsByWebACLIdResult' => [ 'type' => 'structure', 'members' => [ 'DistributionList' => [ 'shape' => 'DistributionList', ], ], 'payload' => 'DistributionList', ], 'ListDistributionsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListDistributionsResult' => [ 'type' => 'structure', 'members' => [ 'DistributionList' => [ 'shape' => 'DistributionList', ], ], 'payload' => 'DistributionList', ], 'ListDomainConflictsRequest' => [ 'type' => 'structure', 'required' => [ 'Domain', 'DomainControlValidationResource', ], 'members' => [ 'Domain' => [ 'shape' => 'string', ], 'DomainControlValidationResource' => [ 'shape' => 'DistributionResourceId', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Marker' => [ 'shape' => 'string', ], ], ], 'ListDomainConflictsResult' => [ 'type' => 'structure', 'members' => [ 'DomainConflicts' => [ 'shape' => 'DomainConflictsList', ], 'NextMarker' => [ 'shape' => 'string', ], ], ], 'ListFieldLevelEncryptionConfigsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListFieldLevelEncryptionConfigsResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryptionList' => [ 'shape' => 'FieldLevelEncryptionList', ], ], 'payload' => 'FieldLevelEncryptionList', ], 'ListFieldLevelEncryptionProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListFieldLevelEncryptionProfilesResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryptionProfileList' => [ 'shape' => 'FieldLevelEncryptionProfileList', ], ], 'payload' => 'FieldLevelEncryptionProfileList', ], 'ListFunctionsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'Stage' => [ 'shape' => 'FunctionStage', 'location' => 'querystring', 'locationName' => 'Stage', ], ], ], 'ListFunctionsResult' => [ 'type' => 'structure', 'members' => [ 'FunctionList' => [ 'shape' => 'FunctionList', ], ], 'payload' => 'FunctionList', ], 'ListInvalidationsForDistributionTenantRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'integer', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListInvalidationsForDistributionTenantResult' => [ 'type' => 'structure', 'members' => [ 'InvalidationList' => [ 'shape' => 'InvalidationList', ], ], 'payload' => 'InvalidationList', ], 'ListInvalidationsRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionId', ], 'members' => [ 'DistributionId' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'DistributionId', ], 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListInvalidationsResult' => [ 'type' => 'structure', 'members' => [ 'InvalidationList' => [ 'shape' => 'InvalidationList', ], ], 'payload' => 'InvalidationList', ], 'ListKeyGroupsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListKeyGroupsResult' => [ 'type' => 'structure', 'members' => [ 'KeyGroupList' => [ 'shape' => 'KeyGroupList', ], ], 'payload' => 'KeyGroupList', ], 'ListKeyValueStoresRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'Status' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Status', ], ], ], 'ListKeyValueStoresResult' => [ 'type' => 'structure', 'members' => [ 'KeyValueStoreList' => [ 'shape' => 'KeyValueStoreList', ], ], 'payload' => 'KeyValueStoreList', ], 'ListOriginAccessControlsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListOriginAccessControlsResult' => [ 'type' => 'structure', 'members' => [ 'OriginAccessControlList' => [ 'shape' => 'OriginAccessControlList', ], ], 'payload' => 'OriginAccessControlList', ], 'ListOriginRequestPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'OriginRequestPolicyType', 'location' => 'querystring', 'locationName' => 'Type', ], 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListOriginRequestPoliciesResult' => [ 'type' => 'structure', 'members' => [ 'OriginRequestPolicyList' => [ 'shape' => 'OriginRequestPolicyList', ], ], 'payload' => 'OriginRequestPolicyList', ], 'ListPublicKeysRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListPublicKeysResult' => [ 'type' => 'structure', 'members' => [ 'PublicKeyList' => [ 'shape' => 'PublicKeyList', ], ], 'payload' => 'PublicKeyList', ], 'ListRealtimeLogConfigsRequest' => [ 'type' => 'structure', 'members' => [ 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], ], ], 'ListRealtimeLogConfigsResult' => [ 'type' => 'structure', 'members' => [ 'RealtimeLogConfigs' => [ 'shape' => 'RealtimeLogConfigs', ], ], 'payload' => 'RealtimeLogConfigs', ], 'ListResponseHeadersPoliciesRequest' => [ 'type' => 'structure', 'members' => [ 'Type' => [ 'shape' => 'ResponseHeadersPolicyType', 'location' => 'querystring', 'locationName' => 'Type', ], 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListResponseHeadersPoliciesResult' => [ 'type' => 'structure', 'members' => [ 'ResponseHeadersPolicyList' => [ 'shape' => 'ResponseHeadersPolicyList', ], ], 'payload' => 'ResponseHeadersPolicyList', ], 'ListStreamingDistributionsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListStreamingDistributionsResult' => [ 'type' => 'structure', 'members' => [ 'StreamingDistributionList' => [ 'shape' => 'StreamingDistributionList', ], ], 'payload' => 'StreamingDistributionList', ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', ], 'members' => [ 'Resource' => [ 'shape' => 'ResourceARN', 'location' => 'querystring', 'locationName' => 'Resource', ], ], ], 'ListTagsForResourceResult' => [ 'type' => 'structure', 'required' => [ 'Tags', ], 'members' => [ 'Tags' => [ 'shape' => 'Tags', ], ], 'payload' => 'Tags', ], 'ListVpcOriginsRequest' => [ 'type' => 'structure', 'members' => [ 'Marker' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'Marker', ], 'MaxItems' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'MaxItems', ], ], ], 'ListVpcOriginsResult' => [ 'type' => 'structure', 'members' => [ 'VpcOriginList' => [ 'shape' => 'VpcOriginList', ], ], 'payload' => 'VpcOriginList', ], 'LocationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Location', ], ], 'LoggingConfig' => [ 'type' => 'structure', 'members' => [ 'Enabled' => [ 'shape' => 'boolean', 'box' => true, ], 'IncludeCookies' => [ 'shape' => 'boolean', 'box' => true, ], 'Bucket' => [ 'shape' => 'string', ], 'Prefix' => [ 'shape' => 'string', ], ], ], 'ManagedCertificateDetails' => [ 'type' => 'structure', 'members' => [ 'CertificateArn' => [ 'shape' => 'string', ], 'CertificateStatus' => [ 'shape' => 'ManagedCertificateStatus', ], 'ValidationTokenHost' => [ 'shape' => 'ValidationTokenHost', ], 'ValidationTokenDetails' => [ 'shape' => 'ValidationTokenDetailList', ], ], ], 'ManagedCertificateRequest' => [ 'type' => 'structure', 'required' => [ 'ValidationTokenHost', ], 'members' => [ 'ValidationTokenHost' => [ 'shape' => 'ValidationTokenHost', ], 'PrimaryDomainName' => [ 'shape' => 'string', ], 'CertificateTransparencyLoggingPreference' => [ 'shape' => 'CertificateTransparencyLoggingPreference', ], ], ], 'ManagedCertificateStatus' => [ 'type' => 'string', 'enum' => [ 'pending-validation', 'issued', 'inactive', 'expired', 'validation-timed-out', 'revoked', 'failed', ], ], 'Method' => [ 'type' => 'string', 'enum' => [ 'GET', 'HEAD', 'POST', 'PUT', 'PATCH', 'OPTIONS', 'DELETE', ], ], 'MethodsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Method', 'locationName' => 'Method', ], ], 'MinimumProtocolVersion' => [ 'type' => 'string', 'enum' => [ 'SSLv3', 'TLSv1', 'TLSv1_2016', 'TLSv1.1_2016', 'TLSv1.2_2018', 'TLSv1.2_2019', 'TLSv1.2_2021', ], ], 'MissingBody' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'MonitoringSubscription' => [ 'type' => 'structure', 'members' => [ 'RealtimeMetricsSubscriptionConfig' => [ 'shape' => 'RealtimeMetricsSubscriptionConfig', ], ], ], 'MonitoringSubscriptionAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchCachePolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchCloudFrontOriginAccessIdentity' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchContinuousDeploymentPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchDistribution' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchFieldLevelEncryptionConfig' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchFieldLevelEncryptionProfile' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchFunctionExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchInvalidation' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchMonitoringSubscription' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchOrigin' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchOriginAccessControl' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchOriginRequestPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchPublicKey' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchRealtimeLogConfig' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchResource' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchResponseHeadersPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'NoSuchStreamingDistribution' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'Origin' => [ 'type' => 'structure', 'required' => [ 'Id', 'DomainName', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'DomainName' => [ 'shape' => 'string', ], 'OriginPath' => [ 'shape' => 'string', ], 'CustomHeaders' => [ 'shape' => 'CustomHeaders', ], 'S3OriginConfig' => [ 'shape' => 'S3OriginConfig', ], 'CustomOriginConfig' => [ 'shape' => 'CustomOriginConfig', ], 'VpcOriginConfig' => [ 'shape' => 'VpcOriginConfig', ], 'ConnectionAttempts' => [ 'shape' => 'integer', ], 'ConnectionTimeout' => [ 'shape' => 'integer', ], 'OriginShield' => [ 'shape' => 'OriginShield', ], 'OriginAccessControlId' => [ 'shape' => 'string', ], ], ], 'OriginAccessControl' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'OriginAccessControlConfig' => [ 'shape' => 'OriginAccessControlConfig', ], ], ], 'OriginAccessControlAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'OriginAccessControlConfig' => [ 'type' => 'structure', 'required' => [ 'Name', 'SigningProtocol', 'SigningBehavior', 'OriginAccessControlOriginType', ], 'members' => [ 'Name' => [ 'shape' => 'string', ], 'Description' => [ 'shape' => 'string', ], 'SigningProtocol' => [ 'shape' => 'OriginAccessControlSigningProtocols', ], 'SigningBehavior' => [ 'shape' => 'OriginAccessControlSigningBehaviors', ], 'OriginAccessControlOriginType' => [ 'shape' => 'OriginAccessControlOriginTypes', ], ], ], 'OriginAccessControlInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'OriginAccessControlList' => [ 'type' => 'structure', 'required' => [ 'Marker', 'MaxItems', 'IsTruncated', 'Quantity', ], 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'OriginAccessControlSummaryList', ], ], ], 'OriginAccessControlOriginTypes' => [ 'type' => 'string', 'enum' => [ 's3', 'mediastore', 'mediapackagev2', 'lambda', ], ], 'OriginAccessControlSigningBehaviors' => [ 'type' => 'string', 'enum' => [ 'never', 'always', 'no-override', ], ], 'OriginAccessControlSigningProtocols' => [ 'type' => 'string', 'enum' => [ 'sigv4', ], ], 'OriginAccessControlSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'Description', 'Name', 'SigningProtocol', 'SigningBehavior', 'OriginAccessControlOriginType', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'Description' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'SigningProtocol' => [ 'shape' => 'OriginAccessControlSigningProtocols', ], 'SigningBehavior' => [ 'shape' => 'OriginAccessControlSigningBehaviors', ], 'OriginAccessControlOriginType' => [ 'shape' => 'OriginAccessControlOriginTypes', ], ], ], 'OriginAccessControlSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OriginAccessControlSummary', 'locationName' => 'OriginAccessControlSummary', ], ], 'OriginCustomHeader' => [ 'type' => 'structure', 'required' => [ 'HeaderName', 'HeaderValue', ], 'members' => [ 'HeaderName' => [ 'shape' => 'string', ], 'HeaderValue' => [ 'shape' => 'sensitiveStringType', ], ], ], 'OriginCustomHeadersList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OriginCustomHeader', 'locationName' => 'OriginCustomHeader', ], ], 'OriginGroup' => [ 'type' => 'structure', 'required' => [ 'Id', 'FailoverCriteria', 'Members', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'FailoverCriteria' => [ 'shape' => 'OriginGroupFailoverCriteria', ], 'Members' => [ 'shape' => 'OriginGroupMembers', ], 'SelectionCriteria' => [ 'shape' => 'OriginGroupSelectionCriteria', ], ], ], 'OriginGroupFailoverCriteria' => [ 'type' => 'structure', 'required' => [ 'StatusCodes', ], 'members' => [ 'StatusCodes' => [ 'shape' => 'StatusCodes', ], ], ], 'OriginGroupList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OriginGroup', 'locationName' => 'OriginGroup', ], ], 'OriginGroupMember' => [ 'type' => 'structure', 'required' => [ 'OriginId', ], 'members' => [ 'OriginId' => [ 'shape' => 'string', ], ], ], 'OriginGroupMemberList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OriginGroupMember', 'locationName' => 'OriginGroupMember', ], 'max' => 2, 'min' => 2, ], 'OriginGroupMembers' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'OriginGroupMemberList', ], ], ], 'OriginGroupSelectionCriteria' => [ 'type' => 'string', 'enum' => [ 'default', 'media-quality-based', ], ], 'OriginGroups' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'OriginGroupList', ], ], ], 'OriginList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Origin', 'locationName' => 'Origin', ], 'min' => 1, ], 'OriginProtocolPolicy' => [ 'type' => 'string', 'enum' => [ 'http-only', 'match-viewer', 'https-only', ], ], 'OriginRequestPolicy' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'OriginRequestPolicyConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'OriginRequestPolicyConfig' => [ 'shape' => 'OriginRequestPolicyConfig', ], ], ], 'OriginRequestPolicyAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'OriginRequestPolicyConfig' => [ 'type' => 'structure', 'required' => [ 'Name', 'HeadersConfig', 'CookiesConfig', 'QueryStringsConfig', ], 'members' => [ 'Comment' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'HeadersConfig' => [ 'shape' => 'OriginRequestPolicyHeadersConfig', ], 'CookiesConfig' => [ 'shape' => 'OriginRequestPolicyCookiesConfig', ], 'QueryStringsConfig' => [ 'shape' => 'OriginRequestPolicyQueryStringsConfig', ], ], ], 'OriginRequestPolicyCookieBehavior' => [ 'type' => 'string', 'enum' => [ 'none', 'whitelist', 'all', 'allExcept', ], ], 'OriginRequestPolicyCookiesConfig' => [ 'type' => 'structure', 'required' => [ 'CookieBehavior', ], 'members' => [ 'CookieBehavior' => [ 'shape' => 'OriginRequestPolicyCookieBehavior', ], 'Cookies' => [ 'shape' => 'CookieNames', ], ], ], 'OriginRequestPolicyHeaderBehavior' => [ 'type' => 'string', 'enum' => [ 'none', 'whitelist', 'allViewer', 'allViewerAndWhitelistCloudFront', 'allExcept', ], ], 'OriginRequestPolicyHeadersConfig' => [ 'type' => 'structure', 'required' => [ 'HeaderBehavior', ], 'members' => [ 'HeaderBehavior' => [ 'shape' => 'OriginRequestPolicyHeaderBehavior', ], 'Headers' => [ 'shape' => 'Headers', ], ], ], 'OriginRequestPolicyInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'OriginRequestPolicyList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'OriginRequestPolicySummaryList', ], ], ], 'OriginRequestPolicyQueryStringBehavior' => [ 'type' => 'string', 'enum' => [ 'none', 'whitelist', 'all', 'allExcept', ], ], 'OriginRequestPolicyQueryStringsConfig' => [ 'type' => 'structure', 'required' => [ 'QueryStringBehavior', ], 'members' => [ 'QueryStringBehavior' => [ 'shape' => 'OriginRequestPolicyQueryStringBehavior', ], 'QueryStrings' => [ 'shape' => 'QueryStringNames', ], ], ], 'OriginRequestPolicySummary' => [ 'type' => 'structure', 'required' => [ 'Type', 'OriginRequestPolicy', ], 'members' => [ 'Type' => [ 'shape' => 'OriginRequestPolicyType', ], 'OriginRequestPolicy' => [ 'shape' => 'OriginRequestPolicy', ], ], ], 'OriginRequestPolicySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OriginRequestPolicySummary', 'locationName' => 'OriginRequestPolicySummary', ], ], 'OriginRequestPolicyType' => [ 'type' => 'string', 'enum' => [ 'managed', 'custom', ], ], 'OriginShield' => [ 'type' => 'structure', 'required' => [ 'Enabled', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'OriginShieldRegion' => [ 'shape' => 'OriginShieldRegion', ], ], ], 'OriginShieldRegion' => [ 'type' => 'string', 'max' => 32, 'min' => 1, 'pattern' => '[a-z]{2}-[a-z]+-\\d', ], 'OriginSslProtocols' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'SslProtocolsList', ], ], ], 'Origins' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'OriginList', ], ], ], 'Parameter' => [ 'type' => 'structure', 'required' => [ 'Name', 'Value', ], 'members' => [ 'Name' => [ 'shape' => 'ParameterName', ], 'Value' => [ 'shape' => 'ParameterValue', ], ], ], 'ParameterDefinition' => [ 'type' => 'structure', 'required' => [ 'Name', 'Definition', ], 'members' => [ 'Name' => [ 'shape' => 'ParameterName', ], 'Definition' => [ 'shape' => 'ParameterDefinitionSchema', ], ], ], 'ParameterDefinitionSchema' => [ 'type' => 'structure', 'members' => [ 'StringSchema' => [ 'shape' => 'StringSchemaConfig', ], ], ], 'ParameterDefinitions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ParameterDefinition', ], ], 'ParameterName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[a-zA-Z0-9-_]+', ], 'ParameterValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Parameters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Parameter', ], ], 'ParametersInCacheKeyAndForwardedToOrigin' => [ 'type' => 'structure', 'required' => [ 'EnableAcceptEncodingGzip', 'HeadersConfig', 'CookiesConfig', 'QueryStringsConfig', ], 'members' => [ 'EnableAcceptEncodingGzip' => [ 'shape' => 'boolean', ], 'EnableAcceptEncodingBrotli' => [ 'shape' => 'boolean', ], 'HeadersConfig' => [ 'shape' => 'CachePolicyHeadersConfig', ], 'CookiesConfig' => [ 'shape' => 'CachePolicyCookiesConfig', ], 'QueryStringsConfig' => [ 'shape' => 'CachePolicyQueryStringsConfig', ], ], ], 'PathList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Path', ], ], 'Paths' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'PathList', ], ], ], 'PreconditionFailed' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 412, 'senderFault' => true, ], 'exception' => true, ], 'PriceClass' => [ 'type' => 'string', 'enum' => [ 'PriceClass_100', 'PriceClass_200', 'PriceClass_All', 'None', ], ], 'PublicKey' => [ 'type' => 'structure', 'required' => [ 'Id', 'CreatedTime', 'PublicKeyConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], 'PublicKeyConfig' => [ 'shape' => 'PublicKeyConfig', ], ], ], 'PublicKeyAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'PublicKeyConfig' => [ 'type' => 'structure', 'required' => [ 'CallerReference', 'Name', 'EncodedKey', ], 'members' => [ 'CallerReference' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'EncodedKey' => [ 'shape' => 'string', ], 'Comment' => [ 'shape' => 'string', ], ], ], 'PublicKeyIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'PublicKey', ], ], 'PublicKeyInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'PublicKeyList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'PublicKeySummaryList', ], ], ], 'PublicKeySummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'Name', 'CreatedTime', 'EncodedKey', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], 'EncodedKey' => [ 'shape' => 'string', ], 'Comment' => [ 'shape' => 'string', ], ], ], 'PublicKeySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PublicKeySummary', 'locationName' => 'PublicKeySummary', ], ], 'PublishFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'IfMatch', ], 'members' => [ 'Name' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Name', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'PublishFunctionResult' => [ 'type' => 'structure', 'members' => [ 'FunctionSummary' => [ 'shape' => 'FunctionSummary', ], ], 'payload' => 'FunctionSummary', ], 'QueryArgProfile' => [ 'type' => 'structure', 'required' => [ 'QueryArg', 'ProfileId', ], 'members' => [ 'QueryArg' => [ 'shape' => 'string', ], 'ProfileId' => [ 'shape' => 'string', ], ], ], 'QueryArgProfileConfig' => [ 'type' => 'structure', 'required' => [ 'ForwardWhenQueryArgProfileIsUnknown', ], 'members' => [ 'ForwardWhenQueryArgProfileIsUnknown' => [ 'shape' => 'boolean', ], 'QueryArgProfiles' => [ 'shape' => 'QueryArgProfiles', ], ], ], 'QueryArgProfileEmpty' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'QueryArgProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'QueryArgProfile', 'locationName' => 'QueryArgProfile', ], ], 'QueryArgProfiles' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'QueryArgProfileList', ], ], ], 'QueryStringCacheKeys' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'QueryStringCacheKeysList', ], ], ], 'QueryStringCacheKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Name', ], ], 'QueryStringNames' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'QueryStringNamesList', ], ], ], 'QueryStringNamesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'Name', ], ], 'RealtimeLogConfig' => [ 'type' => 'structure', 'required' => [ 'ARN', 'Name', 'SamplingRate', 'EndPoints', 'Fields', ], 'members' => [ 'ARN' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'SamplingRate' => [ 'shape' => 'long', ], 'EndPoints' => [ 'shape' => 'EndPointList', ], 'Fields' => [ 'shape' => 'FieldList', ], ], ], 'RealtimeLogConfigAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'RealtimeLogConfigInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'RealtimeLogConfigList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RealtimeLogConfig', ], ], 'RealtimeLogConfigOwnerMismatch' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 401, 'senderFault' => true, ], 'exception' => true, ], 'RealtimeLogConfigs' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'IsTruncated', 'Marker', ], 'members' => [ 'MaxItems' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'RealtimeLogConfigList', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], ], ], 'RealtimeMetricsSubscriptionConfig' => [ 'type' => 'structure', 'required' => [ 'RealtimeMetricsSubscriptionStatus', ], 'members' => [ 'RealtimeMetricsSubscriptionStatus' => [ 'shape' => 'RealtimeMetricsSubscriptionStatus', ], ], ], 'RealtimeMetricsSubscriptionStatus' => [ 'type' => 'string', 'enum' => [ 'Enabled', 'Disabled', ], ], 'ReferrerPolicyList' => [ 'type' => 'string', 'enum' => [ 'no-referrer', 'no-referrer-when-downgrade', 'origin', 'origin-when-cross-origin', 'same-origin', 'strict-origin', 'strict-origin-when-cross-origin', 'unsafe-url', ], ], 'ResourceARN' => [ 'type' => 'string', 'pattern' => 'arn:aws(-cn)?:cloudfront::[0-9]+:.*', ], 'ResourceInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ResourceNotDisabled' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ResponseHeadersPolicy' => [ 'type' => 'structure', 'required' => [ 'Id', 'LastModifiedTime', 'ResponseHeadersPolicyConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'ResponseHeadersPolicyConfig' => [ 'shape' => 'ResponseHeadersPolicyConfig', ], ], ], 'ResponseHeadersPolicyAccessControlAllowHeaders' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'AccessControlAllowHeadersList', ], ], ], 'ResponseHeadersPolicyAccessControlAllowMethods' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'AccessControlAllowMethodsList', ], ], ], 'ResponseHeadersPolicyAccessControlAllowMethodsValues' => [ 'type' => 'string', 'enum' => [ 'GET', 'POST', 'OPTIONS', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'ALL', ], ], 'ResponseHeadersPolicyAccessControlAllowOrigins' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'AccessControlAllowOriginsList', ], ], ], 'ResponseHeadersPolicyAccessControlExposeHeaders' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'AccessControlExposeHeadersList', ], ], ], 'ResponseHeadersPolicyAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ResponseHeadersPolicyConfig' => [ 'type' => 'structure', 'required' => [ 'Name', ], 'members' => [ 'Comment' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'CorsConfig' => [ 'shape' => 'ResponseHeadersPolicyCorsConfig', ], 'SecurityHeadersConfig' => [ 'shape' => 'ResponseHeadersPolicySecurityHeadersConfig', ], 'ServerTimingHeadersConfig' => [ 'shape' => 'ResponseHeadersPolicyServerTimingHeadersConfig', ], 'CustomHeadersConfig' => [ 'shape' => 'ResponseHeadersPolicyCustomHeadersConfig', ], 'RemoveHeadersConfig' => [ 'shape' => 'ResponseHeadersPolicyRemoveHeadersConfig', ], ], ], 'ResponseHeadersPolicyContentSecurityPolicy' => [ 'type' => 'structure', 'required' => [ 'Override', 'ContentSecurityPolicy', ], 'members' => [ 'Override' => [ 'shape' => 'boolean', ], 'ContentSecurityPolicy' => [ 'shape' => 'string', ], ], ], 'ResponseHeadersPolicyContentTypeOptions' => [ 'type' => 'structure', 'required' => [ 'Override', ], 'members' => [ 'Override' => [ 'shape' => 'boolean', ], ], ], 'ResponseHeadersPolicyCorsConfig' => [ 'type' => 'structure', 'required' => [ 'AccessControlAllowOrigins', 'AccessControlAllowHeaders', 'AccessControlAllowMethods', 'AccessControlAllowCredentials', 'OriginOverride', ], 'members' => [ 'AccessControlAllowOrigins' => [ 'shape' => 'ResponseHeadersPolicyAccessControlAllowOrigins', ], 'AccessControlAllowHeaders' => [ 'shape' => 'ResponseHeadersPolicyAccessControlAllowHeaders', ], 'AccessControlAllowMethods' => [ 'shape' => 'ResponseHeadersPolicyAccessControlAllowMethods', ], 'AccessControlAllowCredentials' => [ 'shape' => 'boolean', ], 'AccessControlExposeHeaders' => [ 'shape' => 'ResponseHeadersPolicyAccessControlExposeHeaders', ], 'AccessControlMaxAgeSec' => [ 'shape' => 'integer', ], 'OriginOverride' => [ 'shape' => 'boolean', ], ], ], 'ResponseHeadersPolicyCustomHeader' => [ 'type' => 'structure', 'required' => [ 'Header', 'Value', 'Override', ], 'members' => [ 'Header' => [ 'shape' => 'string', ], 'Value' => [ 'shape' => 'string', ], 'Override' => [ 'shape' => 'boolean', ], ], ], 'ResponseHeadersPolicyCustomHeaderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseHeadersPolicyCustomHeader', 'locationName' => 'ResponseHeadersPolicyCustomHeader', ], ], 'ResponseHeadersPolicyCustomHeadersConfig' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'ResponseHeadersPolicyCustomHeaderList', ], ], ], 'ResponseHeadersPolicyFrameOptions' => [ 'type' => 'structure', 'required' => [ 'Override', 'FrameOption', ], 'members' => [ 'Override' => [ 'shape' => 'boolean', ], 'FrameOption' => [ 'shape' => 'FrameOptionsList', ], ], ], 'ResponseHeadersPolicyInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ResponseHeadersPolicyList' => [ 'type' => 'structure', 'required' => [ 'MaxItems', 'Quantity', ], 'members' => [ 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'ResponseHeadersPolicySummaryList', ], ], ], 'ResponseHeadersPolicyReferrerPolicy' => [ 'type' => 'structure', 'required' => [ 'Override', 'ReferrerPolicy', ], 'members' => [ 'Override' => [ 'shape' => 'boolean', ], 'ReferrerPolicy' => [ 'shape' => 'ReferrerPolicyList', ], ], ], 'ResponseHeadersPolicyRemoveHeader' => [ 'type' => 'structure', 'required' => [ 'Header', ], 'members' => [ 'Header' => [ 'shape' => 'string', ], ], ], 'ResponseHeadersPolicyRemoveHeaderList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseHeadersPolicyRemoveHeader', 'locationName' => 'ResponseHeadersPolicyRemoveHeader', ], ], 'ResponseHeadersPolicyRemoveHeadersConfig' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'ResponseHeadersPolicyRemoveHeaderList', ], ], ], 'ResponseHeadersPolicySecurityHeadersConfig' => [ 'type' => 'structure', 'members' => [ 'XSSProtection' => [ 'shape' => 'ResponseHeadersPolicyXSSProtection', ], 'FrameOptions' => [ 'shape' => 'ResponseHeadersPolicyFrameOptions', ], 'ReferrerPolicy' => [ 'shape' => 'ResponseHeadersPolicyReferrerPolicy', ], 'ContentSecurityPolicy' => [ 'shape' => 'ResponseHeadersPolicyContentSecurityPolicy', ], 'ContentTypeOptions' => [ 'shape' => 'ResponseHeadersPolicyContentTypeOptions', ], 'StrictTransportSecurity' => [ 'shape' => 'ResponseHeadersPolicyStrictTransportSecurity', ], ], ], 'ResponseHeadersPolicyServerTimingHeadersConfig' => [ 'type' => 'structure', 'required' => [ 'Enabled', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'SamplingRate' => [ 'shape' => 'SamplingRate', ], ], ], 'ResponseHeadersPolicyStrictTransportSecurity' => [ 'type' => 'structure', 'required' => [ 'Override', 'AccessControlMaxAgeSec', ], 'members' => [ 'Override' => [ 'shape' => 'boolean', ], 'IncludeSubdomains' => [ 'shape' => 'boolean', ], 'Preload' => [ 'shape' => 'boolean', ], 'AccessControlMaxAgeSec' => [ 'shape' => 'integer', ], ], ], 'ResponseHeadersPolicySummary' => [ 'type' => 'structure', 'required' => [ 'Type', 'ResponseHeadersPolicy', ], 'members' => [ 'Type' => [ 'shape' => 'ResponseHeadersPolicyType', ], 'ResponseHeadersPolicy' => [ 'shape' => 'ResponseHeadersPolicy', ], ], ], 'ResponseHeadersPolicySummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResponseHeadersPolicySummary', 'locationName' => 'ResponseHeadersPolicySummary', ], ], 'ResponseHeadersPolicyType' => [ 'type' => 'string', 'enum' => [ 'managed', 'custom', ], ], 'ResponseHeadersPolicyXSSProtection' => [ 'type' => 'structure', 'required' => [ 'Override', 'Protection', ], 'members' => [ 'Override' => [ 'shape' => 'boolean', ], 'Protection' => [ 'shape' => 'boolean', ], 'ModeBlock' => [ 'shape' => 'boolean', ], 'ReportUri' => [ 'shape' => 'string', ], ], ], 'Restrictions' => [ 'type' => 'structure', 'required' => [ 'GeoRestriction', ], 'members' => [ 'GeoRestriction' => [ 'shape' => 'GeoRestriction', ], ], ], 'S3Origin' => [ 'type' => 'structure', 'required' => [ 'DomainName', 'OriginAccessIdentity', ], 'members' => [ 'DomainName' => [ 'shape' => 'string', ], 'OriginAccessIdentity' => [ 'shape' => 'string', ], ], ], 'S3OriginConfig' => [ 'type' => 'structure', 'required' => [ 'OriginAccessIdentity', ], 'members' => [ 'OriginAccessIdentity' => [ 'shape' => 'string', ], ], ], 'SSLSupportMethod' => [ 'type' => 'string', 'enum' => [ 'sni-only', 'vip', 'static-ip', ], ], 'SamplingRate' => [ 'type' => 'double', 'box' => true, 'max' => 100.0, 'min' => 0.0, ], 'SessionStickinessConfig' => [ 'type' => 'structure', 'required' => [ 'IdleTTL', 'MaximumTTL', ], 'members' => [ 'IdleTTL' => [ 'shape' => 'integer', ], 'MaximumTTL' => [ 'shape' => 'integer', ], ], ], 'Signer' => [ 'type' => 'structure', 'members' => [ 'AwsAccountNumber' => [ 'shape' => 'string', ], 'KeyPairIds' => [ 'shape' => 'KeyPairIds', ], ], ], 'SignerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Signer', 'locationName' => 'Signer', ], ], 'SslProtocol' => [ 'type' => 'string', 'enum' => [ 'SSLv3', 'TLSv1', 'TLSv1.1', 'TLSv1.2', ], ], 'SslProtocolsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SslProtocol', 'locationName' => 'SslProtocol', ], ], 'StagingDistributionDnsNameList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'DnsName', ], ], 'StagingDistributionDnsNames' => [ 'type' => 'structure', 'required' => [ 'Quantity', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'StagingDistributionDnsNameList', ], ], ], 'StagingDistributionInUse' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'StatusCodeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'integer', 'locationName' => 'StatusCode', ], 'min' => 1, ], 'StatusCodes' => [ 'type' => 'structure', 'required' => [ 'Quantity', 'Items', ], 'members' => [ 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'StatusCodeList', ], ], ], 'StreamingDistribution' => [ 'type' => 'structure', 'required' => [ 'Id', 'ARN', 'Status', 'DomainName', 'ActiveTrustedSigners', 'StreamingDistributionConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'DomainName' => [ 'shape' => 'string', ], 'ActiveTrustedSigners' => [ 'shape' => 'ActiveTrustedSigners', ], 'StreamingDistributionConfig' => [ 'shape' => 'StreamingDistributionConfig', ], ], ], 'StreamingDistributionAlreadyExists' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'StreamingDistributionConfig' => [ 'type' => 'structure', 'required' => [ 'CallerReference', 'S3Origin', 'Comment', 'TrustedSigners', 'Enabled', ], 'members' => [ 'CallerReference' => [ 'shape' => 'string', ], 'S3Origin' => [ 'shape' => 'S3Origin', ], 'Aliases' => [ 'shape' => 'Aliases', ], 'Comment' => [ 'shape' => 'string', ], 'Logging' => [ 'shape' => 'StreamingLoggingConfig', ], 'TrustedSigners' => [ 'shape' => 'TrustedSigners', ], 'PriceClass' => [ 'shape' => 'PriceClass', ], 'Enabled' => [ 'shape' => 'boolean', ], ], ], 'StreamingDistributionConfigWithTags' => [ 'type' => 'structure', 'required' => [ 'StreamingDistributionConfig', 'Tags', ], 'members' => [ 'StreamingDistributionConfig' => [ 'shape' => 'StreamingDistributionConfig', ], 'Tags' => [ 'shape' => 'Tags', ], ], ], 'StreamingDistributionList' => [ 'type' => 'structure', 'required' => [ 'Marker', 'MaxItems', 'IsTruncated', 'Quantity', ], 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'StreamingDistributionSummaryList', ], ], ], 'StreamingDistributionNotDisabled' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'StreamingDistributionSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'ARN', 'Status', 'LastModifiedTime', 'DomainName', 'S3Origin', 'Aliases', 'TrustedSigners', 'Comment', 'PriceClass', 'Enabled', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'DomainName' => [ 'shape' => 'string', ], 'S3Origin' => [ 'shape' => 'S3Origin', ], 'Aliases' => [ 'shape' => 'Aliases', ], 'TrustedSigners' => [ 'shape' => 'TrustedSigners', ], 'Comment' => [ 'shape' => 'string', ], 'PriceClass' => [ 'shape' => 'PriceClass', ], 'Enabled' => [ 'shape' => 'boolean', ], ], ], 'StreamingDistributionSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StreamingDistributionSummary', 'locationName' => 'StreamingDistributionSummary', ], ], 'StreamingLoggingConfig' => [ 'type' => 'structure', 'required' => [ 'Enabled', 'Bucket', 'Prefix', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'Bucket' => [ 'shape' => 'string', ], 'Prefix' => [ 'shape' => 'string', ], ], ], 'StringSchemaConfig' => [ 'type' => 'structure', 'required' => [ 'Required', ], 'members' => [ 'Comment' => [ 'shape' => 'string', ], 'DefaultValue' => [ 'shape' => 'ParameterValue', ], 'Required' => [ 'shape' => 'boolean', ], ], ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', 'locationName' => 'Key', ], ], 'TagKeys' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'TagKeyList', ], ], ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', 'locationName' => 'Tag', ], ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', 'Tags', ], 'members' => [ 'Resource' => [ 'shape' => 'ResourceARN', 'location' => 'querystring', 'locationName' => 'Resource', ], 'Tags' => [ 'shape' => 'Tags', 'locationName' => 'Tags', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'Tags', ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, 'pattern' => '([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)', ], 'Tags' => [ 'type' => 'structure', 'members' => [ 'Items' => [ 'shape' => 'TagList', ], ], ], 'TenantConfig' => [ 'type' => 'structure', 'members' => [ 'ParameterDefinitions' => [ 'shape' => 'ParameterDefinitions', ], ], ], 'TestFunctionFailed' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'TestFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'IfMatch', 'EventObject', ], 'members' => [ 'Name' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Name', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], 'Stage' => [ 'shape' => 'FunctionStage', ], 'EventObject' => [ 'shape' => 'FunctionEventObject', ], ], ], 'TestFunctionResult' => [ 'type' => 'structure', 'members' => [ 'TestResult' => [ 'shape' => 'TestResult', ], ], 'payload' => 'TestResult', ], 'TestResult' => [ 'type' => 'structure', 'members' => [ 'FunctionSummary' => [ 'shape' => 'FunctionSummary', ], 'ComputeUtilization' => [ 'shape' => 'string', ], 'FunctionExecutionLogs' => [ 'shape' => 'FunctionExecutionLogList', ], 'FunctionErrorMessage' => [ 'shape' => 'sensitiveStringType', ], 'FunctionOutput' => [ 'shape' => 'sensitiveStringType', ], ], ], 'TooLongCSPInResponseHeadersPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyCacheBehaviors' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyCachePolicies' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyCertificates' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyCloudFrontOriginAccessIdentities' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyContinuousDeploymentPolicies' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyCookieNamesInWhiteList' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyCookiesInCachePolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyCookiesInOriginRequestPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyCustomHeadersInResponseHeadersPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyDistributionCNAMEs' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyDistributions' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyDistributionsAssociatedToCachePolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyDistributionsAssociatedToFieldLevelEncryptionConfig' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyDistributionsAssociatedToKeyGroup' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyDistributionsAssociatedToOriginAccessControl' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyDistributionsAssociatedToOriginRequestPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyDistributionsAssociatedToResponseHeadersPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyDistributionsWithFunctionAssociations' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyDistributionsWithLambdaAssociations' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyDistributionsWithSingleFunctionARN' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyFieldLevelEncryptionConfigs' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyFieldLevelEncryptionContentTypeProfiles' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyFieldLevelEncryptionEncryptionEntities' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyFieldLevelEncryptionFieldPatterns' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyFieldLevelEncryptionProfiles' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyFieldLevelEncryptionQueryArgProfiles' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyFunctionAssociations' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyFunctions' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyHeadersInCachePolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyHeadersInForwardedValues' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyHeadersInOriginRequestPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyInvalidationsInProgress' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyKeyGroups' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyKeyGroupsAssociatedToDistribution' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyLambdaFunctionAssociations' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyOriginAccessControls' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyOriginCustomHeaders' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyOriginGroupsPerDistribution' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyOriginRequestPolicies' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyOrigins' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyPublicKeys' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyPublicKeysInKeyGroup' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyQueryStringParameters' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyQueryStringsInCachePolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyQueryStringsInOriginRequestPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyRealtimeLogConfigs' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyRemoveHeadersInResponseHeadersPolicy' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyResponseHeadersPolicies' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyStreamingDistributionCNAMEs' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyStreamingDistributions' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TooManyTrustedSigners' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TrafficConfig' => [ 'type' => 'structure', 'required' => [ 'Type', ], 'members' => [ 'SingleWeightConfig' => [ 'shape' => 'ContinuousDeploymentSingleWeightConfig', ], 'SingleHeaderConfig' => [ 'shape' => 'ContinuousDeploymentSingleHeaderConfig', ], 'Type' => [ 'shape' => 'ContinuousDeploymentPolicyType', ], ], ], 'TrustedKeyGroupDoesNotExist' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TrustedKeyGroupIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'string', 'locationName' => 'KeyGroup', ], ], 'TrustedKeyGroups' => [ 'type' => 'structure', 'required' => [ 'Enabled', 'Quantity', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'TrustedKeyGroupIdList', ], ], ], 'TrustedSignerDoesNotExist' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'TrustedSigners' => [ 'type' => 'structure', 'required' => [ 'Enabled', 'Quantity', ], 'members' => [ 'Enabled' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'AwsAccountNumberList', ], ], ], 'UnsupportedOperation' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'string', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'Resource', 'TagKeys', ], 'members' => [ 'Resource' => [ 'shape' => 'ResourceARN', 'location' => 'querystring', 'locationName' => 'Resource', ], 'TagKeys' => [ 'shape' => 'TagKeys', 'locationName' => 'TagKeys', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], ], 'payload' => 'TagKeys', ], 'UpdateCachePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'CachePolicyConfig', 'Id', ], 'members' => [ 'CachePolicyConfig' => [ 'shape' => 'CachePolicyConfig', 'locationName' => 'CachePolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'CachePolicyConfig', ], 'UpdateCachePolicyResult' => [ 'type' => 'structure', 'members' => [ 'CachePolicy' => [ 'shape' => 'CachePolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CachePolicy', ], 'UpdateCloudFrontOriginAccessIdentityRequest' => [ 'type' => 'structure', 'required' => [ 'CloudFrontOriginAccessIdentityConfig', 'Id', ], 'members' => [ 'CloudFrontOriginAccessIdentityConfig' => [ 'shape' => 'CloudFrontOriginAccessIdentityConfig', 'locationName' => 'CloudFrontOriginAccessIdentityConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'CloudFrontOriginAccessIdentityConfig', ], 'UpdateCloudFrontOriginAccessIdentityResult' => [ 'type' => 'structure', 'members' => [ 'CloudFrontOriginAccessIdentity' => [ 'shape' => 'CloudFrontOriginAccessIdentity', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'CloudFrontOriginAccessIdentity', ], 'UpdateConnectionGroupRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IfMatch', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'Ipv6Enabled' => [ 'shape' => 'boolean', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], 'AnycastIpListId' => [ 'shape' => 'string', ], 'Enabled' => [ 'shape' => 'boolean', ], ], ], 'UpdateConnectionGroupResult' => [ 'type' => 'structure', 'members' => [ 'ConnectionGroup' => [ 'shape' => 'ConnectionGroup', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ConnectionGroup', ], 'UpdateContinuousDeploymentPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ContinuousDeploymentPolicyConfig', 'Id', ], 'members' => [ 'ContinuousDeploymentPolicyConfig' => [ 'shape' => 'ContinuousDeploymentPolicyConfig', 'locationName' => 'ContinuousDeploymentPolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'ContinuousDeploymentPolicyConfig', ], 'UpdateContinuousDeploymentPolicyResult' => [ 'type' => 'structure', 'members' => [ 'ContinuousDeploymentPolicy' => [ 'shape' => 'ContinuousDeploymentPolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ContinuousDeploymentPolicy', ], 'UpdateDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'DistributionConfig', 'Id', ], 'members' => [ 'DistributionConfig' => [ 'shape' => 'DistributionConfig', 'locationName' => 'DistributionConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'DistributionConfig', ], 'UpdateDistributionResult' => [ 'type' => 'structure', 'members' => [ 'Distribution' => [ 'shape' => 'Distribution', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'Distribution', ], 'UpdateDistributionTenantRequest' => [ 'type' => 'structure', 'required' => [ 'Id', 'IfMatch', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'DistributionId' => [ 'shape' => 'string', ], 'Domains' => [ 'shape' => 'DomainList', ], 'Customizations' => [ 'shape' => 'Customizations', ], 'Parameters' => [ 'shape' => 'Parameters', ], 'ConnectionGroupId' => [ 'shape' => 'string', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], 'ManagedCertificateRequest' => [ 'shape' => 'ManagedCertificateRequest', ], 'Enabled' => [ 'shape' => 'boolean', ], ], ], 'UpdateDistributionTenantResult' => [ 'type' => 'structure', 'members' => [ 'DistributionTenant' => [ 'shape' => 'DistributionTenant', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'DistributionTenant', ], 'UpdateDistributionWithStagingConfigRequest' => [ 'type' => 'structure', 'required' => [ 'Id', ], 'members' => [ 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'StagingDistributionId' => [ 'shape' => 'string', 'location' => 'querystring', 'locationName' => 'StagingDistributionId', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'UpdateDistributionWithStagingConfigResult' => [ 'type' => 'structure', 'members' => [ 'Distribution' => [ 'shape' => 'Distribution', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'Distribution', ], 'UpdateDomainAssociationRequest' => [ 'type' => 'structure', 'required' => [ 'Domain', 'TargetResource', ], 'members' => [ 'Domain' => [ 'shape' => 'string', ], 'TargetResource' => [ 'shape' => 'DistributionResourceId', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'UpdateDomainAssociationResult' => [ 'type' => 'structure', 'members' => [ 'Domain' => [ 'shape' => 'string', ], 'ResourceId' => [ 'shape' => 'string', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], ], 'UpdateFieldLevelEncryptionConfigRequest' => [ 'type' => 'structure', 'required' => [ 'FieldLevelEncryptionConfig', 'Id', ], 'members' => [ 'FieldLevelEncryptionConfig' => [ 'shape' => 'FieldLevelEncryptionConfig', 'locationName' => 'FieldLevelEncryptionConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'FieldLevelEncryptionConfig', ], 'UpdateFieldLevelEncryptionConfigResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryption' => [ 'shape' => 'FieldLevelEncryption', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryption', ], 'UpdateFieldLevelEncryptionProfileRequest' => [ 'type' => 'structure', 'required' => [ 'FieldLevelEncryptionProfileConfig', 'Id', ], 'members' => [ 'FieldLevelEncryptionProfileConfig' => [ 'shape' => 'FieldLevelEncryptionProfileConfig', 'locationName' => 'FieldLevelEncryptionProfileConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'FieldLevelEncryptionProfileConfig', ], 'UpdateFieldLevelEncryptionProfileResult' => [ 'type' => 'structure', 'members' => [ 'FieldLevelEncryptionProfile' => [ 'shape' => 'FieldLevelEncryptionProfile', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'FieldLevelEncryptionProfile', ], 'UpdateFunctionRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'IfMatch', 'FunctionConfig', 'FunctionCode', ], 'members' => [ 'Name' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Name', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], 'FunctionConfig' => [ 'shape' => 'FunctionConfig', ], 'FunctionCode' => [ 'shape' => 'FunctionBlob', ], ], ], 'UpdateFunctionResult' => [ 'type' => 'structure', 'members' => [ 'FunctionSummary' => [ 'shape' => 'FunctionSummary', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETtag', ], ], 'payload' => 'FunctionSummary', ], 'UpdateKeyGroupRequest' => [ 'type' => 'structure', 'required' => [ 'KeyGroupConfig', 'Id', ], 'members' => [ 'KeyGroupConfig' => [ 'shape' => 'KeyGroupConfig', 'locationName' => 'KeyGroupConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'KeyGroupConfig', ], 'UpdateKeyGroupResult' => [ 'type' => 'structure', 'members' => [ 'KeyGroup' => [ 'shape' => 'KeyGroup', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'KeyGroup', ], 'UpdateKeyValueStoreRequest' => [ 'type' => 'structure', 'required' => [ 'Name', 'Comment', 'IfMatch', ], 'members' => [ 'Name' => [ 'shape' => 'KeyValueStoreName', 'location' => 'uri', 'locationName' => 'Name', ], 'Comment' => [ 'shape' => 'KeyValueStoreComment', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], ], 'UpdateKeyValueStoreResult' => [ 'type' => 'structure', 'members' => [ 'KeyValueStore' => [ 'shape' => 'KeyValueStore', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'KeyValueStore', ], 'UpdateOriginAccessControlRequest' => [ 'type' => 'structure', 'required' => [ 'OriginAccessControlConfig', 'Id', ], 'members' => [ 'OriginAccessControlConfig' => [ 'shape' => 'OriginAccessControlConfig', 'locationName' => 'OriginAccessControlConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'OriginAccessControlConfig', ], 'UpdateOriginAccessControlResult' => [ 'type' => 'structure', 'members' => [ 'OriginAccessControl' => [ 'shape' => 'OriginAccessControl', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginAccessControl', ], 'UpdateOriginRequestPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'OriginRequestPolicyConfig', 'Id', ], 'members' => [ 'OriginRequestPolicyConfig' => [ 'shape' => 'OriginRequestPolicyConfig', 'locationName' => 'OriginRequestPolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'OriginRequestPolicyConfig', ], 'UpdateOriginRequestPolicyResult' => [ 'type' => 'structure', 'members' => [ 'OriginRequestPolicy' => [ 'shape' => 'OriginRequestPolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'OriginRequestPolicy', ], 'UpdatePublicKeyRequest' => [ 'type' => 'structure', 'required' => [ 'PublicKeyConfig', 'Id', ], 'members' => [ 'PublicKeyConfig' => [ 'shape' => 'PublicKeyConfig', 'locationName' => 'PublicKeyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'PublicKeyConfig', ], 'UpdatePublicKeyResult' => [ 'type' => 'structure', 'members' => [ 'PublicKey' => [ 'shape' => 'PublicKey', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'PublicKey', ], 'UpdateRealtimeLogConfigRequest' => [ 'type' => 'structure', 'members' => [ 'EndPoints' => [ 'shape' => 'EndPointList', ], 'Fields' => [ 'shape' => 'FieldList', ], 'Name' => [ 'shape' => 'string', ], 'ARN' => [ 'shape' => 'string', ], 'SamplingRate' => [ 'shape' => 'long', ], ], ], 'UpdateRealtimeLogConfigResult' => [ 'type' => 'structure', 'members' => [ 'RealtimeLogConfig' => [ 'shape' => 'RealtimeLogConfig', ], ], ], 'UpdateResponseHeadersPolicyRequest' => [ 'type' => 'structure', 'required' => [ 'ResponseHeadersPolicyConfig', 'Id', ], 'members' => [ 'ResponseHeadersPolicyConfig' => [ 'shape' => 'ResponseHeadersPolicyConfig', 'locationName' => 'ResponseHeadersPolicyConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'ResponseHeadersPolicyConfig', ], 'UpdateResponseHeadersPolicyResult' => [ 'type' => 'structure', 'members' => [ 'ResponseHeadersPolicy' => [ 'shape' => 'ResponseHeadersPolicy', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'ResponseHeadersPolicy', ], 'UpdateStreamingDistributionRequest' => [ 'type' => 'structure', 'required' => [ 'StreamingDistributionConfig', 'Id', ], 'members' => [ 'StreamingDistributionConfig' => [ 'shape' => 'StreamingDistributionConfig', 'locationName' => 'StreamingDistributionConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'StreamingDistributionConfig', ], 'UpdateStreamingDistributionResult' => [ 'type' => 'structure', 'members' => [ 'StreamingDistribution' => [ 'shape' => 'StreamingDistribution', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'StreamingDistribution', ], 'UpdateVpcOriginRequest' => [ 'type' => 'structure', 'required' => [ 'VpcOriginEndpointConfig', 'Id', 'IfMatch', ], 'members' => [ 'VpcOriginEndpointConfig' => [ 'shape' => 'VpcOriginEndpointConfig', 'locationName' => 'VpcOriginEndpointConfig', 'xmlNamespace' => [ 'uri' => 'http://cloudfront.amazonaws.com/doc/2020-05-31/', ], ], 'Id' => [ 'shape' => 'string', 'location' => 'uri', 'locationName' => 'Id', ], 'IfMatch' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'If-Match', ], ], 'payload' => 'VpcOriginEndpointConfig', ], 'UpdateVpcOriginResult' => [ 'type' => 'structure', 'members' => [ 'VpcOrigin' => [ 'shape' => 'VpcOrigin', ], 'ETag' => [ 'shape' => 'string', 'location' => 'header', 'locationName' => 'ETag', ], ], 'payload' => 'VpcOrigin', ], 'ValidationTokenDetail' => [ 'type' => 'structure', 'required' => [ 'Domain', ], 'members' => [ 'Domain' => [ 'shape' => 'string', ], 'RedirectTo' => [ 'shape' => 'string', ], 'RedirectFrom' => [ 'shape' => 'string', ], ], ], 'ValidationTokenDetailList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationTokenDetail', ], ], 'ValidationTokenHost' => [ 'type' => 'string', 'enum' => [ 'cloudfront', 'self-hosted', ], ], 'VerifyDnsConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'Identifier', ], 'members' => [ 'Domain' => [ 'shape' => 'string', ], 'Identifier' => [ 'shape' => 'string', ], ], ], 'VerifyDnsConfigurationResult' => [ 'type' => 'structure', 'members' => [ 'DnsConfigurationList' => [ 'shape' => 'DnsConfigurationList', ], ], ], 'ViewerCertificate' => [ 'type' => 'structure', 'members' => [ 'CloudFrontDefaultCertificate' => [ 'shape' => 'boolean', ], 'IAMCertificateId' => [ 'shape' => 'string', ], 'ACMCertificateArn' => [ 'shape' => 'string', ], 'SSLSupportMethod' => [ 'shape' => 'SSLSupportMethod', ], 'MinimumProtocolVersion' => [ 'shape' => 'MinimumProtocolVersion', ], 'Certificate' => [ 'shape' => 'string', 'deprecated' => true, ], 'CertificateSource' => [ 'shape' => 'CertificateSource', 'deprecated' => true, ], ], ], 'ViewerProtocolPolicy' => [ 'type' => 'string', 'enum' => [ 'allow-all', 'https-only', 'redirect-to-https', ], ], 'VpcOrigin' => [ 'type' => 'structure', 'required' => [ 'Id', 'Arn', 'Status', 'CreatedTime', 'LastModifiedTime', 'VpcOriginEndpointConfig', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'Arn' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'VpcOriginEndpointConfig' => [ 'shape' => 'VpcOriginEndpointConfig', ], ], ], 'VpcOriginConfig' => [ 'type' => 'structure', 'required' => [ 'VpcOriginId', ], 'members' => [ 'VpcOriginId' => [ 'shape' => 'string', ], 'OriginReadTimeout' => [ 'shape' => 'integer', ], 'OriginKeepaliveTimeout' => [ 'shape' => 'integer', ], ], ], 'VpcOriginEndpointConfig' => [ 'type' => 'structure', 'required' => [ 'Name', 'Arn', 'HTTPPort', 'HTTPSPort', 'OriginProtocolPolicy', ], 'members' => [ 'Name' => [ 'shape' => 'string', ], 'Arn' => [ 'shape' => 'string', ], 'HTTPPort' => [ 'shape' => 'integer', ], 'HTTPSPort' => [ 'shape' => 'integer', ], 'OriginProtocolPolicy' => [ 'shape' => 'OriginProtocolPolicy', ], 'OriginSslProtocols' => [ 'shape' => 'OriginSslProtocols', ], ], ], 'VpcOriginList' => [ 'type' => 'structure', 'required' => [ 'Marker', 'MaxItems', 'IsTruncated', 'Quantity', ], 'members' => [ 'Marker' => [ 'shape' => 'string', ], 'NextMarker' => [ 'shape' => 'string', ], 'MaxItems' => [ 'shape' => 'integer', ], 'IsTruncated' => [ 'shape' => 'boolean', ], 'Quantity' => [ 'shape' => 'integer', ], 'Items' => [ 'shape' => 'VpcOriginSummaryList', ], ], ], 'VpcOriginSummary' => [ 'type' => 'structure', 'required' => [ 'Id', 'Name', 'Status', 'CreatedTime', 'LastModifiedTime', 'Arn', 'OriginEndpointArn', ], 'members' => [ 'Id' => [ 'shape' => 'string', ], 'Name' => [ 'shape' => 'string', ], 'Status' => [ 'shape' => 'string', ], 'CreatedTime' => [ 'shape' => 'timestamp', ], 'LastModifiedTime' => [ 'shape' => 'timestamp', ], 'Arn' => [ 'shape' => 'string', ], 'OriginEndpointArn' => [ 'shape' => 'string', ], ], ], 'VpcOriginSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'VpcOriginSummary', 'locationName' => 'VpcOriginSummary', ], ], 'WebAclCustomization' => [ 'type' => 'structure', 'required' => [ 'Action', ], 'members' => [ 'Action' => [ 'shape' => 'CustomizationActionType', ], 'Arn' => [ 'shape' => 'string', ], ], ], 'aliasString' => [ 'type' => 'string', 'max' => 253, 'min' => 0, ], 'boolean' => [ 'type' => 'boolean', 'box' => true, ], 'distributionIdString' => [ 'type' => 'string', 'max' => 25, 'min' => 0, ], 'float' => [ 'type' => 'float', 'box' => true, ], 'integer' => [ 'type' => 'integer', 'box' => true, ], 'listConflictingAliasesMaxItemsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 100, ], 'long' => [ 'type' => 'long', 'box' => true, ], 'sensitiveStringType' => [ 'type' => 'string', 'sensitive' => true, ], 'string' => [ 'type' => 'string', ], 'timestamp' => [ 'type' => 'timestamp', ], ],];
