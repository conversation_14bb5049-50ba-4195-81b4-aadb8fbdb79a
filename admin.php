<?php
require_once 'config/database.php';
require_once 'config/env.php';
session_start();

// Cek login admin (implementasi login bisa ditambahkan nanti)
// if (!isset($_SESSION['admin_logged_in'])) {
//     header('Location: login.php');
//     exit;
// }

// Handle tab selection
$active_tab = isset($_GET['tab']) ? $_GET['tab'] : 'app1';

// Handle search
$search = isset($_GET['search']) ? $_GET['search'] : '';
$search_condition = '';
$search_params = [];

if (!empty($search)) {
    $search_condition = "WHERE full_name LIKE :search OR email LIKE :search OR phone LIKE :search";
    $search_params[':search'] = "%$search%";
}

try {
    // Get applications data
    $table = $active_tab === 'app1' ? 'applications' : 'applications2';
    $query = "SELECT * FROM $table $search_condition ORDER BY created_at DESC";
    $stmt = $pdo->prepare($query);
    
    if (!empty($search_params)) {
        foreach ($search_params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
    }
    
    $stmt->execute();
    $applications = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get application details if ID is provided
    $application_details = null;
    $portfolio_files = null;
    if (isset($_GET['id'])) {
        $id = (int)$_GET['id'];
        $details_query = "SELECT * FROM $table WHERE id = :id";
        $details_stmt = $pdo->prepare($details_query);
        $details_stmt->bindValue(':id', $id, PDO::PARAM_INT);
        $details_stmt->execute();
        
        if ($details_stmt->rowCount() > 0) {
            $application_details = $details_stmt->fetch(PDO::FETCH_ASSOC);
            
            // Get portfolio files
            $files_table = $active_tab === 'app1' ? 'portfolio_files' : 'portfolio_files2';
            $files_query = "SELECT * FROM $files_table WHERE application_id = :id";
            $files_stmt = $pdo->prepare($files_query);
            $files_stmt->bindValue(':id', $id, PDO::PARAM_INT);
            $files_stmt->execute();
            $portfolio_files = $files_stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    }
} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    die("Terjadi kesalahan saat mengakses database");
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Lowongan Gass</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .table-hover tbody tr {
            cursor: pointer;
        }
        .modal-lg {
            max-width: 800px;
        }
        .file-item {
            padding: 10px;
            border: 1px solid #ddd;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .file-item:hover {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <h1 class="mb-4">Admin Panel - Lowongan Gass</h1>
        
        <!-- Tabs -->
        <ul class="nav nav-tabs mb-4">
            <li class="nav-item">
                <a class="nav-link <?php echo $active_tab === 'app1' ? 'active' : ''; ?>" 
                   href="?tab=app1<?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                    Application 1
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo $active_tab === 'app2' ? 'active' : ''; ?>" 
                   href="?tab=app2<?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                    Application 2
                </a>
            </li>
        </ul>

        <!-- Search Form -->
        <form class="mb-4" method="GET">
            <input type="hidden" name="tab" value="<?php echo $active_tab; ?>">
            <div class="input-group">
                <input type="text" class="form-control" name="search" 
                       placeholder="Cari berdasarkan nama, email, atau nomor telepon..." 
                       value="<?php echo htmlspecialchars($search); ?>">
                <button class="btn btn-primary" type="submit">
                    <i class="bi bi-search"></i> Cari
                </button>
                <?php if (!empty($search)): ?>
                    <a href="?tab=<?php echo $active_tab; ?>" class="btn btn-secondary">
                        <i class="bi bi-x"></i> Reset
                    </a>
                <?php endif; ?>
            </div>
        </form>

        <!-- Applications Table -->
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nama Lengkap</th>
                        <th>Email</th>
                        <th>Telepon</th>
                        <th>Domisili</th>
                        <th>Tanggal Submit</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($applications as $row): ?>
                    <tr data-id="<?php echo $row['id']; ?>">
                        <td><?php echo $row['id']; ?></td>
                        <td><?php echo htmlspecialchars($row['full_name']); ?></td>
                        <td><?php echo htmlspecialchars($row['email']); ?></td>
                        <td><?php echo htmlspecialchars($row['phone']); ?></td>
                        <td><?php echo htmlspecialchars($row['domicile']); ?></td>
                        <td><?php echo date('d/m/Y H:i', strtotime($row['created_at'])); ?></td>
                        <td>
                            <button class="btn btn-sm btn-info view-details" 
                                    data-id="<?php echo $row['id']; ?>">
                                <i class="bi bi-eye"></i> Detail
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Detail Modal -->
        <div class="modal fade" id="detailModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Detail Aplikasi</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <?php if ($application_details): ?>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Informasi Pribadi</h6>
                                    <table class="table table-sm">
                                        <tr>
                                            <th>Nama Lengkap</th>
                                            <td><?php echo htmlspecialchars($application_details['full_name']); ?></td>
                                        </tr>
                                        <tr>
                                            <th>Email</th>
                                            <td><?php echo htmlspecialchars($application_details['email']); ?></td>
                                        </tr>
                                        <tr>
                                            <th>Telepon</th>
                                            <td><?php echo htmlspecialchars($application_details['phone']); ?></td>
                                        </tr>
                                        <tr>
                                            <th>Domisili</th>
                                            <td><?php echo htmlspecialchars($application_details['domicile']); ?></td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6>Detail Aplikasi</h6>
                                    <table class="table table-sm">
                                        <?php
                                        $exclude_fields = ['id', 'full_name', 'email', 'phone', 'domicile', 'created_at', 'updated_at'];
                                        foreach ($application_details as $key => $value) {
                                            if (!in_array($key, $exclude_fields)) {
                                                echo "<tr><th>" . ucwords(str_replace('_', ' ', $key)) . "</th>";
                                                echo "<td>" . nl2br(htmlspecialchars($value)) . "</td></tr>";
                                            }
                                        }
                                        ?>
                                    </table>
                                </div>
                            </div>

                            <?php if ($portfolio_files): ?>
                            <div class="mt-4">
                                <h6>File Portfolio</h6>
                                <div class="row">
                                    <?php foreach ($portfolio_files as $file): ?>
                                    <div class="col-md-6">
                                        <div class="file-item">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <i class="bi bi-file-earmark"></i>
                                                    <?php echo htmlspecialchars($file['file_name']); ?>
                                                    <small class="text-muted d-block">
                                                        <?php echo number_format($file['file_size'] / 1024, 2); ?> KB
                                                    </small>
                                                </div>
                                                <a href="<?php echo htmlspecialchars($file['file_path']); ?>" 
                                                   class="btn btn-sm btn-primary" download>
                                                    <i class="bi bi-download"></i> Download
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle detail view
            const detailModal = new bootstrap.Modal(document.getElementById('detailModal'));
            
            document.querySelectorAll('.view-details').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                    const id = this.dataset.id;
                    window.location.href = `?tab=<?php echo $active_tab; ?>&id=${id}${<?php echo !empty($search) ? "'&search=" . urlencode($search) . "'" : "''"; ?>}`;
                });
            });

            // Handle row click
            document.querySelectorAll('tbody tr').forEach(row => {
                row.addEventListener('click', function() {
                    const id = this.dataset.id;
                    window.location.href = `?tab=<?php echo $active_tab; ?>&id=${id}${<?php echo !empty($search) ? "'&search=" . urlencode($search) . "'" : "''"; ?>}`;
                });
            });

            // Show modal if ID is in URL
            <?php if (isset($_GET['id'])): ?>
            detailModal.show();
            <?php endif; ?>
        });
    </script>
</body>
</html>
