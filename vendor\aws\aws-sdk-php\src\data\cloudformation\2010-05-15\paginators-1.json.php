<?php
// This file was auto-generated from sdk-root/src/data/cloudformation/2010-05-15/paginators-1.json
return [ 'pagination' => [ 'DescribeAccountLimits' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'AccountLimits', ], 'DescribeStackEvents' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'StackEvents', ], 'DescribeStackResourceDrifts' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'DescribeStackResources' => [ 'result_key' => 'StackResources', ], 'DescribeStacks' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'Stacks', ], 'ListChangeSets' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'Summaries', ], 'ListExports' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'Exports', ], 'ListGeneratedTemplates' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Summaries', ], 'ListImports' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'Imports', ], 'ListResourceScanRelatedResources' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'RelatedResources', ], 'ListResourceScanResources' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Resources', ], 'ListResourceScans' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'ResourceScanSummaries', ], 'ListStackInstances' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Summaries', ], 'ListStackRefactorActions' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'StackRefactorActions', ], 'ListStackRefactors' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'StackRefactorSummaries', ], 'ListStackResources' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'StackResourceSummaries', ], 'ListStackSetOperationResults' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Summaries', ], 'ListStackSetOperations' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Summaries', ], 'ListStackSets' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'Summaries', ], 'ListStacks' => [ 'input_token' => 'NextToken', 'output_token' => 'NextToken', 'result_key' => 'StackSummaries', ], 'ListTypeRegistrations' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'ListTypeVersions' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', ], 'ListTypes' => [ 'input_token' => 'NextToken', 'limit_key' => 'MaxResults', 'output_token' => 'NextToken', 'result_key' => 'TypeSummaries', ], ],];
