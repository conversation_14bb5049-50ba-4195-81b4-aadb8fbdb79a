<?php
// This file was auto-generated from sdk-root/src/data/amplifyuibuilder/2021-08-11/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-08-11', 'endpointPrefix' => 'amplifyuibuilder', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'serviceFullName' => 'AWS Amplify UI Builder', 'serviceId' => 'AmplifyUIBuilder', 'signatureVersion' => 'v4', 'signingName' => 'amplifyuibuilder', 'uid' => 'amplifyuibuilder-2021-08-11', ], 'operations' => [ 'CreateComponent' => [ 'name' => 'CreateComponent', 'http' => [ 'method' => 'POST', 'requestUri' => '/app/{appId}/environment/{environmentName}/components', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateComponentRequest', ], 'output' => [ 'shape' => 'CreateComponentResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'CreateForm' => [ 'name' => 'CreateForm', 'http' => [ 'method' => 'POST', 'requestUri' => '/app/{appId}/environment/{environmentName}/forms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateFormRequest', ], 'output' => [ 'shape' => 'CreateFormResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'CreateTheme' => [ 'name' => 'CreateTheme', 'http' => [ 'method' => 'POST', 'requestUri' => '/app/{appId}/environment/{environmentName}/themes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateThemeRequest', ], 'output' => [ 'shape' => 'CreateThemeResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'DeleteComponent' => [ 'name' => 'DeleteComponent', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/app/{appId}/environment/{environmentName}/components/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteComponentRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteForm' => [ 'name' => 'DeleteForm', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/app/{appId}/environment/{environmentName}/forms/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteFormRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'DeleteTheme' => [ 'name' => 'DeleteTheme', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/app/{appId}/environment/{environmentName}/themes/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteThemeRequest', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'ExchangeCodeForToken' => [ 'name' => 'ExchangeCodeForToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/tokens/{provider}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExchangeCodeForTokenRequest', ], 'output' => [ 'shape' => 'ExchangeCodeForTokenResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], ], ], 'ExportComponents' => [ 'name' => 'ExportComponents', 'http' => [ 'method' => 'GET', 'requestUri' => '/export/app/{appId}/environment/{environmentName}/components', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExportComponentsRequest', ], 'output' => [ 'shape' => 'ExportComponentsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'ExportForms' => [ 'name' => 'ExportForms', 'http' => [ 'method' => 'GET', 'requestUri' => '/export/app/{appId}/environment/{environmentName}/forms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExportFormsRequest', ], 'output' => [ 'shape' => 'ExportFormsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'ExportThemes' => [ 'name' => 'ExportThemes', 'http' => [ 'method' => 'GET', 'requestUri' => '/export/app/{appId}/environment/{environmentName}/themes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ExportThemesRequest', ], 'output' => [ 'shape' => 'ExportThemesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'GetCodegenJob' => [ 'name' => 'GetCodegenJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/app/{appId}/environment/{environmentName}/codegen-jobs/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCodegenJobRequest', ], 'output' => [ 'shape' => 'GetCodegenJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetComponent' => [ 'name' => 'GetComponent', 'http' => [ 'method' => 'GET', 'requestUri' => '/app/{appId}/environment/{environmentName}/components/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetComponentRequest', ], 'output' => [ 'shape' => 'GetComponentResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetForm' => [ 'name' => 'GetForm', 'http' => [ 'method' => 'GET', 'requestUri' => '/app/{appId}/environment/{environmentName}/forms/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetFormRequest', ], 'output' => [ 'shape' => 'GetFormResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'GetMetadata' => [ 'name' => 'GetMetadata', 'http' => [ 'method' => 'GET', 'requestUri' => '/app/{appId}/environment/{environmentName}/metadata', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMetadataRequest', ], 'output' => [ 'shape' => 'GetMetadataResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'GetTheme' => [ 'name' => 'GetTheme', 'http' => [ 'method' => 'GET', 'requestUri' => '/app/{appId}/environment/{environmentName}/themes/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetThemeRequest', ], 'output' => [ 'shape' => 'GetThemeResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListCodegenJobs' => [ 'name' => 'ListCodegenJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/app/{appId}/environment/{environmentName}/codegen-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCodegenJobsRequest', ], 'output' => [ 'shape' => 'ListCodegenJobsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ListComponents' => [ 'name' => 'ListComponents', 'http' => [ 'method' => 'GET', 'requestUri' => '/app/{appId}/environment/{environmentName}/components', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListComponentsRequest', ], 'output' => [ 'shape' => 'ListComponentsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'ListForms' => [ 'name' => 'ListForms', 'http' => [ 'method' => 'GET', 'requestUri' => '/app/{appId}/environment/{environmentName}/forms', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListFormsRequest', ], 'output' => [ 'shape' => 'ListFormsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], ], 'ListThemes' => [ 'name' => 'ListThemes', 'http' => [ 'method' => 'GET', 'requestUri' => '/app/{appId}/environment/{environmentName}/themes', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListThemesRequest', ], 'output' => [ 'shape' => 'ListThemesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], ], ], 'PutMetadataFlag' => [ 'name' => 'PutMetadataFlag', 'http' => [ 'method' => 'PUT', 'requestUri' => '/app/{appId}/environment/{environmentName}/metadata/features/{featureName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutMetadataFlagRequest', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'RefreshToken' => [ 'name' => 'RefreshToken', 'http' => [ 'method' => 'POST', 'requestUri' => '/tokens/{provider}/refresh', 'responseCode' => 200, ], 'input' => [ 'shape' => 'RefreshTokenRequest', ], 'output' => [ 'shape' => 'RefreshTokenResponse', ], 'errors' => [ [ 'shape' => 'InvalidParameterException', ], ], ], 'StartCodegenJob' => [ 'name' => 'StartCodegenJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/app/{appId}/environment/{environmentName}/codegen-jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartCodegenJobRequest', ], 'output' => [ 'shape' => 'StartCodegenJobResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'UnauthorizedException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateComponent' => [ 'name' => 'UpdateComponent', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/app/{appId}/environment/{environmentName}/components/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateComponentRequest', ], 'output' => [ 'shape' => 'UpdateComponentResponse', ], 'errors' => [ [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'UpdateForm' => [ 'name' => 'UpdateForm', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/app/{appId}/environment/{environmentName}/forms/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateFormRequest', ], 'output' => [ 'shape' => 'UpdateFormResponse', ], 'errors' => [ [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], 'UpdateTheme' => [ 'name' => 'UpdateTheme', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/app/{appId}/environment/{environmentName}/themes/{id}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateThemeRequest', ], 'output' => [ 'shape' => 'UpdateThemeResponse', ], 'errors' => [ [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'InvalidParameterException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'ActionParameters' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'ComponentProperty', ], 'url' => [ 'shape' => 'ComponentProperty', ], 'anchor' => [ 'shape' => 'ComponentProperty', ], 'target' => [ 'shape' => 'ComponentProperty', ], 'global' => [ 'shape' => 'ComponentProperty', ], 'model' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'ComponentProperty', ], 'fields' => [ 'shape' => 'ComponentProperties', ], 'state' => [ 'shape' => 'MutationActionSetStateParameter', ], ], ], 'ApiConfiguration' => [ 'type' => 'structure', 'members' => [ 'graphQLConfig' => [ 'shape' => 'GraphQLRenderConfig', ], 'dataStoreConfig' => [ 'shape' => 'DataStoreRenderConfig', ], 'noApiConfig' => [ 'shape' => 'NoApiRenderConfig', ], ], 'union' => true, ], 'AppId' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => 'd[a-z0-9]+', ], 'AssociatedFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CodegenDependencies' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodegenDependency', ], ], 'CodegenDependency' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'String', ], 'supportedVersion' => [ 'shape' => 'String', ], 'isSemVer' => [ 'shape' => 'Boolean', ], 'reason' => [ 'shape' => 'String', ], ], ], 'CodegenFeatureFlags' => [ 'type' => 'structure', 'members' => [ 'isRelationshipSupported' => [ 'shape' => 'Boolean', ], 'isNonModelSupported' => [ 'shape' => 'Boolean', ], ], ], 'CodegenGenericDataEnum' => [ 'type' => 'structure', 'required' => [ 'values', ], 'members' => [ 'values' => [ 'shape' => 'CodegenGenericDataEnumValuesList', ], ], ], 'CodegenGenericDataEnumValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'CodegenGenericDataEnums' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'CodegenGenericDataEnum', ], ], 'CodegenGenericDataField' => [ 'type' => 'structure', 'required' => [ 'dataType', 'dataTypeValue', 'required', 'readOnly', 'isArray', ], 'members' => [ 'dataType' => [ 'shape' => 'CodegenGenericDataFieldDataType', ], 'dataTypeValue' => [ 'shape' => 'String', ], 'required' => [ 'shape' => 'Boolean', ], 'readOnly' => [ 'shape' => 'Boolean', ], 'isArray' => [ 'shape' => 'Boolean', ], 'relationship' => [ 'shape' => 'CodegenGenericDataRelationshipType', ], ], ], 'CodegenGenericDataFieldDataType' => [ 'type' => 'string', 'enum' => [ 'ID', 'String', 'Int', 'Float', 'AWSDate', 'AWSTime', 'AWSDateTime', 'AWSTimestamp', 'AWSEmail', 'AWSURL', 'AWSIPAddress', 'Boolean', 'AWSJSON', 'AWSPhone', 'Enum', 'Model', 'NonModel', ], ], 'CodegenGenericDataFields' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'CodegenGenericDataField', ], ], 'CodegenGenericDataModel' => [ 'type' => 'structure', 'required' => [ 'fields', 'primaryKeys', ], 'members' => [ 'fields' => [ 'shape' => 'CodegenGenericDataFields', ], 'isJoinTable' => [ 'shape' => 'Boolean', ], 'primaryKeys' => [ 'shape' => 'CodegenPrimaryKeysList', ], ], ], 'CodegenGenericDataModels' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'CodegenGenericDataModel', ], ], 'CodegenGenericDataNonModel' => [ 'type' => 'structure', 'required' => [ 'fields', ], 'members' => [ 'fields' => [ 'shape' => 'CodegenGenericDataNonModelFields', ], ], ], 'CodegenGenericDataNonModelFields' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'CodegenGenericDataField', ], ], 'CodegenGenericDataNonModels' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'CodegenGenericDataNonModel', ], ], 'CodegenGenericDataRelationshipType' => [ 'type' => 'structure', 'required' => [ 'type', 'relatedModelName', ], 'members' => [ 'type' => [ 'shape' => 'GenericDataRelationshipType', ], 'relatedModelName' => [ 'shape' => 'String', ], 'relatedModelFields' => [ 'shape' => 'RelatedModelFieldsList', ], 'canUnlinkAssociatedModel' => [ 'shape' => 'Boolean', ], 'relatedJoinFieldName' => [ 'shape' => 'String', ], 'relatedJoinTableName' => [ 'shape' => 'String', ], 'belongsToFieldOnRelatedModel' => [ 'shape' => 'String', ], 'associatedFields' => [ 'shape' => 'AssociatedFieldsList', ], 'isHasManyIndex' => [ 'shape' => 'Boolean', ], ], ], 'CodegenJob' => [ 'type' => 'structure', 'required' => [ 'id', 'appId', 'environmentName', ], 'members' => [ 'id' => [ 'shape' => 'Uuid', ], 'appId' => [ 'shape' => 'AppId', ], 'environmentName' => [ 'shape' => 'String', ], 'renderConfig' => [ 'shape' => 'CodegenJobRenderConfig', ], 'genericDataSchema' => [ 'shape' => 'CodegenJobGenericDataSchema', ], 'autoGenerateForms' => [ 'shape' => 'Boolean', ], 'features' => [ 'shape' => 'CodegenFeatureFlags', ], 'status' => [ 'shape' => 'CodegenJobStatus', ], 'statusMessage' => [ 'shape' => 'String', ], 'asset' => [ 'shape' => 'CodegenJobAsset', ], 'tags' => [ 'shape' => 'Tags', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'modifiedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'dependencies' => [ 'shape' => 'CodegenDependencies', ], ], ], 'CodegenJobAsset' => [ 'type' => 'structure', 'members' => [ 'downloadUrl' => [ 'shape' => 'String', ], ], ], 'CodegenJobGenericDataSchema' => [ 'type' => 'structure', 'required' => [ 'dataSourceType', 'models', 'enums', 'nonModels', ], 'members' => [ 'dataSourceType' => [ 'shape' => 'CodegenJobGenericDataSourceType', ], 'models' => [ 'shape' => 'CodegenGenericDataModels', ], 'enums' => [ 'shape' => 'CodegenGenericDataEnums', ], 'nonModels' => [ 'shape' => 'CodegenGenericDataNonModels', ], ], ], 'CodegenJobGenericDataSourceType' => [ 'type' => 'string', 'enum' => [ 'DataStore', ], ], 'CodegenJobRenderConfig' => [ 'type' => 'structure', 'members' => [ 'react' => [ 'shape' => 'ReactStartCodegenJobData', ], ], 'union' => true, ], 'CodegenJobStatus' => [ 'type' => 'string', 'enum' => [ 'in_progress', 'failed', 'succeeded', ], ], 'CodegenJobSummary' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', ], 'environmentName' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'Uuid', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'modifiedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], ], ], 'CodegenJobSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CodegenJobSummary', ], ], 'CodegenPrimaryKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Component' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', 'name', 'componentType', 'properties', 'variants', 'overrides', 'bindingProperties', 'createdAt', ], 'members' => [ 'appId' => [ 'shape' => 'String', ], 'environmentName' => [ 'shape' => 'String', ], 'sourceId' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'ComponentName', ], 'componentType' => [ 'shape' => 'ComponentType', ], 'properties' => [ 'shape' => 'ComponentProperties', ], 'children' => [ 'shape' => 'ComponentChildList', ], 'variants' => [ 'shape' => 'ComponentVariants', ], 'overrides' => [ 'shape' => 'ComponentOverrides', ], 'bindingProperties' => [ 'shape' => 'ComponentBindingProperties', ], 'collectionProperties' => [ 'shape' => 'ComponentCollectionProperties', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'modifiedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'tags' => [ 'shape' => 'Tags', ], 'events' => [ 'shape' => 'ComponentEvents', ], 'schemaVersion' => [ 'shape' => 'String', ], ], ], 'ComponentBindingProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'ComponentBindingPropertiesValue', ], ], 'ComponentBindingPropertiesValue' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'String', ], 'bindingProperties' => [ 'shape' => 'ComponentBindingPropertiesValueProperties', ], 'defaultValue' => [ 'shape' => 'String', ], ], ], 'ComponentBindingPropertiesValueProperties' => [ 'type' => 'structure', 'members' => [ 'model' => [ 'shape' => 'String', ], 'field' => [ 'shape' => 'String', ], 'predicates' => [ 'shape' => 'PredicateList', ], 'userAttribute' => [ 'shape' => 'String', ], 'bucket' => [ 'shape' => 'String', ], 'key' => [ 'shape' => 'String', ], 'defaultValue' => [ 'shape' => 'String', ], 'slotName' => [ 'shape' => 'String', ], ], ], 'ComponentChild' => [ 'type' => 'structure', 'required' => [ 'componentType', 'name', 'properties', ], 'members' => [ 'componentType' => [ 'shape' => 'String', ], 'name' => [ 'shape' => 'String', ], 'properties' => [ 'shape' => 'ComponentProperties', ], 'children' => [ 'shape' => 'ComponentChildList', ], 'events' => [ 'shape' => 'ComponentEvents', ], 'sourceId' => [ 'shape' => 'String', ], ], ], 'ComponentChildList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentChild', ], ], 'ComponentCollectionProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'ComponentDataConfiguration', ], ], 'ComponentConditionProperty' => [ 'type' => 'structure', 'members' => [ 'property' => [ 'shape' => 'String', ], 'field' => [ 'shape' => 'String', ], 'operator' => [ 'shape' => 'String', ], 'operand' => [ 'shape' => 'String', ], 'then' => [ 'shape' => 'ComponentProperty', ], 'else' => [ 'shape' => 'ComponentProperty', ], 'operandType' => [ 'shape' => 'String', ], ], ], 'ComponentDataConfiguration' => [ 'type' => 'structure', 'required' => [ 'model', ], 'members' => [ 'model' => [ 'shape' => 'String', ], 'sort' => [ 'shape' => 'SortPropertyList', ], 'predicate' => [ 'shape' => 'Predicate', ], 'identifiers' => [ 'shape' => 'IdentifierList', ], ], ], 'ComponentEvent' => [ 'type' => 'structure', 'members' => [ 'action' => [ 'shape' => 'String', ], 'parameters' => [ 'shape' => 'ActionParameters', ], 'bindingEvent' => [ 'shape' => 'String', ], ], ], 'ComponentEvents' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'ComponentEvent', ], ], 'ComponentList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Component', ], ], 'ComponentName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ComponentOverrides' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'ComponentOverridesValue', ], ], 'ComponentOverridesValue' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'ComponentProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'ComponentProperty', ], ], 'ComponentProperty' => [ 'type' => 'structure', 'members' => [ 'value' => [ 'shape' => 'String', ], 'bindingProperties' => [ 'shape' => 'ComponentPropertyBindingProperties', ], 'collectionBindingProperties' => [ 'shape' => 'ComponentPropertyBindingProperties', ], 'defaultValue' => [ 'shape' => 'String', ], 'model' => [ 'shape' => 'String', ], 'bindings' => [ 'shape' => 'FormBindings', ], 'event' => [ 'shape' => 'String', ], 'userAttribute' => [ 'shape' => 'String', ], 'concat' => [ 'shape' => 'ComponentPropertyList', ], 'condition' => [ 'shape' => 'ComponentConditionProperty', ], 'configured' => [ 'shape' => 'Boolean', ], 'type' => [ 'shape' => 'String', ], 'importedValue' => [ 'shape' => 'String', ], 'componentName' => [ 'shape' => 'String', ], 'property' => [ 'shape' => 'String', ], ], ], 'ComponentPropertyBindingProperties' => [ 'type' => 'structure', 'required' => [ 'property', ], 'members' => [ 'property' => [ 'shape' => 'String', ], 'field' => [ 'shape' => 'String', ], ], ], 'ComponentPropertyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentProperty', ], ], 'ComponentSummary' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', 'name', 'componentType', ], 'members' => [ 'appId' => [ 'shape' => 'String', ], 'environmentName' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'ComponentName', ], 'componentType' => [ 'shape' => 'ComponentType', ], ], ], 'ComponentSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentSummary', ], ], 'ComponentType' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ComponentVariant' => [ 'type' => 'structure', 'members' => [ 'variantValues' => [ 'shape' => 'ComponentVariantValues', ], 'overrides' => [ 'shape' => 'ComponentOverrides', ], ], ], 'ComponentVariantValues' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'ComponentVariants' => [ 'type' => 'list', 'member' => [ 'shape' => 'ComponentVariant', ], ], 'CreateComponentData' => [ 'type' => 'structure', 'required' => [ 'name', 'componentType', 'properties', 'variants', 'overrides', 'bindingProperties', ], 'members' => [ 'name' => [ 'shape' => 'ComponentName', ], 'sourceId' => [ 'shape' => 'String', ], 'componentType' => [ 'shape' => 'ComponentType', ], 'properties' => [ 'shape' => 'ComponentProperties', ], 'children' => [ 'shape' => 'ComponentChildList', ], 'variants' => [ 'shape' => 'ComponentVariants', ], 'overrides' => [ 'shape' => 'ComponentOverrides', ], 'bindingProperties' => [ 'shape' => 'ComponentBindingProperties', ], 'collectionProperties' => [ 'shape' => 'ComponentCollectionProperties', ], 'tags' => [ 'shape' => 'Tags', ], 'events' => [ 'shape' => 'ComponentEvents', ], 'schemaVersion' => [ 'shape' => 'String', ], ], ], 'CreateComponentRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'componentToCreate', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'componentToCreate' => [ 'shape' => 'CreateComponentData', ], ], 'payload' => 'componentToCreate', ], 'CreateComponentResponse' => [ 'type' => 'structure', 'members' => [ 'entity' => [ 'shape' => 'Component', ], ], 'payload' => 'entity', ], 'CreateFormData' => [ 'type' => 'structure', 'required' => [ 'name', 'dataType', 'formActionType', 'fields', 'style', 'sectionalElements', 'schemaVersion', ], 'members' => [ 'name' => [ 'shape' => 'FormName', ], 'dataType' => [ 'shape' => 'FormDataTypeConfig', ], 'formActionType' => [ 'shape' => 'FormActionType', ], 'fields' => [ 'shape' => 'FieldsMap', ], 'style' => [ 'shape' => 'FormStyle', ], 'sectionalElements' => [ 'shape' => 'SectionalElementMap', ], 'schemaVersion' => [ 'shape' => 'String', ], 'cta' => [ 'shape' => 'FormCTA', ], 'tags' => [ 'shape' => 'Tags', ], 'labelDecorator' => [ 'shape' => 'LabelDecorator', ], ], ], 'CreateFormRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'formToCreate', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'formToCreate' => [ 'shape' => 'CreateFormData', ], ], 'payload' => 'formToCreate', ], 'CreateFormResponse' => [ 'type' => 'structure', 'members' => [ 'entity' => [ 'shape' => 'Form', ], ], 'payload' => 'entity', ], 'CreateThemeData' => [ 'type' => 'structure', 'required' => [ 'name', 'values', ], 'members' => [ 'name' => [ 'shape' => 'ThemeName', ], 'values' => [ 'shape' => 'ThemeValuesList', ], 'overrides' => [ 'shape' => 'ThemeValuesList', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'CreateThemeRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'themeToCreate', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'themeToCreate' => [ 'shape' => 'CreateThemeData', ], ], 'payload' => 'themeToCreate', ], 'CreateThemeResponse' => [ 'type' => 'structure', 'members' => [ 'entity' => [ 'shape' => 'Theme', ], ], 'payload' => 'entity', ], 'DataStoreRenderConfig' => [ 'type' => 'structure', 'members' => [], ], 'DeleteComponentRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteFormRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'DeleteThemeRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'ExchangeCodeForTokenRequest' => [ 'type' => 'structure', 'required' => [ 'provider', 'request', ], 'members' => [ 'provider' => [ 'shape' => 'TokenProviders', 'location' => 'uri', 'locationName' => 'provider', ], 'request' => [ 'shape' => 'ExchangeCodeForTokenRequestBody', ], ], 'payload' => 'request', ], 'ExchangeCodeForTokenRequestBody' => [ 'type' => 'structure', 'required' => [ 'code', 'redirectUri', ], 'members' => [ 'code' => [ 'shape' => 'SensitiveString', ], 'redirectUri' => [ 'shape' => 'String', ], 'clientId' => [ 'shape' => 'SensitiveString', ], ], ], 'ExchangeCodeForTokenResponse' => [ 'type' => 'structure', 'required' => [ 'accessToken', 'expiresIn', 'refreshToken', ], 'members' => [ 'accessToken' => [ 'shape' => 'SensitiveString', ], 'expiresIn' => [ 'shape' => 'Integer', ], 'refreshToken' => [ 'shape' => 'SensitiveString', ], ], ], 'ExportComponentsRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ExportComponentsResponse' => [ 'type' => 'structure', 'required' => [ 'entities', ], 'members' => [ 'entities' => [ 'shape' => 'ComponentList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ExportFormsRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ExportFormsResponse' => [ 'type' => 'structure', 'required' => [ 'entities', ], 'members' => [ 'entities' => [ 'shape' => 'FormList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ExportThemesRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], ], ], 'ExportThemesResponse' => [ 'type' => 'structure', 'required' => [ 'entities', ], 'members' => [ 'entities' => [ 'shape' => 'ThemeList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'FeaturesMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'FieldConfig' => [ 'type' => 'structure', 'members' => [ 'label' => [ 'shape' => 'String', ], 'position' => [ 'shape' => 'FieldPosition', ], 'excluded' => [ 'shape' => 'Boolean', ], 'inputType' => [ 'shape' => 'FieldInputConfig', ], 'validations' => [ 'shape' => 'ValidationsList', ], ], ], 'FieldInputConfig' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'String', ], 'required' => [ 'shape' => 'Boolean', ], 'readOnly' => [ 'shape' => 'Boolean', ], 'placeholder' => [ 'shape' => 'String', ], 'defaultValue' => [ 'shape' => 'String', ], 'descriptiveText' => [ 'shape' => 'String', ], 'defaultChecked' => [ 'shape' => 'Boolean', ], 'defaultCountryCode' => [ 'shape' => 'String', ], 'valueMappings' => [ 'shape' => 'ValueMappings', ], 'name' => [ 'shape' => 'String', ], 'minValue' => [ 'shape' => 'Float', ], 'maxValue' => [ 'shape' => 'Float', ], 'step' => [ 'shape' => 'Float', ], 'value' => [ 'shape' => 'String', ], 'isArray' => [ 'shape' => 'Boolean', ], 'fileUploaderConfig' => [ 'shape' => 'FileUploaderFieldConfig', ], ], ], 'FieldPosition' => [ 'type' => 'structure', 'members' => [ 'fixed' => [ 'shape' => 'FixedPosition', ], 'rightOf' => [ 'shape' => 'String', ], 'below' => [ 'shape' => 'String', ], ], 'union' => true, ], 'FieldValidationConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'String', ], 'strValues' => [ 'shape' => 'StrValues', ], 'numValues' => [ 'shape' => 'NumValues', ], 'validationMessage' => [ 'shape' => 'String', ], ], ], 'FieldsMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'FieldConfig', ], ], 'FileUploaderFieldConfig' => [ 'type' => 'structure', 'required' => [ 'accessLevel', 'acceptedFileTypes', ], 'members' => [ 'accessLevel' => [ 'shape' => 'StorageAccessLevel', ], 'acceptedFileTypes' => [ 'shape' => 'StrValues', ], 'showThumbnails' => [ 'shape' => 'Boolean', ], 'isResumable' => [ 'shape' => 'Boolean', ], 'maxFileCount' => [ 'shape' => 'Integer', ], 'maxSize' => [ 'shape' => 'Integer', ], ], ], 'FixedPosition' => [ 'type' => 'string', 'enum' => [ 'first', ], ], 'Float' => [ 'type' => 'float', 'box' => true, ], 'Form' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', 'name', 'formActionType', 'style', 'dataType', 'fields', 'sectionalElements', 'schemaVersion', ], 'members' => [ 'appId' => [ 'shape' => 'String', ], 'environmentName' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'FormName', ], 'formActionType' => [ 'shape' => 'FormActionType', ], 'style' => [ 'shape' => 'FormStyle', ], 'dataType' => [ 'shape' => 'FormDataTypeConfig', ], 'fields' => [ 'shape' => 'FieldsMap', ], 'sectionalElements' => [ 'shape' => 'SectionalElementMap', ], 'schemaVersion' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'Tags', ], 'cta' => [ 'shape' => 'FormCTA', ], 'labelDecorator' => [ 'shape' => 'LabelDecorator', ], ], ], 'FormActionType' => [ 'type' => 'string', 'enum' => [ 'create', 'update', ], ], 'FormBindingElement' => [ 'type' => 'structure', 'required' => [ 'element', 'property', ], 'members' => [ 'element' => [ 'shape' => 'String', ], 'property' => [ 'shape' => 'String', ], ], ], 'FormBindings' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'FormBindingElement', ], ], 'FormButton' => [ 'type' => 'structure', 'members' => [ 'excluded' => [ 'shape' => 'Boolean', ], 'children' => [ 'shape' => 'String', ], 'position' => [ 'shape' => 'FieldPosition', ], ], ], 'FormButtonsPosition' => [ 'type' => 'string', 'enum' => [ 'top', 'bottom', 'top_and_bottom', ], ], 'FormCTA' => [ 'type' => 'structure', 'members' => [ 'position' => [ 'shape' => 'FormButtonsPosition', ], 'clear' => [ 'shape' => 'FormButton', ], 'cancel' => [ 'shape' => 'FormButton', ], 'submit' => [ 'shape' => 'FormButton', ], ], ], 'FormDataSourceType' => [ 'type' => 'string', 'enum' => [ 'DataStore', 'Custom', ], ], 'FormDataTypeConfig' => [ 'type' => 'structure', 'required' => [ 'dataSourceType', 'dataTypeName', ], 'members' => [ 'dataSourceType' => [ 'shape' => 'FormDataSourceType', ], 'dataTypeName' => [ 'shape' => 'String', ], ], ], 'FormInputBindingProperties' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'FormInputBindingPropertiesValue', ], ], 'FormInputBindingPropertiesValue' => [ 'type' => 'structure', 'members' => [ 'type' => [ 'shape' => 'String', ], 'bindingProperties' => [ 'shape' => 'FormInputBindingPropertiesValueProperties', ], ], ], 'FormInputBindingPropertiesValueProperties' => [ 'type' => 'structure', 'members' => [ 'model' => [ 'shape' => 'String', ], ], ], 'FormInputValueProperty' => [ 'type' => 'structure', 'members' => [ 'value' => [ 'shape' => 'String', ], 'bindingProperties' => [ 'shape' => 'FormInputValuePropertyBindingProperties', ], 'concat' => [ 'shape' => 'FormInputValuePropertyList', ], ], ], 'FormInputValuePropertyBindingProperties' => [ 'type' => 'structure', 'required' => [ 'property', ], 'members' => [ 'property' => [ 'shape' => 'String', ], 'field' => [ 'shape' => 'String', ], ], ], 'FormInputValuePropertyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FormInputValueProperty', ], ], 'FormList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Form', ], ], 'FormName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'FormStyle' => [ 'type' => 'structure', 'members' => [ 'horizontalGap' => [ 'shape' => 'FormStyleConfig', ], 'verticalGap' => [ 'shape' => 'FormStyleConfig', ], 'outerPadding' => [ 'shape' => 'FormStyleConfig', ], ], ], 'FormStyleConfig' => [ 'type' => 'structure', 'members' => [ 'tokenReference' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'union' => true, ], 'FormSummary' => [ 'type' => 'structure', 'required' => [ 'appId', 'dataType', 'environmentName', 'formActionType', 'id', 'name', ], 'members' => [ 'appId' => [ 'shape' => 'String', ], 'dataType' => [ 'shape' => 'FormDataTypeConfig', ], 'environmentName' => [ 'shape' => 'String', ], 'formActionType' => [ 'shape' => 'FormActionType', ], 'id' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'FormName', ], ], ], 'FormSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FormSummary', ], ], 'GenericDataRelationshipType' => [ 'type' => 'string', 'enum' => [ 'HAS_MANY', 'HAS_ONE', 'BELONGS_TO', ], ], 'GetCodegenJobRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetCodegenJobResponse' => [ 'type' => 'structure', 'members' => [ 'job' => [ 'shape' => 'CodegenJob', ], ], 'payload' => 'job', ], 'GetComponentRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetComponentResponse' => [ 'type' => 'structure', 'members' => [ 'component' => [ 'shape' => 'Component', ], ], 'payload' => 'component', ], 'GetFormRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetFormResponse' => [ 'type' => 'structure', 'members' => [ 'form' => [ 'shape' => 'Form', ], ], 'payload' => 'form', ], 'GetMetadataRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], ], ], 'GetMetadataResponse' => [ 'type' => 'structure', 'required' => [ 'features', ], 'members' => [ 'features' => [ 'shape' => 'FeaturesMap', ], ], ], 'GetThemeRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], ], ], 'GetThemeResponse' => [ 'type' => 'structure', 'members' => [ 'theme' => [ 'shape' => 'Theme', ], ], 'payload' => 'theme', ], 'GraphQLRenderConfig' => [ 'type' => 'structure', 'required' => [ 'typesFilePath', 'queriesFilePath', 'mutationsFilePath', 'subscriptionsFilePath', 'fragmentsFilePath', ], 'members' => [ 'typesFilePath' => [ 'shape' => 'String', ], 'queriesFilePath' => [ 'shape' => 'String', ], 'mutationsFilePath' => [ 'shape' => 'String', ], 'subscriptionsFilePath' => [ 'shape' => 'String', ], 'fragmentsFilePath' => [ 'shape' => 'String', ], ], ], 'IdentifierList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'InvalidParameterException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'JSModule' => [ 'type' => 'string', 'enum' => [ 'es2020', 'esnext', ], ], 'JSScript' => [ 'type' => 'string', 'enum' => [ 'jsx', 'tsx', 'js', ], ], 'JSTarget' => [ 'type' => 'string', 'enum' => [ 'es2015', 'es2020', ], ], 'LabelDecorator' => [ 'type' => 'string', 'enum' => [ 'required', 'optional', 'none', ], ], 'ListCodegenJobsLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListCodegenJobsRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListCodegenJobsLimit', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListCodegenJobsResponse' => [ 'type' => 'structure', 'required' => [ 'entities', ], 'members' => [ 'entities' => [ 'shape' => 'CodegenJobSummaryList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListComponentsRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListEntityLimit', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListComponentsResponse' => [ 'type' => 'structure', 'required' => [ 'entities', ], 'members' => [ 'entities' => [ 'shape' => 'ComponentSummaryList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListEntityLimit' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ListFormsRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListEntityLimit', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListFormsResponse' => [ 'type' => 'structure', 'required' => [ 'entities', ], 'members' => [ 'entities' => [ 'shape' => 'FormSummaryList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'Tags', ], ], ], 'ListThemesRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListEntityLimit', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListThemesResponse' => [ 'type' => 'structure', 'required' => [ 'entities', ], 'members' => [ 'entities' => [ 'shape' => 'ThemeSummaryList', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'MutationActionSetStateParameter' => [ 'type' => 'structure', 'required' => [ 'componentName', 'property', 'set', ], 'members' => [ 'componentName' => [ 'shape' => 'String', ], 'property' => [ 'shape' => 'String', ], 'set' => [ 'shape' => 'ComponentProperty', ], ], ], 'NoApiRenderConfig' => [ 'type' => 'structure', 'members' => [], ], 'NumValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'Integer', ], ], 'OperandType' => [ 'type' => 'string', 'pattern' => 'boolean|string|number', ], 'Predicate' => [ 'type' => 'structure', 'members' => [ 'or' => [ 'shape' => 'PredicateList', ], 'and' => [ 'shape' => 'PredicateList', ], 'field' => [ 'shape' => 'String', ], 'operator' => [ 'shape' => 'String', ], 'operand' => [ 'shape' => 'String', ], 'operandType' => [ 'shape' => 'OperandType', ], ], ], 'PredicateList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Predicate', ], ], 'PutMetadataFlagBody' => [ 'type' => 'structure', 'required' => [ 'newValue', ], 'members' => [ 'newValue' => [ 'shape' => 'String', ], ], ], 'PutMetadataFlagRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'featureName', 'body', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'featureName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'featureName', ], 'body' => [ 'shape' => 'PutMetadataFlagBody', ], ], 'payload' => 'body', ], 'ReactCodegenDependencies' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'String', ], ], 'ReactStartCodegenJobData' => [ 'type' => 'structure', 'members' => [ 'module' => [ 'shape' => 'JSModule', ], 'target' => [ 'shape' => 'JSTarget', ], 'script' => [ 'shape' => 'JSScript', ], 'renderTypeDeclarations' => [ 'shape' => 'Boolean', ], 'inlineSourceMap' => [ 'shape' => 'Boolean', ], 'apiConfiguration' => [ 'shape' => 'ApiConfiguration', ], 'dependencies' => [ 'shape' => 'ReactCodegenDependencies', ], ], ], 'RefreshTokenRequest' => [ 'type' => 'structure', 'required' => [ 'provider', 'refreshTokenBody', ], 'members' => [ 'provider' => [ 'shape' => 'TokenProviders', 'location' => 'uri', 'locationName' => 'provider', ], 'refreshTokenBody' => [ 'shape' => 'RefreshTokenRequestBody', ], ], 'payload' => 'refreshTokenBody', ], 'RefreshTokenRequestBody' => [ 'type' => 'structure', 'required' => [ 'token', ], 'members' => [ 'token' => [ 'shape' => 'SensitiveString', ], 'clientId' => [ 'shape' => 'SensitiveString', ], ], ], 'RefreshTokenResponse' => [ 'type' => 'structure', 'required' => [ 'accessToken', 'expiresIn', ], 'members' => [ 'accessToken' => [ 'shape' => 'SensitiveString', ], 'expiresIn' => [ 'shape' => 'Integer', ], ], ], 'RelatedModelFieldsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ResourceConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SectionalElement' => [ 'type' => 'structure', 'required' => [ 'type', ], 'members' => [ 'type' => [ 'shape' => 'String', ], 'position' => [ 'shape' => 'FieldPosition', ], 'text' => [ 'shape' => 'String', ], 'level' => [ 'shape' => 'Integer', ], 'orientation' => [ 'shape' => 'String', ], 'excluded' => [ 'shape' => 'Boolean', ], ], ], 'SectionalElementMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'SectionalElement', ], ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'SortDirection' => [ 'type' => 'string', 'enum' => [ 'ASC', 'DESC', ], ], 'SortProperty' => [ 'type' => 'structure', 'required' => [ 'field', 'direction', ], 'members' => [ 'field' => [ 'shape' => 'String', ], 'direction' => [ 'shape' => 'SortDirection', ], ], ], 'SortPropertyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SortProperty', ], ], 'StartCodegenJobData' => [ 'type' => 'structure', 'required' => [ 'renderConfig', ], 'members' => [ 'renderConfig' => [ 'shape' => 'CodegenJobRenderConfig', ], 'genericDataSchema' => [ 'shape' => 'CodegenJobGenericDataSchema', ], 'autoGenerateForms' => [ 'shape' => 'Boolean', ], 'features' => [ 'shape' => 'CodegenFeatureFlags', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'StartCodegenJobRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'codegenJobToCreate', ], 'members' => [ 'appId' => [ 'shape' => 'AppId', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'codegenJobToCreate' => [ 'shape' => 'StartCodegenJobData', ], ], 'payload' => 'codegenJobToCreate', ], 'StartCodegenJobResponse' => [ 'type' => 'structure', 'members' => [ 'entity' => [ 'shape' => 'CodegenJob', ], ], 'payload' => 'entity', ], 'StorageAccessLevel' => [ 'type' => 'string', 'enum' => [ 'public', 'protected', 'private', ], ], 'StrValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'String' => [ 'type' => 'string', ], 'SyntheticTimestamp_date_time' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '(?!aws:)[a-zA-Z+-=._:/]+', ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'Tags' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], 'Theme' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', 'name', 'createdAt', 'values', ], 'members' => [ 'appId' => [ 'shape' => 'String', ], 'environmentName' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'ThemeName', ], 'createdAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'modifiedAt' => [ 'shape' => 'SyntheticTimestamp_date_time', ], 'values' => [ 'shape' => 'ThemeValuesList', ], 'overrides' => [ 'shape' => 'ThemeValuesList', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'ThemeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Theme', ], ], 'ThemeName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ThemeSummary' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', 'name', ], 'members' => [ 'appId' => [ 'shape' => 'String', ], 'environmentName' => [ 'shape' => 'String', ], 'id' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'ThemeName', ], ], ], 'ThemeSummaryList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThemeSummary', ], ], 'ThemeValue' => [ 'type' => 'structure', 'members' => [ 'value' => [ 'shape' => 'String', ], 'children' => [ 'shape' => 'ThemeValuesList', ], ], ], 'ThemeValues' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'String', ], 'value' => [ 'shape' => 'ThemeValue', ], ], ], 'ThemeValuesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThemeValues', ], ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'TokenProviders' => [ 'type' => 'string', 'enum' => [ 'figma', ], ], 'UnauthorizedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 401, 'senderFault' => true, ], 'exception' => true, ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateComponentData' => [ 'type' => 'structure', 'members' => [ 'id' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'ComponentName', ], 'sourceId' => [ 'shape' => 'String', ], 'componentType' => [ 'shape' => 'ComponentType', ], 'properties' => [ 'shape' => 'ComponentProperties', ], 'children' => [ 'shape' => 'ComponentChildList', ], 'variants' => [ 'shape' => 'ComponentVariants', ], 'overrides' => [ 'shape' => 'ComponentOverrides', ], 'bindingProperties' => [ 'shape' => 'ComponentBindingProperties', ], 'collectionProperties' => [ 'shape' => 'ComponentCollectionProperties', ], 'events' => [ 'shape' => 'ComponentEvents', ], 'schemaVersion' => [ 'shape' => 'String', ], ], ], 'UpdateComponentRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', 'updatedComponent', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'updatedComponent' => [ 'shape' => 'UpdateComponentData', ], ], 'payload' => 'updatedComponent', ], 'UpdateComponentResponse' => [ 'type' => 'structure', 'members' => [ 'entity' => [ 'shape' => 'Component', ], ], 'payload' => 'entity', ], 'UpdateFormData' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'FormName', ], 'dataType' => [ 'shape' => 'FormDataTypeConfig', ], 'formActionType' => [ 'shape' => 'FormActionType', ], 'fields' => [ 'shape' => 'FieldsMap', ], 'style' => [ 'shape' => 'FormStyle', ], 'sectionalElements' => [ 'shape' => 'SectionalElementMap', ], 'schemaVersion' => [ 'shape' => 'String', ], 'cta' => [ 'shape' => 'FormCTA', ], 'labelDecorator' => [ 'shape' => 'LabelDecorator', ], ], ], 'UpdateFormRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', 'updatedForm', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'updatedForm' => [ 'shape' => 'UpdateFormData', ], ], 'payload' => 'updatedForm', ], 'UpdateFormResponse' => [ 'type' => 'structure', 'members' => [ 'entity' => [ 'shape' => 'Form', ], ], 'payload' => 'entity', ], 'UpdateThemeData' => [ 'type' => 'structure', 'required' => [ 'values', ], 'members' => [ 'id' => [ 'shape' => 'Uuid', ], 'name' => [ 'shape' => 'ThemeName', ], 'values' => [ 'shape' => 'ThemeValuesList', ], 'overrides' => [ 'shape' => 'ThemeValuesList', ], ], ], 'UpdateThemeRequest' => [ 'type' => 'structure', 'required' => [ 'appId', 'environmentName', 'id', 'updatedTheme', ], 'members' => [ 'appId' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'appId', ], 'environmentName' => [ 'shape' => 'String', 'location' => 'uri', 'locationName' => 'environmentName', ], 'id' => [ 'shape' => 'Uuid', 'location' => 'uri', 'locationName' => 'id', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, 'location' => 'querystring', 'locationName' => 'clientToken', ], 'updatedTheme' => [ 'shape' => 'UpdateThemeData', ], ], 'payload' => 'updatedTheme', ], 'UpdateThemeResponse' => [ 'type' => 'structure', 'members' => [ 'entity' => [ 'shape' => 'Theme', ], ], 'payload' => 'entity', ], 'Uuid' => [ 'type' => 'string', ], 'ValidationsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FieldValidationConfiguration', ], ], 'ValueMapping' => [ 'type' => 'structure', 'required' => [ 'value', ], 'members' => [ 'displayValue' => [ 'shape' => 'FormInputValueProperty', ], 'value' => [ 'shape' => 'FormInputValueProperty', ], ], ], 'ValueMappingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValueMapping', ], ], 'ValueMappings' => [ 'type' => 'structure', 'required' => [ 'values', ], 'members' => [ 'values' => [ 'shape' => 'ValueMappingList', ], 'bindingProperties' => [ 'shape' => 'FormInputBindingProperties', ], ], ], ],];
