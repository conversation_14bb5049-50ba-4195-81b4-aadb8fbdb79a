<?php
// This file was auto-generated from sdk-root/src/data/billing/2023-09-07/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2023-09-07', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'billing', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'AWS Billing', 'serviceId' => 'Billing', 'signatureVersion' => 'v4', 'signingName' => 'billing', 'targetPrefix' => 'AWSBilling', 'uid' => 'billing-2023-09-07', ], 'operations' => [ 'CreateBillingView' => [ 'name' => 'CreateBillingView', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateBillingViewRequest', ], 'output' => [ 'shape' => 'CreateBillingViewResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteBillingView' => [ 'name' => 'DeleteBillingView', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteBillingViewRequest', ], 'output' => [ 'shape' => 'DeleteBillingViewResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'GetBillingView' => [ 'name' => 'GetBillingView', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetBillingViewRequest', ], 'output' => [ 'shape' => 'GetBillingViewResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetResourcePolicy' => [ 'name' => 'GetResourcePolicy', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourcePolicyRequest', ], 'output' => [ 'shape' => 'GetResourcePolicyResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListBillingViews' => [ 'name' => 'ListBillingViews', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListBillingViewsRequest', ], 'output' => [ 'shape' => 'ListBillingViewsResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListSourceViewsForBillingView' => [ 'name' => 'ListSourceViewsForBillingView', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListSourceViewsForBillingViewRequest', ], 'output' => [ 'shape' => 'ListSourceViewsForBillingViewResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'output' => [ 'shape' => 'TagResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'output' => [ 'shape' => 'UntagResourceResponse', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], ], 'UpdateBillingView' => [ 'name' => 'UpdateBillingView', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateBillingViewRequest', ], 'output' => [ 'shape' => 'UpdateBillingViewResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'AccountId' => [ 'type' => 'string', 'pattern' => '[0-9]{12}', ], 'ActiveTimeRange' => [ 'type' => 'structure', 'required' => [ 'activeAfterInclusive', 'activeBeforeInclusive', ], 'members' => [ 'activeAfterInclusive' => [ 'shape' => 'Timestamp', ], 'activeBeforeInclusive' => [ 'shape' => 'Timestamp', ], ], ], 'BillingViewArn' => [ 'type' => 'string', 'pattern' => 'arn:aws[a-z-]*:(billing)::[0-9]{12}:billingview/[a-zA-Z0-9/:_\\+=\\.\\-@]{0,59}[a-zA-Z0-9]', ], 'BillingViewArnList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillingViewArn', ], 'max' => 10, 'min' => 0, ], 'BillingViewDescription' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '([ a-zA-Z0-9_\\+=\\.\\-@]+)?', 'sensitive' => true, ], 'BillingViewElement' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'BillingViewArn', ], 'name' => [ 'shape' => 'BillingViewName', ], 'description' => [ 'shape' => 'BillingViewDescription', ], 'billingViewType' => [ 'shape' => 'BillingViewType', ], 'ownerAccountId' => [ 'shape' => 'AccountId', ], 'dataFilterExpression' => [ 'shape' => 'Expression', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'BillingViewList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillingViewListElement', ], ], 'BillingViewListElement' => [ 'type' => 'structure', 'members' => [ 'arn' => [ 'shape' => 'BillingViewArn', ], 'name' => [ 'shape' => 'BillingViewName', ], 'description' => [ 'shape' => 'BillingViewDescription', ], 'ownerAccountId' => [ 'shape' => 'AccountId', ], 'billingViewType' => [ 'shape' => 'BillingViewType', ], ], ], 'BillingViewName' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[ a-zA-Z0-9_\\+=\\.\\-@]+', 'sensitive' => true, ], 'BillingViewSourceViewsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillingViewArn', ], 'max' => 1, 'min' => 1, ], 'BillingViewType' => [ 'type' => 'string', 'enum' => [ 'PRIMARY', 'BILLING_GROUP', 'CUSTOM', ], ], 'BillingViewTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'BillingViewType', ], ], 'BillingViewsMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ClientToken' => [ 'type' => 'string', 'pattern' => '[a-zA-Z0-9-]+', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'exception' => true, ], 'CreateBillingViewRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'sourceViews', ], 'members' => [ 'name' => [ 'shape' => 'BillingViewName', ], 'description' => [ 'shape' => 'BillingViewDescription', ], 'sourceViews' => [ 'shape' => 'BillingViewSourceViewsList', ], 'dataFilterExpression' => [ 'shape' => 'Expression', ], 'clientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'resourceTags' => [ 'shape' => 'ResourceTagList', ], ], ], 'CreateBillingViewResponse' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'BillingViewArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], ], ], 'DeleteBillingViewRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'BillingViewArn', ], ], ], 'DeleteBillingViewResponse' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'BillingViewArn', ], ], ], 'Dimension' => [ 'type' => 'string', 'enum' => [ 'LINKED_ACCOUNT', ], ], 'DimensionValues' => [ 'type' => 'structure', 'required' => [ 'key', 'values', ], 'members' => [ 'key' => [ 'shape' => 'Dimension', ], 'values' => [ 'shape' => 'Values', ], ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'Expression' => [ 'type' => 'structure', 'members' => [ 'dimensions' => [ 'shape' => 'DimensionValues', ], 'tags' => [ 'shape' => 'TagValues', ], ], ], 'FieldName' => [ 'type' => 'string', 'max' => 100, 'min' => 0, ], 'GetBillingViewRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'BillingViewArn', ], ], ], 'GetBillingViewResponse' => [ 'type' => 'structure', 'required' => [ 'billingView', ], 'members' => [ 'billingView' => [ 'shape' => 'BillingViewElement', ], ], ], 'GetResourcePolicyRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'GetResourcePolicyResponse' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'policy' => [ 'shape' => 'PolicyDocument', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'ListBillingViewsRequest' => [ 'type' => 'structure', 'members' => [ 'activeTimeRange' => [ 'shape' => 'ActiveTimeRange', ], 'arns' => [ 'shape' => 'BillingViewArnList', ], 'billingViewTypes' => [ 'shape' => 'BillingViewTypeList', ], 'ownerAccountId' => [ 'shape' => 'AccountId', ], 'maxResults' => [ 'shape' => 'BillingViewsMaxResults', ], 'nextToken' => [ 'shape' => 'PageToken', ], ], ], 'ListBillingViewsResponse' => [ 'type' => 'structure', 'required' => [ 'billingViews', ], 'members' => [ 'billingViews' => [ 'shape' => 'BillingViewList', ], 'nextToken' => [ 'shape' => 'PageToken', ], ], ], 'ListSourceViewsForBillingViewRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'BillingViewArn', ], 'maxResults' => [ 'shape' => 'BillingViewsMaxResults', ], 'nextToken' => [ 'shape' => 'PageToken', ], ], ], 'ListSourceViewsForBillingViewResponse' => [ 'type' => 'structure', 'required' => [ 'sourceViews', ], 'members' => [ 'sourceViews' => [ 'shape' => 'BillingViewSourceViewsList', ], 'nextToken' => [ 'shape' => 'PageToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'resourceTags' => [ 'shape' => 'ResourceTagList', ], ], ], 'PageToken' => [ 'type' => 'string', 'max' => 2047, 'min' => 1, ], 'PolicyDocument' => [ 'type' => 'string', ], 'QuotaCode' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ResourceArn' => [ 'type' => 'string', 'pattern' => 'arn:aws[a-z-]*:(billing)::[0-9]{12}:[a-zA-Z0-9/:_\\+=\\.\\@-]{0,70}[a-zA-Z0-9]', ], 'ResourceId' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], ], 'exception' => true, ], 'ResourceTag' => [ 'type' => 'structure', 'required' => [ 'key', ], 'members' => [ 'key' => [ 'shape' => 'ResourceTagKey', ], 'value' => [ 'shape' => 'ResourceTagValue', ], ], ], 'ResourceTagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'ResourceTagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceTagKey', ], 'max' => 200, 'min' => 0, ], 'ResourceTagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceTag', ], 'max' => 200, 'min' => 0, ], 'ResourceTagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ResourceType' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ServiceCode' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', 'serviceCode', 'quotaCode', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'serviceCode' => [ 'shape' => 'ServiceCode', ], 'quotaCode' => [ 'shape' => 'QuotaCode', ], ], 'exception' => true, ], 'TagKey' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[\\S\\s]*', ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'resourceTags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'resourceTags' => [ 'shape' => 'ResourceTagList', ], ], ], 'TagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'TagValues' => [ 'type' => 'structure', 'required' => [ 'key', 'values', ], 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'values' => [ 'shape' => 'Values', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'resourceTagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'resourceTagKeys' => [ 'shape' => 'ResourceTagKeyList', ], ], ], 'UntagResourceResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateBillingViewRequest' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'BillingViewArn', ], 'name' => [ 'shape' => 'BillingViewName', ], 'description' => [ 'shape' => 'BillingViewDescription', ], 'dataFilterExpression' => [ 'shape' => 'Expression', ], ], ], 'UpdateBillingViewResponse' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'BillingViewArn', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'FieldName', ], 'message' => [ 'shape' => 'ErrorMessage', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'unknownOperation', 'cannotParse', 'fieldValidationFailed', 'other', ], ], 'Value' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, 'pattern' => '[\\S\\s]*', ], 'Values' => [ 'type' => 'list', 'member' => [ 'shape' => 'Value', ], 'max' => 200, 'min' => 1, ], ],];
