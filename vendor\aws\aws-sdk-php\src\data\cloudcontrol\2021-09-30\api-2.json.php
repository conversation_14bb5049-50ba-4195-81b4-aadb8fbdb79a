<?php
// This file was auto-generated from sdk-root/src/data/cloudcontrol/2021-09-30/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-09-30', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'cloudcontrolapi', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'CloudControlApi', 'serviceFullName' => 'AWS Cloud Control API', 'serviceId' => 'CloudControl', 'signatureVersion' => 'v4', 'signingName' => 'cloudcontrolapi', 'targetPrefix' => 'CloudApiService', 'uid' => 'cloudcontrol-2021-09-30', ], 'operations' => [ 'CancelResourceRequest' => [ 'name' => 'CancelResourceRequest', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CancelResourceRequestInput', ], 'output' => [ 'shape' => 'CancelResourceRequestOutput', ], 'errors' => [ [ 'shape' => 'ConcurrentModificationException', ], [ 'shape' => 'RequestTokenNotFoundException', ], ], 'idempotent' => true, ], 'CreateResource' => [ 'name' => 'CreateResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateResourceInput', ], 'output' => [ 'shape' => 'CreateResourceOutput', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'HandlerInternalFailureException', ], [ 'shape' => 'GeneralServiceException', ], [ 'shape' => 'NotUpdatableException', ], [ 'shape' => 'TypeNotFoundException', ], [ 'shape' => 'ConcurrentOperationException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'PrivateTypeException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NetworkFailureException', ], [ 'shape' => 'UnsupportedActionException', ], [ 'shape' => 'NotStabilizedException', ], [ 'shape' => 'ServiceInternalErrorException', ], [ 'shape' => 'HandlerFailureException', ], [ 'shape' => 'ServiceLimitExceededException', ], [ 'shape' => 'InvalidCredentialsException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ClientTokenConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DeleteResource' => [ 'name' => 'DeleteResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteResourceInput', ], 'output' => [ 'shape' => 'DeleteResourceOutput', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'HandlerInternalFailureException', ], [ 'shape' => 'GeneralServiceException', ], [ 'shape' => 'NotUpdatableException', ], [ 'shape' => 'TypeNotFoundException', ], [ 'shape' => 'ConcurrentOperationException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'PrivateTypeException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NetworkFailureException', ], [ 'shape' => 'UnsupportedActionException', ], [ 'shape' => 'NotStabilizedException', ], [ 'shape' => 'ServiceInternalErrorException', ], [ 'shape' => 'HandlerFailureException', ], [ 'shape' => 'ServiceLimitExceededException', ], [ 'shape' => 'InvalidCredentialsException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ClientTokenConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetResource' => [ 'name' => 'GetResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourceInput', ], 'output' => [ 'shape' => 'GetResourceOutput', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'HandlerInternalFailureException', ], [ 'shape' => 'GeneralServiceException', ], [ 'shape' => 'NotUpdatableException', ], [ 'shape' => 'TypeNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'PrivateTypeException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NetworkFailureException', ], [ 'shape' => 'UnsupportedActionException', ], [ 'shape' => 'NotStabilizedException', ], [ 'shape' => 'ServiceInternalErrorException', ], [ 'shape' => 'HandlerFailureException', ], [ 'shape' => 'ServiceLimitExceededException', ], [ 'shape' => 'InvalidCredentialsException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetResourceRequestStatus' => [ 'name' => 'GetResourceRequestStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetResourceRequestStatusInput', ], 'output' => [ 'shape' => 'GetResourceRequestStatusOutput', ], 'errors' => [ [ 'shape' => 'RequestTokenNotFoundException', ], ], ], 'ListResourceRequests' => [ 'name' => 'ListResourceRequests', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourceRequestsInput', ], 'output' => [ 'shape' => 'ListResourceRequestsOutput', ], ], 'ListResources' => [ 'name' => 'ListResources', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListResourcesInput', ], 'output' => [ 'shape' => 'ListResourcesOutput', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'HandlerInternalFailureException', ], [ 'shape' => 'GeneralServiceException', ], [ 'shape' => 'NotUpdatableException', ], [ 'shape' => 'TypeNotFoundException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'PrivateTypeException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NetworkFailureException', ], [ 'shape' => 'UnsupportedActionException', ], [ 'shape' => 'NotStabilizedException', ], [ 'shape' => 'ServiceInternalErrorException', ], [ 'shape' => 'HandlerFailureException', ], [ 'shape' => 'ServiceLimitExceededException', ], [ 'shape' => 'InvalidCredentialsException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateResource' => [ 'name' => 'UpdateResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateResourceInput', ], 'output' => [ 'shape' => 'UpdateResourceOutput', ], 'errors' => [ [ 'shape' => 'AlreadyExistsException', ], [ 'shape' => 'HandlerInternalFailureException', ], [ 'shape' => 'GeneralServiceException', ], [ 'shape' => 'NotUpdatableException', ], [ 'shape' => 'TypeNotFoundException', ], [ 'shape' => 'ConcurrentOperationException', ], [ 'shape' => 'InvalidRequestException', ], [ 'shape' => 'PrivateTypeException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'NetworkFailureException', ], [ 'shape' => 'UnsupportedActionException', ], [ 'shape' => 'NotStabilizedException', ], [ 'shape' => 'ServiceInternalErrorException', ], [ 'shape' => 'HandlerFailureException', ], [ 'shape' => 'ServiceLimitExceededException', ], [ 'shape' => 'InvalidCredentialsException', ], [ 'shape' => 'ResourceConflictException', ], [ 'shape' => 'ClientTokenConflictException', ], [ 'shape' => 'ThrottlingException', ], ], ], ], 'shapes' => [ 'AlreadyExistsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'CancelResourceRequestInput' => [ 'type' => 'structure', 'required' => [ 'RequestToken', ], 'members' => [ 'RequestToken' => [ 'shape' => 'RequestToken', ], ], ], 'CancelResourceRequestOutput' => [ 'type' => 'structure', 'members' => [ 'ProgressEvent' => [ 'shape' => 'ProgressEvent', ], ], ], 'ClientToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[-A-Za-z0-9+/=]+', ], 'ClientTokenConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ConcurrentModificationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'ConcurrentOperationException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'CreateResourceInput' => [ 'type' => 'structure', 'required' => [ 'TypeName', 'DesiredState', ], 'members' => [ 'TypeName' => [ 'shape' => 'TypeName', ], 'TypeVersionId' => [ 'shape' => 'TypeVersionId', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'DesiredState' => [ 'shape' => 'Properties', ], ], ], 'CreateResourceOutput' => [ 'type' => 'structure', 'members' => [ 'ProgressEvent' => [ 'shape' => 'ProgressEvent', ], ], ], 'DeleteResourceInput' => [ 'type' => 'structure', 'required' => [ 'TypeName', 'Identifier', ], 'members' => [ 'TypeName' => [ 'shape' => 'TypeName', ], 'TypeVersionId' => [ 'shape' => 'TypeVersionId', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Identifier' => [ 'shape' => 'Identifier', ], ], ], 'DeleteResourceOutput' => [ 'type' => 'structure', 'members' => [ 'ProgressEvent' => [ 'shape' => 'ProgressEvent', ], ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '.+', ], 'GeneralServiceException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'GetResourceInput' => [ 'type' => 'structure', 'required' => [ 'TypeName', 'Identifier', ], 'members' => [ 'TypeName' => [ 'shape' => 'TypeName', ], 'TypeVersionId' => [ 'shape' => 'TypeVersionId', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'Identifier' => [ 'shape' => 'Identifier', ], ], ], 'GetResourceOutput' => [ 'type' => 'structure', 'members' => [ 'TypeName' => [ 'shape' => 'TypeName', ], 'ResourceDescription' => [ 'shape' => 'ResourceDescription', ], ], ], 'GetResourceRequestStatusInput' => [ 'type' => 'structure', 'required' => [ 'RequestToken', ], 'members' => [ 'RequestToken' => [ 'shape' => 'RequestToken', ], ], ], 'GetResourceRequestStatusOutput' => [ 'type' => 'structure', 'members' => [ 'ProgressEvent' => [ 'shape' => 'ProgressEvent', ], 'HooksProgressEvent' => [ 'shape' => 'HooksProgressEvent', ], ], ], 'HandlerErrorCode' => [ 'type' => 'string', 'enum' => [ 'NotUpdatable', 'InvalidRequest', 'AccessDenied', 'UnauthorizedTaggingOperation', 'InvalidCredentials', 'AlreadyExists', 'NotFound', 'ResourceConflict', 'Throttling', 'ServiceLimitExceeded', 'NotStabilized', 'GeneralServiceException', 'ServiceInternalError', 'ServiceTimeout', 'NetworkFailure', 'InternalFailure', ], ], 'HandlerFailureException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'HandlerInternalFailureException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'HandlerNextToken' => [ 'type' => 'string', 'max' => 4096, 'min' => 1, 'pattern' => '.+', ], 'HookFailureMode' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[-A-Za-z_]+', ], 'HookInvocationPoint' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[-A-Za-z_]+', ], 'HookProgressEvent' => [ 'type' => 'structure', 'members' => [ 'HookTypeName' => [ 'shape' => 'TypeName', ], 'HookTypeVersionId' => [ 'shape' => 'TypeVersionId', ], 'HookTypeArn' => [ 'shape' => 'HookTypeArn', ], 'InvocationPoint' => [ 'shape' => 'HookInvocationPoint', ], 'HookStatus' => [ 'shape' => 'HookStatus', ], 'HookEventTime' => [ 'shape' => 'Timestamp', ], 'HookStatusMessage' => [ 'shape' => 'StatusMessage', ], 'FailureMode' => [ 'shape' => 'HookFailureMode', ], ], ], 'HookStatus' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[-A-Za-z_]+', ], 'HookTypeArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => 'arn:aws.*:.+:.*:.*:.+', ], 'HooksProgressEvent' => [ 'type' => 'list', 'member' => [ 'shape' => 'HookProgressEvent', ], ], 'Identifier' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '.+', ], 'InvalidCredentialsException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'InvalidRequestException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ListResourceRequestsInput' => [ 'type' => 'structure', 'members' => [ 'MaxResults' => [ 'shape' => 'MaxResults', ], 'NextToken' => [ 'shape' => 'NextToken', ], 'ResourceRequestStatusFilter' => [ 'shape' => 'ResourceRequestStatusFilter', ], ], ], 'ListResourceRequestsOutput' => [ 'type' => 'structure', 'members' => [ 'ResourceRequestStatusSummaries' => [ 'shape' => 'ResourceRequestStatusSummaries', ], 'NextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListResourcesInput' => [ 'type' => 'structure', 'required' => [ 'TypeName', ], 'members' => [ 'TypeName' => [ 'shape' => 'TypeName', ], 'TypeVersionId' => [ 'shape' => 'TypeVersionId', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'NextToken' => [ 'shape' => 'HandlerNextToken', ], 'MaxResults' => [ 'shape' => 'MaxResults', ], 'ResourceModel' => [ 'shape' => 'Properties', ], ], ], 'ListResourcesOutput' => [ 'type' => 'structure', 'members' => [ 'TypeName' => [ 'shape' => 'TypeName', ], 'ResourceDescriptions' => [ 'shape' => 'ResourceDescriptions', ], 'NextToken' => [ 'shape' => 'HandlerNextToken', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'NetworkFailureException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'NextToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, 'pattern' => '[-A-Za-z0-9+/=]+', ], 'NotStabilizedException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'NotUpdatableException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Operation' => [ 'type' => 'string', 'enum' => [ 'CREATE', 'DELETE', 'UPDATE', ], ], 'OperationStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'IN_PROGRESS', 'SUCCESS', 'FAILED', 'CANCEL_IN_PROGRESS', 'CANCEL_COMPLETE', ], ], 'OperationStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'OperationStatus', ], ], 'Operations' => [ 'type' => 'list', 'member' => [ 'shape' => 'Operation', ], ], 'PatchDocument' => [ 'type' => 'string', 'max' => 262144, 'min' => 1, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'PrivateTypeException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ProgressEvent' => [ 'type' => 'structure', 'members' => [ 'TypeName' => [ 'shape' => 'TypeName', ], 'Identifier' => [ 'shape' => 'Identifier', ], 'RequestToken' => [ 'shape' => 'RequestToken', ], 'HooksRequestToken' => [ 'shape' => 'RequestToken', ], 'Operation' => [ 'shape' => 'Operation', ], 'OperationStatus' => [ 'shape' => 'OperationStatus', ], 'EventTime' => [ 'shape' => 'Timestamp', ], 'ResourceModel' => [ 'shape' => 'Properties', ], 'StatusMessage' => [ 'shape' => 'StatusMessage', ], 'ErrorCode' => [ 'shape' => 'HandlerErrorCode', ], 'RetryAfter' => [ 'shape' => 'Timestamp', ], ], ], 'Properties' => [ 'type' => 'string', 'max' => 262144, 'min' => 1, 'pattern' => '[\\s\\S]*', 'sensitive' => true, ], 'RequestToken' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[-A-Za-z0-9+/=]+', ], 'RequestTokenNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceConflictException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceDescription' => [ 'type' => 'structure', 'members' => [ 'Identifier' => [ 'shape' => 'Identifier', ], 'Properties' => [ 'shape' => 'Properties', ], ], ], 'ResourceDescriptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceDescription', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ResourceRequestStatusFilter' => [ 'type' => 'structure', 'members' => [ 'Operations' => [ 'shape' => 'Operations', ], 'OperationStatuses' => [ 'shape' => 'OperationStatuses', ], ], ], 'ResourceRequestStatusSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProgressEvent', ], ], 'RoleArn' => [ 'type' => 'string', 'max' => 2048, 'min' => 20, 'pattern' => 'arn:.+:iam::[0-9]{12}:role/.+', ], 'ServiceInternalErrorException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'ServiceLimitExceededException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'StatusMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 0, 'pattern' => '[\\s\\S]*', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'TypeName' => [ 'type' => 'string', 'max' => 196, 'min' => 10, 'pattern' => '[A-Za-z0-9]{2,64}::[A-Za-z0-9]{2,64}::[A-Za-z0-9]{2,64}', ], 'TypeNotFoundException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'TypeVersionId' => [ 'type' => 'string', 'max' => 128, 'min' => 1, 'pattern' => '[A-Za-z0-9-]+', ], 'UnsupportedActionException' => [ 'type' => 'structure', 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'UpdateResourceInput' => [ 'type' => 'structure', 'required' => [ 'TypeName', 'Identifier', 'PatchDocument', ], 'members' => [ 'TypeName' => [ 'shape' => 'TypeName', ], 'TypeVersionId' => [ 'shape' => 'TypeVersionId', ], 'RoleArn' => [ 'shape' => 'RoleArn', ], 'ClientToken' => [ 'shape' => 'ClientToken', 'idempotencyToken' => true, ], 'Identifier' => [ 'shape' => 'Identifier', ], 'PatchDocument' => [ 'shape' => 'PatchDocument', ], ], ], 'UpdateResourceOutput' => [ 'type' => 'structure', 'members' => [ 'ProgressEvent' => [ 'shape' => 'ProgressEvent', ], ], ], ],];
