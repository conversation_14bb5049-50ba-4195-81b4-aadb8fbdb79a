<?php
// This file was auto-generated from sdk-root/src/data/security-ir/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'security-ir', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'Security Incident Response', 'serviceId' => 'Security IR', 'signatureVersion' => 'v4', 'signingName' => 'security-ir', 'uid' => 'security-ir-2018-05-10', ], 'operations' => [ 'BatchGetMemberAccountDetails' => [ 'name' => 'BatchGetMemberAccountDetails', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/membership/{membershipId}/batch-member-details', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchGetMemberAccountDetailsRequest', ], 'output' => [ 'shape' => 'BatchGetMemberAccountDetailsResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'CancelMembership' => [ 'name' => 'CancelMembership', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/membership/{membershipId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelMembershipRequest', ], 'output' => [ 'shape' => 'CancelMembershipResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], 'idempotent' => true, ], 'CloseCase' => [ 'name' => 'CloseCase', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/cases/{caseId}/close-case', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CloseCaseRequest', ], 'output' => [ 'shape' => 'CloseCaseResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'CreateCase' => [ 'name' => 'CreateCase', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/create-case', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateCaseRequest', ], 'output' => [ 'shape' => 'CreateCaseResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], 'idempotent' => true, ], 'CreateCaseComment' => [ 'name' => 'CreateCaseComment', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/cases/{caseId}/create-comment', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateCaseCommentRequest', ], 'output' => [ 'shape' => 'CreateCaseCommentResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], 'idempotent' => true, ], 'CreateMembership' => [ 'name' => 'CreateMembership', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/membership', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreateMembershipRequest', ], 'output' => [ 'shape' => 'CreateMembershipResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], 'idempotent' => true, ], 'GetCase' => [ 'name' => 'GetCase', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/cases/{caseId}/get-case', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetCaseRequest', ], 'output' => [ 'shape' => 'GetCaseResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'GetCaseAttachmentDownloadUrl' => [ 'name' => 'GetCaseAttachmentDownloadUrl', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/cases/{caseId}/get-presigned-url/{attachmentId}', 'responseCode' => 201, ], 'input' => [ 'shape' => 'GetCaseAttachmentDownloadUrlRequest', ], 'output' => [ 'shape' => 'GetCaseAttachmentDownloadUrlResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'GetCaseAttachmentUploadUrl' => [ 'name' => 'GetCaseAttachmentUploadUrl', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/cases/{caseId}/get-presigned-url', 'responseCode' => 201, ], 'input' => [ 'shape' => 'GetCaseAttachmentUploadUrlRequest', ], 'output' => [ 'shape' => 'GetCaseAttachmentUploadUrlResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], 'idempotent' => true, ], 'GetMembership' => [ 'name' => 'GetMembership', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/membership/{membershipId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMembershipRequest', ], 'output' => [ 'shape' => 'GetMembershipResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'ListCaseEdits' => [ 'name' => 'ListCaseEdits', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/cases/{caseId}/list-case-edits', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCaseEditsRequest', ], 'output' => [ 'shape' => 'ListCaseEditsResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'ListCases' => [ 'name' => 'ListCases', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/list-cases', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCasesRequest', ], 'output' => [ 'shape' => 'ListCasesResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'ListComments' => [ 'name' => 'ListComments', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/cases/{caseId}/list-comments', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListCommentsRequest', ], 'output' => [ 'shape' => 'ListCommentsResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'ListMemberships' => [ 'name' => 'ListMemberships', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/memberships', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMembershipsRequest', ], 'output' => [ 'shape' => 'ListMembershipsResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/v1/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/tags/{resourceArn}', 'responseCode' => 204, ], 'input' => [ 'shape' => 'TagResourceInput', ], 'output' => [ 'shape' => 'TagResourceOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/v1/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'output' => [ 'shape' => 'UntagResourceOutput', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'InvalidTokenException', ], ], 'idempotent' => true, ], 'UpdateCase' => [ 'name' => 'UpdateCase', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/cases/{caseId}/update-case', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCaseRequest', ], 'output' => [ 'shape' => 'UpdateCaseResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'UpdateCaseComment' => [ 'name' => 'UpdateCaseComment', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/cases/{caseId}/update-case-comment/{commentId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateCaseCommentRequest', ], 'output' => [ 'shape' => 'UpdateCaseCommentResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], 'idempotent' => true, ], 'UpdateCaseStatus' => [ 'name' => 'UpdateCaseStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/cases/{caseId}/update-case-status', 'responseCode' => 201, ], 'input' => [ 'shape' => 'UpdateCaseStatusRequest', ], 'output' => [ 'shape' => 'UpdateCaseStatusResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], ], 'UpdateMembership' => [ 'name' => 'UpdateMembership', 'http' => [ 'method' => 'PUT', 'requestUri' => '/v1/membership/{membershipId}/update-membership', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMembershipRequest', ], 'output' => [ 'shape' => 'UpdateMembershipResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], 'idempotent' => true, ], 'UpdateResolverType' => [ 'name' => 'UpdateResolverType', 'http' => [ 'method' => 'POST', 'requestUri' => '/v1/cases/{caseId}/update-resolver-type', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateResolverTypeRequest', ], 'output' => [ 'shape' => 'UpdateResolverTypeResponse', ], 'errors' => [ [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'SecurityIncidentResponseNotActiveException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InvalidTokenException', ], ], ], ], 'shapes' => [ 'AWSAccountId' => [ 'type' => 'string', 'max' => 12, 'min' => 12, 'pattern' => '[0-9]{12}', ], 'AWSAccountIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'AWSAccountId', ], 'max' => 100, 'min' => 1, ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'Arn' => [ 'type' => 'string', 'max' => 1010, 'min' => 12, 'pattern' => 'arn:aws:security-ir:\\w+?-\\w+?-\\d+:[0-9]{12}:(membership/m-[a-z0-9]{10,32}|case/[0-9]{10})', ], 'AttachmentId' => [ 'type' => 'string', 'pattern' => '[0-9a-fA-F]{8}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{4}\\b-[0-9a-fA-F]{12}', ], 'AwsRegion' => [ 'type' => 'string', 'enum' => [ 'af-south-1', 'ap-east-1', 'ap-northeast-1', 'ap-northeast-2', 'ap-northeast-3', 'ap-south-1', 'ap-south-2', 'ap-southeast-1', 'ap-southeast-2', 'ap-southeast-3', 'ap-southeast-4', 'ap-southeast-5', 'ap-southeast-7', 'ca-central-1', 'ca-west-1', 'cn-north-1', 'cn-northwest-1', 'eu-central-1', 'eu-central-2', 'eu-north-1', 'eu-south-1', 'eu-south-2', 'eu-west-1', 'eu-west-2', 'eu-west-3', 'il-central-1', 'me-central-1', 'me-south-1', 'mx-central-1', 'sa-east-1', 'us-east-1', 'us-east-2', 'us-west-1', 'us-west-2', ], ], 'AwsService' => [ 'type' => 'string', 'max' => 50, 'min' => 3, 'pattern' => '[a-zA-Z0-9 -.():]+', ], 'BatchGetMemberAccountDetailsRequest' => [ 'type' => 'structure', 'required' => [ 'membershipId', 'accountIds', ], 'members' => [ 'membershipId' => [ 'shape' => 'MembershipId', 'location' => 'uri', 'locationName' => 'membershipId', ], 'accountIds' => [ 'shape' => 'AWSAccountIds', ], ], ], 'BatchGetMemberAccountDetailsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'GetMembershipAccountDetailItems', ], 'errors' => [ 'shape' => 'GetMembershipAccountDetailErrors', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'CancelMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'membershipId', ], 'members' => [ 'membershipId' => [ 'shape' => 'MembershipId', 'location' => 'uri', 'locationName' => 'membershipId', ], ], ], 'CancelMembershipResponse' => [ 'type' => 'structure', 'required' => [ 'membershipId', ], 'members' => [ 'membershipId' => [ 'shape' => 'MembershipId', ], ], ], 'CaseArn' => [ 'type' => 'string', 'max' => 80, 'min' => 12, 'pattern' => 'arn:aws:security-ir:\\w+?-\\w+?-\\d+:[0-9]{12}:case/[0-9]{10}', ], 'CaseAttachmentAttributes' => [ 'type' => 'structure', 'required' => [ 'attachmentId', 'fileName', 'attachmentStatus', 'creator', 'createdDate', ], 'members' => [ 'attachmentId' => [ 'shape' => 'AttachmentId', ], 'fileName' => [ 'shape' => 'FileName', ], 'attachmentStatus' => [ 'shape' => 'CaseAttachmentStatus', ], 'creator' => [ 'shape' => 'PrincipalId', ], 'createdDate' => [ 'shape' => 'Timestamp', ], ], ], 'CaseAttachmentStatus' => [ 'type' => 'string', 'enum' => [ 'Verified', 'Failed', 'Pending', ], ], 'CaseAttachmentsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CaseAttachmentAttributes', ], 'max' => 50, 'min' => 0, ], 'CaseDescription' => [ 'type' => 'string', 'max' => 8000, 'min' => 1, 'sensitive' => true, ], 'CaseEditAction' => [ 'type' => 'string', 'max' => 100, 'min' => 1, ], 'CaseEditItem' => [ 'type' => 'structure', 'members' => [ 'eventTimestamp' => [ 'shape' => 'Timestamp', ], 'principal' => [ 'shape' => 'String', ], 'action' => [ 'shape' => 'CaseEditAction', ], 'message' => [ 'shape' => 'CaseEditMessage', ], ], ], 'CaseEditItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'CaseEditItem', ], ], 'CaseEditMessage' => [ 'type' => 'string', 'max' => 4096, 'min' => 10, ], 'CaseId' => [ 'type' => 'string', 'max' => 32, 'min' => 10, 'pattern' => '\\d{10,32}.*', ], 'CaseStatus' => [ 'type' => 'string', 'enum' => [ 'Submitted', 'Acknowledged', 'Detection and Analysis', 'Containment, Eradication and Recovery', 'Post-incident Activities', 'Ready to Close', 'Closed', ], ], 'CaseTitle' => [ 'type' => 'string', 'max' => 300, 'min' => 1, 'sensitive' => true, ], 'CloseCaseRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], ], ], 'CloseCaseResponse' => [ 'type' => 'structure', 'members' => [ 'caseStatus' => [ 'shape' => 'CaseStatus', ], 'closedDate' => [ 'shape' => 'Timestamp', ], ], ], 'ClosureCode' => [ 'type' => 'string', 'enum' => [ 'Investigation Completed', 'Not Resolved', 'False Positive', 'Duplicate', ], ], 'CommentBody' => [ 'type' => 'string', 'max' => 12000, 'min' => 1, 'sensitive' => true, ], 'CommentId' => [ 'type' => 'string', 'max' => 6, 'min' => 6, 'pattern' => '\\d{6}', ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ContentLength' => [ 'type' => 'long', 'box' => true, 'max' => 104857600, 'min' => 1, ], 'CreateCaseCommentRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', 'body', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], 'clientToken' => [ 'shape' => 'CreateCaseCommentRequestClientTokenString', 'idempotencyToken' => true, ], 'body' => [ 'shape' => 'CommentBody', ], ], ], 'CreateCaseCommentRequestClientTokenString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'CreateCaseCommentResponse' => [ 'type' => 'structure', 'required' => [ 'commentId', ], 'members' => [ 'commentId' => [ 'shape' => 'CommentId', ], ], ], 'CreateCaseRequest' => [ 'type' => 'structure', 'required' => [ 'resolverType', 'title', 'description', 'engagementType', 'reportedIncidentStartDate', 'impactedAccounts', 'watchers', ], 'members' => [ 'clientToken' => [ 'shape' => 'CreateCaseRequestClientTokenString', 'idempotencyToken' => true, ], 'resolverType' => [ 'shape' => 'ResolverType', ], 'title' => [ 'shape' => 'CaseTitle', ], 'description' => [ 'shape' => 'CaseDescription', ], 'engagementType' => [ 'shape' => 'EngagementType', ], 'reportedIncidentStartDate' => [ 'shape' => 'Timestamp', ], 'impactedAccounts' => [ 'shape' => 'ImpactedAccounts', ], 'watchers' => [ 'shape' => 'Watchers', ], 'threatActorIpAddresses' => [ 'shape' => 'ThreatActorIpList', ], 'impactedServices' => [ 'shape' => 'ImpactedServicesList', ], 'impactedAwsRegions' => [ 'shape' => 'ImpactedAwsRegionList', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateCaseRequestClientTokenString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'CreateCaseResponse' => [ 'type' => 'structure', 'required' => [ 'caseId', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', ], ], ], 'CreateMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'membershipName', 'incidentResponseTeam', ], 'members' => [ 'clientToken' => [ 'shape' => 'CreateMembershipRequestClientTokenString', 'idempotencyToken' => true, ], 'membershipName' => [ 'shape' => 'MembershipName', ], 'incidentResponseTeam' => [ 'shape' => 'IncidentResponseTeam', ], 'optInFeatures' => [ 'shape' => 'OptInFeatures', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateMembershipRequestClientTokenString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'CreateMembershipResponse' => [ 'type' => 'structure', 'required' => [ 'membershipId', ], 'members' => [ 'membershipId' => [ 'shape' => 'MembershipId', ], ], ], 'CustomerType' => [ 'type' => 'string', 'enum' => [ 'Standalone', 'Organization', ], ], 'EmailAddress' => [ 'type' => 'string', 'max' => 254, 'min' => 6, 'pattern' => '[a-zA-Z0-9.!#$%&\'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\\.[a-zA-Z0-9-]+)*', 'sensitive' => true, ], 'EngagementType' => [ 'type' => 'string', 'enum' => [ 'Security Incident', 'Investigation', ], ], 'FileName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z0-9._-]+', 'sensitive' => true, ], 'GetCaseAttachmentDownloadUrlRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', 'attachmentId', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], 'attachmentId' => [ 'shape' => 'AttachmentId', 'location' => 'uri', 'locationName' => 'attachmentId', ], ], ], 'GetCaseAttachmentDownloadUrlResponse' => [ 'type' => 'structure', 'required' => [ 'attachmentPresignedUrl', ], 'members' => [ 'attachmentPresignedUrl' => [ 'shape' => 'Url', ], ], ], 'GetCaseAttachmentUploadUrlRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', 'fileName', 'contentLength', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], 'fileName' => [ 'shape' => 'FileName', ], 'contentLength' => [ 'shape' => 'ContentLength', ], 'clientToken' => [ 'shape' => 'GetCaseAttachmentUploadUrlRequestClientTokenString', 'idempotencyToken' => true, ], ], ], 'GetCaseAttachmentUploadUrlRequestClientTokenString' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'GetCaseAttachmentUploadUrlResponse' => [ 'type' => 'structure', 'required' => [ 'attachmentPresignedUrl', ], 'members' => [ 'attachmentPresignedUrl' => [ 'shape' => 'Url', ], ], ], 'GetCaseRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], ], ], 'GetCaseResponse' => [ 'type' => 'structure', 'members' => [ 'title' => [ 'shape' => 'CaseTitle', ], 'caseArn' => [ 'shape' => 'CaseArn', ], 'description' => [ 'shape' => 'CaseDescription', ], 'caseStatus' => [ 'shape' => 'CaseStatus', ], 'engagementType' => [ 'shape' => 'EngagementType', ], 'reportedIncidentStartDate' => [ 'shape' => 'Timestamp', ], 'actualIncidentStartDate' => [ 'shape' => 'Timestamp', ], 'impactedAwsRegions' => [ 'shape' => 'ImpactedAwsRegionList', ], 'threatActorIpAddresses' => [ 'shape' => 'ThreatActorIpList', ], 'pendingAction' => [ 'shape' => 'PendingAction', ], 'impactedAccounts' => [ 'shape' => 'ImpactedAccounts', ], 'watchers' => [ 'shape' => 'Watchers', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'closureCode' => [ 'shape' => 'ClosureCode', ], 'resolverType' => [ 'shape' => 'ResolverType', ], 'impactedServices' => [ 'shape' => 'ImpactedServicesList', ], 'caseAttachments' => [ 'shape' => 'CaseAttachmentsList', ], 'closedDate' => [ 'shape' => 'Timestamp', ], ], ], 'GetMembershipAccountDetailError' => [ 'type' => 'structure', 'required' => [ 'accountId', 'error', 'message', ], 'members' => [ 'accountId' => [ 'shape' => 'AWSAccountId', ], 'error' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'GetMembershipAccountDetailErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'GetMembershipAccountDetailError', ], 'max' => 100, 'min' => 0, ], 'GetMembershipAccountDetailItem' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AWSAccountId', ], 'relationshipStatus' => [ 'shape' => 'MembershipAccountRelationshipStatus', ], 'relationshipType' => [ 'shape' => 'MembershipAccountRelationshipType', ], ], ], 'GetMembershipAccountDetailItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'GetMembershipAccountDetailItem', ], 'max' => 100, 'min' => 0, ], 'GetMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'membershipId', ], 'members' => [ 'membershipId' => [ 'shape' => 'MembershipId', 'location' => 'uri', 'locationName' => 'membershipId', ], ], ], 'GetMembershipResponse' => [ 'type' => 'structure', 'required' => [ 'membershipId', ], 'members' => [ 'membershipId' => [ 'shape' => 'MembershipId', ], 'accountId' => [ 'shape' => 'AWSAccountId', ], 'region' => [ 'shape' => 'AwsRegion', ], 'membershipName' => [ 'shape' => 'MembershipName', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'membershipStatus' => [ 'shape' => 'MembershipStatus', ], 'membershipActivationTimestamp' => [ 'shape' => 'Timestamp', ], 'membershipDeactivationTimestamp' => [ 'shape' => 'Timestamp', ], 'customerType' => [ 'shape' => 'CustomerType', ], 'numberOfAccountsCovered' => [ 'shape' => 'Long', ], 'incidentResponseTeam' => [ 'shape' => 'IncidentResponseTeam', ], 'optInFeatures' => [ 'shape' => 'OptInFeatures', ], ], ], 'IPAddress' => [ 'type' => 'string', 'pattern' => '(?:(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))|(?:(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4})|(?:(?:[A-F0-9]{1,4}:){6}(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))', 'sensitive' => true, ], 'ImpactedAccounts' => [ 'type' => 'list', 'member' => [ 'shape' => 'AWSAccountId', ], 'max' => 200, 'min' => 0, ], 'ImpactedAwsRegion' => [ 'type' => 'structure', 'required' => [ 'region', ], 'members' => [ 'region' => [ 'shape' => 'AwsRegion', ], ], ], 'ImpactedAwsRegionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ImpactedAwsRegion', ], 'max' => 50, 'min' => 0, ], 'ImpactedServicesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsService', ], 'max' => 600, 'min' => 0, ], 'IncidentResponder' => [ 'type' => 'structure', 'required' => [ 'name', 'jobTitle', 'email', ], 'members' => [ 'name' => [ 'shape' => 'IncidentResponderName', ], 'jobTitle' => [ 'shape' => 'JobTitle', ], 'email' => [ 'shape' => 'EmailAddress', ], ], ], 'IncidentResponderName' => [ 'type' => 'string', 'max' => 50, 'min' => 3, 'sensitive' => true, ], 'IncidentResponseTeam' => [ 'type' => 'list', 'member' => [ 'shape' => 'IncidentResponder', ], 'max' => 10, 'min' => 2, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'InvalidTokenException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 423, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'JobTitle' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'ListCaseEditsRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', ], 'members' => [ 'nextToken' => [ 'shape' => 'ListCaseEditsRequestNextTokenString', ], 'maxResults' => [ 'shape' => 'ListCaseEditsRequestMaxResultsInteger', ], 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], ], ], 'ListCaseEditsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'ListCaseEditsRequestNextTokenString' => [ 'type' => 'string', 'max' => 2000, 'min' => 0, ], 'ListCaseEditsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'items' => [ 'shape' => 'CaseEditItems', ], 'total' => [ 'shape' => 'Integer', ], ], ], 'ListCasesItem' => [ 'type' => 'structure', 'required' => [ 'caseId', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'title' => [ 'shape' => 'CaseTitle', ], 'caseArn' => [ 'shape' => 'CaseArn', ], 'engagementType' => [ 'shape' => 'EngagementType', ], 'caseStatus' => [ 'shape' => 'CaseStatus', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'closedDate' => [ 'shape' => 'Timestamp', ], 'resolverType' => [ 'shape' => 'ResolverType', ], 'pendingAction' => [ 'shape' => 'PendingAction', ], ], ], 'ListCasesItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListCasesItem', ], ], 'ListCasesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'ListCasesRequestNextTokenString', ], 'maxResults' => [ 'shape' => 'ListCasesRequestMaxResultsInteger', ], ], ], 'ListCasesRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'ListCasesRequestNextTokenString' => [ 'type' => 'string', 'max' => 2000, 'min' => 0, ], 'ListCasesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'items' => [ 'shape' => 'ListCasesItems', ], 'total' => [ 'shape' => 'Long', ], ], ], 'ListCommentsItem' => [ 'type' => 'structure', 'required' => [ 'commentId', ], 'members' => [ 'commentId' => [ 'shape' => 'CommentId', ], 'createdDate' => [ 'shape' => 'Timestamp', ], 'lastUpdatedDate' => [ 'shape' => 'Timestamp', ], 'creator' => [ 'shape' => 'PrincipalId', ], 'lastUpdatedBy' => [ 'shape' => 'PrincipalId', ], 'body' => [ 'shape' => 'CommentBody', ], ], ], 'ListCommentsItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListCommentsItem', ], ], 'ListCommentsRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', ], 'members' => [ 'nextToken' => [ 'shape' => 'ListCommentsRequestNextTokenString', ], 'maxResults' => [ 'shape' => 'ListCommentsRequestMaxResultsInteger', ], 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], ], ], 'ListCommentsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'ListCommentsRequestNextTokenString' => [ 'type' => 'string', 'max' => 2000, 'min' => 0, ], 'ListCommentsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'items' => [ 'shape' => 'ListCommentsItems', ], 'total' => [ 'shape' => 'Integer', ], ], ], 'ListMembershipItem' => [ 'type' => 'structure', 'required' => [ 'membershipId', ], 'members' => [ 'membershipId' => [ 'shape' => 'MembershipId', ], 'accountId' => [ 'shape' => 'AWSAccountId', ], 'region' => [ 'shape' => 'AwsRegion', ], 'membershipArn' => [ 'shape' => 'MembershipArn', ], 'membershipStatus' => [ 'shape' => 'MembershipStatus', ], ], ], 'ListMembershipItems' => [ 'type' => 'list', 'member' => [ 'shape' => 'ListMembershipItem', ], ], 'ListMembershipsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'ListMembershipsRequestNextTokenString', ], 'maxResults' => [ 'shape' => 'ListMembershipsRequestMaxResultsInteger', ], ], ], 'ListMembershipsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'ListMembershipsRequestNextTokenString' => [ 'type' => 'string', 'max' => 2000, 'min' => 0, ], 'ListMembershipsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', ], 'items' => [ 'shape' => 'ListMembershipItems', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'Long' => [ 'type' => 'long', 'box' => true, ], 'MembershipAccountRelationshipStatus' => [ 'type' => 'string', 'enum' => [ 'Associated', 'Disassociated', ], ], 'MembershipAccountRelationshipType' => [ 'type' => 'string', 'enum' => [ 'Organization', ], ], 'MembershipArn' => [ 'type' => 'string', 'max' => 80, 'min' => 12, 'pattern' => 'arn:aws:security-ir:\\w+?-\\w+?-\\d+:[0-9]{12}:membership/m-[a-z0-9]{10,32}', ], 'MembershipId' => [ 'type' => 'string', 'max' => 34, 'min' => 12, 'pattern' => 'm-[a-z0-9]{10,32}', ], 'MembershipName' => [ 'type' => 'string', 'max' => 50, 'min' => 3, 'sensitive' => true, ], 'MembershipStatus' => [ 'type' => 'string', 'enum' => [ 'Active', 'Cancelled', 'Terminated', ], ], 'OptInFeature' => [ 'type' => 'structure', 'required' => [ 'featureName', 'isEnabled', ], 'members' => [ 'featureName' => [ 'shape' => 'OptInFeatureName', ], 'isEnabled' => [ 'shape' => 'Boolean', ], ], ], 'OptInFeatureName' => [ 'type' => 'string', 'enum' => [ 'Triage', ], ], 'OptInFeatures' => [ 'type' => 'list', 'member' => [ 'shape' => 'OptInFeature', ], 'max' => 2, 'min' => 1, ], 'PendingAction' => [ 'type' => 'string', 'enum' => [ 'Customer', 'None', ], ], 'PersonName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'PrincipalId' => [ 'type' => 'string', 'pattern' => '.*((^AWS Responder)|(^\\d{12}$)|(^security-ir.amazonaws.com)).*', ], 'ResolverType' => [ 'type' => 'string', 'enum' => [ 'AWS', 'Self', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SecurityIncidentResponseNotActiveException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'SelfManagedCaseStatus' => [ 'type' => 'string', 'enum' => [ 'Submitted', 'Detection and Analysis', 'Containment, Eradication and Recovery', 'Post-incident Activities', ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', 'serviceCode', 'quotaCode', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeys' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThreatActorIp' => [ 'type' => 'structure', 'required' => [ 'ipAddress', ], 'members' => [ 'ipAddress' => [ 'shape' => 'IPAddress', ], 'userAgent' => [ 'shape' => 'UserAgent', ], ], ], 'ThreatActorIpList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ThreatActorIp', ], 'max' => 200, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', 'location' => 'header', 'locationName' => 'Retry-After', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'Arn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeys', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCaseCommentRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', 'commentId', 'body', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], 'commentId' => [ 'shape' => 'CommentId', 'location' => 'uri', 'locationName' => 'commentId', ], 'body' => [ 'shape' => 'CommentBody', ], ], ], 'UpdateCaseCommentResponse' => [ 'type' => 'structure', 'required' => [ 'commentId', ], 'members' => [ 'commentId' => [ 'shape' => 'CommentId', ], 'body' => [ 'shape' => 'CommentBody', ], ], ], 'UpdateCaseRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], 'title' => [ 'shape' => 'CaseTitle', ], 'description' => [ 'shape' => 'CaseDescription', ], 'reportedIncidentStartDate' => [ 'shape' => 'Timestamp', ], 'actualIncidentStartDate' => [ 'shape' => 'Timestamp', ], 'engagementType' => [ 'shape' => 'EngagementType', ], 'watchersToAdd' => [ 'shape' => 'Watchers', ], 'watchersToDelete' => [ 'shape' => 'Watchers', ], 'threatActorIpAddressesToAdd' => [ 'shape' => 'ThreatActorIpList', ], 'threatActorIpAddressesToDelete' => [ 'shape' => 'ThreatActorIpList', ], 'impactedServicesToAdd' => [ 'shape' => 'ImpactedServicesList', ], 'impactedServicesToDelete' => [ 'shape' => 'ImpactedServicesList', ], 'impactedAwsRegionsToAdd' => [ 'shape' => 'ImpactedAwsRegionList', ], 'impactedAwsRegionsToDelete' => [ 'shape' => 'ImpactedAwsRegionList', ], 'impactedAccountsToAdd' => [ 'shape' => 'ImpactedAccounts', ], 'impactedAccountsToDelete' => [ 'shape' => 'ImpactedAccounts', ], ], ], 'UpdateCaseResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateCaseStatusRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', 'caseStatus', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], 'caseStatus' => [ 'shape' => 'SelfManagedCaseStatus', ], ], ], 'UpdateCaseStatusResponse' => [ 'type' => 'structure', 'members' => [ 'caseStatus' => [ 'shape' => 'SelfManagedCaseStatus', ], ], ], 'UpdateMembershipRequest' => [ 'type' => 'structure', 'required' => [ 'membershipId', ], 'members' => [ 'membershipId' => [ 'shape' => 'MembershipId', 'location' => 'uri', 'locationName' => 'membershipId', ], 'membershipName' => [ 'shape' => 'MembershipName', ], 'incidentResponseTeam' => [ 'shape' => 'IncidentResponseTeam', ], 'optInFeatures' => [ 'shape' => 'OptInFeatures', ], ], ], 'UpdateMembershipResponse' => [ 'type' => 'structure', 'members' => [], ], 'UpdateResolverTypeRequest' => [ 'type' => 'structure', 'required' => [ 'caseId', 'resolverType', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', 'location' => 'uri', 'locationName' => 'caseId', ], 'resolverType' => [ 'shape' => 'ResolverType', ], ], ], 'UpdateResolverTypeResponse' => [ 'type' => 'structure', 'required' => [ 'caseId', ], 'members' => [ 'caseId' => [ 'shape' => 'CaseId', ], 'caseStatus' => [ 'shape' => 'CaseStatus', ], 'resolverType' => [ 'shape' => 'ResolverType', ], ], ], 'Url' => [ 'type' => 'string', 'pattern' => 'https?://(?:www.)?[a-zA-Z0-9@:._+~#=-]{2,256}\\.[a-z]{2,6}\\b(?:[-a-zA-Z0-9@:%_+.~#?&/=]{0,2048})', 'sensitive' => true, ], 'UserAgent' => [ 'type' => 'string', 'max' => 500, 'min' => 1, ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'name', 'message', ], 'members' => [ 'name' => [ 'shape' => 'String', ], 'message' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'UNKNOWN_OPERATION', 'CANNOT_PARSE', 'FIELD_VALIDATION_FAILED', 'OTHER', ], ], 'Watcher' => [ 'type' => 'structure', 'required' => [ 'email', ], 'members' => [ 'email' => [ 'shape' => 'EmailAddress', ], 'name' => [ 'shape' => 'PersonName', ], 'jobTitle' => [ 'shape' => 'JobTitle', ], ], ], 'Watchers' => [ 'type' => 'list', 'member' => [ 'shape' => 'Watcher', ], 'max' => 30, 'min' => 0, ], ],];
