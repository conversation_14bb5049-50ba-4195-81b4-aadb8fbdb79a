<?php
// This file was auto-generated from sdk-root/src/data/compute-optimizer/2019-11-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2019-11-01', 'endpointPrefix' => 'compute-optimizer', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceFullName' => 'AWS Compute Optimizer', 'serviceId' => 'Compute Optimizer', 'signatureVersion' => 'v4', 'signingName' => 'compute-optimizer', 'targetPrefix' => 'ComputeOptimizerService', 'uid' => 'compute-optimizer-2019-11-01', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'DeleteRecommendationPreferences' => [ 'name' => 'DeleteRecommendationPreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteRecommendationPreferencesRequest', ], 'output' => [ 'shape' => 'DeleteRecommendationPreferencesResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'DescribeRecommendationExportJobs' => [ 'name' => 'DescribeRecommendationExportJobs', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DescribeRecommendationExportJobsRequest', ], 'output' => [ 'shape' => 'DescribeRecommendationExportJobsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'ExportAutoScalingGroupRecommendations' => [ 'name' => 'ExportAutoScalingGroupRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExportAutoScalingGroupRecommendationsRequest', ], 'output' => [ 'shape' => 'ExportAutoScalingGroupRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ExportEBSVolumeRecommendations' => [ 'name' => 'ExportEBSVolumeRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExportEBSVolumeRecommendationsRequest', ], 'output' => [ 'shape' => 'ExportEBSVolumeRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ExportEC2InstanceRecommendations' => [ 'name' => 'ExportEC2InstanceRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExportEC2InstanceRecommendationsRequest', ], 'output' => [ 'shape' => 'ExportEC2InstanceRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ExportECSServiceRecommendations' => [ 'name' => 'ExportECSServiceRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExportECSServiceRecommendationsRequest', ], 'output' => [ 'shape' => 'ExportECSServiceRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ExportIdleRecommendations' => [ 'name' => 'ExportIdleRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExportIdleRecommendationsRequest', ], 'output' => [ 'shape' => 'ExportIdleRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ExportLambdaFunctionRecommendations' => [ 'name' => 'ExportLambdaFunctionRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExportLambdaFunctionRecommendationsRequest', ], 'output' => [ 'shape' => 'ExportLambdaFunctionRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ExportLicenseRecommendations' => [ 'name' => 'ExportLicenseRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExportLicenseRecommendationsRequest', ], 'output' => [ 'shape' => 'ExportLicenseRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'ExportRDSDatabaseRecommendations' => [ 'name' => 'ExportRDSDatabaseRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ExportRDSDatabaseRecommendationsRequest', ], 'output' => [ 'shape' => 'ExportRDSDatabaseRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetAutoScalingGroupRecommendations' => [ 'name' => 'GetAutoScalingGroupRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetAutoScalingGroupRecommendationsRequest', ], 'output' => [ 'shape' => 'GetAutoScalingGroupRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetEBSVolumeRecommendations' => [ 'name' => 'GetEBSVolumeRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEBSVolumeRecommendationsRequest', ], 'output' => [ 'shape' => 'GetEBSVolumeRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetEC2InstanceRecommendations' => [ 'name' => 'GetEC2InstanceRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEC2InstanceRecommendationsRequest', ], 'output' => [ 'shape' => 'GetEC2InstanceRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetEC2RecommendationProjectedMetrics' => [ 'name' => 'GetEC2RecommendationProjectedMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEC2RecommendationProjectedMetricsRequest', ], 'output' => [ 'shape' => 'GetEC2RecommendationProjectedMetricsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetECSServiceRecommendationProjectedMetrics' => [ 'name' => 'GetECSServiceRecommendationProjectedMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetECSServiceRecommendationProjectedMetricsRequest', ], 'output' => [ 'shape' => 'GetECSServiceRecommendationProjectedMetricsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetECSServiceRecommendations' => [ 'name' => 'GetECSServiceRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetECSServiceRecommendationsRequest', ], 'output' => [ 'shape' => 'GetECSServiceRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetEffectiveRecommendationPreferences' => [ 'name' => 'GetEffectiveRecommendationPreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEffectiveRecommendationPreferencesRequest', ], 'output' => [ 'shape' => 'GetEffectiveRecommendationPreferencesResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetEnrollmentStatus' => [ 'name' => 'GetEnrollmentStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEnrollmentStatusRequest', ], 'output' => [ 'shape' => 'GetEnrollmentStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetEnrollmentStatusesForOrganization' => [ 'name' => 'GetEnrollmentStatusesForOrganization', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetEnrollmentStatusesForOrganizationRequest', ], 'output' => [ 'shape' => 'GetEnrollmentStatusesForOrganizationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetIdleRecommendations' => [ 'name' => 'GetIdleRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetIdleRecommendationsRequest', ], 'output' => [ 'shape' => 'GetIdleRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetLambdaFunctionRecommendations' => [ 'name' => 'GetLambdaFunctionRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLambdaFunctionRecommendationsRequest', ], 'output' => [ 'shape' => 'GetLambdaFunctionRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'LimitExceededException', ], ], ], 'GetLicenseRecommendations' => [ 'name' => 'GetLicenseRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetLicenseRecommendationsRequest', ], 'output' => [ 'shape' => 'GetLicenseRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetRDSDatabaseRecommendationProjectedMetrics' => [ 'name' => 'GetRDSDatabaseRecommendationProjectedMetrics', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRDSDatabaseRecommendationProjectedMetricsRequest', ], 'output' => [ 'shape' => 'GetRDSDatabaseRecommendationProjectedMetricsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetRDSDatabaseRecommendations' => [ 'name' => 'GetRDSDatabaseRecommendations', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRDSDatabaseRecommendationsRequest', ], 'output' => [ 'shape' => 'GetRDSDatabaseRecommendationsResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetRecommendationPreferences' => [ 'name' => 'GetRecommendationPreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRecommendationPreferencesRequest', ], 'output' => [ 'shape' => 'GetRecommendationPreferencesResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'GetRecommendationSummaries' => [ 'name' => 'GetRecommendationSummaries', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetRecommendationSummariesRequest', ], 'output' => [ 'shape' => 'GetRecommendationSummariesResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'PutRecommendationPreferences' => [ 'name' => 'PutRecommendationPreferences', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'PutRecommendationPreferencesRequest', ], 'output' => [ 'shape' => 'PutRecommendationPreferencesResponse', ], 'errors' => [ [ 'shape' => 'OptInRequiredException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], 'UpdateEnrollmentStatus' => [ 'name' => 'UpdateEnrollmentStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateEnrollmentStatusRequest', ], 'output' => [ 'shape' => 'UpdateEnrollmentStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ServiceUnavailableException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'InvalidParameterValueException', ], [ 'shape' => 'MissingAuthenticationToken', ], [ 'shape' => 'ThrottlingException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'synthetic' => true, ], 'AccountEnrollmentStatus' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'status' => [ 'shape' => 'Status', ], 'statusReason' => [ 'shape' => 'StatusReason', ], 'lastUpdatedTimestamp' => [ 'shape' => 'LastUpdatedTimestamp', ], ], ], 'AccountEnrollmentStatuses' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountEnrollmentStatus', ], ], 'AccountId' => [ 'type' => 'string', ], 'AccountIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'AccountId', ], ], 'AllocatedStorage' => [ 'type' => 'integer', ], 'AllocationStrategy' => [ 'type' => 'string', 'enum' => [ 'Prioritized', 'LowestPrice', ], ], 'AsgType' => [ 'type' => 'string', 'enum' => [ 'SingleInstanceType', 'MixedInstanceTypes', ], ], 'AutoScalingConfiguration' => [ 'type' => 'string', 'enum' => [ 'TargetTrackingScalingCpu', 'TargetTrackingScalingMemory', ], ], 'AutoScalingGroupArn' => [ 'type' => 'string', ], 'AutoScalingGroupArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoScalingGroupArn', ], ], 'AutoScalingGroupConfiguration' => [ 'type' => 'structure', 'members' => [ 'desiredCapacity' => [ 'shape' => 'DesiredCapacity', ], 'minSize' => [ 'shape' => 'MinSize', ], 'maxSize' => [ 'shape' => 'MaxSize', ], 'instanceType' => [ 'shape' => 'NullableInstanceType', ], 'allocationStrategy' => [ 'shape' => 'AllocationStrategy', ], 'estimatedInstanceHourReductionPercentage' => [ 'shape' => 'NullableEstimatedInstanceHourReductionPercentage', ], 'type' => [ 'shape' => 'AsgType', ], 'mixedInstanceTypes' => [ 'shape' => 'MixedInstanceTypes', ], ], ], 'AutoScalingGroupEstimatedMonthlySavings' => [ 'type' => 'structure', 'members' => [ 'currency' => [ 'shape' => 'Currency', ], 'value' => [ 'shape' => 'Value', ], ], ], 'AutoScalingGroupName' => [ 'type' => 'string', ], 'AutoScalingGroupRecommendation' => [ 'type' => 'structure', 'members' => [ 'accountId' => [ 'shape' => 'AccountId', ], 'autoScalingGroupArn' => [ 'shape' => 'AutoScalingGroupArn', ], 'autoScalingGroupName' => [ 'shape' => 'AutoScalingGroupName', ], 'finding' => [ 'shape' => 'Finding', ], 'utilizationMetrics' => [ 'shape' => 'UtilizationMetrics', ], 'lookBackPeriodInDays' => [ 'shape' => 'LookBackPeriodInDays', ], 'currentConfiguration' => [ 'shape' => 'AutoScalingGroupConfiguration', ], 'currentInstanceGpuInfo' => [ 'shape' => 'GpuInfo', ], 'recommendationOptions' => [ 'shape' => 'AutoScalingGroupRecommendationOptions', ], 'lastRefreshTimestamp' => [ 'shape' => 'LastRefreshTimestamp', ], 'currentPerformanceRisk' => [ 'shape' => 'CurrentPerformanceRisk', ], 'effectiveRecommendationPreferences' => [ 'shape' => 'EffectiveRecommendationPreferences', ], 'inferredWorkloadTypes' => [ 'shape' => 'InferredWorkloadTypes', ], ], ], 'AutoScalingGroupRecommendationOption' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'AutoScalingGroupConfiguration', ], 'instanceGpuInfo' => [ 'shape' => 'GpuInfo', ], 'projectedUtilizationMetrics' => [ 'shape' => 'ProjectedUtilizationMetrics', ], 'performanceRisk' => [ 'shape' => 'PerformanceRisk', ], 'rank' => [ 'shape' => 'Rank', ], 'savingsOpportunity' => [ 'shape' => 'SavingsOpportunity', ], 'savingsOpportunityAfterDiscounts' => [ 'shape' => 'AutoScalingGroupSavingsOpportunityAfterDiscounts', ], 'migrationEffort' => [ 'shape' => 'MigrationEffort', ], ], ], 'AutoScalingGroupRecommendationOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoScalingGroupRecommendationOption', ], ], 'AutoScalingGroupRecommendations' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoScalingGroupRecommendation', ], ], 'AutoScalingGroupSavingsOpportunityAfterDiscounts' => [ 'type' => 'structure', 'members' => [ 'savingsOpportunityPercentage' => [ 'shape' => 'SavingsOpportunityPercentage', ], 'estimatedMonthlySavings' => [ 'shape' => 'AutoScalingGroupEstimatedMonthlySavings', ], ], ], 'Code' => [ 'type' => 'string', ], 'ContainerConfiguration' => [ 'type' => 'structure', 'members' => [ 'containerName' => [ 'shape' => 'ContainerName', ], 'memorySizeConfiguration' => [ 'shape' => 'MemorySizeConfiguration', ], 'cpu' => [ 'shape' => 'NullableCpu', ], ], ], 'ContainerConfigurations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerConfiguration', ], ], 'ContainerName' => [ 'type' => 'string', ], 'ContainerRecommendation' => [ 'type' => 'structure', 'members' => [ 'containerName' => [ 'shape' => 'ContainerName', ], 'memorySizeConfiguration' => [ 'shape' => 'MemorySizeConfiguration', ], 'cpu' => [ 'shape' => 'NullableCpu', ], ], ], 'ContainerRecommendations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ContainerRecommendation', ], ], 'CpuSize' => [ 'type' => 'integer', ], 'CpuVendorArchitecture' => [ 'type' => 'string', 'enum' => [ 'AWS_ARM64', 'CURRENT', ], ], 'CpuVendorArchitectures' => [ 'type' => 'list', 'member' => [ 'shape' => 'CpuVendorArchitecture', ], ], 'CreationTimestamp' => [ 'type' => 'timestamp', ], 'Currency' => [ 'type' => 'string', 'enum' => [ 'USD', 'CNY', ], ], 'CurrentDBInstanceClass' => [ 'type' => 'string', ], 'CurrentInstanceType' => [ 'type' => 'string', ], 'CurrentPerformanceRisk' => [ 'type' => 'string', 'enum' => [ 'VeryLow', 'Low', 'Medium', 'High', ], ], 'CurrentPerformanceRiskRatings' => [ 'type' => 'structure', 'members' => [ 'high' => [ 'shape' => 'High', ], 'medium' => [ 'shape' => 'Medium', ], 'low' => [ 'shape' => 'Low', ], 'veryLow' => [ 'shape' => 'VeryLow', ], ], ], 'CustomizableMetricHeadroom' => [ 'type' => 'string', 'enum' => [ 'PERCENT_30', 'PERCENT_20', 'PERCENT_10', 'PERCENT_0', ], ], 'CustomizableMetricName' => [ 'type' => 'string', 'enum' => [ 'CpuUtilization', 'MemoryUtilization', ], ], 'CustomizableMetricParameters' => [ 'type' => 'structure', 'members' => [ 'threshold' => [ 'shape' => 'CustomizableMetricThreshold', ], 'headroom' => [ 'shape' => 'CustomizableMetricHeadroom', ], ], ], 'CustomizableMetricThreshold' => [ 'type' => 'string', 'enum' => [ 'P90', 'P95', 'P99_5', ], ], 'DBClusterIdentifier' => [ 'type' => 'string', ], 'DBInstanceClass' => [ 'type' => 'string', ], 'DBStorageConfiguration' => [ 'type' => 'structure', 'members' => [ 'storageType' => [ 'shape' => 'StorageType', ], 'allocatedStorage' => [ 'shape' => 'AllocatedStorage', ], 'iops' => [ 'shape' => 'NullableIOPS', ], 'maxAllocatedStorage' => [ 'shape' => 'NullableMaxAllocatedStorage', ], 'storageThroughput' => [ 'shape' => 'NullableStorageThroughput', ], ], ], 'DeleteRecommendationPreferencesRequest' => [ 'type' => 'structure', 'required' => [ 'resourceType', 'recommendationPreferenceNames', ], 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'scope' => [ 'shape' => 'Scope', ], 'recommendationPreferenceNames' => [ 'shape' => 'RecommendationPreferenceNames', ], ], ], 'DeleteRecommendationPreferencesResponse' => [ 'type' => 'structure', 'members' => [], ], 'DescribeRecommendationExportJobsRequest' => [ 'type' => 'structure', 'members' => [ 'jobIds' => [ 'shape' => 'JobIds', ], 'filters' => [ 'shape' => 'JobFilters', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'DescribeRecommendationExportJobsResponse' => [ 'type' => 'structure', 'members' => [ 'recommendationExportJobs' => [ 'shape' => 'RecommendationExportJobs', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'DesiredCapacity' => [ 'type' => 'integer', ], 'DestinationBucket' => [ 'type' => 'string', ], 'DestinationKey' => [ 'type' => 'string', ], 'DestinationKeyPrefix' => [ 'type' => 'string', ], 'Dimension' => [ 'type' => 'string', 'enum' => [ 'SavingsValue', 'SavingsValueAfterDiscount', ], ], 'EBSEffectiveRecommendationPreferences' => [ 'type' => 'structure', 'members' => [ 'savingsEstimationMode' => [ 'shape' => 'EBSSavingsEstimationMode', ], ], ], 'EBSEstimatedMonthlySavings' => [ 'type' => 'structure', 'members' => [ 'currency' => [ 'shape' => 'Currency', ], 'value' => [ 'shape' => 'Value', ], ], ], 'EBSFilter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'EBSFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], ], ], 'EBSFilterName' => [ 'type' => 'string', 'enum' => [ 'Finding', ], ], 'EBSFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'EBSFilter', ], ], 'EBSFinding' => [ 'type' => 'string', 'enum' => [ 'Optimized', 'NotOptimized', ], ], 'EBSMetricName' => [ 'type' => 'string', 'enum' => [ 'VolumeReadOpsPerSecond', 'VolumeWriteOpsPerSecond', 'VolumeReadBytesPerSecond', 'VolumeWriteBytesPerSecond', ], ], 'EBSSavingsEstimationMode' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'EBSSavingsEstimationModeSource', ], ], ], 'EBSSavingsEstimationModeSource' => [ 'type' => 'string', 'enum' => [ 'PublicPricing', 'CostExplorerRightsizing', 'CostOptimizationHub', ], ], 'EBSSavingsOpportunityAfterDiscounts' => [ 'type' => 'structure', 'members' => [ 'savingsOpportunityPercentage' => [ 'shape' => 'SavingsOpportunityPercentage', ], 'estimatedMonthlySavings' => [ 'shape' => 'EBSEstimatedMonthlySavings', ], ], ], 'EBSUtilizationMetric' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'EBSMetricName', ], 'statistic' => [ 'shape' => 'MetricStatistic', ], 'value' => [ 'shape' => 'MetricValue', ], ], ], 'EBSUtilizationMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'EBSUtilizationMetric', ], ], 'ECSEffectiveRecommendationPreferences' => [ 'type' => 'structure', 'members' => [ 'savingsEstimationMode' => [ 'shape' => 'ECSSavingsEstimationMode', ], ], ], 'ECSEstimatedMonthlySavings' => [ 'type' => 'structure', 'members' => [ 'currency' => [ 'shape' => 'Currency', ], 'value' => [ 'shape' => 'Value', ], ], ], 'ECSSavingsEstimationMode' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'ECSSavingsEstimationModeSource', ], ], ], 'ECSSavingsEstimationModeSource' => [ 'type' => 'string', 'enum' => [ 'PublicPricing', 'CostExplorerRightsizing', 'CostOptimizationHub', ], ], 'ECSSavingsOpportunityAfterDiscounts' => [ 'type' => 'structure', 'members' => [ 'savingsOpportunityPercentage' => [ 'shape' => 'SavingsOpportunityPercentage', ], 'estimatedMonthlySavings' => [ 'shape' => 'ECSEstimatedMonthlySavings', ], ], ], 'ECSServiceLaunchType' => [ 'type' => 'string', 'enum' => [ 'EC2', 'Fargate', ], ], 'ECSServiceMetricName' => [ 'type' => 'string', 'enum' => [ 'Cpu', 'Memory', ], ], 'ECSServiceMetricStatistic' => [ 'type' => 'string', 'enum' => [ 'Maximum', 'Average', ], ], 'ECSServiceProjectedMetric' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ECSServiceMetricName', ], 'timestamps' => [ 'shape' => 'Timestamps', ], 'upperBoundValues' => [ 'shape' => 'MetricValues', ], 'lowerBoundValues' => [ 'shape' => 'MetricValues', ], ], ], 'ECSServiceProjectedMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'ECSServiceProjectedMetric', ], ], 'ECSServiceProjectedUtilizationMetric' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ECSServiceMetricName', ], 'statistic' => [ 'shape' => 'ECSServiceMetricStatistic', ], 'lowerBoundValue' => [ 'shape' => 'LowerBoundValue', ], 'upperBoundValue' => [ 'shape' => 'UpperBoundValue', ], ], ], 'ECSServiceProjectedUtilizationMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'ECSServiceProjectedUtilizationMetric', ], ], 'ECSServiceRecommendation' => [ 'type' => 'structure', 'members' => [ 'serviceArn' => [ 'shape' => 'ServiceArn', ], 'accountId' => [ 'shape' => 'AccountId', ], 'currentServiceConfiguration' => [ 'shape' => 'ServiceConfiguration', ], 'utilizationMetrics' => [ 'shape' => 'ECSServiceUtilizationMetrics', ], 'lookbackPeriodInDays' => [ 'shape' => 'LookBackPeriodInDays', ], 'launchType' => [ 'shape' => 'ECSServiceLaunchType', ], 'lastRefreshTimestamp' => [ 'shape' => 'LastRefreshTimestamp', ], 'finding' => [ 'shape' => 'ECSServiceRecommendationFinding', ], 'findingReasonCodes' => [ 'shape' => 'ECSServiceRecommendationFindingReasonCodes', ], 'serviceRecommendationOptions' => [ 'shape' => 'ECSServiceRecommendationOptions', ], 'currentPerformanceRisk' => [ 'shape' => 'CurrentPerformanceRisk', ], 'effectiveRecommendationPreferences' => [ 'shape' => 'ECSEffectiveRecommendationPreferences', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'ECSServiceRecommendationFilter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ECSServiceRecommendationFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], ], ], 'ECSServiceRecommendationFilterName' => [ 'type' => 'string', 'enum' => [ 'Finding', 'FindingReasonCode', ], ], 'ECSServiceRecommendationFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'ECSServiceRecommendationFilter', ], ], 'ECSServiceRecommendationFinding' => [ 'type' => 'string', 'enum' => [ 'Optimized', 'Underprovisioned', 'Overprovisioned', ], ], 'ECSServiceRecommendationFindingReasonCode' => [ 'type' => 'string', 'enum' => [ 'MemoryOverprovisioned', 'MemoryUnderprovisioned', 'CPUOverprovisioned', 'CPUUnderprovisioned', ], ], 'ECSServiceRecommendationFindingReasonCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ECSServiceRecommendationFindingReasonCode', ], ], 'ECSServiceRecommendationOption' => [ 'type' => 'structure', 'members' => [ 'memory' => [ 'shape' => 'NullableMemory', ], 'cpu' => [ 'shape' => 'NullableCpu', ], 'savingsOpportunity' => [ 'shape' => 'SavingsOpportunity', ], 'savingsOpportunityAfterDiscounts' => [ 'shape' => 'ECSSavingsOpportunityAfterDiscounts', ], 'projectedUtilizationMetrics' => [ 'shape' => 'ECSServiceProjectedUtilizationMetrics', ], 'containerRecommendations' => [ 'shape' => 'ContainerRecommendations', ], ], ], 'ECSServiceRecommendationOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ECSServiceRecommendationOption', ], ], 'ECSServiceRecommendations' => [ 'type' => 'list', 'member' => [ 'shape' => 'ECSServiceRecommendation', ], ], 'ECSServiceRecommendedOptionProjectedMetric' => [ 'type' => 'structure', 'members' => [ 'recommendedCpuUnits' => [ 'shape' => 'CpuSize', ], 'recommendedMemorySize' => [ 'shape' => 'MemorySize', ], 'projectedMetrics' => [ 'shape' => 'ECSServiceProjectedMetrics', ], ], ], 'ECSServiceRecommendedOptionProjectedMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'ECSServiceRecommendedOptionProjectedMetric', ], ], 'ECSServiceUtilizationMetric' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ECSServiceMetricName', ], 'statistic' => [ 'shape' => 'ECSServiceMetricStatistic', ], 'value' => [ 'shape' => 'MetricValue', ], ], ], 'ECSServiceUtilizationMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'ECSServiceUtilizationMetric', ], ], 'EffectivePreferredResource' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'PreferredResourceName', ], 'includeList' => [ 'shape' => 'PreferredResourceValues', ], 'effectiveIncludeList' => [ 'shape' => 'PreferredResourceValues', ], 'excludeList' => [ 'shape' => 'PreferredResourceValues', ], ], ], 'EffectivePreferredResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'EffectivePreferredResource', ], ], 'EffectiveRecommendationPreferences' => [ 'type' => 'structure', 'members' => [ 'cpuVendorArchitectures' => [ 'shape' => 'CpuVendorArchitectures', ], 'enhancedInfrastructureMetrics' => [ 'shape' => 'EnhancedInfrastructureMetrics', ], 'inferredWorkloadTypes' => [ 'shape' => 'InferredWorkloadTypesPreference', ], 'externalMetricsPreference' => [ 'shape' => 'ExternalMetricsPreference', ], 'lookBackPeriod' => [ 'shape' => 'LookBackPeriodPreference', ], 'utilizationPreferences' => [ 'shape' => 'UtilizationPreferences', ], 'preferredResources' => [ 'shape' => 'EffectivePreferredResources', ], 'savingsEstimationMode' => [ 'shape' => 'InstanceSavingsEstimationMode', ], ], ], 'Engine' => [ 'type' => 'string', ], 'EngineVersion' => [ 'type' => 'string', ], 'EnhancedInfrastructureMetrics' => [ 'type' => 'string', 'enum' => [ 'Active', 'Inactive', ], ], 'EnrollmentFilter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'EnrollmentFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], ], ], 'EnrollmentFilterName' => [ 'type' => 'string', 'enum' => [ 'Status', ], ], 'EnrollmentFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'EnrollmentFilter', ], ], 'ErrorMessage' => [ 'type' => 'string', ], 'EstimatedMonthlySavings' => [ 'type' => 'structure', 'members' => [ 'currency' => [ 'shape' => 'Currency', ], 'value' => [ 'shape' => 'Value', ], ], ], 'ExportAutoScalingGroupRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 's3DestinationConfig', ], 'members' => [ 'accountIds' => [ 'shape' => 'AccountIds', ], 'filters' => [ 'shape' => 'Filters', ], 'fieldsToExport' => [ 'shape' => 'ExportableAutoScalingGroupFields', ], 's3DestinationConfig' => [ 'shape' => 'S3DestinationConfig', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'includeMemberAccounts' => [ 'shape' => 'IncludeMemberAccounts', ], 'recommendationPreferences' => [ 'shape' => 'RecommendationPreferences', ], ], ], 'ExportAutoScalingGroupRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 's3Destination' => [ 'shape' => 'S3Destination', ], ], ], 'ExportDestination' => [ 'type' => 'structure', 'members' => [ 's3' => [ 'shape' => 'S3Destination', ], ], ], 'ExportEBSVolumeRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 's3DestinationConfig', ], 'members' => [ 'accountIds' => [ 'shape' => 'AccountIds', ], 'filters' => [ 'shape' => 'EBSFilters', ], 'fieldsToExport' => [ 'shape' => 'ExportableVolumeFields', ], 's3DestinationConfig' => [ 'shape' => 'S3DestinationConfig', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'includeMemberAccounts' => [ 'shape' => 'IncludeMemberAccounts', ], ], ], 'ExportEBSVolumeRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 's3Destination' => [ 'shape' => 'S3Destination', ], ], ], 'ExportEC2InstanceRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 's3DestinationConfig', ], 'members' => [ 'accountIds' => [ 'shape' => 'AccountIds', ], 'filters' => [ 'shape' => 'Filters', ], 'fieldsToExport' => [ 'shape' => 'ExportableInstanceFields', ], 's3DestinationConfig' => [ 'shape' => 'S3DestinationConfig', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'includeMemberAccounts' => [ 'shape' => 'IncludeMemberAccounts', ], 'recommendationPreferences' => [ 'shape' => 'RecommendationPreferences', ], ], ], 'ExportEC2InstanceRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 's3Destination' => [ 'shape' => 'S3Destination', ], ], ], 'ExportECSServiceRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 's3DestinationConfig', ], 'members' => [ 'accountIds' => [ 'shape' => 'AccountIds', ], 'filters' => [ 'shape' => 'ECSServiceRecommendationFilters', ], 'fieldsToExport' => [ 'shape' => 'ExportableECSServiceFields', ], 's3DestinationConfig' => [ 'shape' => 'S3DestinationConfig', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'includeMemberAccounts' => [ 'shape' => 'IncludeMemberAccounts', ], ], ], 'ExportECSServiceRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 's3Destination' => [ 'shape' => 'S3Destination', ], ], ], 'ExportIdleRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 's3DestinationConfig', ], 'members' => [ 'accountIds' => [ 'shape' => 'AccountIds', ], 'filters' => [ 'shape' => 'IdleRecommendationFilters', ], 'fieldsToExport' => [ 'shape' => 'ExportableIdleFields', ], 's3DestinationConfig' => [ 'shape' => 'S3DestinationConfig', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'includeMemberAccounts' => [ 'shape' => 'IncludeMemberAccounts', ], ], ], 'ExportIdleRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 's3Destination' => [ 'shape' => 'S3Destination', ], ], ], 'ExportLambdaFunctionRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 's3DestinationConfig', ], 'members' => [ 'accountIds' => [ 'shape' => 'AccountIds', ], 'filters' => [ 'shape' => 'LambdaFunctionRecommendationFilters', ], 'fieldsToExport' => [ 'shape' => 'ExportableLambdaFunctionFields', ], 's3DestinationConfig' => [ 'shape' => 'S3DestinationConfig', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'includeMemberAccounts' => [ 'shape' => 'IncludeMemberAccounts', ], ], ], 'ExportLambdaFunctionRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 's3Destination' => [ 'shape' => 'S3Destination', ], ], ], 'ExportLicenseRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 's3DestinationConfig', ], 'members' => [ 'accountIds' => [ 'shape' => 'AccountIds', ], 'filters' => [ 'shape' => 'LicenseRecommendationFilters', ], 'fieldsToExport' => [ 'shape' => 'ExportableLicenseFields', ], 's3DestinationConfig' => [ 'shape' => 'S3DestinationConfig', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'includeMemberAccounts' => [ 'shape' => 'IncludeMemberAccounts', ], ], ], 'ExportLicenseRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 's3Destination' => [ 'shape' => 'S3Destination', ], ], ], 'ExportRDSDatabaseRecommendationsRequest' => [ 'type' => 'structure', 'required' => [ 's3DestinationConfig', ], 'members' => [ 'accountIds' => [ 'shape' => 'AccountIds', ], 'filters' => [ 'shape' => 'RDSDBRecommendationFilters', ], 'fieldsToExport' => [ 'shape' => 'ExportableRDSDBFields', ], 's3DestinationConfig' => [ 'shape' => 'S3DestinationConfig', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'includeMemberAccounts' => [ 'shape' => 'IncludeMemberAccounts', ], 'recommendationPreferences' => [ 'shape' => 'RecommendationPreferences', ], ], ], 'ExportRDSDatabaseRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 's3Destination' => [ 'shape' => 'S3Destination', ], ], ], 'ExportableAutoScalingGroupField' => [ 'type' => 'string', 'enum' => [ 'AccountId', 'AutoScalingGroupArn', 'AutoScalingGroupName', 'Finding', 'UtilizationMetricsCpuMaximum', 'UtilizationMetricsMemoryMaximum', 'UtilizationMetricsEbsReadOpsPerSecondMaximum', 'UtilizationMetricsEbsWriteOpsPerSecondMaximum', 'UtilizationMetricsEbsReadBytesPerSecondMaximum', 'UtilizationMetricsEbsWriteBytesPerSecondMaximum', 'UtilizationMetricsDiskReadOpsPerSecondMaximum', 'UtilizationMetricsDiskWriteOpsPerSecondMaximum', 'UtilizationMetricsDiskReadBytesPerSecondMaximum', 'UtilizationMetricsDiskWriteBytesPerSecondMaximum', 'UtilizationMetricsNetworkInBytesPerSecondMaximum', 'UtilizationMetricsNetworkOutBytesPerSecondMaximum', 'UtilizationMetricsNetworkPacketsInPerSecondMaximum', 'UtilizationMetricsNetworkPacketsOutPerSecondMaximum', 'LookbackPeriodInDays', 'CurrentConfigurationInstanceType', 'CurrentConfigurationDesiredCapacity', 'CurrentConfigurationMinSize', 'CurrentConfigurationMaxSize', 'CurrentConfigurationAllocationStrategy', 'CurrentConfigurationMixedInstanceTypes', 'CurrentConfigurationType', 'CurrentOnDemandPrice', 'CurrentStandardOneYearNoUpfrontReservedPrice', 'CurrentStandardThreeYearNoUpfrontReservedPrice', 'CurrentVCpus', 'CurrentMemory', 'CurrentStorage', 'CurrentNetwork', 'RecommendationOptionsConfigurationInstanceType', 'RecommendationOptionsConfigurationDesiredCapacity', 'RecommendationOptionsConfigurationMinSize', 'RecommendationOptionsConfigurationMaxSize', 'RecommendationOptionsConfigurationEstimatedInstanceHourReductionPercentage', 'RecommendationOptionsConfigurationAllocationStrategy', 'RecommendationOptionsConfigurationMixedInstanceTypes', 'RecommendationOptionsConfigurationType', 'RecommendationOptionsProjectedUtilizationMetricsCpuMaximum', 'RecommendationOptionsProjectedUtilizationMetricsMemoryMaximum', 'RecommendationOptionsPerformanceRisk', 'RecommendationOptionsOnDemandPrice', 'RecommendationOptionsStandardOneYearNoUpfrontReservedPrice', 'RecommendationOptionsStandardThreeYearNoUpfrontReservedPrice', 'RecommendationOptionsVcpus', 'RecommendationOptionsMemory', 'RecommendationOptionsStorage', 'RecommendationOptionsNetwork', 'LastRefreshTimestamp', 'CurrentPerformanceRisk', 'RecommendationOptionsSavingsOpportunityPercentage', 'RecommendationOptionsEstimatedMonthlySavingsCurrency', 'RecommendationOptionsEstimatedMonthlySavingsValue', 'EffectiveRecommendationPreferencesCpuVendorArchitectures', 'EffectiveRecommendationPreferencesEnhancedInfrastructureMetrics', 'EffectiveRecommendationPreferencesInferredWorkloadTypes', 'EffectiveRecommendationPreferencesPreferredResources', 'EffectiveRecommendationPreferencesLookBackPeriod', 'InferredWorkloadTypes', 'RecommendationOptionsMigrationEffort', 'CurrentInstanceGpuInfo', 'RecommendationOptionsInstanceGpuInfo', 'UtilizationMetricsGpuPercentageMaximum', 'UtilizationMetricsGpuMemoryPercentageMaximum', 'RecommendationOptionsProjectedUtilizationMetricsGpuPercentageMaximum', 'RecommendationOptionsProjectedUtilizationMetricsGpuMemoryPercentageMaximum', 'EffectiveRecommendationPreferencesSavingsEstimationMode', 'RecommendationOptionsSavingsOpportunityAfterDiscountsPercentage', 'RecommendationOptionsEstimatedMonthlySavingsCurrencyAfterDiscounts', 'RecommendationOptionsEstimatedMonthlySavingsValueAfterDiscounts', ], ], 'ExportableAutoScalingGroupFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportableAutoScalingGroupField', ], ], 'ExportableECSServiceField' => [ 'type' => 'string', 'enum' => [ 'AccountId', 'ServiceArn', 'LookbackPeriodInDays', 'LastRefreshTimestamp', 'LaunchType', 'CurrentPerformanceRisk', 'CurrentServiceConfigurationMemory', 'CurrentServiceConfigurationCpu', 'CurrentServiceConfigurationTaskDefinitionArn', 'CurrentServiceConfigurationAutoScalingConfiguration', 'CurrentServiceContainerConfigurations', 'UtilizationMetricsCpuMaximum', 'UtilizationMetricsMemoryMaximum', 'Finding', 'FindingReasonCodes', 'RecommendationOptionsMemory', 'RecommendationOptionsCpu', 'RecommendationOptionsSavingsOpportunityPercentage', 'RecommendationOptionsEstimatedMonthlySavingsCurrency', 'RecommendationOptionsEstimatedMonthlySavingsValue', 'RecommendationOptionsContainerRecommendations', 'RecommendationOptionsProjectedUtilizationMetricsCpuMaximum', 'RecommendationOptionsProjectedUtilizationMetricsMemoryMaximum', 'Tags', 'EffectiveRecommendationPreferencesSavingsEstimationMode', 'RecommendationOptionsSavingsOpportunityAfterDiscountsPercentage', 'RecommendationOptionsEstimatedMonthlySavingsCurrencyAfterDiscounts', 'RecommendationOptionsEstimatedMonthlySavingsValueAfterDiscounts', ], ], 'ExportableECSServiceFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportableECSServiceField', ], ], 'ExportableIdleField' => [ 'type' => 'string', 'enum' => [ 'AccountId', 'ResourceArn', 'ResourceId', 'ResourceType', 'LastRefreshTimestamp', 'LookbackPeriodInDays', 'SavingsOpportunity', 'SavingsOpportunityAfterDiscount', 'UtilizationMetricsCpuMaximum', 'UtilizationMetricsMemoryMaximum', 'UtilizationMetricsNetworkOutBytesPerSecondMaximum', 'UtilizationMetricsNetworkInBytesPerSecondMaximum', 'UtilizationMetricsDatabaseConnectionsMaximum', 'UtilizationMetricsEBSVolumeReadIOPSMaximum', 'UtilizationMetricsEBSVolumeWriteIOPSMaximum', 'UtilizationMetricsVolumeReadOpsPerSecondMaximum', 'UtilizationMetricsVolumeWriteOpsPerSecondMaximum', 'Finding', 'FindingDescription', 'Tags', ], ], 'ExportableIdleFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportableIdleField', ], ], 'ExportableInstanceField' => [ 'type' => 'string', 'enum' => [ 'AccountId', 'InstanceArn', 'InstanceName', 'Finding', 'FindingReasonCodes', 'LookbackPeriodInDays', 'CurrentInstanceType', 'UtilizationMetricsCpuMaximum', 'UtilizationMetricsMemoryMaximum', 'UtilizationMetricsEbsReadOpsPerSecondMaximum', 'UtilizationMetricsEbsWriteOpsPerSecondMaximum', 'UtilizationMetricsEbsReadBytesPerSecondMaximum', 'UtilizationMetricsEbsWriteBytesPerSecondMaximum', 'UtilizationMetricsDiskReadOpsPerSecondMaximum', 'UtilizationMetricsDiskWriteOpsPerSecondMaximum', 'UtilizationMetricsDiskReadBytesPerSecondMaximum', 'UtilizationMetricsDiskWriteBytesPerSecondMaximum', 'UtilizationMetricsNetworkInBytesPerSecondMaximum', 'UtilizationMetricsNetworkOutBytesPerSecondMaximum', 'UtilizationMetricsNetworkPacketsInPerSecondMaximum', 'UtilizationMetricsNetworkPacketsOutPerSecondMaximum', 'CurrentOnDemandPrice', 'CurrentStandardOneYearNoUpfrontReservedPrice', 'CurrentStandardThreeYearNoUpfrontReservedPrice', 'CurrentVCpus', 'CurrentMemory', 'CurrentStorage', 'CurrentNetwork', 'RecommendationOptionsInstanceType', 'RecommendationOptionsProjectedUtilizationMetricsCpuMaximum', 'RecommendationOptionsProjectedUtilizationMetricsMemoryMaximum', 'RecommendationOptionsPlatformDifferences', 'RecommendationOptionsPerformanceRisk', 'RecommendationOptionsVcpus', 'RecommendationOptionsMemory', 'RecommendationOptionsStorage', 'RecommendationOptionsNetwork', 'RecommendationOptionsOnDemandPrice', 'RecommendationOptionsStandardOneYearNoUpfrontReservedPrice', 'RecommendationOptionsStandardThreeYearNoUpfrontReservedPrice', 'RecommendationsSourcesRecommendationSourceArn', 'RecommendationsSourcesRecommendationSourceType', 'LastRefreshTimestamp', 'CurrentPerformanceRisk', 'RecommendationOptionsSavingsOpportunityPercentage', 'RecommendationOptionsEstimatedMonthlySavingsCurrency', 'RecommendationOptionsEstimatedMonthlySavingsValue', 'EffectiveRecommendationPreferencesCpuVendorArchitectures', 'EffectiveRecommendationPreferencesEnhancedInfrastructureMetrics', 'EffectiveRecommendationPreferencesInferredWorkloadTypes', 'InferredWorkloadTypes', 'RecommendationOptionsMigrationEffort', 'EffectiveRecommendationPreferencesExternalMetricsSource', 'Tags', 'InstanceState', 'ExternalMetricStatusCode', 'ExternalMetricStatusReason', 'CurrentInstanceGpuInfo', 'RecommendationOptionsInstanceGpuInfo', 'UtilizationMetricsGpuPercentageMaximum', 'UtilizationMetricsGpuMemoryPercentageMaximum', 'RecommendationOptionsProjectedUtilizationMetricsGpuPercentageMaximum', 'RecommendationOptionsProjectedUtilizationMetricsGpuMemoryPercentageMaximum', 'Idle', 'EffectiveRecommendationPreferencesPreferredResources', 'EffectiveRecommendationPreferencesLookBackPeriod', 'EffectiveRecommendationPreferencesUtilizationPreferences', 'EffectiveRecommendationPreferencesSavingsEstimationMode', 'RecommendationOptionsSavingsOpportunityAfterDiscountsPercentage', 'RecommendationOptionsEstimatedMonthlySavingsCurrencyAfterDiscounts', 'RecommendationOptionsEstimatedMonthlySavingsValueAfterDiscounts', ], ], 'ExportableInstanceFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportableInstanceField', ], ], 'ExportableLambdaFunctionField' => [ 'type' => 'string', 'enum' => [ 'AccountId', 'FunctionArn', 'FunctionVersion', 'Finding', 'FindingReasonCodes', 'NumberOfInvocations', 'UtilizationMetricsDurationMaximum', 'UtilizationMetricsDurationAverage', 'UtilizationMetricsMemoryMaximum', 'UtilizationMetricsMemoryAverage', 'LookbackPeriodInDays', 'CurrentConfigurationMemorySize', 'CurrentConfigurationTimeout', 'CurrentCostTotal', 'CurrentCostAverage', 'RecommendationOptionsConfigurationMemorySize', 'RecommendationOptionsCostLow', 'RecommendationOptionsCostHigh', 'RecommendationOptionsProjectedUtilizationMetricsDurationLowerBound', 'RecommendationOptionsProjectedUtilizationMetricsDurationUpperBound', 'RecommendationOptionsProjectedUtilizationMetricsDurationExpected', 'LastRefreshTimestamp', 'CurrentPerformanceRisk', 'RecommendationOptionsSavingsOpportunityPercentage', 'RecommendationOptionsEstimatedMonthlySavingsCurrency', 'RecommendationOptionsEstimatedMonthlySavingsValue', 'Tags', 'EffectiveRecommendationPreferencesSavingsEstimationMode', 'RecommendationOptionsSavingsOpportunityAfterDiscountsPercentage', 'RecommendationOptionsEstimatedMonthlySavingsCurrencyAfterDiscounts', 'RecommendationOptionsEstimatedMonthlySavingsValueAfterDiscounts', ], ], 'ExportableLambdaFunctionFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportableLambdaFunctionField', ], ], 'ExportableLicenseField' => [ 'type' => 'string', 'enum' => [ 'AccountId', 'ResourceArn', 'LookbackPeriodInDays', 'LastRefreshTimestamp', 'Finding', 'FindingReasonCodes', 'CurrentLicenseConfigurationNumberOfCores', 'CurrentLicenseConfigurationInstanceType', 'CurrentLicenseConfigurationOperatingSystem', 'CurrentLicenseConfigurationLicenseName', 'CurrentLicenseConfigurationLicenseEdition', 'CurrentLicenseConfigurationLicenseModel', 'CurrentLicenseConfigurationLicenseVersion', 'CurrentLicenseConfigurationMetricsSource', 'RecommendationOptionsOperatingSystem', 'RecommendationOptionsLicenseEdition', 'RecommendationOptionsLicenseModel', 'RecommendationOptionsSavingsOpportunityPercentage', 'RecommendationOptionsEstimatedMonthlySavingsCurrency', 'RecommendationOptionsEstimatedMonthlySavingsValue', 'Tags', ], ], 'ExportableLicenseFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportableLicenseField', ], ], 'ExportableRDSDBField' => [ 'type' => 'string', 'enum' => [ 'ResourceArn', 'AccountId', 'Engine', 'EngineVersion', 'Idle', 'MultiAZDBInstance', 'ClusterWriter', 'CurrentDBInstanceClass', 'CurrentStorageConfigurationStorageType', 'CurrentStorageConfigurationAllocatedStorage', 'CurrentStorageConfigurationMaxAllocatedStorage', 'CurrentStorageConfigurationIOPS', 'CurrentStorageConfigurationStorageThroughput', 'CurrentStorageEstimatedMonthlyVolumeIOPsCostVariation', 'CurrentInstanceOnDemandHourlyPrice', 'CurrentStorageOnDemandMonthlyPrice', 'LookbackPeriodInDays', 'CurrentStorageEstimatedClusterInstanceOnDemandMonthlyCost', 'CurrentStorageEstimatedClusterStorageOnDemandMonthlyCost', 'CurrentStorageEstimatedClusterStorageIOOnDemandMonthlyCost', 'CurrentInstancePerformanceRisk', 'UtilizationMetricsCpuMaximum', 'UtilizationMetricsMemoryMaximum', 'UtilizationMetricsEBSVolumeStorageSpaceUtilizationMaximum', 'UtilizationMetricsNetworkReceiveThroughputMaximum', 'UtilizationMetricsNetworkTransmitThroughputMaximum', 'UtilizationMetricsEBSVolumeReadIOPSMaximum', 'UtilizationMetricsEBSVolumeWriteIOPSMaximum', 'UtilizationMetricsEBSVolumeReadThroughputMaximum', 'UtilizationMetricsEBSVolumeWriteThroughputMaximum', 'UtilizationMetricsDatabaseConnectionsMaximum', 'UtilizationMetricsStorageNetworkReceiveThroughputMaximum', 'UtilizationMetricsStorageNetworkTransmitThroughputMaximum', 'UtilizationMetricsAuroraMemoryHealthStateMaximum', 'UtilizationMetricsAuroraMemoryNumDeclinedSqlTotalMaximum', 'UtilizationMetricsAuroraMemoryNumKillConnTotalMaximum', 'UtilizationMetricsAuroraMemoryNumKillQueryTotalMaximum', 'UtilizationMetricsReadIOPSEphemeralStorageMaximum', 'UtilizationMetricsWriteIOPSEphemeralStorageMaximum', 'UtilizationMetricsVolumeBytesUsedAverage', 'UtilizationMetricsVolumeReadIOPsAverage', 'UtilizationMetricsVolumeWriteIOPsAverage', 'InstanceFinding', 'InstanceFindingReasonCodes', 'StorageFinding', 'StorageFindingReasonCodes', 'InstanceRecommendationOptionsDBInstanceClass', 'InstanceRecommendationOptionsRank', 'InstanceRecommendationOptionsPerformanceRisk', 'InstanceRecommendationOptionsProjectedUtilizationMetricsCpuMaximum', 'StorageRecommendationOptionsStorageType', 'StorageRecommendationOptionsAllocatedStorage', 'StorageRecommendationOptionsMaxAllocatedStorage', 'StorageRecommendationOptionsIOPS', 'StorageRecommendationOptionsStorageThroughput', 'StorageRecommendationOptionsRank', 'StorageRecommendationOptionsEstimatedMonthlyVolumeIOPsCostVariation', 'InstanceRecommendationOptionsInstanceOnDemandHourlyPrice', 'InstanceRecommendationOptionsSavingsOpportunityPercentage', 'InstanceRecommendationOptionsEstimatedMonthlySavingsCurrency', 'InstanceRecommendationOptionsEstimatedMonthlySavingsValue', 'InstanceRecommendationOptionsSavingsOpportunityAfterDiscountsPercentage', 'InstanceRecommendationOptionsEstimatedMonthlySavingsCurrencyAfterDiscounts', 'InstanceRecommendationOptionsEstimatedMonthlySavingsValueAfterDiscounts', 'StorageRecommendationOptionsOnDemandMonthlyPrice', 'StorageRecommendationOptionsEstimatedClusterInstanceOnDemandMonthlyCost', 'StorageRecommendationOptionsEstimatedClusterStorageOnDemandMonthlyCost', 'StorageRecommendationOptionsEstimatedClusterStorageIOOnDemandMonthlyCost', 'StorageRecommendationOptionsSavingsOpportunityPercentage', 'StorageRecommendationOptionsEstimatedMonthlySavingsCurrency', 'StorageRecommendationOptionsEstimatedMonthlySavingsValue', 'StorageRecommendationOptionsSavingsOpportunityAfterDiscountsPercentage', 'StorageRecommendationOptionsEstimatedMonthlySavingsCurrencyAfterDiscounts', 'StorageRecommendationOptionsEstimatedMonthlySavingsValueAfterDiscounts', 'EffectiveRecommendationPreferencesCpuVendorArchitectures', 'EffectiveRecommendationPreferencesEnhancedInfrastructureMetrics', 'EffectiveRecommendationPreferencesLookBackPeriod', 'EffectiveRecommendationPreferencesSavingsEstimationMode', 'LastRefreshTimestamp', 'Tags', 'DBClusterIdentifier', 'PromotionTier', ], ], 'ExportableRDSDBFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportableRDSDBField', ], ], 'ExportableVolumeField' => [ 'type' => 'string', 'enum' => [ 'AccountId', 'VolumeArn', 'Finding', 'UtilizationMetricsVolumeReadOpsPerSecondMaximum', 'UtilizationMetricsVolumeWriteOpsPerSecondMaximum', 'UtilizationMetricsVolumeReadBytesPerSecondMaximum', 'UtilizationMetricsVolumeWriteBytesPerSecondMaximum', 'LookbackPeriodInDays', 'CurrentConfigurationVolumeType', 'CurrentConfigurationVolumeBaselineIOPS', 'CurrentConfigurationVolumeBaselineThroughput', 'CurrentConfigurationVolumeBurstIOPS', 'CurrentConfigurationVolumeBurstThroughput', 'CurrentConfigurationVolumeSize', 'CurrentMonthlyPrice', 'RecommendationOptionsConfigurationVolumeType', 'RecommendationOptionsConfigurationVolumeBaselineIOPS', 'RecommendationOptionsConfigurationVolumeBaselineThroughput', 'RecommendationOptionsConfigurationVolumeBurstIOPS', 'RecommendationOptionsConfigurationVolumeBurstThroughput', 'RecommendationOptionsConfigurationVolumeSize', 'RecommendationOptionsMonthlyPrice', 'RecommendationOptionsPerformanceRisk', 'LastRefreshTimestamp', 'CurrentPerformanceRisk', 'RecommendationOptionsSavingsOpportunityPercentage', 'RecommendationOptionsEstimatedMonthlySavingsCurrency', 'RecommendationOptionsEstimatedMonthlySavingsValue', 'Tags', 'RootVolume', 'CurrentConfigurationRootVolume', 'EffectiveRecommendationPreferencesSavingsEstimationMode', 'RecommendationOptionsSavingsOpportunityAfterDiscountsPercentage', 'RecommendationOptionsEstimatedMonthlySavingsCurrencyAfterDiscounts', 'RecommendationOptionsEstimatedMonthlySavingsValueAfterDiscounts', ], ], 'ExportableVolumeFields' => [ 'type' => 'list', 'member' => [ 'shape' => 'ExportableVolumeField', ], ], 'ExternalMetricStatus' => [ 'type' => 'structure', 'members' => [ 'statusCode' => [ 'shape' => 'ExternalMetricStatusCode', ], 'statusReason' => [ 'shape' => 'ExternalMetricStatusReason', ], ], ], 'ExternalMetricStatusCode' => [ 'type' => 'string', 'enum' => [ 'NO_EXTERNAL_METRIC_SET', 'INTEGRATION_SUCCESS', 'DATADOG_INTEGRATION_ERROR', 'DYNATRACE_INTEGRATION_ERROR', 'NEWRELIC_INTEGRATION_ERROR', 'INSTANA_INTEGRATION_ERROR', 'INSUFFICIENT_DATADOG_METRICS', 'INSUFFICIENT_DYNATRACE_METRICS', 'INSUFFICIENT_NEWRELIC_METRICS', 'INSUFFICIENT_INSTANA_METRICS', ], ], 'ExternalMetricStatusReason' => [ 'type' => 'string', ], 'ExternalMetricsPreference' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'ExternalMetricsSource', ], ], ], 'ExternalMetricsSource' => [ 'type' => 'string', 'enum' => [ 'Datadog', 'Dynatrace', 'NewRelic', 'Instana', ], ], 'FailureReason' => [ 'type' => 'string', ], 'FileFormat' => [ 'type' => 'string', 'enum' => [ 'Csv', ], ], 'Filter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'FilterName', ], 'values' => [ 'shape' => 'FilterValues', ], ], ], 'FilterName' => [ 'type' => 'string', 'enum' => [ 'Finding', 'FindingReasonCodes', 'RecommendationSourceType', 'InferredWorkloadTypes', ], ], 'FilterValue' => [ 'type' => 'string', ], 'FilterValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'FilterValue', ], ], 'Filters' => [ 'type' => 'list', 'member' => [ 'shape' => 'Filter', ], ], 'Finding' => [ 'type' => 'string', 'enum' => [ 'Underprovisioned', 'Overprovisioned', 'Optimized', 'NotOptimized', ], ], 'FindingReasonCode' => [ 'type' => 'string', 'enum' => [ 'MemoryOverprovisioned', 'MemoryUnderprovisioned', ], ], 'FunctionArn' => [ 'type' => 'string', ], 'FunctionArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'FunctionArn', ], ], 'FunctionVersion' => [ 'type' => 'string', ], 'GetAutoScalingGroupRecommendationsRequest' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'AccountIds', ], 'autoScalingGroupArns' => [ 'shape' => 'AutoScalingGroupArns', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'Filters', ], 'recommendationPreferences' => [ 'shape' => 'RecommendationPreferences', ], ], ], 'GetAutoScalingGroupRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'autoScalingGroupRecommendations' => [ 'shape' => 'AutoScalingGroupRecommendations', ], 'errors' => [ 'shape' => 'GetRecommendationErrors', ], ], ], 'GetEBSVolumeRecommendationsRequest' => [ 'type' => 'structure', 'members' => [ 'volumeArns' => [ 'shape' => 'VolumeArns', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'EBSFilters', ], 'accountIds' => [ 'shape' => 'AccountIds', ], ], ], 'GetEBSVolumeRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'volumeRecommendations' => [ 'shape' => 'VolumeRecommendations', ], 'errors' => [ 'shape' => 'GetRecommendationErrors', ], ], ], 'GetEC2InstanceRecommendationsRequest' => [ 'type' => 'structure', 'members' => [ 'instanceArns' => [ 'shape' => 'InstanceArns', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'Filters', ], 'accountIds' => [ 'shape' => 'AccountIds', ], 'recommendationPreferences' => [ 'shape' => 'RecommendationPreferences', ], ], ], 'GetEC2InstanceRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'instanceRecommendations' => [ 'shape' => 'InstanceRecommendations', ], 'errors' => [ 'shape' => 'GetRecommendationErrors', ], ], ], 'GetEC2RecommendationProjectedMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'instanceArn', 'stat', 'period', 'startTime', 'endTime', ], 'members' => [ 'instanceArn' => [ 'shape' => 'InstanceArn', ], 'stat' => [ 'shape' => 'MetricStatistic', ], 'period' => [ 'shape' => 'Period', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'recommendationPreferences' => [ 'shape' => 'RecommendationPreferences', ], ], ], 'GetEC2RecommendationProjectedMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'recommendedOptionProjectedMetrics' => [ 'shape' => 'RecommendedOptionProjectedMetrics', ], ], ], 'GetECSServiceRecommendationProjectedMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'serviceArn', 'stat', 'period', 'startTime', 'endTime', ], 'members' => [ 'serviceArn' => [ 'shape' => 'ServiceArn', ], 'stat' => [ 'shape' => 'MetricStatistic', ], 'period' => [ 'shape' => 'Period', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], ], ], 'GetECSServiceRecommendationProjectedMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'recommendedOptionProjectedMetrics' => [ 'shape' => 'ECSServiceRecommendedOptionProjectedMetrics', ], ], ], 'GetECSServiceRecommendationsRequest' => [ 'type' => 'structure', 'members' => [ 'serviceArns' => [ 'shape' => 'ServiceArns', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'ECSServiceRecommendationFilters', ], 'accountIds' => [ 'shape' => 'AccountIds', ], ], ], 'GetECSServiceRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'ecsServiceRecommendations' => [ 'shape' => 'ECSServiceRecommendations', ], 'errors' => [ 'shape' => 'GetRecommendationErrors', ], ], ], 'GetEffectiveRecommendationPreferencesRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], ], ], 'GetEffectiveRecommendationPreferencesResponse' => [ 'type' => 'structure', 'members' => [ 'enhancedInfrastructureMetrics' => [ 'shape' => 'EnhancedInfrastructureMetrics', ], 'externalMetricsPreference' => [ 'shape' => 'ExternalMetricsPreference', ], 'lookBackPeriod' => [ 'shape' => 'LookBackPeriodPreference', ], 'utilizationPreferences' => [ 'shape' => 'UtilizationPreferences', ], 'preferredResources' => [ 'shape' => 'EffectivePreferredResources', ], ], ], 'GetEnrollmentStatusRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetEnrollmentStatusResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'Status', ], 'statusReason' => [ 'shape' => 'StatusReason', ], 'memberAccountsEnrolled' => [ 'shape' => 'MemberAccountsEnrolled', ], 'lastUpdatedTimestamp' => [ 'shape' => 'LastUpdatedTimestamp', ], 'numberOfMemberAccountsOptedIn' => [ 'shape' => 'NumberOfMemberAccountsOptedIn', ], ], ], 'GetEnrollmentStatusesForOrganizationRequest' => [ 'type' => 'structure', 'members' => [ 'filters' => [ 'shape' => 'EnrollmentFilters', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetEnrollmentStatusesForOrganizationResponse' => [ 'type' => 'structure', 'members' => [ 'accountEnrollmentStatuses' => [ 'shape' => 'AccountEnrollmentStatuses', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'GetIdleRecommendationsRequest' => [ 'type' => 'structure', 'members' => [ 'resourceArns' => [ 'shape' => 'ResourceArns', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'IdleMaxResults', ], 'filters' => [ 'shape' => 'IdleRecommendationFilters', ], 'accountIds' => [ 'shape' => 'AccountIds', ], 'orderBy' => [ 'shape' => 'OrderBy', ], ], ], 'GetIdleRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'idleRecommendations' => [ 'shape' => 'IdleRecommendations', ], 'errors' => [ 'shape' => 'IdleRecommendationErrors', ], ], ], 'GetLambdaFunctionRecommendationsRequest' => [ 'type' => 'structure', 'members' => [ 'functionArns' => [ 'shape' => 'FunctionArns', ], 'accountIds' => [ 'shape' => 'AccountIds', ], 'filters' => [ 'shape' => 'LambdaFunctionRecommendationFilters', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetLambdaFunctionRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'lambdaFunctionRecommendations' => [ 'shape' => 'LambdaFunctionRecommendations', ], ], ], 'GetLicenseRecommendationsRequest' => [ 'type' => 'structure', 'members' => [ 'resourceArns' => [ 'shape' => 'ResourceArns', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'LicenseRecommendationFilters', ], 'accountIds' => [ 'shape' => 'AccountIds', ], ], ], 'GetLicenseRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'licenseRecommendations' => [ 'shape' => 'LicenseRecommendations', ], 'errors' => [ 'shape' => 'GetRecommendationErrors', ], ], ], 'GetRDSDatabaseRecommendationProjectedMetricsRequest' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'stat', 'period', 'startTime', 'endTime', ], 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'stat' => [ 'shape' => 'MetricStatistic', ], 'period' => [ 'shape' => 'Period', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'recommendationPreferences' => [ 'shape' => 'RecommendationPreferences', ], ], ], 'GetRDSDatabaseRecommendationProjectedMetricsResponse' => [ 'type' => 'structure', 'members' => [ 'recommendedOptionProjectedMetrics' => [ 'shape' => 'RDSDatabaseRecommendedOptionProjectedMetrics', ], ], ], 'GetRDSDatabaseRecommendationsRequest' => [ 'type' => 'structure', 'members' => [ 'resourceArns' => [ 'shape' => 'ResourceArns', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], 'filters' => [ 'shape' => 'RDSDBRecommendationFilters', ], 'accountIds' => [ 'shape' => 'AccountIds', ], 'recommendationPreferences' => [ 'shape' => 'RecommendationPreferences', ], ], ], 'GetRDSDatabaseRecommendationsResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'rdsDBRecommendations' => [ 'shape' => 'RDSDBRecommendations', ], 'errors' => [ 'shape' => 'GetRecommendationErrors', ], ], ], 'GetRecommendationError' => [ 'type' => 'structure', 'members' => [ 'identifier' => [ 'shape' => 'Identifier', ], 'code' => [ 'shape' => 'Code', ], 'message' => [ 'shape' => 'Message', ], ], ], 'GetRecommendationErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'GetRecommendationError', ], ], 'GetRecommendationPreferencesRequest' => [ 'type' => 'structure', 'required' => [ 'resourceType', ], 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'scope' => [ 'shape' => 'Scope', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetRecommendationPreferencesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'recommendationPreferencesDetails' => [ 'shape' => 'RecommendationPreferencesDetails', ], ], ], 'GetRecommendationSummariesRequest' => [ 'type' => 'structure', 'members' => [ 'accountIds' => [ 'shape' => 'AccountIds', ], 'nextToken' => [ 'shape' => 'NextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'GetRecommendationSummariesResponse' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', ], 'recommendationSummaries' => [ 'shape' => 'RecommendationSummaries', ], ], ], 'Gpu' => [ 'type' => 'structure', 'members' => [ 'gpuCount' => [ 'shape' => 'GpuCount', ], 'gpuMemorySizeInMiB' => [ 'shape' => 'GpuMemorySizeInMiB', ], ], ], 'GpuCount' => [ 'type' => 'integer', ], 'GpuInfo' => [ 'type' => 'structure', 'members' => [ 'gpus' => [ 'shape' => 'Gpus', ], ], ], 'GpuMemorySizeInMiB' => [ 'type' => 'integer', ], 'Gpus' => [ 'type' => 'list', 'member' => [ 'shape' => 'Gpu', ], ], 'High' => [ 'type' => 'long', ], 'Identifier' => [ 'type' => 'string', ], 'Idle' => [ 'type' => 'string', 'enum' => [ 'True', 'False', ], ], 'IdleEstimatedMonthlySavings' => [ 'type' => 'structure', 'members' => [ 'currency' => [ 'shape' => 'Currency', ], 'value' => [ 'shape' => 'Value', ], ], ], 'IdleFinding' => [ 'type' => 'string', 'enum' => [ 'Idle', 'Unattached', ], ], 'IdleFindingDescription' => [ 'type' => 'string', ], 'IdleMaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 0, ], 'IdleMetricName' => [ 'type' => 'string', 'enum' => [ 'CPU', 'Memory', 'NetworkOutBytesPerSecond', 'NetworkInBytesPerSecond', 'DatabaseConnections', 'EBSVolumeReadIOPS', 'EBSVolumeWriteIOPS', 'VolumeReadOpsPerSecond', 'VolumeWriteOpsPerSecond', ], ], 'IdleRecommendation' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'resourceId' => [ 'shape' => 'ResourceId', ], 'resourceType' => [ 'shape' => 'IdleRecommendationResourceType', ], 'accountId' => [ 'shape' => 'AccountId', ], 'finding' => [ 'shape' => 'IdleFinding', ], 'findingDescription' => [ 'shape' => 'IdleFindingDescription', ], 'savingsOpportunity' => [ 'shape' => 'IdleSavingsOpportunity', ], 'savingsOpportunityAfterDiscounts' => [ 'shape' => 'IdleSavingsOpportunityAfterDiscounts', ], 'utilizationMetrics' => [ 'shape' => 'IdleUtilizationMetrics', ], 'lookBackPeriodInDays' => [ 'shape' => 'LookBackPeriodInDays', ], 'lastRefreshTimestamp' => [ 'shape' => 'LastRefreshTimestamp', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'IdleRecommendationError' => [ 'type' => 'structure', 'members' => [ 'identifier' => [ 'shape' => 'Identifier', ], 'code' => [ 'shape' => 'Code', ], 'message' => [ 'shape' => 'Message', ], 'resourceType' => [ 'shape' => 'IdleRecommendationResourceType', ], ], ], 'IdleRecommendationErrors' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdleRecommendationError', ], ], 'IdleRecommendationFilter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'IdleRecommendationFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], ], ], 'IdleRecommendationFilterName' => [ 'type' => 'string', 'enum' => [ 'Finding', 'ResourceType', ], ], 'IdleRecommendationFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdleRecommendationFilter', ], ], 'IdleRecommendationResourceType' => [ 'type' => 'string', 'enum' => [ 'EC2Instance', 'AutoScalingGroup', 'EBSVolume', 'ECSService', 'RDSDBInstance', ], ], 'IdleRecommendations' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdleRecommendation', ], ], 'IdleSavingsOpportunity' => [ 'type' => 'structure', 'members' => [ 'savingsOpportunityPercentage' => [ 'shape' => 'SavingsOpportunityPercentage', ], 'estimatedMonthlySavings' => [ 'shape' => 'IdleEstimatedMonthlySavings', ], ], ], 'IdleSavingsOpportunityAfterDiscounts' => [ 'type' => 'structure', 'members' => [ 'savingsOpportunityPercentage' => [ 'shape' => 'SavingsOpportunityPercentage', ], 'estimatedMonthlySavings' => [ 'shape' => 'IdleEstimatedMonthlySavings', ], ], ], 'IdleSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdleSummary', ], ], 'IdleSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'IdleFinding', ], 'value' => [ 'shape' => 'SummaryValue', ], ], ], 'IdleUtilizationMetric' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'IdleMetricName', ], 'statistic' => [ 'shape' => 'MetricStatistic', ], 'value' => [ 'shape' => 'MetricValue', ], ], ], 'IdleUtilizationMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdleUtilizationMetric', ], ], 'IncludeMemberAccounts' => [ 'type' => 'boolean', ], 'InferredWorkloadSaving' => [ 'type' => 'structure', 'members' => [ 'inferredWorkloadTypes' => [ 'shape' => 'InferredWorkloadTypes', ], 'estimatedMonthlySavings' => [ 'shape' => 'EstimatedMonthlySavings', ], ], ], 'InferredWorkloadSavings' => [ 'type' => 'list', 'member' => [ 'shape' => 'InferredWorkloadSaving', ], ], 'InferredWorkloadType' => [ 'type' => 'string', 'enum' => [ 'AmazonEmr', 'ApacheCassandra', 'ApacheHadoop', 'Memcached', 'Nginx', 'PostgreSql', 'Redis', 'Kafka', 'SQLServer', ], ], 'InferredWorkloadTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'InferredWorkloadType', ], ], 'InferredWorkloadTypesPreference' => [ 'type' => 'string', 'enum' => [ 'Active', 'Inactive', ], ], 'InstanceArn' => [ 'type' => 'string', ], 'InstanceArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceArn', ], ], 'InstanceEstimatedMonthlySavings' => [ 'type' => 'structure', 'members' => [ 'currency' => [ 'shape' => 'Currency', ], 'value' => [ 'shape' => 'Value', ], ], ], 'InstanceIdle' => [ 'type' => 'string', 'enum' => [ 'True', 'False', ], ], 'InstanceName' => [ 'type' => 'string', ], 'InstanceRecommendation' => [ 'type' => 'structure', 'members' => [ 'instanceArn' => [ 'shape' => 'InstanceArn', ], 'accountId' => [ 'shape' => 'AccountId', ], 'instanceName' => [ 'shape' => 'InstanceName', ], 'currentInstanceType' => [ 'shape' => 'CurrentInstanceType', ], 'finding' => [ 'shape' => 'Finding', ], 'findingReasonCodes' => [ 'shape' => 'InstanceRecommendationFindingReasonCodes', ], 'utilizationMetrics' => [ 'shape' => 'UtilizationMetrics', ], 'lookBackPeriodInDays' => [ 'shape' => 'LookBackPeriodInDays', ], 'recommendationOptions' => [ 'shape' => 'RecommendationOptions', ], 'recommendationSources' => [ 'shape' => 'RecommendationSources', ], 'lastRefreshTimestamp' => [ 'shape' => 'LastRefreshTimestamp', ], 'currentPerformanceRisk' => [ 'shape' => 'CurrentPerformanceRisk', ], 'effectiveRecommendationPreferences' => [ 'shape' => 'EffectiveRecommendationPreferences', ], 'inferredWorkloadTypes' => [ 'shape' => 'InferredWorkloadTypes', ], 'instanceState' => [ 'shape' => 'InstanceState', ], 'tags' => [ 'shape' => 'Tags', ], 'externalMetricStatus' => [ 'shape' => 'ExternalMetricStatus', ], 'currentInstanceGpuInfo' => [ 'shape' => 'GpuInfo', ], 'idle' => [ 'shape' => 'InstanceIdle', ], ], ], 'InstanceRecommendationFindingReasonCode' => [ 'type' => 'string', 'enum' => [ 'CPUOverprovisioned', 'CPUUnderprovisioned', 'MemoryOverprovisioned', 'MemoryUnderprovisioned', 'EBSThroughputOverprovisioned', 'EBSThroughputUnderprovisioned', 'EBSIOPSOverprovisioned', 'EBSIOPSUnderprovisioned', 'NetworkBandwidthOverprovisioned', 'NetworkBandwidthUnderprovisioned', 'NetworkPPSOverprovisioned', 'NetworkPPSUnderprovisioned', 'DiskIOPSOverprovisioned', 'DiskIOPSUnderprovisioned', 'DiskThroughputOverprovisioned', 'DiskThroughputUnderprovisioned', 'GPUUnderprovisioned', 'GPUOverprovisioned', 'GPUMemoryUnderprovisioned', 'GPUMemoryOverprovisioned', ], ], 'InstanceRecommendationFindingReasonCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceRecommendationFindingReasonCode', ], ], 'InstanceRecommendationOption' => [ 'type' => 'structure', 'members' => [ 'instanceType' => [ 'shape' => 'InstanceType', ], 'instanceGpuInfo' => [ 'shape' => 'GpuInfo', ], 'projectedUtilizationMetrics' => [ 'shape' => 'ProjectedUtilizationMetrics', ], 'platformDifferences' => [ 'shape' => 'PlatformDifferences', ], 'performanceRisk' => [ 'shape' => 'PerformanceRisk', ], 'rank' => [ 'shape' => 'Rank', ], 'savingsOpportunity' => [ 'shape' => 'SavingsOpportunity', ], 'savingsOpportunityAfterDiscounts' => [ 'shape' => 'InstanceSavingsOpportunityAfterDiscounts', ], 'migrationEffort' => [ 'shape' => 'MigrationEffort', ], ], ], 'InstanceRecommendations' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceRecommendation', ], ], 'InstanceSavingsEstimationMode' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'InstanceSavingsEstimationModeSource', ], ], ], 'InstanceSavingsEstimationModeSource' => [ 'type' => 'string', 'enum' => [ 'PublicPricing', 'CostExplorerRightsizing', 'CostOptimizationHub', ], ], 'InstanceSavingsOpportunityAfterDiscounts' => [ 'type' => 'structure', 'members' => [ 'savingsOpportunityPercentage' => [ 'shape' => 'SavingsOpportunityPercentage', ], 'estimatedMonthlySavings' => [ 'shape' => 'InstanceEstimatedMonthlySavings', ], ], ], 'InstanceState' => [ 'type' => 'string', 'enum' => [ 'pending', 'running', 'shutting-down', 'terminated', 'stopping', 'stopped', ], ], 'InstanceType' => [ 'type' => 'string', ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'InvalidParameterValueException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'synthetic' => true, ], 'JobFilter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'JobFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], ], ], 'JobFilterName' => [ 'type' => 'string', 'enum' => [ 'ResourceType', 'JobStatus', ], ], 'JobFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobFilter', ], ], 'JobId' => [ 'type' => 'string', ], 'JobIds' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobId', ], ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'Queued', 'InProgress', 'Complete', 'Failed', ], ], 'LambdaEffectiveRecommendationPreferences' => [ 'type' => 'structure', 'members' => [ 'savingsEstimationMode' => [ 'shape' => 'LambdaSavingsEstimationMode', ], ], ], 'LambdaEstimatedMonthlySavings' => [ 'type' => 'structure', 'members' => [ 'currency' => [ 'shape' => 'Currency', ], 'value' => [ 'shape' => 'Value', ], ], ], 'LambdaFunctionMemoryMetricName' => [ 'type' => 'string', 'enum' => [ 'Duration', ], ], 'LambdaFunctionMemoryMetricStatistic' => [ 'type' => 'string', 'enum' => [ 'LowerBound', 'UpperBound', 'Expected', ], ], 'LambdaFunctionMemoryProjectedMetric' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'LambdaFunctionMemoryMetricName', ], 'statistic' => [ 'shape' => 'LambdaFunctionMemoryMetricStatistic', ], 'value' => [ 'shape' => 'MetricValue', ], ], ], 'LambdaFunctionMemoryProjectedMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'LambdaFunctionMemoryProjectedMetric', ], ], 'LambdaFunctionMemoryRecommendationOption' => [ 'type' => 'structure', 'members' => [ 'rank' => [ 'shape' => 'Rank', ], 'memorySize' => [ 'shape' => 'MemorySize', ], 'projectedUtilizationMetrics' => [ 'shape' => 'LambdaFunctionMemoryProjectedMetrics', ], 'savingsOpportunity' => [ 'shape' => 'SavingsOpportunity', ], 'savingsOpportunityAfterDiscounts' => [ 'shape' => 'LambdaSavingsOpportunityAfterDiscounts', ], ], ], 'LambdaFunctionMemoryRecommendationOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'LambdaFunctionMemoryRecommendationOption', ], ], 'LambdaFunctionMetricName' => [ 'type' => 'string', 'enum' => [ 'Duration', 'Memory', ], ], 'LambdaFunctionMetricStatistic' => [ 'type' => 'string', 'enum' => [ 'Maximum', 'Average', ], ], 'LambdaFunctionRecommendation' => [ 'type' => 'structure', 'members' => [ 'functionArn' => [ 'shape' => 'FunctionArn', ], 'functionVersion' => [ 'shape' => 'FunctionVersion', ], 'accountId' => [ 'shape' => 'AccountId', ], 'currentMemorySize' => [ 'shape' => 'MemorySize', ], 'numberOfInvocations' => [ 'shape' => 'NumberOfInvocations', ], 'utilizationMetrics' => [ 'shape' => 'LambdaFunctionUtilizationMetrics', ], 'lookbackPeriodInDays' => [ 'shape' => 'LookBackPeriodInDays', ], 'lastRefreshTimestamp' => [ 'shape' => 'LastRefreshTimestamp', ], 'finding' => [ 'shape' => 'LambdaFunctionRecommendationFinding', ], 'findingReasonCodes' => [ 'shape' => 'LambdaFunctionRecommendationFindingReasonCodes', ], 'memorySizeRecommendationOptions' => [ 'shape' => 'LambdaFunctionMemoryRecommendationOptions', ], 'currentPerformanceRisk' => [ 'shape' => 'CurrentPerformanceRisk', ], 'effectiveRecommendationPreferences' => [ 'shape' => 'LambdaEffectiveRecommendationPreferences', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'LambdaFunctionRecommendationFilter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'LambdaFunctionRecommendationFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], ], ], 'LambdaFunctionRecommendationFilterName' => [ 'type' => 'string', 'enum' => [ 'Finding', 'FindingReasonCode', ], ], 'LambdaFunctionRecommendationFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'LambdaFunctionRecommendationFilter', ], ], 'LambdaFunctionRecommendationFinding' => [ 'type' => 'string', 'enum' => [ 'Optimized', 'NotOptimized', 'Unavailable', ], ], 'LambdaFunctionRecommendationFindingReasonCode' => [ 'type' => 'string', 'enum' => [ 'MemoryOverprovisioned', 'MemoryUnderprovisioned', 'InsufficientData', 'Inconclusive', ], ], 'LambdaFunctionRecommendationFindingReasonCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'LambdaFunctionRecommendationFindingReasonCode', ], ], 'LambdaFunctionRecommendations' => [ 'type' => 'list', 'member' => [ 'shape' => 'LambdaFunctionRecommendation', ], ], 'LambdaFunctionUtilizationMetric' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'LambdaFunctionMetricName', ], 'statistic' => [ 'shape' => 'LambdaFunctionMetricStatistic', ], 'value' => [ 'shape' => 'MetricValue', ], ], ], 'LambdaFunctionUtilizationMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'LambdaFunctionUtilizationMetric', ], ], 'LambdaSavingsEstimationMode' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'LambdaSavingsEstimationModeSource', ], ], ], 'LambdaSavingsEstimationModeSource' => [ 'type' => 'string', 'enum' => [ 'PublicPricing', 'CostExplorerRightsizing', 'CostOptimizationHub', ], ], 'LambdaSavingsOpportunityAfterDiscounts' => [ 'type' => 'structure', 'members' => [ 'savingsOpportunityPercentage' => [ 'shape' => 'SavingsOpportunityPercentage', ], 'estimatedMonthlySavings' => [ 'shape' => 'LambdaEstimatedMonthlySavings', ], ], ], 'LastRefreshTimestamp' => [ 'type' => 'timestamp', ], 'LastUpdatedTimestamp' => [ 'type' => 'timestamp', ], 'LicenseConfiguration' => [ 'type' => 'structure', 'members' => [ 'numberOfCores' => [ 'shape' => 'NumberOfCores', ], 'instanceType' => [ 'shape' => 'InstanceType', ], 'operatingSystem' => [ 'shape' => 'OperatingSystem', ], 'licenseEdition' => [ 'shape' => 'LicenseEdition', ], 'licenseName' => [ 'shape' => 'LicenseName', ], 'licenseModel' => [ 'shape' => 'LicenseModel', ], 'licenseVersion' => [ 'shape' => 'LicenseVersion', ], 'metricsSource' => [ 'shape' => 'MetricsSource', ], ], ], 'LicenseEdition' => [ 'type' => 'string', 'enum' => [ 'Enterprise', 'Standard', 'Free', 'NoLicenseEditionFound', ], ], 'LicenseFinding' => [ 'type' => 'string', 'enum' => [ 'InsufficientMetrics', 'Optimized', 'NotOptimized', ], ], 'LicenseFindingReasonCode' => [ 'type' => 'string', 'enum' => [ 'InvalidCloudWatchApplicationInsightsSetup', 'CloudWatchApplicationInsightsError', 'LicenseOverprovisioned', 'Optimized', ], ], 'LicenseFindingReasonCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseFindingReasonCode', ], ], 'LicenseModel' => [ 'type' => 'string', 'enum' => [ 'LicenseIncluded', 'BringYourOwnLicense', ], ], 'LicenseName' => [ 'type' => 'string', 'enum' => [ 'SQLServer', ], ], 'LicenseRecommendation' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'accountId' => [ 'shape' => 'AccountId', ], 'currentLicenseConfiguration' => [ 'shape' => 'LicenseConfiguration', ], 'lookbackPeriodInDays' => [ 'shape' => 'LookBackPeriodInDays', ], 'lastRefreshTimestamp' => [ 'shape' => 'LastRefreshTimestamp', ], 'finding' => [ 'shape' => 'LicenseFinding', ], 'findingReasonCodes' => [ 'shape' => 'LicenseFindingReasonCodes', ], 'licenseRecommendationOptions' => [ 'shape' => 'LicenseRecommendationOptions', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'LicenseRecommendationFilter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'LicenseRecommendationFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], ], ], 'LicenseRecommendationFilterName' => [ 'type' => 'string', 'enum' => [ 'Finding', 'FindingReasonCode', 'LicenseName', ], ], 'LicenseRecommendationFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseRecommendationFilter', ], ], 'LicenseRecommendationOption' => [ 'type' => 'structure', 'members' => [ 'rank' => [ 'shape' => 'Rank', ], 'operatingSystem' => [ 'shape' => 'OperatingSystem', ], 'licenseEdition' => [ 'shape' => 'LicenseEdition', ], 'licenseModel' => [ 'shape' => 'LicenseModel', ], 'savingsOpportunity' => [ 'shape' => 'SavingsOpportunity', ], ], ], 'LicenseRecommendationOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseRecommendationOption', ], ], 'LicenseRecommendations' => [ 'type' => 'list', 'member' => [ 'shape' => 'LicenseRecommendation', ], ], 'LicenseVersion' => [ 'type' => 'string', ], 'LimitExceededException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'synthetic' => true, ], 'LookBackPeriodInDays' => [ 'type' => 'double', ], 'LookBackPeriodPreference' => [ 'type' => 'string', 'enum' => [ 'DAYS_14', 'DAYS_32', 'DAYS_93', ], ], 'Low' => [ 'type' => 'long', ], 'LowerBoundValue' => [ 'type' => 'double', ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 1000, 'min' => 0, ], 'MaxSize' => [ 'type' => 'integer', ], 'Medium' => [ 'type' => 'long', ], 'MemberAccountsEnrolled' => [ 'type' => 'boolean', ], 'MemorySize' => [ 'type' => 'integer', ], 'MemorySizeConfiguration' => [ 'type' => 'structure', 'members' => [ 'memory' => [ 'shape' => 'NullableMemory', ], 'memoryReservation' => [ 'shape' => 'NullableMemoryReservation', ], ], ], 'Message' => [ 'type' => 'string', ], 'MetadataKey' => [ 'type' => 'string', ], 'MetricName' => [ 'type' => 'string', 'enum' => [ 'Cpu', 'Memory', 'EBS_READ_OPS_PER_SECOND', 'EBS_WRITE_OPS_PER_SECOND', 'EBS_READ_BYTES_PER_SECOND', 'EBS_WRITE_BYTES_PER_SECOND', 'DISK_READ_OPS_PER_SECOND', 'DISK_WRITE_OPS_PER_SECOND', 'DISK_READ_BYTES_PER_SECOND', 'DISK_WRITE_BYTES_PER_SECOND', 'NETWORK_IN_BYTES_PER_SECOND', 'NETWORK_OUT_BYTES_PER_SECOND', 'NETWORK_PACKETS_IN_PER_SECOND', 'NETWORK_PACKETS_OUT_PER_SECOND', 'GPU_PERCENTAGE', 'GPU_MEMORY_PERCENTAGE', ], ], 'MetricProviderArn' => [ 'type' => 'string', ], 'MetricSource' => [ 'type' => 'structure', 'members' => [ 'provider' => [ 'shape' => 'MetricSourceProvider', ], 'providerArn' => [ 'shape' => 'MetricProviderArn', ], ], ], 'MetricSourceProvider' => [ 'type' => 'string', 'enum' => [ 'CloudWatchApplicationInsights', ], ], 'MetricStatistic' => [ 'type' => 'string', 'enum' => [ 'Maximum', 'Average', ], ], 'MetricValue' => [ 'type' => 'double', ], 'MetricValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricValue', ], ], 'MetricsSource' => [ 'type' => 'list', 'member' => [ 'shape' => 'MetricSource', ], ], 'MigrationEffort' => [ 'type' => 'string', 'enum' => [ 'VeryLow', 'Low', 'Medium', 'High', ], ], 'MinSize' => [ 'type' => 'integer', ], 'MissingAuthenticationToken' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'synthetic' => true, ], 'MixedInstanceType' => [ 'type' => 'string', ], 'MixedInstanceTypes' => [ 'type' => 'list', 'member' => [ 'shape' => 'MixedInstanceType', ], ], 'NextToken' => [ 'type' => 'string', ], 'NullableCpu' => [ 'type' => 'integer', ], 'NullableEstimatedInstanceHourReductionPercentage' => [ 'type' => 'double', ], 'NullableIOPS' => [ 'type' => 'integer', ], 'NullableInstanceType' => [ 'type' => 'string', ], 'NullableMaxAllocatedStorage' => [ 'type' => 'integer', ], 'NullableMemory' => [ 'type' => 'integer', ], 'NullableMemoryReservation' => [ 'type' => 'integer', ], 'NullableStorageThroughput' => [ 'type' => 'integer', ], 'NumberOfCores' => [ 'type' => 'integer', ], 'NumberOfInvocations' => [ 'type' => 'long', ], 'NumberOfMemberAccountsOptedIn' => [ 'type' => 'integer', ], 'OperatingSystem' => [ 'type' => 'string', ], 'OptInRequiredException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'synthetic' => true, ], 'Order' => [ 'type' => 'string', 'enum' => [ 'Asc', 'Desc', ], ], 'OrderBy' => [ 'type' => 'structure', 'members' => [ 'dimension' => [ 'shape' => 'Dimension', ], 'order' => [ 'shape' => 'Order', ], ], ], 'PerformanceRisk' => [ 'type' => 'double', 'max' => 4, 'min' => 0, ], 'Period' => [ 'type' => 'integer', ], 'PlatformDifference' => [ 'type' => 'string', 'enum' => [ 'Hypervisor', 'NetworkInterface', 'StorageInterface', 'InstanceStoreAvailability', 'VirtualizationType', 'Architecture', ], ], 'PlatformDifferences' => [ 'type' => 'list', 'member' => [ 'shape' => 'PlatformDifference', ], ], 'PreferredResource' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'PreferredResourceName', ], 'includeList' => [ 'shape' => 'PreferredResourceValues', ], 'excludeList' => [ 'shape' => 'PreferredResourceValues', ], ], ], 'PreferredResourceName' => [ 'type' => 'string', 'enum' => [ 'Ec2InstanceTypes', ], ], 'PreferredResourceValue' => [ 'type' => 'string', ], 'PreferredResourceValues' => [ 'type' => 'list', 'member' => [ 'shape' => 'PreferredResourceValue', ], ], 'PreferredResources' => [ 'type' => 'list', 'member' => [ 'shape' => 'PreferredResource', ], ], 'ProjectedMetric' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'MetricName', ], 'timestamps' => [ 'shape' => 'Timestamps', ], 'values' => [ 'shape' => 'MetricValues', ], ], ], 'ProjectedMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProjectedMetric', ], ], 'ProjectedUtilizationMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'UtilizationMetric', ], ], 'PromotionTier' => [ 'type' => 'integer', ], 'PutRecommendationPreferencesRequest' => [ 'type' => 'structure', 'required' => [ 'resourceType', ], 'members' => [ 'resourceType' => [ 'shape' => 'ResourceType', ], 'scope' => [ 'shape' => 'Scope', ], 'enhancedInfrastructureMetrics' => [ 'shape' => 'EnhancedInfrastructureMetrics', ], 'inferredWorkloadTypes' => [ 'shape' => 'InferredWorkloadTypesPreference', ], 'externalMetricsPreference' => [ 'shape' => 'ExternalMetricsPreference', ], 'lookBackPeriod' => [ 'shape' => 'LookBackPeriodPreference', ], 'utilizationPreferences' => [ 'shape' => 'UtilizationPreferences', ], 'preferredResources' => [ 'shape' => 'PreferredResources', ], 'savingsEstimationMode' => [ 'shape' => 'SavingsEstimationMode', ], ], ], 'PutRecommendationPreferencesResponse' => [ 'type' => 'structure', 'members' => [], ], 'RDSCurrentInstancePerformanceRisk' => [ 'type' => 'string', 'enum' => [ 'VeryLow', 'Low', 'Medium', 'High', ], ], 'RDSDBInstanceRecommendationOption' => [ 'type' => 'structure', 'members' => [ 'dbInstanceClass' => [ 'shape' => 'DBInstanceClass', ], 'projectedUtilizationMetrics' => [ 'shape' => 'RDSDBProjectedUtilizationMetrics', ], 'performanceRisk' => [ 'shape' => 'PerformanceRisk', ], 'rank' => [ 'shape' => 'Rank', ], 'savingsOpportunity' => [ 'shape' => 'SavingsOpportunity', ], 'savingsOpportunityAfterDiscounts' => [ 'shape' => 'RDSInstanceSavingsOpportunityAfterDiscounts', ], ], ], 'RDSDBInstanceRecommendationOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RDSDBInstanceRecommendationOption', ], ], 'RDSDBMetricName' => [ 'type' => 'string', 'enum' => [ 'CPU', 'Memory', 'EBSVolumeStorageSpaceUtilization', 'NetworkReceiveThroughput', 'NetworkTransmitThroughput', 'EBSVolumeReadIOPS', 'EBSVolumeWriteIOPS', 'EBSVolumeReadThroughput', 'EBSVolumeWriteThroughput', 'DatabaseConnections', 'StorageNetworkReceiveThroughput', 'StorageNetworkTransmitThroughput', 'AuroraMemoryHealthState', 'AuroraMemoryNumDeclinedSql', 'AuroraMemoryNumKillConnTotal', 'AuroraMemoryNumKillQueryTotal', 'ReadIOPSEphemeralStorage', 'WriteIOPSEphemeralStorage', 'VolumeReadIOPs', 'VolumeBytesUsed', 'VolumeWriteIOPs', ], ], 'RDSDBMetricStatistic' => [ 'type' => 'string', 'enum' => [ 'Maximum', 'Minimum', 'Average', ], ], 'RDSDBProjectedUtilizationMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'RDSDBUtilizationMetric', ], ], 'RDSDBRecommendation' => [ 'type' => 'structure', 'members' => [ 'resourceArn' => [ 'shape' => 'ResourceArn', ], 'accountId' => [ 'shape' => 'AccountId', ], 'engine' => [ 'shape' => 'Engine', ], 'engineVersion' => [ 'shape' => 'EngineVersion', ], 'promotionTier' => [ 'shape' => 'PromotionTier', ], 'currentDBInstanceClass' => [ 'shape' => 'CurrentDBInstanceClass', ], 'currentStorageConfiguration' => [ 'shape' => 'DBStorageConfiguration', ], 'dbClusterIdentifier' => [ 'shape' => 'DBClusterIdentifier', ], 'idle' => [ 'shape' => 'Idle', ], 'instanceFinding' => [ 'shape' => 'RDSInstanceFinding', ], 'storageFinding' => [ 'shape' => 'RDSStorageFinding', ], 'instanceFindingReasonCodes' => [ 'shape' => 'RDSInstanceFindingReasonCodes', ], 'currentInstancePerformanceRisk' => [ 'shape' => 'RDSCurrentInstancePerformanceRisk', ], 'currentStorageEstimatedMonthlyVolumeIOPsCostVariation' => [ 'shape' => 'RDSEstimatedMonthlyVolumeIOPsCostVariation', ], 'storageFindingReasonCodes' => [ 'shape' => 'RDSStorageFindingReasonCodes', ], 'instanceRecommendationOptions' => [ 'shape' => 'RDSDBInstanceRecommendationOptions', ], 'storageRecommendationOptions' => [ 'shape' => 'RDSDBStorageRecommendationOptions', ], 'utilizationMetrics' => [ 'shape' => 'RDSDBUtilizationMetrics', ], 'effectiveRecommendationPreferences' => [ 'shape' => 'RDSEffectiveRecommendationPreferences', ], 'lookbackPeriodInDays' => [ 'shape' => 'LookBackPeriodInDays', ], 'lastRefreshTimestamp' => [ 'shape' => 'LastRefreshTimestamp', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'RDSDBRecommendationFilter' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'RDSDBRecommendationFilterName', ], 'values' => [ 'shape' => 'FilterValues', ], ], ], 'RDSDBRecommendationFilterName' => [ 'type' => 'string', 'enum' => [ 'InstanceFinding', 'InstanceFindingReasonCode', 'StorageFinding', 'StorageFindingReasonCode', 'Idle', ], ], 'RDSDBRecommendationFilters' => [ 'type' => 'list', 'member' => [ 'shape' => 'RDSDBRecommendationFilter', ], ], 'RDSDBRecommendations' => [ 'type' => 'list', 'member' => [ 'shape' => 'RDSDBRecommendation', ], ], 'RDSDBStorageRecommendationOption' => [ 'type' => 'structure', 'members' => [ 'storageConfiguration' => [ 'shape' => 'DBStorageConfiguration', ], 'rank' => [ 'shape' => 'Rank', ], 'savingsOpportunity' => [ 'shape' => 'SavingsOpportunity', ], 'savingsOpportunityAfterDiscounts' => [ 'shape' => 'RDSStorageSavingsOpportunityAfterDiscounts', ], 'estimatedMonthlyVolumeIOPsCostVariation' => [ 'shape' => 'RDSEstimatedMonthlyVolumeIOPsCostVariation', ], ], ], 'RDSDBStorageRecommendationOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'RDSDBStorageRecommendationOption', ], ], 'RDSDBUtilizationMetric' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'RDSDBMetricName', ], 'statistic' => [ 'shape' => 'RDSDBMetricStatistic', ], 'value' => [ 'shape' => 'MetricValue', ], ], ], 'RDSDBUtilizationMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'RDSDBUtilizationMetric', ], ], 'RDSDatabaseProjectedMetric' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'RDSDBMetricName', ], 'timestamps' => [ 'shape' => 'Timestamps', ], 'values' => [ 'shape' => 'MetricValues', ], ], ], 'RDSDatabaseProjectedMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'RDSDatabaseProjectedMetric', ], ], 'RDSDatabaseRecommendedOptionProjectedMetric' => [ 'type' => 'structure', 'members' => [ 'recommendedDBInstanceClass' => [ 'shape' => 'RecommendedDBInstanceClass', ], 'rank' => [ 'shape' => 'Rank', ], 'projectedMetrics' => [ 'shape' => 'RDSDatabaseProjectedMetrics', ], ], ], 'RDSDatabaseRecommendedOptionProjectedMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'RDSDatabaseRecommendedOptionProjectedMetric', ], ], 'RDSEffectiveRecommendationPreferences' => [ 'type' => 'structure', 'members' => [ 'cpuVendorArchitectures' => [ 'shape' => 'CpuVendorArchitectures', ], 'enhancedInfrastructureMetrics' => [ 'shape' => 'EnhancedInfrastructureMetrics', ], 'lookBackPeriod' => [ 'shape' => 'LookBackPeriodPreference', ], 'savingsEstimationMode' => [ 'shape' => 'RDSSavingsEstimationMode', ], ], ], 'RDSEstimatedMonthlyVolumeIOPsCostVariation' => [ 'type' => 'string', 'enum' => [ 'None', 'Low', 'Medium', 'High', ], ], 'RDSInstanceEstimatedMonthlySavings' => [ 'type' => 'structure', 'members' => [ 'currency' => [ 'shape' => 'Currency', ], 'value' => [ 'shape' => 'Value', ], ], ], 'RDSInstanceFinding' => [ 'type' => 'string', 'enum' => [ 'Optimized', 'Underprovisioned', 'Overprovisioned', ], ], 'RDSInstanceFindingReasonCode' => [ 'type' => 'string', 'enum' => [ 'CPUOverprovisioned', 'NetworkBandwidthOverprovisioned', 'EBSIOPSOverprovisioned', 'EBSIOPSUnderprovisioned', 'EBSThroughputOverprovisioned', 'CPUUnderprovisioned', 'NetworkBandwidthUnderprovisioned', 'EBSThroughputUnderprovisioned', 'NewGenerationDBInstanceClassAvailable', 'NewEngineVersionAvailable', 'DBClusterWriterUnderprovisioned', 'MemoryUnderprovisioned', 'InstanceStorageReadIOPSUnderprovisioned', 'InstanceStorageWriteIOPSUnderprovisioned', ], ], 'RDSInstanceFindingReasonCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'RDSInstanceFindingReasonCode', ], ], 'RDSInstanceSavingsOpportunityAfterDiscounts' => [ 'type' => 'structure', 'members' => [ 'savingsOpportunityPercentage' => [ 'shape' => 'SavingsOpportunityPercentage', ], 'estimatedMonthlySavings' => [ 'shape' => 'RDSInstanceEstimatedMonthlySavings', ], ], ], 'RDSSavingsEstimationMode' => [ 'type' => 'structure', 'members' => [ 'source' => [ 'shape' => 'RDSSavingsEstimationModeSource', ], ], ], 'RDSSavingsEstimationModeSource' => [ 'type' => 'string', 'enum' => [ 'PublicPricing', 'CostExplorerRightsizing', 'CostOptimizationHub', ], ], 'RDSStorageEstimatedMonthlySavings' => [ 'type' => 'structure', 'members' => [ 'currency' => [ 'shape' => 'Currency', ], 'value' => [ 'shape' => 'Value', ], ], ], 'RDSStorageFinding' => [ 'type' => 'string', 'enum' => [ 'Optimized', 'Underprovisioned', 'Overprovisioned', 'NotOptimized', ], ], 'RDSStorageFindingReasonCode' => [ 'type' => 'string', 'enum' => [ 'EBSVolumeAllocatedStorageUnderprovisioned', 'EBSVolumeThroughputUnderprovisioned', 'EBSVolumeIOPSOverprovisioned', 'EBSVolumeThroughputOverprovisioned', 'NewGenerationStorageTypeAvailable', 'DBClusterStorageOptionAvailable', 'DBClusterStorageSavingsAvailable', ], ], 'RDSStorageFindingReasonCodes' => [ 'type' => 'list', 'member' => [ 'shape' => 'RDSStorageFindingReasonCode', ], ], 'RDSStorageSavingsOpportunityAfterDiscounts' => [ 'type' => 'structure', 'members' => [ 'savingsOpportunityPercentage' => [ 'shape' => 'SavingsOpportunityPercentage', ], 'estimatedMonthlySavings' => [ 'shape' => 'RDSStorageEstimatedMonthlySavings', ], ], ], 'Rank' => [ 'type' => 'integer', ], 'ReasonCodeSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ReasonCodeSummary', ], ], 'ReasonCodeSummary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'FindingReasonCode', ], 'value' => [ 'shape' => 'SummaryValue', ], ], ], 'RecommendationExportJob' => [ 'type' => 'structure', 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'destination' => [ 'shape' => 'ExportDestination', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'status' => [ 'shape' => 'JobStatus', ], 'creationTimestamp' => [ 'shape' => 'CreationTimestamp', ], 'lastUpdatedTimestamp' => [ 'shape' => 'LastUpdatedTimestamp', ], 'failureReason' => [ 'shape' => 'FailureReason', ], ], ], 'RecommendationExportJobs' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationExportJob', ], ], 'RecommendationOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'InstanceRecommendationOption', ], ], 'RecommendationPreferenceName' => [ 'type' => 'string', 'enum' => [ 'EnhancedInfrastructureMetrics', 'InferredWorkloadTypes', 'ExternalMetricsPreference', 'LookBackPeriodPreference', 'PreferredResources', 'UtilizationPreferences', ], ], 'RecommendationPreferenceNames' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationPreferenceName', ], ], 'RecommendationPreferences' => [ 'type' => 'structure', 'members' => [ 'cpuVendorArchitectures' => [ 'shape' => 'CpuVendorArchitectures', ], ], ], 'RecommendationPreferencesDetail' => [ 'type' => 'structure', 'members' => [ 'scope' => [ 'shape' => 'Scope', ], 'resourceType' => [ 'shape' => 'ResourceType', ], 'enhancedInfrastructureMetrics' => [ 'shape' => 'EnhancedInfrastructureMetrics', ], 'inferredWorkloadTypes' => [ 'shape' => 'InferredWorkloadTypesPreference', ], 'externalMetricsPreference' => [ 'shape' => 'ExternalMetricsPreference', ], 'lookBackPeriod' => [ 'shape' => 'LookBackPeriodPreference', ], 'utilizationPreferences' => [ 'shape' => 'UtilizationPreferences', ], 'preferredResources' => [ 'shape' => 'EffectivePreferredResources', ], 'savingsEstimationMode' => [ 'shape' => 'SavingsEstimationMode', ], ], ], 'RecommendationPreferencesDetails' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationPreferencesDetail', ], ], 'RecommendationSource' => [ 'type' => 'structure', 'members' => [ 'recommendationSourceArn' => [ 'shape' => 'RecommendationSourceArn', ], 'recommendationSourceType' => [ 'shape' => 'RecommendationSourceType', ], ], ], 'RecommendationSourceArn' => [ 'type' => 'string', ], 'RecommendationSourceType' => [ 'type' => 'string', 'enum' => [ 'Ec2Instance', 'AutoScalingGroup', 'EbsVolume', 'LambdaFunction', 'EcsService', 'License', 'RdsDBInstance', 'RdsDBInstanceStorage', 'AuroraDBClusterStorage', ], ], 'RecommendationSources' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationSource', ], ], 'RecommendationSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendationSummary', ], ], 'RecommendationSummary' => [ 'type' => 'structure', 'members' => [ 'summaries' => [ 'shape' => 'Summaries', ], 'idleSummaries' => [ 'shape' => 'IdleSummaries', ], 'recommendationResourceType' => [ 'shape' => 'RecommendationSourceType', ], 'accountId' => [ 'shape' => 'AccountId', ], 'savingsOpportunity' => [ 'shape' => 'SavingsOpportunity', ], 'idleSavingsOpportunity' => [ 'shape' => 'SavingsOpportunity', ], 'aggregatedSavingsOpportunity' => [ 'shape' => 'SavingsOpportunity', ], 'currentPerformanceRiskRatings' => [ 'shape' => 'CurrentPerformanceRiskRatings', ], 'inferredWorkloadSavings' => [ 'shape' => 'InferredWorkloadSavings', ], ], ], 'RecommendedDBInstanceClass' => [ 'type' => 'string', ], 'RecommendedInstanceType' => [ 'type' => 'string', ], 'RecommendedOptionProjectedMetric' => [ 'type' => 'structure', 'members' => [ 'recommendedInstanceType' => [ 'shape' => 'RecommendedInstanceType', ], 'rank' => [ 'shape' => 'Rank', ], 'projectedMetrics' => [ 'shape' => 'ProjectedMetrics', ], ], ], 'RecommendedOptionProjectedMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecommendedOptionProjectedMetric', ], ], 'ResourceArn' => [ 'type' => 'string', ], 'ResourceArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'ResourceArn', ], ], 'ResourceId' => [ 'type' => 'string', ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'synthetic' => true, ], 'ResourceType' => [ 'type' => 'string', 'enum' => [ 'Ec2Instance', 'AutoScalingGroup', 'EbsVolume', 'LambdaFunction', 'NotApplicable', 'EcsService', 'License', 'RdsDBInstance', 'AuroraDBClusterStorage', 'Idle', ], ], 'RootVolume' => [ 'type' => 'boolean', ], 'S3Destination' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'DestinationBucket', ], 'key' => [ 'shape' => 'DestinationKey', ], 'metadataKey' => [ 'shape' => 'MetadataKey', ], ], ], 'S3DestinationConfig' => [ 'type' => 'structure', 'members' => [ 'bucket' => [ 'shape' => 'DestinationBucket', ], 'keyPrefix' => [ 'shape' => 'DestinationKeyPrefix', ], ], ], 'SavingsEstimationMode' => [ 'type' => 'string', 'enum' => [ 'AfterDiscounts', 'BeforeDiscounts', ], ], 'SavingsOpportunity' => [ 'type' => 'structure', 'members' => [ 'savingsOpportunityPercentage' => [ 'shape' => 'SavingsOpportunityPercentage', ], 'estimatedMonthlySavings' => [ 'shape' => 'EstimatedMonthlySavings', ], ], ], 'SavingsOpportunityPercentage' => [ 'type' => 'double', ], 'Scope' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'ScopeName', ], 'value' => [ 'shape' => 'ScopeValue', ], ], ], 'ScopeName' => [ 'type' => 'string', 'enum' => [ 'Organization', 'AccountId', 'ResourceArn', ], ], 'ScopeValue' => [ 'type' => 'string', ], 'ServiceArn' => [ 'type' => 'string', ], 'ServiceArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'ServiceArn', ], ], 'ServiceConfiguration' => [ 'type' => 'structure', 'members' => [ 'memory' => [ 'shape' => 'NullableMemory', ], 'cpu' => [ 'shape' => 'NullableCpu', ], 'containerConfigurations' => [ 'shape' => 'ContainerConfigurations', ], 'autoScalingConfiguration' => [ 'shape' => 'AutoScalingConfiguration', ], 'taskDefinitionArn' => [ 'shape' => 'TaskDefinitionArn', ], ], ], 'ServiceUnavailableException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'fault' => true, ], 'Status' => [ 'type' => 'string', 'enum' => [ 'Active', 'Inactive', 'Pending', 'Failed', ], ], 'StatusReason' => [ 'type' => 'string', ], 'StorageType' => [ 'type' => 'string', ], 'Summaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'Summary', ], ], 'Summary' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'Finding', ], 'value' => [ 'shape' => 'SummaryValue', ], 'reasonCodeSummaries' => [ 'shape' => 'ReasonCodeSummaries', ], ], ], 'SummaryValue' => [ 'type' => 'double', ], 'Tag' => [ 'type' => 'structure', 'members' => [ 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', ], 'TagValue' => [ 'type' => 'string', ], 'Tags' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], ], 'TaskDefinitionArn' => [ 'type' => 'string', ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, 'synthetic' => true, ], 'Timestamp' => [ 'type' => 'timestamp', ], 'Timestamps' => [ 'type' => 'list', 'member' => [ 'shape' => 'Timestamp', ], ], 'UpdateEnrollmentStatusRequest' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'Status', ], 'includeMemberAccounts' => [ 'shape' => 'IncludeMemberAccounts', ], ], ], 'UpdateEnrollmentStatusResponse' => [ 'type' => 'structure', 'members' => [ 'status' => [ 'shape' => 'Status', ], 'statusReason' => [ 'shape' => 'StatusReason', ], ], ], 'UpperBoundValue' => [ 'type' => 'double', ], 'UtilizationMetric' => [ 'type' => 'structure', 'members' => [ 'name' => [ 'shape' => 'MetricName', ], 'statistic' => [ 'shape' => 'MetricStatistic', ], 'value' => [ 'shape' => 'MetricValue', ], ], ], 'UtilizationMetrics' => [ 'type' => 'list', 'member' => [ 'shape' => 'UtilizationMetric', ], ], 'UtilizationPreference' => [ 'type' => 'structure', 'members' => [ 'metricName' => [ 'shape' => 'CustomizableMetricName', ], 'metricParameters' => [ 'shape' => 'CustomizableMetricParameters', ], ], ], 'UtilizationPreferences' => [ 'type' => 'list', 'member' => [ 'shape' => 'UtilizationPreference', ], ], 'Value' => [ 'type' => 'double', ], 'VeryLow' => [ 'type' => 'long', ], 'VolumeArn' => [ 'type' => 'string', ], 'VolumeArns' => [ 'type' => 'list', 'member' => [ 'shape' => 'VolumeArn', ], ], 'VolumeBaselineIOPS' => [ 'type' => 'integer', ], 'VolumeBaselineThroughput' => [ 'type' => 'integer', ], 'VolumeBurstIOPS' => [ 'type' => 'integer', ], 'VolumeBurstThroughput' => [ 'type' => 'integer', ], 'VolumeConfiguration' => [ 'type' => 'structure', 'members' => [ 'volumeType' => [ 'shape' => 'VolumeType', ], 'volumeSize' => [ 'shape' => 'VolumeSize', ], 'volumeBaselineIOPS' => [ 'shape' => 'VolumeBaselineIOPS', ], 'volumeBurstIOPS' => [ 'shape' => 'VolumeBurstIOPS', ], 'volumeBaselineThroughput' => [ 'shape' => 'VolumeBaselineThroughput', ], 'volumeBurstThroughput' => [ 'shape' => 'VolumeBurstThroughput', ], 'rootVolume' => [ 'shape' => 'RootVolume', ], ], ], 'VolumeRecommendation' => [ 'type' => 'structure', 'members' => [ 'volumeArn' => [ 'shape' => 'VolumeArn', ], 'accountId' => [ 'shape' => 'AccountId', ], 'currentConfiguration' => [ 'shape' => 'VolumeConfiguration', ], 'finding' => [ 'shape' => 'EBSFinding', ], 'utilizationMetrics' => [ 'shape' => 'EBSUtilizationMetrics', ], 'lookBackPeriodInDays' => [ 'shape' => 'LookBackPeriodInDays', ], 'volumeRecommendationOptions' => [ 'shape' => 'VolumeRecommendationOptions', ], 'lastRefreshTimestamp' => [ 'shape' => 'LastRefreshTimestamp', ], 'currentPerformanceRisk' => [ 'shape' => 'CurrentPerformanceRisk', ], 'effectiveRecommendationPreferences' => [ 'shape' => 'EBSEffectiveRecommendationPreferences', ], 'tags' => [ 'shape' => 'Tags', ], ], ], 'VolumeRecommendationOption' => [ 'type' => 'structure', 'members' => [ 'configuration' => [ 'shape' => 'VolumeConfiguration', ], 'performanceRisk' => [ 'shape' => 'PerformanceRisk', ], 'rank' => [ 'shape' => 'Rank', ], 'savingsOpportunity' => [ 'shape' => 'SavingsOpportunity', ], 'savingsOpportunityAfterDiscounts' => [ 'shape' => 'EBSSavingsOpportunityAfterDiscounts', ], ], ], 'VolumeRecommendationOptions' => [ 'type' => 'list', 'member' => [ 'shape' => 'VolumeRecommendationOption', ], ], 'VolumeRecommendations' => [ 'type' => 'list', 'member' => [ 'shape' => 'VolumeRecommendation', ], ], 'VolumeSize' => [ 'type' => 'integer', ], 'VolumeType' => [ 'type' => 'string', ], ],];
