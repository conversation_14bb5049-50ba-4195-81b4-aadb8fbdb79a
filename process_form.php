<?php
// Allow CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');



// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config/database.php';
require_once 'config/env.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Log data yang diterima
    error_log("Received POST data: " . print_r($_POST, true));
    error_log("Received FILES data: " . print_r($_FILES, true));

    // Validasi input
    $required_fields = [
        'profile_type',
        'full_name',
        'phone',
        'email',
        'domicile',
        'has_experience',
        'presentation_frequency',
        'voice_comfort_level',
        'new_tools_approach',
        'meta_ads_learning_plan',
        'challenge_response',
        'motivation_reason',
        'criticism_response',
        'work_preference',
        'career_goals',
        'main_motivation'
    ];

    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            throw new Exception("Field {$field} harus diisi");
        }
    }

    // Validasi email
    if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
        throw new Exception("Format email tidak valid");
    }

    // Validasi nomor telepon (minimal 10 digit, maksimal 15 digit)
    if (!preg_match('/^[0-9]{10,15}$/', $_POST['phone'])) {
        throw new Exception("Format nomor telepon tidak valid");
    }

    // Mulai transaksi
    $pdo->beginTransaction();

    // Insert ke tabel applications
    $sql = "INSERT INTO applications (
        profile_type, full_name, phone, email, domicile, has_experience,
        experience_description, presentation_frequency, ads_improvement_ideas,
        voice_comfort_level, new_tools_approach, meta_ads_learning_plan,
        learning_frustration, quick_learning_decision, ai_usage_experience,
        challenge_response, motivation_reason, criticism_response,
        initiative_moment, work_preference, work_preference_reason,
        career_goals, main_motivation, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";



    $stmt = $pdo->prepare($sql);

    // Eksekusi query dengan data dari form
    $result = $stmt->execute([
        $_POST['profile_type'],
        $_POST['full_name'],
        $_POST['phone'],
        $_POST['email'],
        $_POST['domicile'],
        $_POST['has_experience'],
        $_POST['experience_description'] ?? null,
        $_POST['presentation_frequency'],
        $_POST['ads_improvement_ideas'] ?? null,
        $_POST['voice_comfort_level'],
        $_POST['new_tools_approach'],
        $_POST['meta_ads_learning_plan'],
        $_POST['learning_frustration'] ?? null,
        $_POST['quick_learning_decision'] ?? null,
        $_POST['ai_usage_experience'] ?? null,
        $_POST['challenge_response'],
        $_POST['motivation_reason'],
        $_POST['criticism_response'],
        $_POST['initiative_moment'] ?? null,
        $_POST['work_preference'],
        $_POST['work_preference_reason'] ?? null,
        $_POST['career_goals'],
        $_POST['main_motivation']
    ]);

    // Ambil parameter yang dipakai
    $params = [
        $_POST['profile_type'],
        $_POST['full_name'],
        $_POST['phone'],
        $_POST['email'],
        $_POST['domicile'],
        $_POST['has_experience'],
        $_POST['experience_description'] ?? null,
        $_POST['presentation_frequency'],
        $_POST['ads_improvement_ideas'] ?? null,
        $_POST['voice_comfort_level'],
        $_POST['new_tools_approach'],
        $_POST['meta_ads_learning_plan'],
        $_POST['learning_frustration'] ?? null,
        $_POST['quick_learning_decision'] ?? null,
        $_POST['ai_usage_experience'] ?? null,
        $_POST['challenge_response'],
        $_POST['motivation_reason'],
        $_POST['criticism_response'],
        $_POST['initiative_moment'] ?? null,
        $_POST['work_preference'],
        $_POST['work_preference_reason'] ?? null,
        $_POST['career_goals'],
        $_POST['main_motivation']
    ];

    // Log query ke file log-sql.txt (versi full dengan data bind)
    $log_file = 'log-sql.txt';

    // Ambil SQL mentah
    $raw_sql = $sql;
    // Ganti semua tanda tanya (?) dengan parameter, satu per satu
    foreach ($params as $param) {
        $v = is_null($param) ? 'NULL' : (is_numeric($param) ? $param : "'" . addslashes($param) . "'");
        $raw_sql = preg_replace('/\?/', $v, $raw_sql, 1);
    }

    // Tambahkan timestamp ke log
    $log_line = "[" . date('Y-m-d H:i:s') . "] " . $raw_sql . PHP_EOL;
    file_put_contents($log_file, $log_line, FILE_APPEND);

    $application_id = $pdo->lastInsertId();

    // Handle portfolio files jika ada (dari R2 upload)
    if (isset($_POST['portfolio_files']) && !empty($_POST['portfolio_files'])) {
        $portfolio_files = json_decode($_POST['portfolio_files'], true);

        if (is_array($portfolio_files)) {
            foreach ($portfolio_files as $file) {
                // Insert file info ke database
                $sql = "INSERT INTO portfolio_files (
                    application_id, file_name, file_path, created_at
                ) VALUES (?, ?, ?, NOW())";

                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    $application_id,
                    $file['name'],
                    $file['url']
                ]);
            }
        }
    }

    // Commit transaksi
    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => 'Aplikasi berhasil dikirim'
    ]);

} catch (Exception $e) {
    // Rollback jika terjadi error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }

    // Hapus file yang sudah terupload jika terjadi error
    if (isset($file_path) && file_exists($file_path)) {
        unlink($file_path);
    }

    error_log("Error processing form: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Terjadi kesalahan saat memproses aplikasi: ' . $e->getMessage()
    ]);
}
?>
