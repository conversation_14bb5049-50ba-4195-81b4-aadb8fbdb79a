<?php
// This file was auto-generated from sdk-root/src/data/entityresolution/2018-05-10/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2018-05-10', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'entityresolution', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceAbbreviation' => 'AWSEntityResolution', 'serviceFullName' => 'AWS EntityResolution', 'serviceId' => 'EntityResolution', 'signatureVersion' => 'v4', 'signingName' => 'entityresolution', 'uid' => 'entityresolution-2018-05-10', ], 'operations' => [ 'AddPolicyStatement' => [ 'name' => 'AddPolicyStatement', 'http' => [ 'method' => 'POST', 'requestUri' => '/policies/{arn}/{statementId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AddPolicyStatementInput', ], 'output' => [ 'shape' => 'AddPolicyStatementOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'BatchDeleteUniqueId' => [ 'name' => 'BatchDeleteUniqueId', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/matchingworkflows/{workflowName}/uniqueids', 'responseCode' => 200, ], 'input' => [ 'shape' => 'BatchDeleteUniqueIdInput', ], 'output' => [ 'shape' => 'BatchDeleteUniqueIdOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateIdMappingWorkflow' => [ 'name' => 'CreateIdMappingWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/idmappingworkflows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIdMappingWorkflowInput', ], 'output' => [ 'shape' => 'CreateIdMappingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateIdNamespace' => [ 'name' => 'CreateIdNamespace', 'http' => [ 'method' => 'POST', 'requestUri' => '/idnamespaces', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateIdNamespaceInput', ], 'output' => [ 'shape' => 'CreateIdNamespaceOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateMatchingWorkflow' => [ 'name' => 'CreateMatchingWorkflow', 'http' => [ 'method' => 'POST', 'requestUri' => '/matchingworkflows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateMatchingWorkflowInput', ], 'output' => [ 'shape' => 'CreateMatchingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreateSchemaMapping' => [ 'name' => 'CreateSchemaMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/schemas', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CreateSchemaMappingInput', ], 'output' => [ 'shape' => 'CreateSchemaMappingOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeleteIdMappingWorkflow' => [ 'name' => 'DeleteIdMappingWorkflow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/idmappingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteIdMappingWorkflowInput', ], 'output' => [ 'shape' => 'DeleteIdMappingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteIdNamespace' => [ 'name' => 'DeleteIdNamespace', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/idnamespaces/{idNamespaceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteIdNamespaceInput', ], 'output' => [ 'shape' => 'DeleteIdNamespaceOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteMatchingWorkflow' => [ 'name' => 'DeleteMatchingWorkflow', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/matchingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteMatchingWorkflowInput', ], 'output' => [ 'shape' => 'DeleteMatchingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeletePolicyStatement' => [ 'name' => 'DeletePolicyStatement', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/policies/{arn}/{statementId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePolicyStatementInput', ], 'output' => [ 'shape' => 'DeletePolicyStatementOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'DeleteSchemaMapping' => [ 'name' => 'DeleteSchemaMapping', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/schemas/{schemaName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteSchemaMappingInput', ], 'output' => [ 'shape' => 'DeleteSchemaMappingOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'GenerateMatchId' => [ 'name' => 'GenerateMatchId', 'http' => [ 'method' => 'POST', 'requestUri' => '/matchingworkflows/{workflowName}/generateMatches', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GenerateMatchIdInput', ], 'output' => [ 'shape' => 'GenerateMatchIdOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetIdMappingJob' => [ 'name' => 'GetIdMappingJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/idmappingworkflows/{workflowName}/jobs/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIdMappingJobInput', ], 'output' => [ 'shape' => 'GetIdMappingJobOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetIdMappingWorkflow' => [ 'name' => 'GetIdMappingWorkflow', 'http' => [ 'method' => 'GET', 'requestUri' => '/idmappingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIdMappingWorkflowInput', ], 'output' => [ 'shape' => 'GetIdMappingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetIdNamespace' => [ 'name' => 'GetIdNamespace', 'http' => [ 'method' => 'GET', 'requestUri' => '/idnamespaces/{idNamespaceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetIdNamespaceInput', ], 'output' => [ 'shape' => 'GetIdNamespaceOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetMatchId' => [ 'name' => 'GetMatchId', 'http' => [ 'method' => 'POST', 'requestUri' => '/matchingworkflows/{workflowName}/matches', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMatchIdInput', ], 'output' => [ 'shape' => 'GetMatchIdOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetMatchingJob' => [ 'name' => 'GetMatchingJob', 'http' => [ 'method' => 'GET', 'requestUri' => '/matchingworkflows/{workflowName}/jobs/{jobId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMatchingJobInput', ], 'output' => [ 'shape' => 'GetMatchingJobOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetMatchingWorkflow' => [ 'name' => 'GetMatchingWorkflow', 'http' => [ 'method' => 'GET', 'requestUri' => '/matchingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetMatchingWorkflowInput', ], 'output' => [ 'shape' => 'GetMatchingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetPolicy' => [ 'name' => 'GetPolicy', 'http' => [ 'method' => 'GET', 'requestUri' => '/policies/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPolicyInput', ], 'output' => [ 'shape' => 'GetPolicyOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetProviderService' => [ 'name' => 'GetProviderService', 'http' => [ 'method' => 'GET', 'requestUri' => '/providerservices/{providerName}/{providerServiceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetProviderServiceInput', ], 'output' => [ 'shape' => 'GetProviderServiceOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'GetSchemaMapping' => [ 'name' => 'GetSchemaMapping', 'http' => [ 'method' => 'GET', 'requestUri' => '/schemas/{schemaName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetSchemaMappingInput', ], 'output' => [ 'shape' => 'GetSchemaMappingOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListIdMappingJobs' => [ 'name' => 'ListIdMappingJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/idmappingworkflows/{workflowName}/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIdMappingJobsInput', ], 'output' => [ 'shape' => 'ListIdMappingJobsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListIdMappingWorkflows' => [ 'name' => 'ListIdMappingWorkflows', 'http' => [ 'method' => 'GET', 'requestUri' => '/idmappingworkflows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIdMappingWorkflowsInput', ], 'output' => [ 'shape' => 'ListIdMappingWorkflowsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListIdNamespaces' => [ 'name' => 'ListIdNamespaces', 'http' => [ 'method' => 'GET', 'requestUri' => '/idnamespaces', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListIdNamespacesInput', ], 'output' => [ 'shape' => 'ListIdNamespacesOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListMatchingJobs' => [ 'name' => 'ListMatchingJobs', 'http' => [ 'method' => 'GET', 'requestUri' => '/matchingworkflows/{workflowName}/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMatchingJobsInput', ], 'output' => [ 'shape' => 'ListMatchingJobsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListMatchingWorkflows' => [ 'name' => 'ListMatchingWorkflows', 'http' => [ 'method' => 'GET', 'requestUri' => '/matchingworkflows', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListMatchingWorkflowsInput', ], 'output' => [ 'shape' => 'ListMatchingWorkflowsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListProviderServices' => [ 'name' => 'ListProviderServices', 'http' => [ 'method' => 'GET', 'requestUri' => '/providerservices', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListProviderServicesInput', ], 'output' => [ 'shape' => 'ListProviderServicesOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListSchemaMappings' => [ 'name' => 'ListSchemaMappings', 'http' => [ 'method' => 'GET', 'requestUri' => '/schemas', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListSchemaMappingsInput', ], 'output' => [ 'shape' => 'ListSchemaMappingsOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListTagsForResourceInput', ], 'output' => [ 'shape' => 'ListTagsForResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'PutPolicy' => [ 'name' => 'PutPolicy', 'http' => [ 'method' => 'PUT', 'requestUri' => '/policies/{arn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutPolicyInput', ], 'output' => [ 'shape' => 'PutPolicyOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'StartIdMappingJob' => [ 'name' => 'StartIdMappingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/idmappingworkflows/{workflowName}/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartIdMappingJobInput', ], 'output' => [ 'shape' => 'StartIdMappingJobOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartMatchingJob' => [ 'name' => 'StartMatchingJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/matchingworkflows/{workflowName}/jobs', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartMatchingJobInput', ], 'output' => [ 'shape' => 'StartMatchingJobOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ExceedsLimitException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'TagResourceInput', ], 'output' => [ 'shape' => 'TagResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], ], ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/tags/{resourceArn}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UntagResourceInput', ], 'output' => [ 'shape' => 'UntagResourceOutput', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], ], 'idempotent' => true, ], 'UpdateIdMappingWorkflow' => [ 'name' => 'UpdateIdMappingWorkflow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/idmappingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIdMappingWorkflowInput', ], 'output' => [ 'shape' => 'UpdateIdMappingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateIdNamespace' => [ 'name' => 'UpdateIdNamespace', 'http' => [ 'method' => 'PUT', 'requestUri' => '/idnamespaces/{idNamespaceName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateIdNamespaceInput', ], 'output' => [ 'shape' => 'UpdateIdNamespaceOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateMatchingWorkflow' => [ 'name' => 'UpdateMatchingWorkflow', 'http' => [ 'method' => 'PUT', 'requestUri' => '/matchingworkflows/{workflowName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateMatchingWorkflowInput', ], 'output' => [ 'shape' => 'UpdateMatchingWorkflowOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateSchemaMapping' => [ 'name' => 'UpdateSchemaMapping', 'http' => [ 'method' => 'PUT', 'requestUri' => '/schemas/{schemaName}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateSchemaMappingInput', ], 'output' => [ 'shape' => 'UpdateSchemaMappingOutput', ], 'errors' => [ [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AddPolicyStatementInput' => [ 'type' => 'structure', 'required' => [ 'arn', 'statementId', 'effect', 'action', 'principal', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'arn', ], 'statementId' => [ 'shape' => 'StatementId', 'location' => 'uri', 'locationName' => 'statementId', ], 'effect' => [ 'shape' => 'StatementEffect', ], 'action' => [ 'shape' => 'StatementActionList', ], 'principal' => [ 'shape' => 'StatementPrincipalList', ], 'condition' => [ 'shape' => 'StatementCondition', ], ], ], 'AddPolicyStatementOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'token', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', ], 'token' => [ 'shape' => 'PolicyToken', ], 'policy' => [ 'shape' => 'PolicyDocument', ], ], ], 'AttributeMatchingModel' => [ 'type' => 'string', 'enum' => [ 'ONE_TO_ONE', 'MANY_TO_MANY', ], ], 'AttributeName' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '[a-zA-Z_0-9- ]*', ], 'AwsAccountId' => [ 'type' => 'string', 'pattern' => '\\d{12}', ], 'AwsAccountIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AwsAccountId', ], ], 'BatchDeleteUniqueIdInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'uniqueIds', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], 'inputSource' => [ 'shape' => 'BatchDeleteUniqueIdInputInputSourceString', 'location' => 'header', 'locationName' => 'inputSource', ], 'uniqueIds' => [ 'shape' => 'UniqueIdList', 'location' => 'header', 'locationName' => 'uniqueIds', ], ], ], 'BatchDeleteUniqueIdInputInputSourceString' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov|aws-cn):glue:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(table/[a-zA-Z_0-9-]{1,255}/[a-zA-Z_0-9-]{1,255})', ], 'BatchDeleteUniqueIdOutput' => [ 'type' => 'structure', 'required' => [ 'status', 'errors', 'deleted', 'disconnectedUniqueIds', ], 'members' => [ 'status' => [ 'shape' => 'DeleteUniqueIdStatus', ], 'errors' => [ 'shape' => 'DeleteUniqueIdErrorsList', ], 'deleted' => [ 'shape' => 'DeletedUniqueIdList', ], 'disconnectedUniqueIds' => [ 'shape' => 'DisconnectedUniqueIdsList', ], ], ], 'Boolean' => [ 'type' => 'boolean', 'box' => true, ], 'ConflictException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'CreateIdMappingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'inputSourceConfig', 'idMappingTechniques', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'roleArn' => [ 'shape' => 'IdMappingRoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateIdMappingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'workflowArn', 'inputSourceConfig', 'idMappingTechniques', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', ], 'workflowArn' => [ 'shape' => 'IdMappingWorkflowArn', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'roleArn' => [ 'shape' => 'IdMappingRoleArn', ], ], ], 'CreateIdNamespaceInput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceName', 'type', ], 'members' => [ 'idNamespaceName' => [ 'shape' => 'EntityName', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'IdNamespaceInputSourceConfig', ], 'idMappingWorkflowProperties' => [ 'shape' => 'IdNamespaceIdMappingWorkflowPropertiesList', ], 'type' => [ 'shape' => 'IdNamespaceType', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateIdNamespaceOutput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceName', 'idNamespaceArn', 'type', 'createdAt', 'updatedAt', ], 'members' => [ 'idNamespaceName' => [ 'shape' => 'EntityName', ], 'idNamespaceArn' => [ 'shape' => 'IdNamespaceArn', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'IdNamespaceInputSourceConfig', ], 'idMappingWorkflowProperties' => [ 'shape' => 'IdNamespaceIdMappingWorkflowPropertiesList', ], 'type' => [ 'shape' => 'IdNamespaceType', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateMatchingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'roleArn', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'roleArn' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateMatchingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'workflowArn', 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'roleArn', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', ], 'workflowArn' => [ 'shape' => 'MatchingWorkflowArn', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'roleArn' => [ 'shape' => 'String', ], ], ], 'CreateSchemaMappingInput' => [ 'type' => 'structure', 'required' => [ 'schemaName', 'mappedInputFields', ], 'members' => [ 'schemaName' => [ 'shape' => 'EntityName', ], 'description' => [ 'shape' => 'Description', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'CreateSchemaMappingOutput' => [ 'type' => 'structure', 'required' => [ 'schemaName', 'schemaArn', 'description', 'mappedInputFields', ], 'members' => [ 'schemaName' => [ 'shape' => 'EntityName', ], 'schemaArn' => [ 'shape' => 'SchemaMappingArn', ], 'description' => [ 'shape' => 'Description', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], ], ], 'DeleteIdMappingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'DeleteIdMappingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], ], 'DeleteIdNamespaceInput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceName', ], 'members' => [ 'idNamespaceName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'idNamespaceName', ], ], ], 'DeleteIdNamespaceOutput' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], ], 'DeleteMatchingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'DeleteMatchingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], ], 'DeletePolicyStatementInput' => [ 'type' => 'structure', 'required' => [ 'arn', 'statementId', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'arn', ], 'statementId' => [ 'shape' => 'StatementId', 'location' => 'uri', 'locationName' => 'statementId', ], ], ], 'DeletePolicyStatementOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'token', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', ], 'token' => [ 'shape' => 'PolicyToken', ], 'policy' => [ 'shape' => 'PolicyDocument', ], ], ], 'DeleteSchemaMappingInput' => [ 'type' => 'structure', 'required' => [ 'schemaName', ], 'members' => [ 'schemaName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'schemaName', ], ], ], 'DeleteSchemaMappingOutput' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], ], 'DeleteUniqueIdError' => [ 'type' => 'structure', 'required' => [ 'uniqueId', 'errorType', ], 'members' => [ 'uniqueId' => [ 'shape' => 'HeaderSafeUniqueId', ], 'errorType' => [ 'shape' => 'DeleteUniqueIdErrorType', ], ], ], 'DeleteUniqueIdErrorType' => [ 'type' => 'string', 'enum' => [ 'SERVICE_ERROR', 'VALIDATION_ERROR', ], ], 'DeleteUniqueIdErrorsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeleteUniqueIdError', ], ], 'DeleteUniqueIdStatus' => [ 'type' => 'string', 'enum' => [ 'COMPLETED', 'ACCEPTED', ], ], 'DeletedUniqueId' => [ 'type' => 'structure', 'required' => [ 'uniqueId', ], 'members' => [ 'uniqueId' => [ 'shape' => 'HeaderSafeUniqueId', ], ], ], 'DeletedUniqueIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'DeletedUniqueId', ], ], 'Description' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'DisconnectedUniqueIdsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HeaderSafeUniqueId', ], ], 'Document' => [ 'type' => 'structure', 'members' => [], 'document' => true, ], 'EntityName' => [ 'type' => 'string', 'max' => 255, 'min' => 1, 'pattern' => '[a-zA-Z_0-9-]*', ], 'EntityNameOrIdMappingWorkflowArn' => [ 'type' => 'string', 'pattern' => '[a-zA-Z_0-9-=+/]*$|^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idmappingworkflow/[a-zA-Z_0-9-]{1,255})', ], 'EntityNameOrIdNamespaceArn' => [ 'type' => 'string', 'pattern' => '[a-zA-Z_0-9-=+/]*$|^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idnamespace/[a-zA-Z_0-9-]{1,255})', ], 'ErrorDetails' => [ 'type' => 'structure', 'members' => [ 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ExceedsLimitException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'quotaName' => [ 'shape' => 'String', ], 'quotaValue' => [ 'shape' => 'Integer', ], ], 'error' => [ 'httpStatusCode' => 402, 'senderFault' => true, ], 'exception' => true, ], 'FailedRecord' => [ 'type' => 'structure', 'required' => [ 'inputSourceARN', 'uniqueId', 'errorMessage', ], 'members' => [ 'inputSourceARN' => [ 'shape' => 'FailedRecordInputSourceARNString', ], 'uniqueId' => [ 'shape' => 'String', ], 'errorMessage' => [ 'shape' => 'ErrorMessage', ], ], ], 'FailedRecordInputSourceARNString' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idnamespace/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(matchingworkflow/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):glue:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(table/[a-zA-Z_0-9-]{1,255}/[a-zA-Z_0-9-]{1,255})', ], 'FailedRecordsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'FailedRecord', ], ], 'GenerateMatchIdInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'records', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], 'records' => [ 'shape' => 'GenerateMatchIdInputRecordsList', ], 'processingType' => [ 'shape' => 'ProcessingType', ], ], ], 'GenerateMatchIdInputRecordsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Record', ], 'max' => 1, 'min' => 1, ], 'GenerateMatchIdOutput' => [ 'type' => 'structure', 'required' => [ 'matchGroups', 'failedRecords', ], 'members' => [ 'matchGroups' => [ 'shape' => 'MatchGroupsList', ], 'failedRecords' => [ 'shape' => 'FailedRecordsList', ], ], ], 'GetIdMappingJobInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'jobId', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityNameOrIdMappingWorkflowArn', 'location' => 'uri', 'locationName' => 'workflowName', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'GetIdMappingJobOutput' => [ 'type' => 'structure', 'required' => [ 'jobId', 'status', 'startTime', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'status' => [ 'shape' => 'JobStatus', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'metrics' => [ 'shape' => 'IdMappingJobMetrics', ], 'errorDetails' => [ 'shape' => 'ErrorDetails', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingJobOutputSourceConfig', ], ], ], 'GetIdMappingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'GetIdMappingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'workflowArn', 'inputSourceConfig', 'idMappingTechniques', 'createdAt', 'updatedAt', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', ], 'workflowArn' => [ 'shape' => 'IdMappingWorkflowArn', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'roleArn' => [ 'shape' => 'IdMappingRoleArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'GetIdNamespaceInput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceName', ], 'members' => [ 'idNamespaceName' => [ 'shape' => 'EntityNameOrIdNamespaceArn', 'location' => 'uri', 'locationName' => 'idNamespaceName', ], ], ], 'GetIdNamespaceOutput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceName', 'idNamespaceArn', 'type', 'createdAt', 'updatedAt', ], 'members' => [ 'idNamespaceName' => [ 'shape' => 'EntityName', ], 'idNamespaceArn' => [ 'shape' => 'IdNamespaceArn', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'IdNamespaceInputSourceConfig', ], 'idMappingWorkflowProperties' => [ 'shape' => 'IdNamespaceIdMappingWorkflowPropertiesList', ], 'type' => [ 'shape' => 'IdNamespaceType', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'GetMatchIdInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'record', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], 'record' => [ 'shape' => 'RecordAttributeMap', ], 'applyNormalization' => [ 'shape' => 'Boolean', ], ], ], 'GetMatchIdOutput' => [ 'type' => 'structure', 'members' => [ 'matchId' => [ 'shape' => 'String', ], 'matchRule' => [ 'shape' => 'String', ], ], ], 'GetMatchingJobInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'jobId', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], 'jobId' => [ 'shape' => 'JobId', 'location' => 'uri', 'locationName' => 'jobId', ], ], ], 'GetMatchingJobOutput' => [ 'type' => 'structure', 'required' => [ 'jobId', 'status', 'startTime', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'status' => [ 'shape' => 'JobStatus', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], 'metrics' => [ 'shape' => 'JobMetrics', ], 'errorDetails' => [ 'shape' => 'ErrorDetails', ], 'outputSourceConfig' => [ 'shape' => 'JobOutputSourceConfig', ], ], ], 'GetMatchingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'GetMatchingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'workflowArn', 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'createdAt', 'updatedAt', 'roleArn', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', ], 'workflowArn' => [ 'shape' => 'MatchingWorkflowArn', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'roleArn' => [ 'shape' => 'String', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'GetPolicyInput' => [ 'type' => 'structure', 'required' => [ 'arn', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'arn', ], ], ], 'GetPolicyOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'token', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', ], 'token' => [ 'shape' => 'PolicyToken', ], 'policy' => [ 'shape' => 'PolicyDocument', ], ], ], 'GetProviderServiceInput' => [ 'type' => 'structure', 'required' => [ 'providerName', 'providerServiceName', ], 'members' => [ 'providerName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'providerName', ], 'providerServiceName' => [ 'shape' => 'ProviderServiceArn', 'location' => 'uri', 'locationName' => 'providerServiceName', ], ], ], 'GetProviderServiceOutput' => [ 'type' => 'structure', 'required' => [ 'providerName', 'providerServiceName', 'providerServiceDisplayName', 'providerServiceType', 'providerServiceArn', 'providerEndpointConfiguration', 'anonymizedOutput', 'providerEntityOutputDefinition', ], 'members' => [ 'providerName' => [ 'shape' => 'EntityName', ], 'providerServiceName' => [ 'shape' => 'EntityName', ], 'providerServiceDisplayName' => [ 'shape' => 'ProviderServiceDisplayName', ], 'providerServiceType' => [ 'shape' => 'ServiceType', ], 'providerServiceArn' => [ 'shape' => 'ProviderServiceArn', ], 'providerConfigurationDefinition' => [ 'shape' => 'Document', ], 'providerIdNameSpaceConfiguration' => [ 'shape' => 'ProviderIdNameSpaceConfiguration', ], 'providerJobConfiguration' => [ 'shape' => 'Document', ], 'providerEndpointConfiguration' => [ 'shape' => 'ProviderEndpointConfiguration', ], 'anonymizedOutput' => [ 'shape' => 'Boolean', ], 'providerEntityOutputDefinition' => [ 'shape' => 'Document', ], 'providerIntermediateDataAccessConfiguration' => [ 'shape' => 'ProviderIntermediateDataAccessConfiguration', ], 'providerComponentSchema' => [ 'shape' => 'ProviderComponentSchema', ], ], ], 'GetSchemaMappingInput' => [ 'type' => 'structure', 'required' => [ 'schemaName', ], 'members' => [ 'schemaName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'schemaName', ], ], ], 'GetSchemaMappingOutput' => [ 'type' => 'structure', 'required' => [ 'schemaName', 'schemaArn', 'mappedInputFields', 'createdAt', 'updatedAt', 'hasWorkflows', ], 'members' => [ 'schemaName' => [ 'shape' => 'EntityName', ], 'schemaArn' => [ 'shape' => 'SchemaMappingArn', ], 'description' => [ 'shape' => 'Description', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'tags' => [ 'shape' => 'TagMap', ], 'hasWorkflows' => [ 'shape' => 'Boolean', ], ], ], 'HeaderSafeUniqueId' => [ 'type' => 'string', 'max' => 780, 'min' => 1, 'pattern' => '[a-zA-Z_0-9-+=/,]*', ], 'IdMappingJobMetrics' => [ 'type' => 'structure', 'members' => [ 'inputRecords' => [ 'shape' => 'Integer', ], 'totalRecordsProcessed' => [ 'shape' => 'Integer', ], 'recordsNotProcessed' => [ 'shape' => 'Integer', ], 'totalMappedRecords' => [ 'shape' => 'Integer', ], 'totalMappedSourceRecords' => [ 'shape' => 'Integer', ], 'totalMappedTargetRecords' => [ 'shape' => 'Integer', ], 'uniqueRecordsLoaded' => [ 'shape' => 'Integer', ], ], ], 'IdMappingJobOutputSource' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'outputS3Path', ], 'members' => [ 'roleArn' => [ 'shape' => 'RoleArn', ], 'outputS3Path' => [ 'shape' => 'S3Path', ], 'KMSArn' => [ 'shape' => 'KMSArn', ], ], ], 'IdMappingJobOutputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingJobOutputSource', ], 'max' => 1, 'min' => 1, ], 'IdMappingRoleArn' => [ 'type' => 'string', 'max' => 512, 'min' => 0, 'pattern' => '$|^arn:aws:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+', ], 'IdMappingRuleBasedProperties' => [ 'type' => 'structure', 'required' => [ 'ruleDefinitionType', 'attributeMatchingModel', 'recordMatchingModel', ], 'members' => [ 'rules' => [ 'shape' => 'IdMappingRuleBasedPropertiesRulesList', ], 'ruleDefinitionType' => [ 'shape' => 'IdMappingWorkflowRuleDefinitionType', ], 'attributeMatchingModel' => [ 'shape' => 'AttributeMatchingModel', ], 'recordMatchingModel' => [ 'shape' => 'RecordMatchingModel', ], ], ], 'IdMappingRuleBasedPropertiesRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], 'max' => 25, 'min' => 1, ], 'IdMappingTechniques' => [ 'type' => 'structure', 'required' => [ 'idMappingType', ], 'members' => [ 'idMappingType' => [ 'shape' => 'IdMappingType', ], 'ruleBasedProperties' => [ 'shape' => 'IdMappingRuleBasedProperties', ], 'providerProperties' => [ 'shape' => 'ProviderProperties', ], ], ], 'IdMappingType' => [ 'type' => 'string', 'enum' => [ 'PROVIDER', 'RULE_BASED', ], ], 'IdMappingWorkflowArn' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idmappingworkflow/[a-zA-Z_0-9-]{1,255})', ], 'IdMappingWorkflowInputSource' => [ 'type' => 'structure', 'required' => [ 'inputSourceARN', ], 'members' => [ 'inputSourceARN' => [ 'shape' => 'IdMappingWorkflowInputSourceInputSourceARNString', ], 'schemaName' => [ 'shape' => 'EntityName', ], 'type' => [ 'shape' => 'IdNamespaceType', ], ], ], 'IdMappingWorkflowInputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingWorkflowInputSource', ], 'max' => 20, 'min' => 1, ], 'IdMappingWorkflowInputSourceInputSourceARNString' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idnamespace/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(matchingworkflow/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):glue:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(table/[a-zA-Z_0-9-]{1,255}/[a-zA-Z_0-9-]{1,255})', ], 'IdMappingWorkflowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingWorkflowSummary', ], ], 'IdMappingWorkflowOutputSource' => [ 'type' => 'structure', 'required' => [ 'outputS3Path', ], 'members' => [ 'outputS3Path' => [ 'shape' => 'S3Path', ], 'KMSArn' => [ 'shape' => 'KMSArn', ], ], ], 'IdMappingWorkflowOutputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingWorkflowOutputSource', ], 'max' => 1, 'min' => 1, ], 'IdMappingWorkflowRuleDefinitionType' => [ 'type' => 'string', 'enum' => [ 'SOURCE', 'TARGET', ], ], 'IdMappingWorkflowRuleDefinitionTypeList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdMappingWorkflowRuleDefinitionType', ], ], 'IdMappingWorkflowSummary' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'workflowArn', 'createdAt', 'updatedAt', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', ], 'workflowArn' => [ 'shape' => 'IdMappingWorkflowArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'IdNamespaceArn' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idnamespace/[a-zA-Z_0-9-]{1,255})', ], 'IdNamespaceIdMappingWorkflowMetadata' => [ 'type' => 'structure', 'required' => [ 'idMappingType', ], 'members' => [ 'idMappingType' => [ 'shape' => 'IdMappingType', ], ], ], 'IdNamespaceIdMappingWorkflowMetadataList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdNamespaceIdMappingWorkflowMetadata', ], 'max' => 1, 'min' => 1, ], 'IdNamespaceIdMappingWorkflowProperties' => [ 'type' => 'structure', 'required' => [ 'idMappingType', ], 'members' => [ 'idMappingType' => [ 'shape' => 'IdMappingType', ], 'ruleBasedProperties' => [ 'shape' => 'NamespaceRuleBasedProperties', ], 'providerProperties' => [ 'shape' => 'NamespaceProviderProperties', ], ], ], 'IdNamespaceIdMappingWorkflowPropertiesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdNamespaceIdMappingWorkflowProperties', ], 'max' => 1, 'min' => 1, ], 'IdNamespaceInputSource' => [ 'type' => 'structure', 'required' => [ 'inputSourceARN', ], 'members' => [ 'inputSourceARN' => [ 'shape' => 'IdNamespaceInputSourceInputSourceARNString', ], 'schemaName' => [ 'shape' => 'EntityName', ], ], ], 'IdNamespaceInputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdNamespaceInputSource', ], 'max' => 20, 'min' => 0, ], 'IdNamespaceInputSourceInputSourceARNString' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idnamespace/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(matchingworkflow/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):glue:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(table/[a-zA-Z_0-9-]{1,255}/[a-zA-Z_0-9-]{1,255})', ], 'IdNamespaceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'IdNamespaceSummary', ], ], 'IdNamespaceSummary' => [ 'type' => 'structure', 'required' => [ 'idNamespaceName', 'idNamespaceArn', 'type', 'createdAt', 'updatedAt', ], 'members' => [ 'idNamespaceName' => [ 'shape' => 'EntityName', ], 'idNamespaceArn' => [ 'shape' => 'IdNamespaceArn', ], 'description' => [ 'shape' => 'Description', ], 'idMappingWorkflowProperties' => [ 'shape' => 'IdNamespaceIdMappingWorkflowMetadataList', ], 'type' => [ 'shape' => 'IdNamespaceType', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'IdNamespaceType' => [ 'type' => 'string', 'enum' => [ 'SOURCE', 'TARGET', ], ], 'IncrementalRunConfig' => [ 'type' => 'structure', 'members' => [ 'incrementalRunType' => [ 'shape' => 'IncrementalRunType', ], ], ], 'IncrementalRunType' => [ 'type' => 'string', 'enum' => [ 'IMMEDIATE', ], ], 'InputSource' => [ 'type' => 'structure', 'required' => [ 'inputSourceARN', 'schemaName', ], 'members' => [ 'inputSourceARN' => [ 'shape' => 'InputSourceInputSourceARNString', ], 'schemaName' => [ 'shape' => 'EntityName', ], 'applyNormalization' => [ 'shape' => 'Boolean', ], ], ], 'InputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'InputSource', ], 'max' => 20, 'min' => 1, ], 'InputSourceInputSourceARNString' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idnamespace/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(matchingworkflow/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):glue:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(table/[a-zA-Z_0-9-]{1,255}/[a-zA-Z_0-9-]{1,255})', ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'IntermediateSourceConfiguration' => [ 'type' => 'structure', 'required' => [ 'intermediateS3Path', ], 'members' => [ 'intermediateS3Path' => [ 'shape' => 'S3Path', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'JobId' => [ 'type' => 'string', 'pattern' => '[a-f0-9]{32}', ], 'JobList' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobSummary', ], ], 'JobMetrics' => [ 'type' => 'structure', 'members' => [ 'inputRecords' => [ 'shape' => 'Integer', ], 'totalRecordsProcessed' => [ 'shape' => 'Integer', ], 'recordsNotProcessed' => [ 'shape' => 'Integer', ], 'matchIDs' => [ 'shape' => 'Integer', ], ], ], 'JobOutputSource' => [ 'type' => 'structure', 'required' => [ 'roleArn', 'outputS3Path', ], 'members' => [ 'roleArn' => [ 'shape' => 'RoleArn', ], 'outputS3Path' => [ 'shape' => 'S3Path', ], 'KMSArn' => [ 'shape' => 'KMSArn', ], ], ], 'JobOutputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'JobOutputSource', ], 'max' => 1, 'min' => 1, ], 'JobStatus' => [ 'type' => 'string', 'enum' => [ 'RUNNING', 'SUCCEEDED', 'FAILED', 'QUEUED', ], ], 'JobSummary' => [ 'type' => 'structure', 'required' => [ 'jobId', 'status', 'startTime', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'status' => [ 'shape' => 'JobStatus', ], 'startTime' => [ 'shape' => 'Timestamp', ], 'endTime' => [ 'shape' => 'Timestamp', ], ], ], 'KMSArn' => [ 'type' => 'string', 'pattern' => 'arn:aws:kms:.*:[0-9]+:.*', ], 'ListIdMappingJobsInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityNameOrIdMappingWorkflowArn', 'location' => 'uri', 'locationName' => 'workflowName', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListIdMappingJobsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListIdMappingJobsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'ListIdMappingJobsOutput' => [ 'type' => 'structure', 'members' => [ 'jobs' => [ 'shape' => 'JobList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIdMappingWorkflowsInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListIdMappingWorkflowsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListIdMappingWorkflowsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, ], 'ListIdMappingWorkflowsOutput' => [ 'type' => 'structure', 'members' => [ 'workflowSummaries' => [ 'shape' => 'IdMappingWorkflowList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListIdNamespacesInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListIdNamespacesInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListIdNamespacesInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, ], 'ListIdNamespacesOutput' => [ 'type' => 'structure', 'members' => [ 'idNamespaceSummaries' => [ 'shape' => 'IdNamespaceList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMatchingJobsInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListMatchingJobsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListMatchingJobsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 1, ], 'ListMatchingJobsOutput' => [ 'type' => 'structure', 'members' => [ 'jobs' => [ 'shape' => 'JobList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListMatchingWorkflowsInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListMatchingWorkflowsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListMatchingWorkflowsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, ], 'ListMatchingWorkflowsOutput' => [ 'type' => 'structure', 'members' => [ 'workflowSummaries' => [ 'shape' => 'MatchingWorkflowList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListProviderServicesInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListProviderServicesInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'providerName' => [ 'shape' => 'EntityName', 'location' => 'querystring', 'locationName' => 'providerName', ], ], ], 'ListProviderServicesInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, 'min' => 15, ], 'ListProviderServicesOutput' => [ 'type' => 'structure', 'members' => [ 'providerServiceSummaries' => [ 'shape' => 'ProviderServiceList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListSchemaMappingsInput' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'NextToken', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'ListSchemaMappingsInputMaxResultsInteger', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListSchemaMappingsInputMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 25, ], 'ListSchemaMappingsOutput' => [ 'type' => 'structure', 'members' => [ 'schemaList' => [ 'shape' => 'SchemaMappingList', ], 'nextToken' => [ 'shape' => 'NextToken', ], ], ], 'ListTagsForResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', ], 'members' => [ 'resourceArn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], ], ], 'ListTagsForResourceOutput' => [ 'type' => 'structure', 'required' => [ 'tags', ], 'members' => [ 'tags' => [ 'shape' => 'TagMap', ], ], ], 'MatchGroup' => [ 'type' => 'structure', 'required' => [ 'records', 'matchId', 'matchRule', ], 'members' => [ 'records' => [ 'shape' => 'MatchedRecordsList', ], 'matchId' => [ 'shape' => 'String', ], 'matchRule' => [ 'shape' => 'String', ], ], ], 'MatchGroupsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchGroup', ], ], 'MatchPurpose' => [ 'type' => 'string', 'enum' => [ 'IDENTIFIER_GENERATION', 'INDEXING', ], ], 'MatchedRecord' => [ 'type' => 'structure', 'required' => [ 'inputSourceARN', 'recordId', ], 'members' => [ 'inputSourceARN' => [ 'shape' => 'MatchedRecordInputSourceARNString', ], 'recordId' => [ 'shape' => 'String', ], ], ], 'MatchedRecordInputSourceARNString' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idnamespace/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(matchingworkflow/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):glue:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(table/[a-zA-Z_0-9-]{1,255}/[a-zA-Z_0-9-]{1,255})', ], 'MatchedRecordsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchedRecord', ], ], 'MatchingWorkflowArn' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(matchingworkflow/[a-zA-Z_0-9-]{1,255})', ], 'MatchingWorkflowList' => [ 'type' => 'list', 'member' => [ 'shape' => 'MatchingWorkflowSummary', ], ], 'MatchingWorkflowSummary' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'workflowArn', 'createdAt', 'updatedAt', 'resolutionType', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', ], 'workflowArn' => [ 'shape' => 'MatchingWorkflowArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'resolutionType' => [ 'shape' => 'ResolutionType', ], ], ], 'NamespaceProviderProperties' => [ 'type' => 'structure', 'required' => [ 'providerServiceArn', ], 'members' => [ 'providerServiceArn' => [ 'shape' => 'ProviderServiceArn', ], 'providerConfiguration' => [ 'shape' => 'Document', ], ], ], 'NamespaceRuleBasedProperties' => [ 'type' => 'structure', 'members' => [ 'rules' => [ 'shape' => 'NamespaceRuleBasedPropertiesRulesList', ], 'ruleDefinitionTypes' => [ 'shape' => 'IdMappingWorkflowRuleDefinitionTypeList', ], 'attributeMatchingModel' => [ 'shape' => 'AttributeMatchingModel', ], 'recordMatchingModels' => [ 'shape' => 'RecordMatchingModelList', ], ], ], 'NamespaceRuleBasedPropertiesRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], 'max' => 25, 'min' => 1, ], 'NextToken' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => '[a-zA-Z_0-9-=+/]*', ], 'OutputAttribute' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'AttributeName', ], 'hashed' => [ 'shape' => 'Boolean', ], ], ], 'OutputSource' => [ 'type' => 'structure', 'required' => [ 'outputS3Path', 'output', ], 'members' => [ 'outputS3Path' => [ 'shape' => 'S3Path', ], 'KMSArn' => [ 'shape' => 'KMSArn', ], 'output' => [ 'shape' => 'OutputSourceOutputList', ], 'applyNormalization' => [ 'shape' => 'Boolean', ], ], ], 'OutputSourceConfig' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputSource', ], 'max' => 1, 'min' => 1, ], 'OutputSourceOutputList' => [ 'type' => 'list', 'member' => [ 'shape' => 'OutputAttribute', ], 'max' => 750, 'min' => 0, ], 'PolicyDocument' => [ 'type' => 'string', 'max' => 40960, 'min' => 1, ], 'PolicyToken' => [ 'type' => 'string', 'max' => 36, 'min' => 36, 'pattern' => '[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}', ], 'ProcessingType' => [ 'type' => 'string', 'enum' => [ 'CONSISTENT', 'EVENTUAL', 'EVENTUAL_NO_LOOKUP', ], ], 'ProviderComponentSchema' => [ 'type' => 'structure', 'members' => [ 'schemas' => [ 'shape' => 'Schemas', ], 'providerSchemaAttributes' => [ 'shape' => 'ProviderSchemaAttributes', ], ], ], 'ProviderEndpointConfiguration' => [ 'type' => 'structure', 'members' => [ 'marketplaceConfiguration' => [ 'shape' => 'ProviderMarketplaceConfiguration', ], ], 'union' => true, ], 'ProviderIdNameSpaceConfiguration' => [ 'type' => 'structure', 'members' => [ 'description' => [ 'shape' => 'String', ], 'providerTargetConfigurationDefinition' => [ 'shape' => 'Document', ], 'providerSourceConfigurationDefinition' => [ 'shape' => 'Document', ], ], ], 'ProviderIntermediateDataAccessConfiguration' => [ 'type' => 'structure', 'members' => [ 'awsAccountIds' => [ 'shape' => 'AwsAccountIdList', ], 'requiredBucketActions' => [ 'shape' => 'RequiredBucketActionsList', ], ], ], 'ProviderMarketplaceConfiguration' => [ 'type' => 'structure', 'required' => [ 'dataSetId', 'revisionId', 'assetId', 'listingId', ], 'members' => [ 'dataSetId' => [ 'shape' => 'String', ], 'revisionId' => [ 'shape' => 'String', ], 'assetId' => [ 'shape' => 'String', ], 'listingId' => [ 'shape' => 'String', ], ], ], 'ProviderProperties' => [ 'type' => 'structure', 'required' => [ 'providerServiceArn', ], 'members' => [ 'providerServiceArn' => [ 'shape' => 'ProviderServiceArn', ], 'providerConfiguration' => [ 'shape' => 'Document', ], 'intermediateSourceConfiguration' => [ 'shape' => 'IntermediateSourceConfiguration', ], ], ], 'ProviderSchemaAttribute' => [ 'type' => 'structure', 'required' => [ 'fieldName', 'type', ], 'members' => [ 'fieldName' => [ 'shape' => 'AttributeName', ], 'type' => [ 'shape' => 'SchemaAttributeType', ], 'subType' => [ 'shape' => 'AttributeName', ], 'hashing' => [ 'shape' => 'Boolean', ], ], ], 'ProviderSchemaAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProviderSchemaAttribute', ], ], 'ProviderServiceArn' => [ 'type' => 'string', 'max' => 255, 'min' => 20, 'pattern' => 'arn:(aws|aws-us-gov|aws-cn):(entityresolution):([a-z]{2}-[a-z]{1,10}-[0-9])::providerservice/([a-zA-Z0-9_-]{1,255})/([a-zA-Z0-9_-]{1,255})', ], 'ProviderServiceDisplayName' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'ProviderServiceList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProviderServiceSummary', ], ], 'ProviderServiceSummary' => [ 'type' => 'structure', 'required' => [ 'providerServiceArn', 'providerName', 'providerServiceDisplayName', 'providerServiceName', 'providerServiceType', ], 'members' => [ 'providerServiceArn' => [ 'shape' => 'ProviderServiceArn', ], 'providerName' => [ 'shape' => 'EntityName', ], 'providerServiceDisplayName' => [ 'shape' => 'ProviderServiceDisplayName', ], 'providerServiceName' => [ 'shape' => 'EntityName', ], 'providerServiceType' => [ 'shape' => 'ServiceType', ], ], ], 'PutPolicyInput' => [ 'type' => 'structure', 'required' => [ 'arn', 'policy', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'arn', ], 'token' => [ 'shape' => 'PolicyToken', ], 'policy' => [ 'shape' => 'PolicyDocument', ], ], ], 'PutPolicyOutput' => [ 'type' => 'structure', 'required' => [ 'arn', 'token', ], 'members' => [ 'arn' => [ 'shape' => 'VeniceGlobalArn', ], 'token' => [ 'shape' => 'PolicyToken', ], 'policy' => [ 'shape' => 'PolicyDocument', ], ], ], 'Record' => [ 'type' => 'structure', 'required' => [ 'inputSourceARN', 'uniqueId', 'recordAttributeMap', ], 'members' => [ 'inputSourceARN' => [ 'shape' => 'RecordInputSourceARNString', ], 'uniqueId' => [ 'shape' => 'UniqueId', ], 'recordAttributeMap' => [ 'shape' => 'RecordAttributeMapString255', ], ], ], 'RecordAttributeMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'RecordAttributeMapKeyString', ], 'value' => [ 'shape' => 'RecordAttributeMapValueString', ], 'sensitive' => true, ], 'RecordAttributeMapKeyString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '[a-zA-Z_0-9- \\t]*', ], 'RecordAttributeMapString255' => [ 'type' => 'map', 'key' => [ 'shape' => 'RecordAttributeMapString255KeyString', ], 'value' => [ 'shape' => 'RecordAttributeMapString255ValueString', ], 'sensitive' => true, ], 'RecordAttributeMapString255KeyString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'RecordAttributeMapString255ValueString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, ], 'RecordAttributeMapValueString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '[a-zA-Z_0-9-./@ ()+\\t]*', ], 'RecordInputSourceARNString' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(idnamespace/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(matchingworkflow/[a-zA-Z_0-9-]{1,255})$|^arn:(aws|aws-us-gov|aws-cn):glue:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(table/[a-zA-Z_0-9-]{1,255}/[a-zA-Z_0-9-]{1,255})', ], 'RecordMatchingModel' => [ 'type' => 'string', 'enum' => [ 'ONE_SOURCE_TO_ONE_TARGET', 'MANY_SOURCE_TO_ONE_TARGET', ], ], 'RecordMatchingModelList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RecordMatchingModel', ], ], 'RequiredBucketActionsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'ResolutionTechniques' => [ 'type' => 'structure', 'required' => [ 'resolutionType', ], 'members' => [ 'resolutionType' => [ 'shape' => 'ResolutionType', ], 'ruleBasedProperties' => [ 'shape' => 'RuleBasedProperties', ], 'providerProperties' => [ 'shape' => 'ProviderProperties', ], ], ], 'ResolutionType' => [ 'type' => 'string', 'enum' => [ 'RULE_MATCHING', 'ML_MATCHING', 'PROVIDER', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'RoleArn' => [ 'type' => 'string', 'max' => 512, 'min' => 32, 'pattern' => 'arn:aws:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+', ], 'Rule' => [ 'type' => 'structure', 'required' => [ 'ruleName', 'matchingKeys', ], 'members' => [ 'ruleName' => [ 'shape' => 'RuleRuleNameString', ], 'matchingKeys' => [ 'shape' => 'RuleMatchingKeysList', ], ], ], 'RuleBasedProperties' => [ 'type' => 'structure', 'required' => [ 'rules', 'attributeMatchingModel', ], 'members' => [ 'rules' => [ 'shape' => 'RuleBasedPropertiesRulesList', ], 'attributeMatchingModel' => [ 'shape' => 'AttributeMatchingModel', ], 'matchPurpose' => [ 'shape' => 'MatchPurpose', ], ], ], 'RuleBasedPropertiesRulesList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Rule', ], 'max' => 25, 'min' => 1, ], 'RuleMatchingKeysList' => [ 'type' => 'list', 'member' => [ 'shape' => 'AttributeName', ], 'max' => 15, 'min' => 0, ], 'RuleRuleNameString' => [ 'type' => 'string', 'max' => 255, 'min' => 0, 'pattern' => '[a-zA-Z_0-9- ]*', ], 'S3Path' => [ 'type' => 'string', 'max' => 1024, 'min' => 1, 'pattern' => 's3://[a-z0-9][\\.\\-a-z0-9]{1,61}[a-z0-9](/.*)?', ], 'SchemaAttributeType' => [ 'type' => 'string', 'enum' => [ 'NAME', 'NAME_FIRST', 'NAME_MIDDLE', 'NAME_LAST', 'ADDRESS', 'ADDRESS_STREET1', 'ADDRESS_STREET2', 'ADDRESS_STREET3', 'ADDRESS_CITY', 'ADDRESS_STATE', 'ADDRESS_COUNTRY', 'ADDRESS_POSTALCODE', 'PHONE', 'PHONE_NUMBER', 'PHONE_COUNTRYCODE', 'EMAIL_ADDRESS', 'UNIQUE_ID', 'DATE', 'STRING', 'PROVIDER_ID', 'IPV4', 'IPV6', 'MAID', ], ], 'SchemaInputAttribute' => [ 'type' => 'structure', 'required' => [ 'fieldName', 'type', ], 'members' => [ 'fieldName' => [ 'shape' => 'AttributeName', ], 'type' => [ 'shape' => 'SchemaAttributeType', ], 'groupName' => [ 'shape' => 'AttributeName', ], 'matchKey' => [ 'shape' => 'AttributeName', ], 'subType' => [ 'shape' => 'AttributeName', ], 'hashed' => [ 'shape' => 'Boolean', ], ], ], 'SchemaInputAttributes' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaInputAttribute', ], 'max' => 35, 'min' => 2, ], 'SchemaList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'SchemaMappingArn' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:(schemamapping/[a-zA-Z_0-9-]{1,255})', ], 'SchemaMappingList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaMappingSummary', ], ], 'SchemaMappingSummary' => [ 'type' => 'structure', 'required' => [ 'schemaName', 'schemaArn', 'createdAt', 'updatedAt', 'hasWorkflows', ], 'members' => [ 'schemaName' => [ 'shape' => 'EntityName', ], 'schemaArn' => [ 'shape' => 'SchemaMappingArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], 'hasWorkflows' => [ 'shape' => 'Boolean', ], ], ], 'Schemas' => [ 'type' => 'list', 'member' => [ 'shape' => 'SchemaList', ], ], 'ServiceType' => [ 'type' => 'string', 'enum' => [ 'ASSIGNMENT', 'ID_MAPPING', ], ], 'StartIdMappingJobInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityNameOrIdMappingWorkflowArn', 'location' => 'uri', 'locationName' => 'workflowName', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingJobOutputSourceConfig', ], ], ], 'StartIdMappingJobOutput' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingJobOutputSourceConfig', ], ], ], 'StartMatchingJobInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], ], ], 'StartMatchingJobOutput' => [ 'type' => 'structure', 'required' => [ 'jobId', ], 'members' => [ 'jobId' => [ 'shape' => 'JobId', ], ], ], 'StatementAction' => [ 'type' => 'string', 'max' => 64, 'min' => 3, 'pattern' => '(entityresolution:[a-zA-Z0-9]+)', ], 'StatementActionList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatementAction', ], 'min' => 1, ], 'StatementCondition' => [ 'type' => 'string', 'max' => 40960, 'min' => 1, ], 'StatementEffect' => [ 'type' => 'string', 'enum' => [ 'Allow', 'Deny', ], ], 'StatementId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[0-9A-Za-z]+', ], 'StatementPrincipal' => [ 'type' => 'string', 'max' => 64, 'min' => 12, 'pattern' => '(\\d{12})|([a-z0-9\\.]+)', ], 'StatementPrincipalList' => [ 'type' => 'list', 'member' => [ 'shape' => 'StatementPrincipal', ], 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagMap' => [ 'type' => 'map', 'key' => [ 'shape' => 'TagKey', ], 'value' => [ 'shape' => 'TagValue', ], 'max' => 200, 'min' => 0, ], 'TagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tags', ], 'members' => [ 'resourceArn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tags' => [ 'shape' => 'TagMap', ], ], ], 'TagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'Timestamp' => [ 'type' => 'timestamp', ], 'UniqueId' => [ 'type' => 'string', 'max' => 38, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]*', ], 'UniqueIdList' => [ 'type' => 'list', 'member' => [ 'shape' => 'HeaderSafeUniqueId', ], ], 'UntagResourceInput' => [ 'type' => 'structure', 'required' => [ 'resourceArn', 'tagKeys', ], 'members' => [ 'resourceArn' => [ 'shape' => 'VeniceGlobalArn', 'location' => 'uri', 'locationName' => 'resourceArn', ], 'tagKeys' => [ 'shape' => 'TagKeyList', 'location' => 'querystring', 'locationName' => 'tagKeys', ], ], ], 'UntagResourceOutput' => [ 'type' => 'structure', 'members' => [], ], 'UpdateIdMappingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'inputSourceConfig', 'idMappingTechniques', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'roleArn' => [ 'shape' => 'IdMappingRoleArn', ], ], ], 'UpdateIdMappingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'workflowArn', 'inputSourceConfig', 'idMappingTechniques', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', ], 'workflowArn' => [ 'shape' => 'IdMappingWorkflowArn', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'IdMappingWorkflowInputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'IdMappingWorkflowOutputSourceConfig', ], 'idMappingTechniques' => [ 'shape' => 'IdMappingTechniques', ], 'roleArn' => [ 'shape' => 'IdMappingRoleArn', ], ], ], 'UpdateIdNamespaceInput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceName', ], 'members' => [ 'idNamespaceName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'idNamespaceName', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'IdNamespaceInputSourceConfig', ], 'idMappingWorkflowProperties' => [ 'shape' => 'IdNamespaceIdMappingWorkflowPropertiesList', ], 'roleArn' => [ 'shape' => 'RoleArn', ], ], ], 'UpdateIdNamespaceOutput' => [ 'type' => 'structure', 'required' => [ 'idNamespaceName', 'idNamespaceArn', 'type', 'createdAt', 'updatedAt', ], 'members' => [ 'idNamespaceName' => [ 'shape' => 'EntityName', ], 'idNamespaceArn' => [ 'shape' => 'IdNamespaceArn', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'IdNamespaceInputSourceConfig', ], 'idMappingWorkflowProperties' => [ 'shape' => 'IdNamespaceIdMappingWorkflowPropertiesList', ], 'type' => [ 'shape' => 'IdNamespaceType', ], 'roleArn' => [ 'shape' => 'RoleArn', ], 'createdAt' => [ 'shape' => 'Timestamp', ], 'updatedAt' => [ 'shape' => 'Timestamp', ], ], ], 'UpdateMatchingWorkflowInput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'roleArn', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'workflowName', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'roleArn' => [ 'shape' => 'String', ], ], ], 'UpdateMatchingWorkflowOutput' => [ 'type' => 'structure', 'required' => [ 'workflowName', 'inputSourceConfig', 'outputSourceConfig', 'resolutionTechniques', 'roleArn', ], 'members' => [ 'workflowName' => [ 'shape' => 'EntityName', ], 'description' => [ 'shape' => 'Description', ], 'inputSourceConfig' => [ 'shape' => 'InputSourceConfig', ], 'outputSourceConfig' => [ 'shape' => 'OutputSourceConfig', ], 'resolutionTechniques' => [ 'shape' => 'ResolutionTechniques', ], 'incrementalRunConfig' => [ 'shape' => 'IncrementalRunConfig', ], 'roleArn' => [ 'shape' => 'String', ], ], ], 'UpdateSchemaMappingInput' => [ 'type' => 'structure', 'required' => [ 'schemaName', 'mappedInputFields', ], 'members' => [ 'schemaName' => [ 'shape' => 'EntityName', 'location' => 'uri', 'locationName' => 'schemaName', ], 'description' => [ 'shape' => 'Description', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], ], ], 'UpdateSchemaMappingOutput' => [ 'type' => 'structure', 'required' => [ 'schemaName', 'schemaArn', 'mappedInputFields', ], 'members' => [ 'schemaName' => [ 'shape' => 'EntityName', ], 'schemaArn' => [ 'shape' => 'SchemaMappingArn', ], 'description' => [ 'shape' => 'Description', ], 'mappedInputFields' => [ 'shape' => 'SchemaInputAttributes', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'VeniceGlobalArn' => [ 'type' => 'string', 'pattern' => 'arn:(aws|aws-us-gov|aws-cn):entityresolution:[a-z]{2}-[a-z]{1,10}-[0-9]:[0-9]{12}:((schemamapping|matchingworkflow|idmappingworkflow|idnamespace)/[a-zA-Z_0-9-]{1,255})', ], ],];
