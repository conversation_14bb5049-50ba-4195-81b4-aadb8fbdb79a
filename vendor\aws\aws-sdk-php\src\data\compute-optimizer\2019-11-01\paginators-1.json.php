<?php
// This file was auto-generated from sdk-root/src/data/compute-optimizer/2019-11-01/paginators-1.json
return [ 'pagination' => [ 'DescribeRecommendationExportJobs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'recommendationExportJobs', ], 'GetEnrollmentStatusesForOrganization' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'accountEnrollmentStatuses', ], 'GetLambdaFunctionRecommendations' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'lambdaFunctionRecommendations', ], 'GetRecommendationPreferences' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'recommendationPreferencesDetails', ], 'GetRecommendationSummaries' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'recommendationSummaries', ], ],];
