<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/env.php';

use Aws\S3\S3Client;
use Aws\Exception\AwsException;

// Konfigurasi R2
$r2Config = [
    'version' => 'latest',
    'region'  => 'auto',
    'endpoint' => 'https://' . env('R2_ACCOUNT_ID') . '.r2.cloudflarestorage.com',
    'credentials' => [
        'key'    => env('R2_ACCESS_KEY_ID'),
        'secret' => env('R2_SECRET_ACCESS_KEY'),
    ],
    'use_aws_shared_config_files' => false,
];

// Inisialisasi S3 Client untuk R2
$s3Client = new S3Client($r2Config);

// CORS configuration
$corsConfig = [
    'CORSRules' => [
        [
            'AllowedHeaders' => ['*'],
            'AllowedMethods' => ['GET', 'PUT', 'POST', 'DELETE', 'HEAD'],
            'AllowedOrigins' => ['*'], // <PERSON><PERSON> produksi, ganti dengan domain spesifik
            'ExposeHeaders' => ['ETag'],
            'MaxAgeSeconds' => 3600
        ]
    ]
];

try {
    // Set CORS configuration
    $result = $s3Client->putBucketCors([
        'Bucket' => env('R2_BUCKET_NAME'),
        'CORSConfiguration' => $corsConfig
    ]);
    
    echo "CORS configuration berhasil diatur!\n";
    
} catch (AwsException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?> 