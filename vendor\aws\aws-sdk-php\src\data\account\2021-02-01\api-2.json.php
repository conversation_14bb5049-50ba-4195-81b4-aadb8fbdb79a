<?php
// This file was auto-generated from sdk-root/src/data/account/2021-02-01/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2021-02-01', 'endpointPrefix' => 'account', 'jsonVersion' => '1.1', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS Account', 'serviceId' => 'Account', 'signatureVersion' => 'v4', 'signingName' => 'account', 'uid' => 'account-2021-02-01', 'auth' => [ 'aws.auth#sigv4', ], ], 'operations' => [ 'AcceptPrimaryEmailUpdate' => [ 'name' => 'AcceptPrimaryEmailUpdate', 'http' => [ 'method' => 'POST', 'requestUri' => '/acceptPrimaryEmailUpdate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'AcceptPrimaryEmailUpdateRequest', ], 'output' => [ 'shape' => 'AcceptPrimaryEmailUpdateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'DeleteAlternateContact' => [ 'name' => 'DeleteAlternateContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/deleteAlternateContact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeleteAlternateContactRequest', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DisableRegion' => [ 'name' => 'DisableRegion', 'http' => [ 'method' => 'POST', 'requestUri' => '/disableRegion', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DisableRegionRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'EnableRegion' => [ 'name' => 'EnableRegion', 'http' => [ 'method' => 'POST', 'requestUri' => '/enableRegion', 'responseCode' => 200, ], 'input' => [ 'shape' => 'EnableRegionRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetAccountInformation' => [ 'name' => 'GetAccountInformation', 'http' => [ 'method' => 'POST', 'requestUri' => '/getAccountInformation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAccountInformationRequest', ], 'output' => [ 'shape' => 'GetAccountInformationResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetAlternateContact' => [ 'name' => 'GetAlternateContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/getAlternateContact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAlternateContactRequest', ], 'output' => [ 'shape' => 'GetAlternateContactResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetContactInformation' => [ 'name' => 'GetContactInformation', 'http' => [ 'method' => 'POST', 'requestUri' => '/getContactInformation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetContactInformationRequest', ], 'output' => [ 'shape' => 'GetContactInformationResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetPrimaryEmail' => [ 'name' => 'GetPrimaryEmail', 'http' => [ 'method' => 'POST', 'requestUri' => '/getPrimaryEmail', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetPrimaryEmailRequest', ], 'output' => [ 'shape' => 'GetPrimaryEmailResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetRegionOptStatus' => [ 'name' => 'GetRegionOptStatus', 'http' => [ 'method' => 'POST', 'requestUri' => '/getRegionOptStatus', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetRegionOptStatusRequest', ], 'output' => [ 'shape' => 'GetRegionOptStatusResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListRegions' => [ 'name' => 'ListRegions', 'http' => [ 'method' => 'POST', 'requestUri' => '/listRegions', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListRegionsRequest', ], 'output' => [ 'shape' => 'ListRegionsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], 'PutAccountName' => [ 'name' => 'PutAccountName', 'http' => [ 'method' => 'POST', 'requestUri' => '/putAccountName', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutAccountNameRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'PutAlternateContact' => [ 'name' => 'PutAlternateContact', 'http' => [ 'method' => 'POST', 'requestUri' => '/putAlternateContact', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutAlternateContactRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'PutContactInformation' => [ 'name' => 'PutContactInformation', 'http' => [ 'method' => 'POST', 'requestUri' => '/putContactInformation', 'responseCode' => 200, ], 'input' => [ 'shape' => 'PutContactInformationRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'StartPrimaryEmailUpdate' => [ 'name' => 'StartPrimaryEmailUpdate', 'http' => [ 'method' => 'POST', 'requestUri' => '/startPrimaryEmailUpdate', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartPrimaryEmailUpdateRequest', ], 'output' => [ 'shape' => 'StartPrimaryEmailUpdateResponse', ], 'errors' => [ [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'TooManyRequestsException', ], [ 'shape' => 'InternalServerException', ], ], ], ], 'shapes' => [ 'AcceptPrimaryEmailUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'Otp', 'PrimaryEmail', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'Otp' => [ 'shape' => 'Otp', ], 'PrimaryEmail' => [ 'shape' => 'PrimaryEmailAddress', ], ], ], 'AcceptPrimaryEmailUpdateResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'PrimaryEmailUpdateStatus', ], ], ], 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'errorType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AccountCreatedDate' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'AccountId' => [ 'type' => 'string', 'pattern' => '^\\d{12}$', ], 'AccountName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'pattern' => '^[ -;=?-~]+$', 'sensitive' => true, ], 'AddressLine' => [ 'type' => 'string', 'max' => 60, 'min' => 1, 'sensitive' => true, ], 'AlternateContact' => [ 'type' => 'structure', 'members' => [ 'AlternateContactType' => [ 'shape' => 'AlternateContactType', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'Name' => [ 'shape' => 'Name', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'Title' => [ 'shape' => 'Title', ], ], ], 'AlternateContactType' => [ 'type' => 'string', 'enum' => [ 'BILLING', 'OPERATIONS', 'SECURITY', ], ], 'City' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'CompanyName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'errorType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ContactInformation' => [ 'type' => 'structure', 'required' => [ 'AddressLine1', 'City', 'CountryCode', 'FullName', 'PhoneNumber', 'PostalCode', ], 'members' => [ 'AddressLine1' => [ 'shape' => 'AddressLine', ], 'AddressLine2' => [ 'shape' => 'AddressLine', ], 'AddressLine3' => [ 'shape' => 'AddressLine', ], 'City' => [ 'shape' => 'City', ], 'CompanyName' => [ 'shape' => 'CompanyName', ], 'CountryCode' => [ 'shape' => 'CountryCode', ], 'DistrictOrCounty' => [ 'shape' => 'DistrictOrCounty', ], 'FullName' => [ 'shape' => 'FullName', ], 'PhoneNumber' => [ 'shape' => 'ContactInformationPhoneNumber', ], 'PostalCode' => [ 'shape' => 'PostalCode', ], 'StateOrRegion' => [ 'shape' => 'StateOrRegion', ], 'WebsiteUrl' => [ 'shape' => 'WebsiteUrl', ], ], ], 'ContactInformationPhoneNumber' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'pattern' => '^[+][\\s0-9()-]+$', 'sensitive' => true, ], 'CountryCode' => [ 'type' => 'string', 'max' => 2, 'min' => 2, 'sensitive' => true, ], 'DeleteAlternateContactRequest' => [ 'type' => 'structure', 'required' => [ 'AlternateContactType', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'AlternateContactType' => [ 'shape' => 'AlternateContactType', ], ], ], 'DisableRegionRequest' => [ 'type' => 'structure', 'required' => [ 'RegionName', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'RegionName' => [ 'shape' => 'RegionName', ], ], ], 'DistrictOrCounty' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'EmailAddress' => [ 'type' => 'string', 'max' => 254, 'min' => 1, 'pattern' => '^[\\s]*[\\w+=.#|!&-]+@[\\w.-]+\\.[\\w]+[\\s]*$', 'sensitive' => true, ], 'EnableRegionRequest' => [ 'type' => 'structure', 'required' => [ 'RegionName', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'RegionName' => [ 'shape' => 'RegionName', ], ], ], 'FullName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'GetAccountInformationRequest' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'GetAccountInformationResponse' => [ 'type' => 'structure', 'members' => [ 'AccountCreatedDate' => [ 'shape' => 'AccountCreatedDate', ], 'AccountId' => [ 'shape' => 'AccountId', ], 'AccountName' => [ 'shape' => 'AccountName', ], ], ], 'GetAlternateContactRequest' => [ 'type' => 'structure', 'required' => [ 'AlternateContactType', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'AlternateContactType' => [ 'shape' => 'AlternateContactType', ], ], ], 'GetAlternateContactResponse' => [ 'type' => 'structure', 'members' => [ 'AlternateContact' => [ 'shape' => 'AlternateContact', ], ], ], 'GetContactInformationRequest' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'GetContactInformationResponse' => [ 'type' => 'structure', 'members' => [ 'ContactInformation' => [ 'shape' => 'ContactInformation', ], ], ], 'GetPrimaryEmailRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], ], ], 'GetPrimaryEmailResponse' => [ 'type' => 'structure', 'members' => [ 'PrimaryEmail' => [ 'shape' => 'PrimaryEmailAddress', ], ], ], 'GetRegionOptStatusRequest' => [ 'type' => 'structure', 'required' => [ 'RegionName', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'RegionName' => [ 'shape' => 'RegionName', ], ], ], 'GetRegionOptStatusResponse' => [ 'type' => 'structure', 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], 'RegionOptStatus' => [ 'shape' => 'RegionOptStatus', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'errorType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'ListRegionsRequest' => [ 'type' => 'structure', 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'MaxResults' => [ 'shape' => 'ListRegionsRequestMaxResultsInteger', ], 'NextToken' => [ 'shape' => 'ListRegionsRequestNextTokenString', ], 'RegionOptStatusContains' => [ 'shape' => 'RegionOptStatusList', ], ], ], 'ListRegionsRequestMaxResultsInteger' => [ 'type' => 'integer', 'box' => true, 'max' => 50, 'min' => 1, ], 'ListRegionsRequestNextTokenString' => [ 'type' => 'string', 'max' => 1000, 'min' => 0, ], 'ListRegionsResponse' => [ 'type' => 'structure', 'members' => [ 'NextToken' => [ 'shape' => 'String', ], 'Regions' => [ 'shape' => 'RegionOptList', ], ], ], 'Name' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'sensitive' => true, ], 'Otp' => [ 'type' => 'string', 'pattern' => '^[a-zA-Z0-9]{6}$', 'sensitive' => true, ], 'PhoneNumber' => [ 'type' => 'string', 'max' => 25, 'min' => 1, 'pattern' => '^[\\s0-9()+-]+$', 'sensitive' => true, ], 'PostalCode' => [ 'type' => 'string', 'max' => 20, 'min' => 1, 'sensitive' => true, ], 'PrimaryEmailAddress' => [ 'type' => 'string', 'max' => 64, 'min' => 5, 'sensitive' => true, ], 'PrimaryEmailUpdateStatus' => [ 'type' => 'string', 'enum' => [ 'PENDING', 'ACCEPTED', ], ], 'PutAccountNameRequest' => [ 'type' => 'structure', 'required' => [ 'AccountName', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'AccountName' => [ 'shape' => 'AccountName', ], ], ], 'PutAlternateContactRequest' => [ 'type' => 'structure', 'required' => [ 'AlternateContactType', 'EmailAddress', 'Name', 'PhoneNumber', 'Title', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'AlternateContactType' => [ 'shape' => 'AlternateContactType', ], 'EmailAddress' => [ 'shape' => 'EmailAddress', ], 'Name' => [ 'shape' => 'Name', ], 'PhoneNumber' => [ 'shape' => 'PhoneNumber', ], 'Title' => [ 'shape' => 'Title', ], ], ], 'PutContactInformationRequest' => [ 'type' => 'structure', 'required' => [ 'ContactInformation', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'ContactInformation' => [ 'shape' => 'ContactInformation', ], ], ], 'Region' => [ 'type' => 'structure', 'members' => [ 'RegionName' => [ 'shape' => 'RegionName', ], 'RegionOptStatus' => [ 'shape' => 'RegionOptStatus', ], ], ], 'RegionName' => [ 'type' => 'string', 'max' => 50, 'min' => 1, ], 'RegionOptList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Region', ], ], 'RegionOptStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'ENABLING', 'DISABLING', 'DISABLED', 'ENABLED_BY_DEFAULT', ], ], 'RegionOptStatusList' => [ 'type' => 'list', 'member' => [ 'shape' => 'RegionOptStatus', ], ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'errorType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'SensitiveString' => [ 'type' => 'string', 'sensitive' => true, ], 'StartPrimaryEmailUpdateRequest' => [ 'type' => 'structure', 'required' => [ 'AccountId', 'PrimaryEmail', ], 'members' => [ 'AccountId' => [ 'shape' => 'AccountId', ], 'PrimaryEmail' => [ 'shape' => 'PrimaryEmailAddress', ], ], ], 'StartPrimaryEmailUpdateResponse' => [ 'type' => 'structure', 'members' => [ 'Status' => [ 'shape' => 'PrimaryEmailUpdateStatus', ], ], ], 'StateOrRegion' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'String' => [ 'type' => 'string', ], 'Title' => [ 'type' => 'string', 'max' => 50, 'min' => 1, 'sensitive' => true, ], 'TooManyRequestsException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'errorType' => [ 'shape' => 'String', 'location' => 'header', 'locationName' => 'x-amzn-ErrorType', ], 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, 'retryable' => [ 'throttling' => true, ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'fieldList' => [ 'shape' => 'ValidationExceptionFieldList', ], 'message' => [ 'shape' => 'SensitiveString', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionField' => [ 'type' => 'structure', 'required' => [ 'message', 'name', ], 'members' => [ 'message' => [ 'shape' => 'SensitiveString', ], 'name' => [ 'shape' => 'String', ], ], ], 'ValidationExceptionFieldList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ValidationExceptionField', ], ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'invalidRegionOptTarget', 'fieldValidationFailed', ], ], 'WebsiteUrl' => [ 'type' => 'string', 'max' => 256, 'min' => 1, 'sensitive' => true, ], ],];
