<?php
// Allow CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config/database.php';
require_once 'config/env.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Log data yang diterima
    error_log("Received POST data: " . print_r($_POST, true));
    error_log("Received FILES data: " . print_r($_FILES, true));
    
    // Validasi input
    $required_fields = [
        'full_name', 'phone', 'email', 'domicile', 'has_experience',
        'meta_ads_frequency', 'pixel_experience', 'ai_tools_usage',
        'technical_explanation_comfort', 'campaign_failure_experience',
        'technical_explanation_experience', 'client_confusion_approach',
        'consultant_role_opinion', 'case_study_response', 'joining_motivation'
    ];

    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            throw new Exception("Field {$field} harus diisi");
        }
    }

    // Validasi email
    if (!filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
        throw new Exception("Format email tidak valid");
    }

    // Validasi nomor telepon (minimal 10 digit, maksimal 15 digit)
    if (!preg_match('/^[0-9]{10,15}$/', $_POST['phone'])) {
        throw new Exception("Format nomor telepon tidak valid");
    }

    // Validasi tools_used (checkbox)
    if (empty($_POST['tools_used'])) {
        throw new Exception("Pilih minimal satu tools yang pernah digunakan");
    }

    // Mulai transaksi
    $pdo->beginTransaction();
    
    // Proses tools_used (checkbox) menjadi JSON
    $tools_used = isset($_POST['tools_used']) ? json_encode($_POST['tools_used']) : '[]';
    
    // Insert ke tabel applications2
    $sql = "INSERT INTO applications2 (
        full_name, phone, email, domicile, has_experience,
        experience_description, meta_ads_frequency, pixel_experience,
        ai_tools_usage, technical_explanation_comfort, tools_used,
        other_tools, campaign_failure_experience, technical_explanation_experience,
        client_confusion_approach, consultant_role_opinion,
        case_study_response, joining_motivation, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

    $stmt = $pdo->prepare($sql);
    
    // Eksekusi query dengan data dari form
    $result = $stmt->execute([
        $_POST['full_name'],
        $_POST['phone'],
        $_POST['email'],
        $_POST['domicile'],
        $_POST['has_experience'],
        $_POST['experience_description'] ?? null,
        $_POST['meta_ads_frequency'],
        $_POST['pixel_experience'],
        $_POST['ai_tools_usage'],
        $_POST['technical_explanation_comfort'],
        $tools_used,
        $_POST['other_tools'] ?? null,
        $_POST['campaign_failure_experience'],
        $_POST['technical_explanation_experience'],
        $_POST['client_confusion_approach'],
        $_POST['consultant_role_opinion'],
        $_POST['case_study_response'],
        $_POST['joining_motivation']
    ]);
    
    if (!$result) {
        throw new Exception("Database error: " . implode(", ", $stmt->errorInfo()));
    }
    
    $application_id = $pdo->lastInsertId();
    
    // Handle file uploads
    if (!empty($_FILES['portfolio_files']['name'][0])) {
        $upload_dir = 'uploads/portfolio2/';
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }

        $allowed_types = [
            'application/pdf',
            'image/jpeg',
            'image/png',
            'audio/mpeg',
            'audio/wav',
            'video/mp4',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        ];

        $max_file_size = 10 * 1024 * 1024; // 10MB

        foreach ($_FILES['portfolio_files']['tmp_name'] as $key => $tmp_name) {
            $file_name = $_FILES['portfolio_files']['name'][$key];
            $file_type = $_FILES['portfolio_files']['type'][$key];
            $file_size = $_FILES['portfolio_files']['size'][$key];
            $file_error = $_FILES['portfolio_files']['error'][$key];

            // Validasi file
            if ($file_error !== UPLOAD_ERR_OK) {
                throw new Exception("Error uploading file {$file_name}");
            }

            if (!in_array($file_type, $allowed_types)) {
                throw new Exception("Tipe file {$file_name} tidak diizinkan");
            }

            if ($file_size > $max_file_size) {
                throw new Exception("Ukuran file {$file_name} melebihi batas maksimal (10MB)");
            }

            // Generate unique filename
            $file_extension = pathinfo($file_name, PATHINFO_EXTENSION);
            $new_file_name = uniqid() . '_' . time() . '.' . $file_extension;
            $file_path = $upload_dir . $new_file_name;

            // Move uploaded file
            if (!move_uploaded_file($tmp_name, $file_path)) {
                throw new Exception("Gagal menyimpan file {$file_name}");
            }

            // Insert file info ke database
            $sql = "INSERT INTO portfolio_files2 (
                application_id, file_name, file_path, file_type, file_size, created_at
            ) VALUES (?, ?, ?, ?, ?, NOW())";

            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                $application_id,
                $file_name,
                $file_path,
                $file_type,
                $file_size
            ]);
        }
    }
    
    // Commit transaksi
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Aplikasi berhasil dikirim'
    ]);
    
} catch (Exception $e) {
    // Rollback jika terjadi error
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    // Hapus file yang sudah terupload jika terjadi error
    if (isset($file_path) && file_exists($file_path)) {
        unlink($file_path);
    }
    
    error_log("Error processing form: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Terjadi kesalahan saat memproses aplikasi: ' . $e->getMessage()
    ]);
} 